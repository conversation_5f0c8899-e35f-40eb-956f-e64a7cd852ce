{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "allowSyntheticDefaultImports": true, "incremental": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "target": "es2015", "allowJs": true, "types": ["node", "webspeechapi"], "typeRoots": ["node_modules/@types", "./types"], "lib": ["es2018", "dom"]}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableIvy": true}}