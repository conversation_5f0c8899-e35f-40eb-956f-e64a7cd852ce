include: api-deployment-config.yml  # Include file with variables containing pipeline config

# Create function for preparing bash scripts to execute.

.shell_script: &configure_ci_scripts
  - echo "Setting CI scripts to executable within ${CI_SCRIPT_BASE_DIR}..."
  - . $CI_SCRIPT_BASE_DIR/bootstrap_cicd_env.sh

# Build API using latest commit when not performing an API rollback

build_latest_api:
  stage: build_latest_api
  image: node:18.18.2-bullseye-slim

  # rules:
  #   - if: $CI_API_ROLLBACK !~ /^standard/i && $CI_API_ROLLBACK !~ /^emergency/i  # Case insensitive matches (typ.)

  script:
    - echo "Running API build stage..."
    - echo "ASG name is $CI_API_ASG_NM"
    - *configure_ci_scripts
    - . $CI_SCRIPT_DIR/build_api.sh

  # Explanation on cache key files here: https://docs.gitlab.com/ee/ci/yaml/#cachekeyfiles
  # Tips on cache here: https://www.reddit.com/r/devops/comments/fnhzux/best_practice_to_use_cache_for_gitlab_ci/
  cache:
    key:
      files:
        - package.json
        - package-lock.json
      prefix: $CI_COMMIT_BRANCH
    paths:
      - node_modules/
      - .npm/

  environment:
    name: $CI_COMMIT_BRANCH

  artifacts:
    untracked: true # This picks up the .tgz output by build_api.sh as well as the uncompressed build results
    expire_in: 2 hours

  tags:
    - docker-15

# Create standby API instance in the ASG to test build on before deploying to live APIs if it doesn't exist
# This runs only when not performing an API rollback

deploy_test_api:
  stage: deploy_test_api
  image: public.ecr.aws/e0w3i6s4/vea-api-base:latest
  dependencies: []        # We don't need to download artifacts from previous stage here (typ.)

  # Run this stage only if USE_TEST_API_INSTANCE is set to true within the project's CI/CD variables (by default
  # this is true for all release branches. You need to create a new variable and scope it to the environment you
  # want to disable this job for.
  # This also applies to the get_ansible_container and test_latest_api_build jobs which all relate to testing the API
  # build.

  only:
    variables:
      - $USE_TEST_API_INSTANCE =~ /^true/i

  script:
    - echo "Running deploy_test_api stage..."
    - *configure_ci_scripts
    - . $CI_SCRIPT_DIR/configure_aws_cli.sh
    - . $CI_SCRIPT_DIR/deploy_test_api.sh --api_asg_nm $CI_API_ASG_NM

  before_script:
  #- apk add --no-cache bash curl git openssl build-base libffi-dev openssl-dev bzip2-dev zlib-dev readline-dev sqlite-dev
  # Use PyENV to install a newer version of python than available on the docker image
  #   in order for `pip install awscli` to install
  #- curl https://pyenv.run | bash
  #- export PATH="/root/.pyenv/bin:$PATH"
  #- eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)"
  #- pyenv install 3.11
  #- pyenv global 3.11
  #- python -m ensurepip
  #- pip install --upgrade pip
  #- pip install Cython
  #- cython --version
  #- pip install awscli
  - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
  - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
  - aws configure set region ca-central-1
  - aws sts get-caller-identity
  #- apk add --update --no-cache docker
  - echo $(aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin 066432346479.dkr.ecr.ca-central-1.amazonaws.com)
  # This job outputs a text file containing the instance ID of the test API instance which API builds are tested on
  # This file is referenced in the test_latest_api_build job
  artifacts:
    untracked: true
    expire_in: 2 hours

  environment:
    name: $CI_COMMIT_BRANCH

  tags:
    - docker-15

# Test API build on the Standby API instance for the ASG
# This runs only when not performing an API rollback

test_latest_api_build:
  image:
    name: docker:26.1.3
    entrypoint: [""]  # Overwrite the default entrypoint
  services:
    - name: docker:26.1.3-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  stage: test_latest_api_build
  dependencies:
    - build_latest_api
    - deploy_test_api

  only:
    variables:
      - $USE_TEST_API_INSTANCE =~ /^true/i

  before_script:
    - apk add --no-cache bash curl git openssl build-base libffi-dev openssl-dev bzip2-dev zlib-dev readline-dev sqlite-dev jq
    - curl https://pyenv.run | bash
    - export PATH="/root/.pyenv/bin:$PATH"
    - eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)"
    - pyenv install 3.11
    - pyenv global 3.11
    - python -m ensurepip
    - pip install -q --upgrade pip
    - pip install -q Cython
    - cython --version
    - pip install -q awscli
    - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
    - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
    - aws configure set region ca-central-1
    - aws sts get-caller-identity
    - echo $(aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com)
    # - $CI_SCRIPT_DIR/setup_ssm.sh

  script:
    - echo "Running test_latest_api_build stage..."
    - cat $CI_PROJECT_DIR/$CI_COMMIT_SHA-test_api_instance_id
    - $CI_SCRIPT_DIR/upload_files_to_s3.sh
    - $CI_SCRIPT_DIR/connect_to_deployment_instance.sh

  after_script:
    - apk add --no-cache bash curl git openssl build-base libffi-dev openssl-dev bzip2-dev zlib-dev readline-dev sqlite-dev jq
    # remove older pyenv install
    - rm -rf "/root/.pyenv"
    - curl https://pyenv.run | bash
    - export PATH="/root/.pyenv/bin:$PATH"
    - eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)"
    - pyenv install 3.11
    - pyenv global 3.11
    - python -m ensurepip
    - pip install -q --upgrade pip
    - pip install -q Cython
    - cython --version
    - pip install -q awscli
    - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
    - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
    - aws configure set region ca-central-1
    - aws sts get-caller-identity
    - echo $(aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com)
    - echo "Cleaning up test resources..."
    - $CI_SCRIPT_DIR/cleanup_test_resources.sh

  environment:
    name: $CI_COMMIT_BRANCH

  tags:
    - docker-15



# Upload latest API to S3 live release location for the ASG of the current branch
# This runs only when not performing an API rollback

release_api_build:
  image:
    name: docker:26.1.3
    entrypoint: [""]  # Overwrite the default entrypoint
  services:
    - name: docker:26.1.3-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  stage: release_api_build
  dependencies:
    - build_latest_api
  needs:
    - build_latest_api
  when: manual

  before_script:
    - apk add --no-cache bash curl git openssl build-base libffi-dev openssl-dev bzip2-dev zlib-dev readline-dev sqlite-dev
    - curl https://pyenv.run | bash
    - export PATH="/root/.pyenv/bin:$PATH"
    - eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)"
    - pyenv install 3.11
    - pyenv global 3.11
    - python -m ensurepip
    - pip install --upgrade pip
    - pip install Cython
    - cython --version
    - pip install awscli
    - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
    - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
    - aws configure set region ca-central-1
    - aws sts get-caller-identity
    - echo $(aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com)

  script:
    - echo "Running release_latest_api_build stage..."
    - *configure_ci_scripts
    - . $CI_SCRIPT_DIR/configure_aws_cli.sh
    - docker pull $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com/$AWS_ECR_NM:$AWS_ECR_CONTAINER_TAG
    - export ANSIBLE_PLAYBOOK="/project/scripts/gitlab-ci-scripts/api-deploy-automation/ansible/update_api.yml"
    - docker run --rm -e CI_API_ASG_NM -e CI_API_ROLLBACK -e CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER -e CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER -e CI_SCRIPT_DIR -e CI_COMMIT_SHA -e CI_COMMIT_BRANCH -e ANSIBLE_INVENTORY_NM -e ANSIBLE_PLAYBOOK -v $CI_PROJECT_DIR:/project $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com/$AWS_ECR_NM:$AWS_ECR_CONTAINER_TAG /bin/bash /project/docker_command_release.sh

  environment:
    name: $CI_COMMIT_BRANCH
    action: start

  allow_failure: false  # Setting this to false prevents next stages from running before this one

  tags:
    - docker-15


# Perform instance refresh after uploading new API build, to ensure ASG instances are
# using the new build
# Note: with alpine 3.18, we can apk add aws-cli directly

deploy_api:
  stage: deploy_api
  dependencies:
    - release_api_build
  needs:
    - release_api_build

  before_script:
    - apk add --no-cache bash curl git openssl build-base libffi-dev openssl-dev bzip2-dev zlib-dev readline-dev sqlite-dev jq
    - curl https://pyenv.run | bash
    - export PATH="/root/.pyenv/bin:$PATH"
    - eval "$(pyenv init -)" && eval "$(pyenv virtualenv-init -)"
    - pyenv install 3.11
    - pyenv global 3.11
    - python -m ensurepip
    - pip install --upgrade pip
    - pip install Cython
    - cython --version
    - pip install awscli
    # - curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip"
    # - unzip awscliv2.zip
    # - aws/install
    - aws --version
    - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
    - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
    - aws configure set region ca-central-1
    - aws sts get-caller-identity

  script:
    - echo "Running deploy_api stage..."
    - echo "ASG name is $CI_API_ASG_NM"
    - *configure_ci_scripts
    - . $CI_SCRIPT_DIR/configure_aws_cli.sh
    - bash $CI_SCRIPT_DIR/refresh_apis.sh --api_asg_nm "$CI_API_ASG_NM"

  environment:
    name: $CI_COMMIT_BRANCH
    action: start

  allow_failure: false  # Setting this to false prevents next stages from running before this one

  tags:
    - docker-15

check_endpoints:
  stage: .post
  allow_failure:
    exit_codes: 2
  #stage: check_endpoints
  # image:
  #   name: docker:26.1.3
  #   entrypoint: [""]  # Overwrite the default entrypoint
  #services:
  #  - name: docker:26.1.3-dind
  #    alias: docker
  # variables:
  #   DOCKER_HOST: tcp://docker:2375
  #   DOCKER_TLS_CERTDIR: ""
  dependencies:
    - deploy_api
  needs:
    - deploy_api

  before_script:
    - apk add --no-cache python3 py3-pip jq bash curl > /dev/null 2>&1
    - pip3 install --upgrade pip > /dev/null 2>&1
    - pip3 install awscli > /dev/null 2>&1
    - aws configure set aws_access_key_id $CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER
    - aws configure set aws_secret_access_key $CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER
    - aws configure set region ca-central-1

  script:
    - echo "Running check_endpoints stage..."
    #- *configure_ci_scripts
    #- . $CI_SCRIPT_BASE_DIR/configure_aws_cli.sh
    #- docker pull $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com/$AWS_ECR_NM:$AWS_ECR_CONTAINER_TAG
    #- export ANSIBLE_PLAYBOOK="/project/scripts/gitlab-ci-scripts/api-deploy-automation/ansible/check_githash_nodevalue.yml"
    #- docker run --rm -v $CI_PROJECT_DIR:/project -e CI_API_ASG_NM -e CI_API_ROLLBACK -e CI_AWS_ACCESS_KEY_ID_GITLAB_RUNNER -e CI_AWS_SECRET_KEY_ID_GITLAB_RUNNER -e CI_SCRIPT_DIR -e CI_COMMIT_SHA -e CI_COMMIT_BRANCH -e ANSIBLE_INVENTORY_NM -e ANSIBLE_PLAYBOOK $CI_AWS_ACCT_ID.dkr.ecr.$AWS_ECR_REGION.amazonaws.com/$AWS_ECR_NM:$AWS_ECR_CONTAINER_TAG bash -c 'ls /project/scripts/gitlab-ci-scripts/api-deploy-automation/bash/ec2-list.sh && if [ -f /project/scripts/gitlab-ci-scripts/api-deploy-automation/bash/ec2-list.sh ]; then /project/scripts/gitlab-ci-scripts/api-deploy-automation/bash/ec2-list.sh; else echo "ec2-list.sh not found" && exit 1; fi'
    - *configure_ci_scripts
    - . $CI_SCRIPT_DIR/configure_aws_cli.sh
    - |
      if ! . $CI_SCRIPT_DIR/validate_instance_count.sh $CI_API_ASG_NM; then
        echo "scale up failed, could cause issues during deployment"
        exit 2
      fi
    - $CI_SCRIPT_BASE_DIR/check_githash_nodevalue.sh


  environment:
    name: $CI_COMMIT_BRANCH
    action: start

  tags:
    - docker-15

finalize_deployment:
  stage: .post
  dependencies:
    - check_endpoints
  needs:
    - check_endpoints

  before_script:
    - apk add --no-cache python3 py3-pip jq bash curl > /dev/null 2>&1


  script:
   - echo "Running finalize_deployment stage..."

  after_script:
   # run it in after_script so we get the right CI job status
   # it should check for a successful deployment
   - $CI_SCRIPT_BASE_DIR/finalize_deployment.sh


  environment:
    name: $CI_COMMIT_BRANCH
    action: start

  allow_failure: false  # Setting this to false prevents next stages from running before this one

  tags:
    - docker-15
