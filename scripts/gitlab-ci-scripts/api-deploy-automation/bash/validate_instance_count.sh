#!/bin/bash

# Exit script if any args are not defined
if [[ -z "$1" ]]; then
  echo -e "\nOne or more arguments missing when calling script! Terminating pipeline."
  exit 1
fi

# Print all arguments
echo "All arguments:"
echo "$@"

# Set default value for api_asg_nm
api_asg_nm=${1:-null}
instance_min=2

# Verify api_asg_nm is not null
if [[ "$api_asg_nm" == "null" ]]; then
  echo -e "\nASG name is required! Terminating pipeline."
  exit 1
fi

# Get count of healthy instances
instance_count=$(aws autoscaling describe-auto-scaling-groups \
    --auto-scaling-group-names "$api_asg_nm" \
    --query 'AutoScalingGroups[0].Instances[?LifecycleState==`InService` && HealthStatus==`Healthy`].[InstanceId]' \
    --output text | wc -l)

# Check if instance count is less than 4
if [[ "$instance_count" -lt $instance_min ]]; then
  echo "ASG active instances less than $instance_min, scaling to $instance_min"
  aws autoscaling set-desired-capacity --auto-scaling-group-name "$api_asg_nm" --desired-capacity 4
fi
