locals {
  api_domain    = var.api_domain != null ? var.api_domain : "${var.deployment_name}-api.${var.base_domain}"
  api_domain_zone_ids = { for d in compact([local.api_domain, var.api_domain_alt])
      : d
      => join("", [regex(".*?([a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+)$", d)[0], "."]) == data.aws_route53_zone.for_api_domain.name ? data.aws_route53_zone.for_api_domain.zone_id : data.aws_route53_zone.for_api_domain_alt.zone_id
    }
  asg_name = "vea-${var.deployment_name}-asg"
}

data "aws_route53_zone" "for_api_domain" {
  name = regex(".*?([a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+)$", local.api_domain)[0]
}

data "aws_route53_zone" "for_api_domain_alt" {
  name = var.api_domain_alt != "" ? regex(".*?([a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+)$", var.api_domain_alt)[0] : "vretta.com"
}

resource "aws_cloudfront_origin_access_identity" "default" {
  comment = var.deployment_name
}

resource "aws_acm_certificate" "api" {
  domain_name       = local.api_domain
  validation_method = "DNS"
  subject_alternative_names = compact([var.api_domain_alt])
  tags = {
    terraform_group = "vea-${var.deployment_name}"
    billing-group = "vea"
    Name = local.api_domain
    Product = var.product_tag
    Type = var.type_tag
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "api_cert_validation" {
  depends_on = [aws_acm_certificate.api]
  for_each = {
    for dvo in aws_acm_certificate.api.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
      zone_id = local.api_domain_zone_ids[dvo.domain_name]
    }
  }
  name    = each.value.name
  type    = each.value.type
  zone_id = each.value.zone_id
  records = [each.value.record]
  ttl     = "60"
}

resource "aws_acm_certificate_validation" "api" {
  certificate_arn = aws_acm_certificate.api.arn
  validation_record_fqdns = [
    for v in aws_route53_record.api_cert_validation : v.fqdn
  ]
}

data "aws_vpc" "vea" {
  id = var.vpc_id
}

data "aws_subnet" "api_a" {
  id = var.api_a_subnet_id
}

data "aws_subnet" "api_b" {
  id = var.api_b_subnet_id
}

# data "aws_subnets" "pubs" {
#   filter {
#     name   = "vpc-id"
#     values = var.pub_subnet_ids
#   }
# }

data "aws_security_group" "vpc_default" {
  vpc_id = var.vpc_id
  name = "default"
}

# data "aws_security_group" "allow_nat" {
#   vpc_id = var.vpc_id
#   name = "allow_nat"
# }

data "aws_security_group" "allow_http" {
  id = var.allow_http_security_group_id
}

data "aws_security_group" "allow_tls" {
  id = var.allow_tls_security_group_id
}

data "aws_sns_topic" "general_systems_alarm" {
  name = "general-systems-alarm"
}

resource "aws_lb" "api" {
  name               = "vea-${var.deployment_name}-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [data.aws_security_group.vpc_default.id, data.aws_security_group.allow_http.id, data.aws_security_group.allow_tls.id]
  subnets            = var.nat_gateway_eip == "" ? [data.aws_subnet.api_a.id, data.aws_subnet.api_b.id] : [var.pub_subnet_ids[0], var.pub_subnet_ids[1]]

  idle_timeout       = var.lb_idle_timeout

  access_logs {
    bucket  = "vretta-logs-ca-central-1"
    prefix  = "ec2-elb/${local.api_domain}"
    enabled = true
  }

  tags = {
    terraform_group = "vea-${var.deployment_name}"
    billing-group = "vea"
    Name = "vea-${var.deployment_name}:vea-${var.deployment_name}-lb"
    Division = "K-12"
    Product = var.product_tag
    Type = var.type_tag
  }
}



resource "aws_route53_record" "api" {
  zone_id = data.aws_route53_zone.for_api_domain.zone_id
  name    = local.api_domain
  type    = "A"

  alias {
    name                   = aws_lb.api.dns_name
    zone_id                = aws_lb.api.zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "api_alt" {
  count = var.api_domain_alt == "" ? 0 : 1
  zone_id = data.aws_route53_zone.for_api_domain_alt.zone_id
  name    = var.api_domain_alt
  type    = "A"

  alias {
    name                   = aws_lb.api.dns_name
    zone_id                = aws_lb.api.zone_id
    evaluate_target_health = true
  }
}

resource "aws_lb_target_group" "api" {
  name     = "mpt-${var.deployment_name}-tg"
  port     = 3030
  protocol = "HTTP"
  vpc_id   = data.aws_vpc.vea.id
  deregistration_delay = 150
  health_check {
    path = "/public/ping/1"
    matcher = "200"
    unhealthy_threshold = 2
  }
}

resource "aws_lb_listener" "api_http_redirect" {
  load_balancer_arn = aws_lb.api.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "api_tls" {
  load_balancer_arn = aws_lb.api.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-FS-1-2-Res-2019-08"
  certificate_arn   = aws_acm_certificate_validation.api.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api.arn
  }
}

# Store user data script which is able to reference Terraform variables / resources, specifically the name of the
# log group created for each deployment. This is used to ensure error and output logs of API instances send their logs
# to this group.
# We also pass in the names of the error and output log streams created by Terraform as well.
# This information is generated from resources within cloudwatch-api-log-alerts.tf

data "template_file" "user-data" {

  vars = {
    log_group_name      = aws_cloudwatch_log_group.log_group.name
    enable_api_s3_deployments = var.enable_api_s3_deployments
    # This must match aws_autoscaling_group_api.name or else API deploys will fail
    # Was unable to find a way to set this to aws_autoscaling_group.api.name without getting an "Error: Cycle:" error
    # When running terraform apply
    asg_name            = var.api_branch_override == null ? local.asg_name : var.api_branch_override
    api_config_file = var.api_config_file
    api_config_data = var.api_config_data
    deployment_name = var.deployment_name
  }

  template = var.arctic_wolf_agent_enabled ? file("modules/mpt-instance/templates/user-data-s3-deploy-with-arctic-wolf.sh") : file("modules/mpt-instance/templates/user-data-s3-deploy.sh")
}

# Create Launch Configuration and use the user data script to configure instances launched within the ASG

resource "aws_launch_template" "as_conf" {
  name_prefix   = "mpt-${var.deployment_name}-lt-"
  image_id      = var.mpt_api_ami_id
  instance_type = var.api_instance_class
  update_default_version = true

  network_interfaces {
    delete_on_termination = true
    associate_public_ip_address = var.nat_gateway_eip == ""
    security_groups = compact([data.aws_security_group.vpc_default.id, var.nat_gateway_eip == "" ? null : data.aws_security_group.allow_http.id, data.aws_security_group.allow_tls.id])
  }

  iam_instance_profile {
    name = var.asg_instance_iam_role
  }

  user_data = base64encode(data.template_file.user-data.rendered)

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "mpt-${var.deployment_name}-lt"
      Product = var.product_tag
      Type = var.type_tag
    }
  }
  tag_specifications {
    resource_type = "volume"
    tags = {
      Name = "mpt-${var.deployment_name}-lt"
      Product = var.product_tag
      Type = var.type_tag
    }
  }

  tags = {
    Name = "vea-${var.deployment_name}-lt"
    Product = var.product_tag
    Type = var.type_tag
  }

  lifecycle {
    create_before_destroy = true
  }
}



resource "aws_autoscaling_group" "api" {

  name                 = local.asg_name
    launch_template {
    id = aws_launch_template.as_conf.id
    version = aws_launch_template.as_conf.latest_version
}
  min_size             = var.api_asg_min_size
  desired_capacity     = var.api_asg_desired_capacity
  max_size             = var.api_asg_max_size
  vpc_zone_identifier  = [data.aws_subnet.api_a.id, data.aws_subnet.api_b.id]
  target_group_arns    = [aws_lb_target_group.api.arn]
  health_check_grace_period = 200
  health_check_type = "ELB"

  enabled_metrics= [
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances"
  ]

  lifecycle {
    create_before_destroy = true

    ignore_changes = [
      min_size,
      max_size,
      desired_capacity
    ]

  }

  tag {
    key = "terraform_group"
    value = "vea-${var.deployment_name}"
    propagate_at_launch = true
  }

  tag {
    key = "billing-group"
    value = "vea"
    propagate_at_launch = true
  }

  tag {
    key = "Product"
    value = var.product_tag
    propagate_at_launch = true
  }

  tag {
    key = "Type"
    value = var.type_tag
    propagate_at_launch = true
  }

  tag {
    key = "Name"
    value = local.asg_name
    propagate_at_launch = true
  }
}

# Create lifecycle hook for API scaling group. This lets us specify a a startup time APIs are allowed prior to bringing
# them in service to receive traffic. API's need time to start up to download builds from S3 and restart the API process
# During testing with t2.medium API instances, this startup process completes in 150 seconds (2.5 minutes).

resource "aws_autoscaling_lifecycle_hook" "api_startup_lifecycle_hook" {

  name                    = "api-startup-lifecycle-hook"
  autoscaling_group_name  = aws_autoscaling_group.api.name
  default_result          = "CONTINUE"
  heartbeat_timeout       = 200                                   # Startup time to allow APIs
  lifecycle_transition    = "autoscaling:EC2_INSTANCE_LAUNCHING"

}

resource "aws_cloudwatch_metric_alarm" "api_tg_unhealthy_hosts" {

  # Only create this alarm if vea_cloudwatch_alarms_enabled variable is set to true from deployments.tf
  count = var.vea_cloudwatch_alarms_enabled == true ? 1 : 0

  alarm_name = "vea-${var.deployment_name}-tg/unhealthy-hosts"
  actions_enabled = true
  metric_name = "UnHealthyHostCount"
  namespace = "AWS/ApplicationELB"
  statistic = "Average"
  dimensions = {
    TargetGroup = aws_lb_target_group.api.arn_suffix
    LoadBalancer = aws_lb.api.arn_suffix
  }
  period = 300
  evaluation_periods = 1
  datapoints_to_alarm = 1
  threshold = 0
  comparison_operator = "GreaterThanThreshold"
  treat_missing_data = "notBreaching"
  alarm_actions             = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]

  tags = {
    Name = "vea-${var.deployment_name}-tg/unhealthy-hosts"
    Product = var.product_tag
    Type = var.type_tag
  }
}

resource "aws_cloudwatch_metric_alarm" "api_tg_5XX_resp" {

  # Only create this alarm if vea_cloudwatch_alarms_enabled variable is set to true from deployments.tf
  count = var.vea_cloudwatch_alarms_enabled == true ? 1 : 0

  alarm_name = "vea-${var.deployment_name}-tg/5XX"
  actions_enabled = true
  metric_name = "HTTPCode_Target_5XX_Count"
  namespace = "AWS/ApplicationELB"
  statistic = "Sum"
  dimensions = {
    TargetGroup = aws_lb_target_group.api.arn_suffix
    LoadBalancer = aws_lb.api.arn_suffix
  }
  period = 300
  evaluation_periods = 1
  datapoints_to_alarm = 1
  threshold = 0
  comparison_operator = "GreaterThanThreshold"
  treat_missing_data = "notBreaching"
  alarm_actions             = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]
  ok_actions = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]

  tags = {
    Name = "vea-${var.deployment_name}-tg/5XX"
    Product = var.product_tag
    Type = var.type_tag
  }
}

resource "aws_cloudwatch_metric_alarm" "api_lb_504_resp" {

  # Only create this alarm if vea_cloudwatch_alarms_enabled variable is set to true from deployments.tf
  count = var.vea_cloudwatch_alarms_enabled == true ? 1 : 0

  alarm_name = "vea-${var.deployment_name}-lb/504"
  actions_enabled = true
  metric_name = "HTTPCode_ELB_504_Count"
  namespace = "AWS/ApplicationELB"
  statistic = "Sum"
  dimensions = {
    LoadBalancer = aws_lb.api.arn_suffix
  }
  period = 300
  evaluation_periods = 1
  datapoints_to_alarm = 1
  threshold = 0
  comparison_operator = "GreaterThanThreshold"
  treat_missing_data = "notBreaching"
  alarm_actions             = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]
  ok_actions = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]

  tags = {
    Name = "vea-${var.deployment_name}-lb/504"
    Product = var.product_tag
    Type = var.type_tag
  }
}

resource "aws_cloudwatch_metric_alarm" "api_lb_response_time" {

  # Only create this alarm if vea_cloudwatch_alarms_enabled variable is set to true from deployments.tf
  count = var.vea_cloudwatch_alarms_enabled == true ? 1 : 0

  alarm_name = "vea-${var.deployment_name}-lb/response-time"
  actions_enabled = true
  evaluation_periods = 5
  datapoints_to_alarm = 4
  comparison_operator = "GreaterThanUpperThreshold"
  threshold_metric_id = "ad1"
  treat_missing_data = "ignore"
  metric_query {
    id = "m1"
    metric {
      namespace = "AWS/ApplicationELB"
      metric_name = "TargetResponseTime"
      dimensions = {
        LoadBalancer = aws_lb.api.arn_suffix
      }
      period = 60
      stat = "p90"
    }
    return_data = true
  }
  metric_query {
    expression = "ANOMALY_DETECTION_BAND(m1, 25)"
    id = "ad1"
    label = "TargetResponseTime (expected)"
    return_data = true
  }
  alarm_actions             = [
    data.aws_sns_topic.general_systems_alarm.arn
  ]

  tags = {
    Name = "vea-${var.deployment_name}-lb/response-time"
    Product = var.product_tag
    Type = var.type_tag
  }
}

# Create CloudWatch alarm for ASG API instance high CPU utilization
# CPU metrics are automatically colected without enabling monitoring for the ASG

resource "aws_cloudwatch_metric_alarm" "api_high_cpu" {

  # Only create this alarm if vea_cloudwatch_alarms_enabled variable is set to true from deployments.tf
  count               = var.vea_cloudwatch_alarms_enabled == true ? 1 : 0

  alarm_name          = "vea-${var.deployment_name}-api-high-cpu-alert"
  actions_enabled     = true
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  statistic           = "Average"   # Set measurement type to average CPU utilization
  dimensions          = {
    AutoScalingGroupName = aws_autoscaling_group.api.name
  }
  period              = 600         # Set measurement period to 10 minutes
  evaluation_periods  = 1
  datapoints_to_alarm = 1 # If CPU utilization exceeds the threshold once over the measurement period, trigger alarm
  threshold           = 90          # Set threshold ot 90% CPU utilization
  comparison_operator = "GreaterThanThreshold"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [
    var.vea_cloudwatch_alarms_sns_arn
  ]
  ok_actions          = [
    var.vea_cloudwatch_alarms_sns_arn
  ]

  tags = {
    Name = "vea-${var.deployment_name}-api-high-cpu-alert"
    Product = var.product_tag
    Type = var.type_tag
  }
}
