locals {
  primary_web_ui_domain = "${var.deployment_name}.${var.base_domain}"
  all_web_ui_domains = distinct(compact(concat([local.primary_web_ui_domain], var.web_ui_alt_domains, [var.serve_ui_from_base_domain ? var.base_domain : ""])))
  s3_origin_id  = "S3-${local.primary_web_ui_domain}"
  web_ui_domain_zone_ids = { for d in local.all_web_ui_domains
      : d
      => join("", [regex(".*?([a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+)$", d)[0], "."]) == data.aws_route53_zone.en.name ? data.aws_route53_zone.en.zone_id : join("", [regex(".*?([a-zA-Z0-9-]+\\.[a-zA-Z0-9-]+)$", d)[0], "."]) == data.aws_route53_zone.fr.name ? data.aws_route53_zone.fr.zone_id : data.aws_route53_zone.vretta.zone_id
    }
}
variable "create_cname_records" { default = {} }

resource "aws_s3_bucket" "web_ui_storage" {
  bucket = local.primary_web_ui_domain
  acl    = "private"

  tags = {
    terraform_group = "mpt-${var.deployment_name}"
    billing-group = "mpt"
    Name = "${var.deployment_name}.${var.base_domain}"
    Product = var.product_tag
    Type = var.type_tag
  }

  dynamic "cors_rule" {
    for_each = var.web_ui_cors_origins
    content {
      allowed_headers = ["*"]
      allowed_methods = ["GET"]
      allowed_origins = [cors_rule.value]
      expose_headers  = []
      max_age_seconds = 0
    }
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  logging {
    target_bucket = "vretta-logs-ca-central-1"
    target_prefix = "s3/${local.primary_web_ui_domain}/"
  }

}

resource "aws_iam_group" "web_ui_storage_deployers" {
  name = "s3-deployers-${local.primary_web_ui_domain}"
}

resource "aws_iam_policy" "web_ui_storage_deployers" {
  name        = "s3-deployers-${local.primary_web_ui_domain}-policy"
  description = "grants authority to create and update files in the s3 bucket backing the web ui"
  policy      = data.aws_iam_policy_document.web_ui_storage_deployers.json
}

resource "aws_iam_group_policy_attachment" "web_ui_storage_deployers" {
  group      = aws_iam_group.web_ui_storage_deployers.name
  policy_arn = aws_iam_policy.web_ui_storage_deployers.arn
}

data "aws_iam_policy_document" "web_ui_storage_deployers" {
  statement {
    actions   = ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"]
    resources = ["${aws_s3_bucket.web_ui_storage.arn}/*"]
  }

  statement {
    actions   = ["s3:ListBucket"]
    resources = [aws_s3_bucket.web_ui_storage.arn]
  }

  statement {
    actions   = ["cloudfront:List*","cloudfront:Get*","cloudfront:CreateInvalidation"]
    resources = [aws_cloudfront_distribution.web_ui.arn]
  }
}

resource "aws_route53_record" "web_ui" {
  for_each = aws_cloudfront_distribution.web_ui.aliases
  zone_id  = local.web_ui_domain_zone_ids[each.value]
  name     = each.value
  type     = "A"

  alias {
    name                   = aws_cloudfront_distribution.web_ui.domain_name
    zone_id                = aws_cloudfront_distribution.web_ui.hosted_zone_id
    evaluate_target_health = true
  }
}

resource "aws_acm_certificate" "web_ui" {
  provider          = aws.us-east
  domain_name       = local.primary_web_ui_domain
  subject_alternative_names = distinct(compact(concat(var.web_ui_alt_domains, [var.serve_ui_from_base_domain ? var.base_domain : ""])))
  validation_method = "DNS"
  tags = {
    terraform_group = "mpt-${var.deployment_name}"
    billing-group = "mpt"
    Product = var.product_tag
    Type = var.type_tag
  }

  lifecycle {
    create_before_destroy = true
  }

  options {
    certificate_transparency_logging_preference = "ENABLED"
  }

}

resource "aws_route53_record" "web_ui_cert_validation" {
  depends_on = [aws_acm_certificate.web_ui]
  for_each = {
    for dvo in aws_acm_certificate.web_ui.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
      zone_id = local.web_ui_domain_zone_ids[dvo.domain_name]
    }
  }
  name    = each.value.name
  type    = each.value.type
  zone_id = each.value.zone_id
  records = [each.value.record]
  ttl     = "60"
}
resource "aws_route53_record" "additional_cnames" {
  for_each = var.create_cname_records

  zone_id  = "ZQUJ73WUHW90F"  # Given zone ID
  name     = each.key
  type     = "CNAME"
  ttl      = "300"
  records  = [each.value]
}

resource "aws_acm_certificate_validation" "web_ui" {
  provider        = aws.us-east
  certificate_arn = aws_acm_certificate.web_ui.arn
  validation_record_fqdns = [
    for v in aws_route53_record.web_ui_cert_validation : v.fqdn
  ]
}

resource "aws_cloudfront_origin_access_identity" "default" {
  comment = var.deployment_name
}

data "aws_iam_policy_document" "s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.web_ui_storage.arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.default.iam_arn]
    }
  }

  statement {
    actions   = ["s3:ListBucket"]
    resources = [aws_s3_bucket.web_ui_storage.arn]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.default.iam_arn]
    }
  }
}

resource "aws_s3_bucket_policy" "web_ui_storage" {
  bucket = aws_s3_bucket.web_ui_storage.id
  policy = data.aws_iam_policy_document.s3_policy.json
}

resource "aws_cloudfront_distribution" "web_ui" {
  origin {
    domain_name = aws_s3_bucket.web_ui_storage.bucket_regional_domain_name
    origin_id   = local.s3_origin_id

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.default.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = local.primary_web_ui_domain
  default_root_object = "index.html"

  logging_config {
    include_cookies = true
    bucket          = "vretta-logs-ca-central-1.s3.amazonaws.com"
    prefix          = "cloudfront/${local.primary_web_ui_domain}"
  }

  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/error.html"
  }

  aliases = local.all_web_ui_domains

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = length(var.web_ui_cors_origins) == 0 ? ["GET", "HEAD"] : ["GET", "HEAD", "OPTIONS"]
    target_origin_id = local.s3_origin_id

    forwarded_values {
      headers = length(var.web_ui_cors_origins) == 0 ? null : ["Access-Control-Request-Headers", "Access-Control-Request-Method", "Origin"]
      query_string = false

      cookies {
        forward = "none"
      }
    }

    ## Setting default cache, origin request, and response header policies is only supported in AWS Provider V3 and above
    ## https://www.notion.so/vretta/DC-Align-all-environments-Cloudfront-cache-settings-1b66ed9659b280e2aef1f290d51f2124
    # cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6" # CachingOptimized
    # origin_request_policy_id   = "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf" # CORS-S3Origin
    # response_headers_policy_id = "eaab4381-ed33-4a86-88ca-d9558dc6cd63" # CORS-with-preflight-and-SecurityHeadersPolicy

    min_ttl                = 0
    default_ttl            = 10800
    max_ttl                = 10800
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  tags = {
    terraform_group = "mpt-${var.deployment_name}"
    billing-group = "mpt"
    Name = "${var.deployment_name}.${var.base_domain}"
    Product = var.product_tag
    Type = var.type_tag
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate_validation.web_ui.certificate_arn
    minimum_protocol_version = "TLSv1.2_2018"
    ssl_support_method       = "sni-only"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
}
