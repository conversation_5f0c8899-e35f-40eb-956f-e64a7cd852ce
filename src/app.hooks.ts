// Application hooks that run for every service
// Don't remove this comment. It's needed to format import lines nicely.

import axiosErrorHandler from './hooks/axios-hooks';
import errorHandler from './hooks/error-handler';
import loggerHandler from './hooks/logger-handler';

export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [
      loggerHandler()
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [
      errorHandler(),
      axiosErrorHandler
    ],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
