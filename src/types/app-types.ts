export enum VEA_CONTEXT {
  IS_BCED = 'isBCED',
  IS_EQAO = 'isEQAO'
}

export interface IWebsocketConfig {
  authoring: {
    url: string;
    endpoint: string;
    region: string;
  },
  invigilation: {
    url: string;
    endpoint: string;
    region: string;
    secretPass: string;
    gracePeriod: number;
  }
}

export interface IRespondusConfig {
  secret_index: string;
  secret_1: string;
  secret_2: string;
  secret_iv: string;
  secret_version: number;
  cipher: string;
}