import { AuthenticationRequest } from '@feathersjs/authentication';
import { JWTStrategy } from '@feathersjs/authentication';
import { Params } from '@feathersjs/feathers';
import { Application } from '../declarations';
import { NotAuthenticated } from '@feathersjs/errors';
import logger from '../logger';

interface BlacklistCheckResult {
  isBlacklisted: boolean;
}

export class BlacklistJWTStrategy extends JWTStrategy {
  app: Application;

  constructor(app: Application) {
    super();
    this.app = app;
  }

  /**
   * Validates that an access token is provided
   * @param accessToken - The token to validate
   * @throws {NotAuthenticated} When no token is provided
   */
  private validateTokenPresence(accessToken: string | undefined): asserts accessToken is string {
    if (!accessToken) {
      throw new NotAuthenticated('No access token provided');
    }
  }

  /**
   * Handles errors from blacklist service calls
   * @param error - The error that occurred
   * @returns false to allow authentication to continue for non-critical errors
   * @throws {NotAuthenticated} For authentication-related errors
   */
  private handleBlacklistError(error: unknown): boolean {
    // If the error is from blacklist check (token invalidated), re-throw it
    if (error instanceof NotAuthenticated) {
      throw error;
    }

    // For other errors (like Redis connectivity), log but continue
    // This ensures the system doesn't break if Redis is temporarily unavailable
    logger.warn('Failed to check token blacklist, proceeding with standard validation', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return false;
  }

  /**
   * Check if a token is blacklisted using the JWT blacklist service
   * @param token - The JWT token to check
   * @returns Promise<boolean> - true if token is blacklisted, false otherwise
   */
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const result = await this.app.service('auth/jwt-blacklist').find({
        query: { token }
      }) as BlacklistCheckResult;

      return !!result.isBlacklisted;
    } catch (error) {
      return this.handleBlacklistError(error);
    }
  }

  /**
   * Validates that a token is not blacklisted
   * @param token - The token to validate
   * @throws {NotAuthenticated} When token is blacklisted
   */
  private async validateTokenNotBlacklisted(token: string): Promise<void> {
    const isBlacklisted = await this.isTokenBlacklisted(token);
    if (isBlacklisted) {
      throw new NotAuthenticated('Token has been invalidated');
    }
  }

  /**
   * Override the authenticate method to check token blacklist before standard JWT validation
   * @param authentication - The authentication request containing the access token
   * @param params - Additional parameters for authentication
   * @returns The result of the parent JWT strategy authentication
   */
  async authenticate(authentication: AuthenticationRequest, params: Params) {
    const { accessToken } = authentication;

    // Validate token presence
    this.validateTokenPresence(accessToken);

    // Validate token is not blacklisted
    await this.validateTokenNotBlacklisted(accessToken);

    // If token is valid and not blacklisted, proceed with normal JWT validation
    return super.authenticate(authentication, params);
  }
}
