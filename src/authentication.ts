import { ServiceAddons, Params } from '@feathersjs/feathers';
import { AuthenticationService } from '@feathersjs/authentication';
import { LocalStrategy } from '@feathersjs/authentication-local';
import { LoginKeyStrategy } from './login-key.strategy'
import { LoginAliasStrategy } from './auth-strategies/alias.strategy'
import { BlacklistJWTStrategy } from './auth-strategies/blacklist-jwt.strategy';
import { CustomAuthenticationService } from './custom-auth.service';
import { Application } from './declarations';
import { TotpStrategy } from './totp.strategy';
import resetFailedLoginAttempts from './hooks/reset-failed-login-attempts';
import { LoginSessionKeyStrategy } from './login-session-key.strategy';
import { LoginFieldTestStrategy } from './login-field-test.strategy';
import { LoginMarkerKeyStrategy } from './login-marker-key.strategy';

declare module './declarations' {
  interface ServiceTypes {
    'authentication': AuthenticationService & ServiceAddons<any>;
  }
}


  // class MyAuthService extends AuthenticationService {
  //   async getPayload(authResult:any, params:Params) {
  //     // Call original `getPayload` first
  //     var payload = await super.getPayload(authResult, params);
  //     const { user } = authResult;
  
  //     if (user) {
  //       payload = user;
  //     }

  //     return payload;
  //   }
  
  // }

  // const authentication = new MyAuthService(app);

export default function(app: Application) {
    
  //const authentication = new MyAuthService(app);
  const authentication = new CustomAuthenticationService(app);
  authentication.register('jwt', new BlacklistJWTStrategy(app));
  authentication.register('local', new LocalStrategy());
  authentication.register('alias', new LoginAliasStrategy(app));
  authentication.register('loginKey', new LoginKeyStrategy(app));
  authentication.register('totp', new TotpStrategy(app));
  authentication.register('loginFieldTest', new LoginFieldTestStrategy(app));
  authentication.register('loginMarkerKey', new LoginMarkerKeyStrategy(app));
  authentication.register('loginSessionKey', new LoginSessionKeyStrategy(app));

  app.use('/authentication', authentication);
  // app.configure(expressOauth());
// }
  app.service("authentication").hooks({
    before: 
    {
      all: [],
      find: [],
      get: [],
      create: [],
      update: [],
      patch: [],
      remove: []
    },
  
    after: 
    {
      all: [],
      find: [],
      get: [],
      create: [resetFailedLoginAttempts()],
      update: [],
      patch: [],
      remove: []
    },
  
    error: {
      all: [],
      find: [],
      get: [],
      create: [],
      update: [],
      patch: [],
      remove: []
    }
  });
}


