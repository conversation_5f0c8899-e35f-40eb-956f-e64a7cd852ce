<!--
  DEPRECATED COMPONENT - SECURITY REDIRECT
  This component is deprecated due to MFA bypass vulnerability.
  Users are automatically redirected to the secure login page.
-->
<div class="page-body">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    [accountType]="getCurrentAccountType()"
    ></header>
    <div class="page-content is-fullpage" >
      <div class="has-text-centered" style="padding: 2rem;">
        <div class="notification is-info">
          <p><strong>Redirecting to secure login...</strong></p>
          <p>You are being redirected to the secure login page for enhanced security.</p>
        </div>
      </div>
      <!-- Original form preserved but hidden due to redirect -->
      <div *ngIf="isLoaded" style="display: none;">
        <login-form
          [accountType]="accountType"
          [prefilledEmail]="prefilledEmail"
          (onLoginAttempt)="trackLoginAttempted()"
        ></login-form>
      </div>
    </div>
  </div>
  <footer [hasLinks]="true"></footer>
</div>
