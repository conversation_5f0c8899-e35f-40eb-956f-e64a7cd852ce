import { Component, OnInit } from '@angular/core';
import { BreadcrumbsService, IBreadcrumbRoute } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { Router } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { GridOptions } from 'ag-grid-community';

@Component({
  selector: 'validate-stu-ta-tass-count',
  templateUrl: './validate-stu-ta-tass-count.component.html',
  styleUrls: ['./validate-stu-ta-tass-count.component.scss']
})
export class ValidateStuTaTassCountComponent implements OnInit {

  constructor(
    private breadcrumbsService: BreadcrumbsService,
    private lang: LangService,
    private router: Router,
    private auth:AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService,
  ) { }

  public validateStudentTATASSCountForm = new FormGroup({
    twId: new FormControl(),
  });

  public breadcrumb: IBreadcrumbRoute[];

  hasLoaded
  isLoading

  /*
    Duplicate TestAttempts Grid
  */
  duplicateTestAttempts:any[] = [];
  duplicateTestAttemptsGridOptions:GridOptions = {
    columnDefs: [
      { headerName:'Test Window ID', field:'test_window_id',},
      { headerName:'School Mident', field:'school_mident'},
      { headerName:'Class Access Code', field:'class_access_code'},
      { headerName:'Test Session ID', field:'test_session_id'},
      { headerName:'Session Type', field:'scts_slug' },
      { headerName:'Student UID', field:'student_uid' },
      { headerName:'TWTDAR Order', field:'twtdar_order'},
      { headerName:'Attempt Counts', field:'ta_counts'},
      { headerName:'Attempt IDs', field:'ta_ids'},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  /*
    Test Attempt SubSessions Count Error Grid
  */
  testAttemptSubSessionsCountError:any[] = [];
  testAttemptSubSessionsCountErrorGridOptions:GridOptions = {
    columnDefs: [
      { headerName:'Test Window ID', field:'test_window_id',},
      { headerName:'School Mident', field:'school_mident'},
      { headerName:'Class Access Code', field:'class_access_code'},
      { headerName:'Test Session ID', field:'test_session_id'},
      { headerName:'Session Type', field:'scts_slug' },
      { headerName:'Student UID', field:'student_uid' },
      { headerName:'TWTDAR Order', field:'twtdar_order'},
      { headerName:'Attempt ID', field:'ta_id'},
      { headerName:'TSSS ID', field:'tsss_id'},
      { headerName:'TASS Count', field:'tass_count'},
      { headerName:'TASS IDs', field:'tass_ids'},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  /*
    Duplicate started operational test attempt in test window
  */
  duplicateTAsInTestWindow:any[] = [];
  duplicateTAsInTestWindowGridOptions:GridOptions = {
    columnDefs: [
      { headerName:'Test Window ID', field:'test_window_id',},
      { headerName:'School Mident', field:'school_mident'},
      { headerName:'Class Access Code', field:'class_access_code'},
      { headerName:'Test Session IDs', field:'test_session_ids'},
      { headerName:'Session Type', field:'scts_slug' },
      { headerName:'Student UID', field:'student_uid' },
      { headerName:'TWTDAR Order', field:'twtdar_order'},
      { headerName:'Attempt Counts', field:'ta_counts'},
      { headerName:'Attempt IDs', field:'ta_ids'},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  /*
    Unlinked test attempts
  */
    unlinkedTestAttempts:any[] = [];
    unlinkedTestAttemptsGridOptions:GridOptions = {
      columnDefs: [
        { headerName:'Test Window ID', field:'test_window_id'},
        { headerName:'Reported Issue ID', field:'ric_id'},
        { headerName:'School Mident', field:'school_mident'},
        { headerName:'Sample School', field:'is_sample_schl'},
        { headerName:'Class Access Code', field:'class_access_code'},
        { headerName:'Test Session ID', field:'test_session_id'},
        { headerName:'Session Type', field:'scts_slug' },
        { headerName:'Student UID', field:'student_uid' },
        { headerName:'TWTDAR Order', field:'twtdar_order'},
        { headerName:'Attempt ID', field:'ta_id'},
        { headerName:'Attempt Started On', field:'ta_started_on'},
        { headerName:'Attempt Invalid Reason', field:'ta_invalid_reason'},
        { headerName:'TAQR Count', field:'taqr_count'},
      ],
      defaultColDef: {
        filter: true,
        sortable: true,
        resizable: true,
      },
    };

  ngOnInit(): void {
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(this.lang.tra('title_dashboard'), `/${this.lang.c()}/support/dashboard`),
      this.breadcrumbsService._CURRENT('Schools', this.router.url),
    ];
  }

  /**
   * Call API and check if there's 
   * 1) student have duplicate test attempts in same test session
   * 2) test attempt subsessions counts/null test attempt sub session
   * 3) duplicate "started" operational (twtdar.is_secured = 1) test attempt in test window
   * 4) unlinked test attempts in test window
   * in the test window 
   */
  validateStudentTATASSCount(){
    this.isLoading = true;
    this.hasLoaded = false
    const query = {
      test_window_id: this.validateStudentTATASSCountForm.controls.twId.value,
    }
    this.auth
      .apiFind(this.routes.SUPPORT_VALIDATE_STU_TA_TASS_COUNTS, {query})
      .then (res => {
        this.duplicateTestAttempts = res.duplicateTestAttempts
        this.testAttemptSubSessionsCountError = res.testAttemptSubSessionsCountError
        this.duplicateTAsInTestWindow = res.duplicateTAsInTestWindow
        this.unlinkedTestAttempts = res.unlinkedTestAttempts
      }).catch( error => {
        this.loginGuard.quickPopup(error.message)
      }).finally(() => {
        this.hasLoaded = true;
        this.isLoading = false;
      })
  }

  /**
   * function to export Duplicate TestAttempts to CSV
   */
  exportDuplicateTestAttemptsCsv(){
    this.duplicateTestAttemptsGridOptions.api.exportDataAsCsv();
  }

  /**
   * function to export TestAttempt SubSessions Count Error to CSV
   */
  exportTestAttemptSubSessionsCountErrorCsv(){
    this.testAttemptSubSessionsCountErrorGridOptions.api.exportDataAsCsv();
  }

  /**
   * function to export Duplicate TAs In TestWindow to CSV
   */
  exportDuplicateTAsInTestWindowCsv(){
    this.duplicateTAsInTestWindowGridOptions.api.exportDataAsCsv();
  }

  /**
   * function to export unlinked TestAttempts to CSV
   */
  exportUnlinkedTestAttemptsCsv(){
    this.unlinkedTestAttemptsGridOptions.api.exportDataAsCsv();
  }
}
