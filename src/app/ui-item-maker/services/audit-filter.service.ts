import { Injectable, OnDestroy } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { AuditMem, ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { Subscription } from 'rxjs';
import { IAuditConfig } from '../widget-audits/data/audits';

export enum AuditFilterOptions {
  ISSUES = 'ISSUES',
  PASSED = 'PASSED',
  INFORMATIVE = 'INFORMATIVE',
  NOT_RAN = 'NOT_RAN'
}
export type AuditStatusMap = {
  [key in AuditFilterOptions]: {
    icon: string;
    color: string;
    tooltip: string;
    defaultState: boolean // filter state toggle on or off
  };
};

export const AuditStatusConfig: AuditStatusMap = {
  [AuditFilterOptions.ISSUES]: {
    icon: 'fa fa-exclamation-triangle',
    color: '#ce0000',
    tooltip: 'Have issues',
    defaultState: true,
  },
  [AuditFilterOptions.PASSED]: {
    icon: 'fa fa-check-circle',
    color: '#7bb658',
    tooltip: 'Have passed',
    defaultState: true,
  },
  [AuditFilterOptions.INFORMATIVE]: {
    icon: 'fa fa-info-circle',
    color: '#3e72c0',
    tooltip: 'Have informative',
    defaultState: true,
  },
  [AuditFilterOptions.NOT_RAN]: {
    icon: 'fa fa-question-circle',
    color: '#ffdd57',
    tooltip: 'Yet to run',
    defaultState: true,
  },
};


@Injectable()
export class AuditFilterService implements OnDestroy {
  AuditFilterOptions = AuditFilterOptions;

  private _filterOption = [
    AuditFilterOptions.INFORMATIVE,
    AuditFilterOptions.PASSED,
    AuditFilterOptions.ISSUES,
    AuditFilterOptions.NOT_RAN,
  ];

  private _filterOptionDict = AuditStatusConfig;

  private _filterControl = new FormGroup({
    [AuditFilterOptions.INFORMATIVE]: new FormControl(AuditStatusConfig[AuditFilterOptions.INFORMATIVE].defaultState),
    [AuditFilterOptions.PASSED]: new FormControl(AuditStatusConfig[AuditFilterOptions.PASSED].defaultState),
    [AuditFilterOptions.ISSUES]: new FormControl(AuditStatusConfig[AuditFilterOptions.ISSUES].defaultState),
    [AuditFilterOptions.NOT_RAN]: new FormControl(AuditStatusConfig[AuditFilterOptions.NOT_RAN].defaultState),
  });

  private _audits: any[] = [];
  private _hiddenAudits: Record<string, boolean> = {};
  auditCtrl:ItemBankAuditor;

  private subscription = new Subscription();

  constructor() {
    this.subscription.add(this.filterControl.valueChanges.subscribe(() => this.updateFilters())); // if updating filter
  }

  ngOnDestroy() {
    this.subscription.unsubscribe()
  }

  get filterOption() {
    return this._filterOption;
  }

  get filterOptionDict() {
    return this._filterOptionDict;
  }

  get filterControl() {
    return this._filterControl;
  }

  set filterControl(control: FormGroup) {
    // Unsubscribe from the old control
    this.subscription.unsubscribe();
    this.subscription = new Subscription();
    
    // Set the new control
    this._filterControl = control;
    
    // Subscribe to the new control
    this.subscription.add(this._filterControl.valueChanges.subscribe(() => this.updateFilters()));
  }

  filterControlbyOption(option:AuditFilterOptions): boolean{
    return !!this.filterControl.get(option)?.value
  }
  
  // Getter & Setter for audits
  get audits(): any[] {
    return this._audits;
  }

  set audits(audits: any[]) {
    this._audits = audits;
  }

  // Getter & Setter for hiddenAudits
  get hiddenAudits(): Record<string, boolean> {
    return this._hiddenAudits;
  }

  updateFilters(auditSlug?: string | boolean) {
    if (!this.audits) return;
    const audits = auditSlug
      ? this.audits.filter((audit) => audit.slug === auditSlug)
      : this.audits;

    audits.forEach((audit) => {
      const questionMem = this.getQuestionMemResult(audit.slug);
      const questionMemCopy = JSON.parse(JSON.stringify(questionMem));
      const { __metaData } = questionMem;

      let hide = true;
      let hasOneSubaudit = false;

      if (__metaData) {
        if (
          this.isFilterActive(AuditFilterOptions.INFORMATIVE) &&
          __metaData.informative
        ) {
          hide = false;
        }
        if (
          this.isFilterActive(AuditFilterOptions.PASSED) &&
          __metaData.pass
        ) {
          hide = false;
        }
        if (
          this.isFilterActive(AuditFilterOptions.ISSUES) &&
          __metaData.fail
        ) {
          hide = false;
        }
      } else if (this.isFilterActive(AuditFilterOptions.NOT_RAN)){
        hide = false
        hasOneSubaudit = true;
      }

      Object.values(questionMemCopy)?.forEach((result: any) => {
        const id = result?.id ?? result.key;
        if (id) {
          this.hiddenAudits[result.id] =
            this.isHiddenAuditCheck(result) || hide;
          hasOneSubaudit ||= !this.hiddenAudits[result.id];
        }
      });

      this.hiddenAudits[audit.slug] = hide || !hasOneSubaudit;
    });
  }

  isHiddenAuditCheck(result: any): any {
    if (result?.isHidden) {
      return true;
    }

    const hasFlagged = result?.items?.length || result.num_issues;
    const isInformative = result?.informativeOnly;

    if (
      this.isFilterActive(AuditFilterOptions.PASSED) &&
      !hasFlagged
    ) {
      return false;
    }
    if (
      this.isFilterActive(AuditFilterOptions.INFORMATIVE) &&
      hasFlagged &&
      isInformative
    ) {
      return false;
    }
    if (
      this.isFilterActive(AuditFilterOptions.ISSUES) &&
      hasFlagged &&
      !isInformative
    ) {
      return false;
    }

    return true;
  }

  isFilterActive(option: AuditFilterOptions): boolean {
    return !!this.filterControl.get(option)?.value;
  }
  
  getQuestionMemResult(slug: string){
    return this.auditCtrl.getQuestionMemResults(slug);
  }

  /**
   * 
   * @param audits List of audit that we'll be filtering
   * @param auditCtrl 
   * @param itemLevel is it item level audit filtering default (false)
   */
  initAuditFilterService(audits:IAuditConfig[], auditCtrl: ItemBankAuditor, itemLevel = false){
    this.audits = audits;
    this.auditCtrl = auditCtrl;
    if(itemLevel){
      const notRanIndex = this.filterOption.findIndex((opt) => opt === AuditFilterOptions.NOT_RAN);
      if (notRanIndex !== -1) {
        this.filterOption.splice(notRanIndex, 1);
      }
      this.filterOption.forEach(opt =>{
        const optControl = this.filterControl.get(opt);
        optControl.setValue(opt === AuditFilterOptions.ISSUES);
      })
    }

    this.updateFilters();

  }

  resetFilters(itemLevel = false){
    this.filterOption.forEach((opt)=>{
      this.filterControl.get(opt).setValue(itemLevel? opt === AuditFilterOptions.ISSUES : true);
    })
  }
}