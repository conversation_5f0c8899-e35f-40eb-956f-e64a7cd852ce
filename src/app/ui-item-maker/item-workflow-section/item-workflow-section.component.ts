import { Component, OnInit, ViewChild, Input} from '@angular/core';
import { TwiddleState } from '../../ui-partial/twiddle/twiddle.component';
import { MatStepper } from '@angular/material/stepper';
import { AUTH_WORKFLOW_STAGES, IWorkflowStage, IStageAssignee, initOldItemsRoles} from './model';
import {renderAssigneeOption, renderAssigneeChip, filterAssignees} from './functions'
import { ItemBankCtrl} from "../item-set-editor/controllers/item-bank";
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AuthRolesService } from '../auth-roles.service';
import { LangService } from 'src/app/core/lang.service';
import {AssignmentState} from './../../ui-scoring-leader/panel-sl-new-assignment/panel-sl-new-assignment.component'
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { ButtonState } from '../item-set-editor/models/types';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { WidgetAuthoringConnectionService } from '../widget-authoring-connection.service';

@Component({
  selector: 'item-workflow-section',
  templateUrl: './item-workflow-section.component.html',
  styleUrls: ['./item-workflow-section.component.scss']
})
export class ItemWorkflowSectionComponent implements OnInit {

  @Input() itemBankCtrl!: ItemBankCtrl;
  @Input() toggleIsComparingView: (stage:number)=>void;
  @Input() ws!: WidgetAuthoringConnectionService;

  itemWorkflowTwiddle = new TwiddleState(false);

  workflowStages = AUTH_WORKFLOW_STAGES.sort((a, b) => a.order - b.order);
  activeStage:IWorkflowStage;
  isStageStatusLoading:boolean = false;
  isItemBankUserLoading:boolean = false;
  dropdownStageOrder:number = 0;
  loadedAssignedUids:number[] = [];
  isAssignmentTouched:boolean = false;
  isStepSelectionCustomSkip: boolean = false;

  renderAssigneeOption = renderAssigneeOption;
  renderAssigneeChip = renderAssigneeChip;
  filterAssignees = filterAssignees;

  isSignedOff: boolean;
  isDoneEditing: boolean;
  isChangesRequired: boolean;
  isEditReview: boolean;
  numEnterEdit: number;
  numEnterReview: number;

  allItemBankUsers:IStageAssignee[] = [];
  potentialAssignees:IStageAssignee[] = [];
  selectedAssignees:IStageAssignee[] = [];
  ButtonState = ButtonState
  AssignmentState = AssignmentState;
  currAssignmentState:AssignmentState = AssignmentState.IDLE;

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private authRoles: AuthRolesService,
    public lang: LangService,
    private loginGuard: LoginGuardService
  ) {
  }

  async ngOnInit(): Promise<void> {
    await this.loadStageStatus();
    await this.loadItemBankUsers();
    if (this.activeStage) {
      this.resetPotentialAssignees();
      this.setSelectedAssignees(this.loadedAssignedUids);
    }
  }

  setSelectedAssignees(assigneeUids:number[]){
    this.selectedAssignees = this.potentialAssignees.filter(user => assigneeUids.includes(user.uid))
    this.itemBankCtrl.setWorkflowStageAssignees(this.selectedAssignees);
  }

  

  async loadStageStatus(){
    this.isStageStatusLoading = true;
    const test_question_id = this.itemBankCtrl.currentQuestion?.id;
    return this.auth.apiGet(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, test_question_id, {
      query: { lang: this.lang.c() }
    })
    .then((res) => {
      this.setActiveStage(res.stage_order)
      this.isSignedOff = !!res.is_signed_off;
      this.isDoneEditing = !!res.is_done_editing;
      this.isChangesRequired = !!res.is_changes_required;
      this.isEditReview = !!res.is_edit_review;
      this.loadedAssignedUids = res.assignedUids;
      this.numEnterEdit = res.numEnterEdit
      this.numEnterReview = res.numEnterReview
      this.refreshIsStageWorkCompleted();
      this.isStageStatusLoading = false;
      if (this.isTrackingChangeToggleRequired(res.stage_order)){
        this.itemBankCtrl.toggleCurrentQuestionTrackChanges()
      }
    })
  }

  async loadItemBankUsers(){
    this.isItemBankUserLoading = true;
    const {groupId, single_groupId} = this.itemBankCtrl;
    const query = {
      single_group_id: single_groupId,
      group_id: groupId,
      auth_group_ids: single_groupId ? [groupId, single_groupId] : groupId,
      getAuthEmails: true
    }

    return this.auth.apiFind(this.routes.TEST_AUTH_GROUP_MEMBERS, {query})
    .then((res: any[]) => {
      res.forEach(r => {
        this.allItemBankUsers.push({
          uid: r.id, 
          first_name: r.first_name,
          last_name: r.last_name,
          contact_email: r.contact_email,
          actual_role: this.authRoles.determineRoleFromRoleTypes(r.role_types)
        })
      })
    })
    .finally(() => {
      this.isItemBankUserLoading = false;
    })

  }

  @ViewChild('stepper') stepper: MatStepper;

  /** Handle step change - default handling if it's from clicking on a previous step */
  onStepSelectionChange(event: any){
    // If instructed to skip custom steps, do nothing else and change the setting back
    // Custom steps that follow should only happen when clicking on a previously completed step and attempting to revert back to it
    if (this.isStepSelectionCustomSkip) return this.isStepSelectionCustomSkip = false;
    //Effectively cancel the automatic step by returning to previous one
    this.isStepSelectionCustomSkip = true;
    this.stepperMoveToActiveIndex();
    // Process returning to previous stage
    this.initMoveToStage(event.selectedIndex+1, true)
  }

  setActiveStage(stageOrder: number){
    this.activeStage = AUTH_WORKFLOW_STAGES.find(stage => stage.order === stageOrder);
    // Update stage order in the set-wide list seen on the framework page also
    this.itemBankCtrl.setCurrWorkflowStage(stageOrder);
  }

  getSignOffSlug(){
    return (this.isSignedOff) ? "auth_workflow_remove_sign_off" : "auth_workflow_sign_off"
  }

  getDoneEditingSlug(){
    return (this.isDoneEditing) ? "auth_workflow_remove_no_edits_req" : "auth_workflow_no_edits_req"
  }

  getChangesRequiredSlug(){
    return (this.isChangesRequired) ? "auth_workflow_remove_changes_required" : "auth_workflow_changes_required"
  }

  getEditReviewSlug(){
    return (this.isEditReview) ? "auth_workflow_remove_edit_review" : "auth_workflow_edit_review"
  }

  async patchStageSettings(data: {is_done_editing?:number, is_changes_required?:number, is_signed_off?:number, is_edit_review?:number}){
    const test_question_id = this.itemBankCtrl.currentQuestion?.id
    return this.auth.apiPatch(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, test_question_id, data, {
      query: { lang: this.lang.c(), stage_order: this.activeStage.order }
    })
  }

  toggleEditReview(){
    const confirmCaption = this.lang.tra(this.isEditReview ? 'auth_workflow_remove_edit_review_confirm' : 'auth_workflow_edit_review_confirm')
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      confirm: () => {
        this.patchStageSettings({is_edit_review: this.isEditReview ? 0 : 1})
        .then(() => this.isEditReview = !this.isEditReview)
      }
    });
  }

  toggleDoneEditing(){
    const confirmCaption = this.lang.tra(this.isDoneEditing ? 'auth_workflow_remove_no_edits_req_confirm' : 'auth_workflow_no_edits_req_confirm')
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      confirm: () => {
        this.patchStageSettings({is_done_editing: this.isDoneEditing ? 0 : 1})
        .then(() => {
          this.isDoneEditing = !this.isDoneEditing
          this.refreshIsStageWorkCompleted();
        })
      }
    });
  }

  toggleChangesRequired(){
    const confirmCaption = this.lang.tra(this.isChangesRequired ? 'auth_workflow_remove_changes_required_confirm' : 'auth_workflow_changes_required_confirm')
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      confirm: () => {
        this.patchStageSettings({is_changes_required: this.isChangesRequired ? 0 : 1})
        .then(() => {
          this.isChangesRequired = !this.isChangesRequired
          this.refreshIsStageWorkCompleted();
        })
      }
    });
  }

  toggleSignedOff(){
    const confirmCaption = this.lang.tra(this.isSignedOff ? 'auth_workflow_remove_sign_off_confirm' : 'auth_workflow_sign_off_confirm')
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      confirm: () => {
        this.patchStageSettings({is_signed_off: this.isSignedOff ? 0 : 1})
        .then(() => {
          this.isSignedOff = !this.isSignedOff
          // When signing off on the final stage, the item should lock
          if (this.isSignedOff && this.activeStage.isFinalStage) this.handleQuestionLock();
          this.refreshIsStageWorkCompleted();
        })
      }
    });
  }

  toggleIsComparingViewAvoidPropagation($event, stage:number){
    $event.stopPropagation()
    this.toggleIsComparingView(stage)
  }

  /** Manually transition the stepper to move to active stage */
  stepperMoveToActiveIndex(){
    //Note: This needs setTimeout - https://github.com/angular/components/issues/8479#issuecomment-*********
     setTimeout( () => this.stepper.selectedIndex = this.activeStage.order - 1);
  }

  /** Is a move to the new stage not allowed due to pending graphic requests? */
  isPendingReqBlocked(newStageOrder: number){
    const isFinalStage = this.getStageByOrder(newStageOrder)?.isFinalStage
    const hasPendingReq = this.itemBankCtrl.getNumPendingGraphicReqs();
    return (isFinalStage && hasPendingReq)
  }


  initMoveToStage(stageOrder: number, isReturn?:boolean){
    // If user not permitted to move back to this stage from future stages, stop here
    if(isReturn && !this.canUserMoveBackTo(stageOrder)) return;

    // Can not move to final stage while pending graphic requests remain
    if (this.isPendingReqBlocked(stageOrder)) {
      this.loginGuard.quickPopup(this.lang.tra('auth_workflow_block_proceed_graphic_req'))
      return;
    }

    // Require confirmation before proceeding
    const newStageName = this.lang.tra(this.getStageByOrder(stageOrder)?.slug);
    const confirmCaption = isReturn ?  `${this.lang.tra('auth_workflow_back_to_confirm_prefix')} ${newStageName}?` : `${this.lang.tra('auth_workflow_proceed_to_confirm_prefix')} ${newStageName}?`;
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      confirm: async () => {
        try {
          if (this.itemBankCtrl.isEditActivationPending()) {
            await this.itemBankCtrl.finishManualEditing();
          } else {
            await this.itemBankCtrl.toggleManualEdit();
            if (this.itemBankCtrl.saveLoadCtrl.isSaving) {
              this.ws.updateItem(this.itemBankCtrl.currentQuestion.id);
            }
          }
        } catch (error) {
          console.error('Error saving item before stage transition:', error);
          this.loginGuard.quickPopup('Failed to save item. Please try again.');
          return;
        }
        
        // If need to enter/exit tracking changes, initiate this first. Stage move will happen once it's confirmed and succeeds.
        if (this.isTrackingChangeToggleRequired(stageOrder)) {
          this.itemBankCtrl.toggleCurrentQuestionTrackChanges(() => {
            this.moveToStage(stageOrder);
          })
        }
        else {
          this.moveToStage(stageOrder);
        }
      }
    })
  }

   /** If the new stage requires different tracking changes mode than what is currently set */
  isTrackingChangeToggleRequired(newStageOrder:number){
    const currentQuestion = this.itemBankCtrl.getCurrentQuestionContent();
    const stageByOrder = this.getStageByOrder(newStageOrder)
    const currTrackingChanges = currentQuestion?.isTrackingChanges;
    return (!!currTrackingChanges !== !!stageByOrder?.isTrackingChangesStage || (stageByOrder === undefined && !!stageByOrder?.isTrackingChangesStage));
  }


  async moveToStage(stageOrder:number){
    const test_question_id = this.itemBankCtrl.currentQuestion?.id
    this.auth.apiCreate(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, {stage_order: stageOrder}, {
      query: { test_question_id, lang: this.lang.c()}
    })
    .then((res) => {
      this.setActiveStage(stageOrder)

      // If just entered the editing stage, increment count
      if (this.activeStage.isEditStage) {
        this.incrementNumEnterEdit();
      }
      if (this.activeStage.isReviewStage){
        this.incrementNumEnterReview();
      }
      this.isStepSelectionCustomSkip = true;
      
      const assigneesFromPReviousStageUse = res.assignedUids || [];
      this.resetStageSettings(assigneesFromPReviousStageUse);

      this.refreshIsStageWorkCompleted();
      this.stepperMoveToActiveIndex();
    })
  }


  /** When entering the editing stage, incrememt the count of times entered */
  incrementNumEnterEdit(){
    this.numEnterEdit = (this.numEnterEdit ?? 0) + 1
    this.itemBankCtrl.setCurrNumEnterEdit(this.numEnterEdit);
  }

  /** When entering the review stage, incrememt the count of times entered */
  incrementNumEnterReview(){
    this.numEnterReview = (this.numEnterReview ?? 0) + 1
  }

  refreshIsStageWorkCompleted(){
    const isStageWorkCompleted = this.isSignedOff || this.isDoneEditing || this.isChangesRequired
    this.itemBankCtrl.updateCurrStageWorkCompleted(isStageWorkCompleted)
  }

  /** Lock an item if it is unlocked */
  handleQuestionLock(){
    if (!this.itemBankCtrl.getCurrentQuestionContent().isLocked) this.itemBankCtrl.toggleCurrentQuestionLock();
  }


  /** From all item bank users, only show the ones with user roles specifies for current stage in the assignment options */
  resetPotentialAssignees(){
    this.potentialAssignees = this.allItemBankUsers.filter(user => {
      return this.activeStage.assigneeRoles.includes(user.actual_role)
    })
  }

  /**
   * When moving to a new stage, reset all choices to blanks and update assignees
   * @param assigneeUids - list of selected assignees for the current stage to be reset
   */
  resetStageSettings(assigneeUids: number[]){
    this.isSignedOff = false;
    this.isDoneEditing = false;
    this.isChangesRequired = false;
    this.isEditReview = false;

    this.isAssignmentTouched = false;

    this.resetPotentialAssignees();
    this.setSelectedAssignees(assigneeUids);
  }

  getStageByOrder(stageOrder:number){
    return this.itemBankCtrl.getStageByOrder(stageOrder)
  }
  
  isPrevStage(stageOder: number) {
    return stageOder < this.activeStage.order;
  }

  /** "Item in Edit" title should only be in orange if currently in "Assessment Review" */
  isEditStageOrange(stage:IWorkflowStage){
    return (this.activeStage.isReviewStage && stage.isEditStage)
  }

  getCurrActualRole(){
    return this.itemBankCtrl.getCurrActualRole();
  }

  canUserAssign(stageOrder:number){
    return this.getStageByOrder(stageOrder).assignerRoles.includes(this.getCurrActualRole())
  }

  canUserChange(stageOrder:number){
    return this.getStageByOrder(stageOrder).changesRoles.includes(this.getCurrActualRole())
  }

  canUserMoveBackTo(stageOrder:number){
    const moveBackRoles = this.getStageByOrder(stageOrder).moveBackRoles
    return moveBackRoles && moveBackRoles.includes(this.getCurrActualRole())
  }
  
  canUserInitStageOldItem(){
    return initOldItemsRoles.includes(this.getCurrActualRole())
  }

  setAssignmentTouched(){
    this.isAssignmentTouched = true;
  }

  getSaveAssignmentSlug(){
    if (this.currAssignmentState==AssignmentState.SENT) return "btn_success"
    else if (this.currAssignmentState==AssignmentState.IDLE) return "auth_workflow_save_assignment"
    else if (this.currAssignmentState==AssignmentState.ERROR) return "btn_error"
  }

  /** Save assignments selected in the input*/
  async saveAssignment(){
    const data = {
      assigned_uids: this.selectedAssignees.map(user => user.uid)
    }
    this.currAssignmentState = AssignmentState.SENDING;

    const test_question_id = this.itemBankCtrl.currentQuestion?.id
    this.auth.apiPatch(this.routes.TEST_AUTH_QUESTION_WORKFLOW_STAGES, test_question_id, data, {
      query: { lang: this.lang.c(), stage_order: this.activeStage.order }
    })
    .then(() => {
      this.currAssignmentState = AssignmentState.SENT;
      // Update assignees in the set-wide list seen on the framework page also
      this.itemBankCtrl.setWorkflowStageAssignees(this.selectedAssignees)
    })
    .catch(() => {
      this.currAssignmentState = AssignmentState.ERROR;
    })
    .finally(() => {
      this.isAssignmentTouched = false;
      // Displayed success/error for 1.5secs only, then return to normal
      setTimeout(() => {
        this.currAssignmentState = AssignmentState.IDLE;
      }, 1500);
    })
  }

  async initSaveAssignment(){
    if (this.currAssignmentState !== AssignmentState.IDLE) return;
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('auth_workflow_save_assignment_confirm'),
      confirm: () => {
        this.saveAssignment()
      }
    });
  }
  stageButtonSlugs = {
    2: {idle:'btn_view_suggestions', loaded: "btn_exit_comparison"},
    3:  {idle:'btn_view_review', loaded: "btn_exit_review"}
  }
  getCompareButtonSlug(stage:number){
    const loadState = this.itemBankCtrl.getWorkflowViewButtonState(stage);
    if (loadState==ButtonState.LOADED) return this.stageButtonSlugs[stage].loaded
    else if (loadState==ButtonState.IDLE ) return this.stageButtonSlugs[stage].idle
    else if (loadState==ButtonState.ERROR) return "btn_error"
  }

  getCompareButtonState(stage:number, state: ButtonState){
    const loadState = this.itemBankCtrl.getWorkflowViewButtonState(stage);
    switch(state){
    case ButtonState.LOADING:
      return loadState==ButtonState.LOADING;
    case ButtonState.LOADED:
      return loadState==ButtonState.LOADED
    default:
      return loadState==ButtonState.ERROR
    }
  }
}
