<div class="page-body">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    ></header>
    <div class="page-content is-fullpage" >
        <h4><tra slug="el_notes_issues"></tra></h4>
        <menu-bar 
          [menuTabs]="issueTableTabs"
          [tabIdInit]=initialIssueTab
          (change)="selectIssuesTab($event)">
        </menu-bar>
        <p *ngIf="activeRecords?.data?.length">
          <tra slug="auth_comments_header"></tra>
        </p>  
        <p *ngIf="!activeRecords?.data?.length">
          There are no {{getResolvedText()}} issues related to your authoring groups to display.
        </p>

        <table>
          <tr>
            <!-- #REDO <th *ngFor="let col of issueCols"  (click)="headerClicked(header.id)" class="hover-header"> -->
            <th *ngFor="let col of issueCols" class="hover-header">
              <div class ="table-header-container">
                <tra [slug]="col.caption"></tra>
                <!-- #REDO <i *ngIf="selectedHeader === header.id && sortType === 'asc'" class="fa fa-arrow-up"> </i> -->
                <!-- #REDO <i *ngIf="selectedHeader === header.id && sortType === 'desc'" class="fa fa-arrow-down"></i> -->
              </div>
              <a *ngIf="col.prop" (click)="changeFilter($event, col.prop, col.caption)" style="white-space: nowrap;">
                <i class="fa fa-search"></i>
                {{filters.config[col.prop]}}
              </a>
            </th>
          </tr>
          <!-- #REDO <tr *ngFor="let issue of getFilteredIssues()"> -->
          <tr *ngFor="let issue of activeRecords?.data">
            <!-- <ng-container *ngIf="issue.show"> -->
            <td *ngFor="let col of issueCols">
              <div *ngIf="issueTableValue(issue, col.id) !== ''" [ngSwitch]="col.id">
                <div *ngSwitchCase="EIssueCol.ITEM_BANK">
                  <span class="tag">{{issueTableValue(issue, col.id)}}</span>
                </div>

                <div *ngSwitchCase="EIssueCol.ISSUE" class="columns">
                  <div class="column">
                    {{issueTableValue(issue, col.id)}}
                  </div>
                  <div class="column is-narrow">
                    <button class="button is-small is-outlined" (click)="commentContextModalStart(issue)">
                      <tra slug="btn_view_context"></tra>
                    </button>
                  </div>
                </div>

                <div *ngSwitchCase="EIssueCol.ISSUE_TYPE">
                  <span *ngIf="issueTableValue(issue, col.id)" class="tag is-light"
                  [class.is-warning]="issueTableValue(issue, col.id)===ENoteItemType.HIGHLIGHT"
                  [class.is-link]="issueTableValue(issue, col.id)!==ENoteItemType.HIGHLIGHT"
                  >{{issueTableValue(issue, col.id)}}</span>
                </div>
                <div *ngSwitchCase="EIssueCol.ITEM">
                  <!-- #REDO <a [routerLink]="generateIssueLink(issue)" [queryParams]="generateQueryParams(issue)">{{issueTableValue(issue, header.id)}}</a> -->
                  <a [routerLink]="generateIssueLink(issue)">{{issueTableValue(issue, col.id)}}</a>
                </div>
                <div *ngSwitchCase="EIssueCol.ASSIGNED_TO"> 
                  <a class="tag is-primary" (click)="reassignIssue(issue)" *ngIf="issue.assigned_uid && issue.assigned_name">
                    {{issueTableValue(issue, col.id)}}
                  </a> 
                </div>
                <div *ngSwitchDefault>
                  {{issueTableValue(issue, col.id)}}
                </div>
              </div>
            </td>
            <!-- </ng-container> -->
          </tr>
        </table>  
        <div style="margin-top: 0.5em; display: flex; align-items: center">
          <button class="button is-small" (click)="prevPage()" [disabled]="filters.currentPage == 1"> <i class="fa fa-caret-left" aria-hidden="true"></i> </button>
          <button class="button is-small" (click)="nextPage()" [disabled]="filters.currentPage >= getMaxPages()"> <i class="fa fa-caret-right" aria-hidden="true"></i> </button>
          Page {{filters.currentPage}} of {{getMaxPages()}}
          <div style="margin-left:0.2em; display:flex; align-items: center">
            <button class="button is-small" title="Entries per page" (click)="isShowingPageLength = !isShowingPageLength"> <i class="fas fa-cog" aria-hidden="true"></i> </button>
            <div style="display:flex; align-items: center">
                <form *ngIf="isShowingPageLength" [formGroup]="pageLengthG" (ngSubmit)="retrieveIssues()" >
                  <label>
                      &nbsp;&nbsp;Entries per page &nbsp;&nbsp;
                    <input type="number" style="width:5em" [formControl]="pageLength" />
                  </label>
                </form>
            </div>
          </div>
        </div>
      </div>
  </div>
  <footer [hasLinks]="true"></footer>
</div>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <element-notes 
      [itemId]="cmc().item_id"
      [nestedZoomNoteId]="cmc().comment_id"
      [initShowResolvedComments]="filters.config.is_resolved"
      [newCommentDisabled]="true"
      [itemType]="cmc().item_type"
      [graphicRequestId]="cmc().graphic_request_id"
    ></element-notes>
    <modal-footer [confirmButton]="false" [pageModal]="pageModal" [closeMessage]="'btn_close'"></modal-footer>
  </div>
</div>