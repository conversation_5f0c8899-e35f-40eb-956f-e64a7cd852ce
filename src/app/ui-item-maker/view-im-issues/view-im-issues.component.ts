import { testAuthPanels } from './../../core/main-nav/panels/test-auth';
import * as moment from 'moment-timezone';
import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { Router, ActivatedRoute } from '@angular/router';
import { FormControl, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { LangService } from 'src/app/core/lang.service';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { IItemAuthNote } from '../element-notes/element-notes.component';
import { ItemMakerService } from 'src/app/ui-item-maker/item-maker.service';
import { AuthRolesService } from 'src/app/ui-item-maker/auth-roles.service';
import { identity } from 'angular';
import { IMenuTabConfig } from 'src/app/ui-partial/menu-bar/menu-bar.component';
import { IActiveRecords } from 'src/app/ui-trans/ui-trans-db/ui-trans-db.component';
import {ENoteItemType} from './../element-notes/types';
import { Time } from '@angular/common';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';

enum IssueTab {
  UNRESOLVED = 'UNRESOLVED',
  RESOLVED = 'RESOLVED'
}



/* #REDO  ( Means To reimplement - feature / logic eliminated from merging in pagination logic ) 
enum Time {
  THIS_WEEK = "Week",
  THIS_MONTH = "Month",
  LAST_SIX_MONTHS = "Six Months",
  ALL = "All"
}
*/

interface IFilterSettings {
  currentPage: number;
  pageLength: number;
  config: {
    is_resolved,
    id,
    text,
    name,
    question_label,
    item_type
  };
}

enum EIssueCol {
  ID = 'ID',
  ISSUE = 'ISSUE',
  ISSUE_TYPE = 'ISSUE_TYPE',
  ITEM_BANK = 'ITEM_BANK',
  ITEM = 'ITEM',
  CREATED = 'CREATED',
  LAST_UPDATED = 'LAST_UPDATED',
  ASSIGNED_TO = 'ASSIGNED_TO'
}

interface IssueHeader {
  id: EIssueCol,
  caption: string,
  prop?: string
  //allowOrFilter?: boolean #REDO
}

@Component({
  selector: 'view-im-issues',
  templateUrl: './view-im-issues.component.html',
  styleUrls: ['./view-im-issues.component.scss']
})

export class ViewImIssuesComponent implements OnInit, OnDestroy{
  pageModal: PageModalController;

  EIssueCol = EIssueCol;
  public breadcrumb = [];

  public isShowingPageLength;
  public pageLength = new FormControl(10);
  public pageLengthG = new FormGroup({});

  activeRecords: IActiveRecords;
  ENoteItemType = ENoteItemType;

  filters: IFilterSettings = {
    currentPage: 1,
    pageLength: 10,
    config: {
      is_resolved: null,
      id: null,
      text: null,
      name: null,
      question_label: null,
      item_type: null
    }
  };
  
  subscription: Subscription = new Subscription();
  routeSub: Subscription;

  // private querySub:Subscription; #REDO

  private readonly UNRESOLVED_TEXT:string = "unresolved";
  private readonly RESOLVED_TEXT:string = "resolved"
  public issuesText:string = this.UNRESOLVED_TEXT;

  //#REDO
  // issueHeadersMap: {[key:string]: IssueHeader} = {}


  // public selectedHeader: EIssueHeader; #REDO
  // public sortType: string; #REDO

  public issueCols: IssueHeader[] = [
    {
      id: EIssueCol.ID,
      caption: 'ie_id',
      prop: 'id'
    },
    {
      id: EIssueCol.ISSUE_TYPE,
      caption: 'ie_type',
      prop: 'item_type'
    },
    {
      id: EIssueCol.ISSUE,
      caption: 'auth_comment',
      prop: 'text'
    },
    {
      id: EIssueCol.ITEM_BANK,
      caption: 'item_banks_header',
      prop: 'name'
    },
    {
      id: EIssueCol.ITEM,
      caption: 'ie_item',
      prop: 'question_label'
    },
    {
      id: EIssueCol.CREATED,
      caption: 'auth_created'
    },
    {
      id: EIssueCol.LAST_UPDATED,
      caption: 'auth_last_updated'
    },
    {
      id: EIssueCol.ASSIGNED_TO,
      caption: 'auth_assigned_to',
      prop: 'assigned_name'
    }
  ]

  issueTableTabs: IMenuTabConfig<IssueTab>[] = [
    {id: IssueTab.UNRESOLVED, caption: 'auth_unresolved'},
    {id: IssueTab.RESOLVED, caption: 'issues_archived_caption'}
  ];
  initialIssueTab = IssueTab.UNRESOLVED;

  constructor(private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    private route: ActivatedRoute,
    private auth: AuthService,
    private lang: LangService,
    private itemMaker: ItemMakerService,
    private sidePanel: SidepanelService,
    private authRoles: AuthRolesService,
    private authScopeSettings: AuthScopeSettingsService,
    private pageModalService: PageModalService,
    ) { }

  ngOnInit(): void {
    this.sidePanel.activate(testAuthPanels)
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
      this.breadcrumbsService._CURRENT('auth_comments', this.router.url),
    ];
    this.routeSub = this.route.params.subscribe(routeParams => {
      this.reloadFilters(routeParams['filters']);
      this.initFilterFormControls();
      this.retrieveIssues();
    });
    this.itemMaker.loadMyAuthoringGroups()
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }
  
  showResolved = false;

  // 
  // loadIssues() { #REDO now retrieveIssues
  //   if(!this.querySub) {
  //     this.querySub = this.route.queryParams.subscribe(params => {
  //       const showResolvedParam = params['showResolved']; 
        
  //       this.showResolved = false;
  //       if(showResolvedParam !== undefined) {
  //         this.showResolved = JSON.parse(showResolvedParam);
  //       }
  
  //       this.issuesText = this.showResolved ? this.RESOLVED_TEXT : this.UNRESOLVED_TEXT;
  //       let issueList = this.showResolved ? this.resolvedIssues : this.unresolvedIssues;
  
  //       if(issueList !== undefined) {
  //         this.setIssues(issueList);
  //         this.updateFilterFromParam(params['filter']);
  //       } else {
  //         this.getIssues(this.showResolved).then(() => {this.updateFilterFromParam(params['filter']);});
  //       }
  //     });
  //   }
  // }

  // id2Issue = new Map<number, any>(); #REDO
  //#REDO
  // getIssues(getResolved: boolean) {
  //   const ASSIGNMENT_SORT = (a,b) => {
  //     const uid = this.auth.user().getValue().uid;
  //     if (+a.assigned_uid === +uid) {
  //       if (+a.assigned_uid === +b.assigned_uid) {
  //         return this.dateSort(a.created_on, b.created_on);
  //       }
  //       return -1;
  //     }
  //   }
  //   return this.auth.apiFind('public/test-auth/issues', {query: {is_resolved: getResolved ? 1 : 0}})
  //     .then(issues => {
  //       console.log(issues)
  //       issues.data.sort(ASSIGNMENT_SORT);
  //       if(getResolved) {
  //         this.resolvedIssues = issues.data;
  //       } else {
  //         this.unresolvedIssues = issues.data;
  //       }
  //       this.setIssues(issues.data);
  //       for (let issue of this.issues) {
  //         issue.show = true; //for clarity
  //       }
  //     })
  //     .catch(e => {
  //       console.error(e);
  //     });    
    
  // }


  reloadFilters(encodedFilterStr) {
    if (encodedFilterStr) {
      try {
        this.filters = JSON.parse( atob(encodedFilterStr) ) ;
        // console.log('this.filters', this.filters)
        if (!this.filters.pageLength) { this.filters.pageLength = 10; }
        this.pageLength.setValue(this.filters.pageLength);
        this.filters.currentPage = (this.filters.currentPage * 1) || 1 ;
        // this.issueCols.filter(col => col.fc && col.prop).forEach(col => {
        //   col.fc.setValue( this.filters.config[col.prop] );
        // });
      } catch (e) {}
    }
  }

  initFilterFormControls() {
    this.subscription.add(this.pageLength.valueChanges.subscribe(v => { if(this.filters.pageLength !== v) { this.filters.pageLength = v; this.updateRouteFilter();} } ));
  }

  
  setFilterConfig(prop: string, val: string) {
    this.filters.config[prop] = val;
  }

  retrieveIssues() {
    const query: any = {
      $limit: this.filters.pageLength,
      $skip: (this.filters.currentPage - 1) * this.filters.pageLength ,
    };

    for(const prop in this.filters.config) {
      if(this.filters.config[prop] != null) {
        query[prop] = {$like: `%${this.filters.config[prop]}%`};
      } 
    }

    return this.auth.apiFind('public/test-auth/issues', {query})
      .then(issues => {
    
        this.activeRecords = issues;
      })
      .catch(e => {
        console.error(e);
      });
  }

  getResolvedText() {
    return this.filters.config.is_resolved ? this.RESOLVED_TEXT : this.UNRESOLVED_TEXT;
  }

  reassignIssue(issue){
    const assigned_uid = prompt('Enter replacement User ID');
    const noteId = issue.id
    this.auth
      .apiPatch('public/test-auth/notes', noteId, {assigned_uid})
      .then(()=>{
        issue.assigned_name = 'REASSIGNED';
      })
  }
  
  // #REDO
  // setIssues(newIssues) {
  //   this.issues = newIssues;
  //   /*this.issues.forEach((issue)=>{
  //     this.id2Issue.set(issue.id, issue)
  //   })
  //   this.issues.forEach((issue)=>{
  //     if (issue.parent_note_id) {
  //       issue["parent"] = this.id2Issue.get(issue.parent_note_id)
  //     }
  //   })
  //   console.log(this.issues)*/
  //   //Maintain filtering and sorting whenever we change between resolved and unresolved issues
  //   this.updateShownIssues();
  //   this.sortIssues();
    
  // }

  selectIssuesTab(id: IssueTab) {
    this.filters.config.is_resolved = id === IssueTab.RESOLVED ? 1 : 0
    this.updateRouteFilter();
  }

  updateRouteFilter() {
    // console.log(filters)
    this.router.navigate([
      `en`,
      `test-auth`,
      `issues`,
      btoa(JSON.stringify(this.filters))
    ]);
  }

  changeFilter(event, prop: string, caption) {
    event.stopPropagation();
    const currentSetting = this.filters.config[prop];
    this.filters.config[prop] = prompt('Filter ' + caption, currentSetting == null ? "" : currentSetting) || undefined;
    this.updateRouteFilter();
  }

  timeFrom(date) {
    if (date) {
      return moment(date).fromNow();
    }
    return '';
  }

  
  issueTableValue(issue, col: EIssueCol): string {
    switch(col) {
      case EIssueCol.ID:
        return issue.id;
      case EIssueCol.ISSUE:
        return issue.text;
      case EIssueCol.ISSUE_TYPE:
        const graphicId = issue.graphic_request_id;
        let type = issue.item_type
        // If it's a comment for a graphic request, specify the request ID to make it easier to find
        if (type === ENoteItemType.GRAPHIC_REQUEST && graphicId) type += ` (ID: ${graphicId})`
        return type;
      case EIssueCol.ITEM_BANK:
        return issue.name;
      case EIssueCol.ITEM:
        return issue.question_label;
      case EIssueCol.CREATED:
        return this.timeFrom(issue.created_on);
      case EIssueCol.LAST_UPDATED:
        if(issue.last_updated_on) {
          return this.timeFrom(issue.last_updated_on);
        } else {
          return this.timeFrom(issue.created_on);
        }
      case EIssueCol.ASSIGNED_TO:
        if(issue.assigned_uid && issue.assigned_name) {
          return issue.assigned_name;
        }
      default:
        return "";
    }
  }

  generateIssueLink(issue){
    return [
      '/',
      this.lang.c(),
      'test-auth',
      'item-set-editor',
      issue.qs_id,
      encodeURIComponent(issue.question_label)
    ]
  }

  prevPage() {
    if (this.filters.currentPage > 1) {
      this.filters.currentPage -= 1;
      this.retrieveIssues();
    }
  }

  nextPage() {
    if (this.filters.currentPage < this.getMaxPages()) {
      this.filters.currentPage += 1;
      this.retrieveIssues();
    }
  }

  getMaxPages() {
    if (this.activeRecords) {
      return Math.ceil(this.activeRecords.total / this.filters.pageLength);
    }
    return 0;
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  commentContextModalStart(issue){
    const config = {
      item_id: issue.item_id,
      comment_id: issue.id,
      item_type: issue.item_type,
      graphic_request_id: issue.graphic_request_id
    };

    console.log("commentContextModalStart", config)
    this.pageModal.newModal({type: '', config, finish: () => {}});
  }

}
