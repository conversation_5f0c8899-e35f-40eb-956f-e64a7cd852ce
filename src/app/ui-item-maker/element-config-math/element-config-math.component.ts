import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { IContentElementInput, InputFormat } from 'src/app/ui-testrunner/element-render-input/model';
import { IContentElementMath, IEquationBlockMode } from 'src/app/ui-testrunner/element-render-math/model';
import { TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { ElementType } from "src/app/ui-testrunner/models";
import { EditingDisabledService } from '../editing-disabled.service';
import { bindFormControls } from '../services/data-bind';

@Component({
  selector: 'element-config-math',
  templateUrl: './element-config-math.component.html',
  styleUrls: ['./element-config-math.component.scss']
})
export class ElementConfigMathComponent implements OnInit {

  @Input() element:IContentElementMath;

  paragraphStyle = new FormControl('');
  isShowAdvancedOptions = new FormControl(false);
  altText = new FormControl('');
  
  EquationBlockMode = IEquationBlockMode
  elementMode = new FormControl();
  elementModeOptions = [
    {id: IEquationBlockMode.MATH, caption: IEquationBlockMode.MATH},
    {id: IEquationBlockMode.CHEMISTRY, caption: IEquationBlockMode.CHEMISTRY}
  ]


  paragraphStyles = [
    {id: TextParagraphStyle.HEADLINE, caption:'Extra Large'},
    {id: TextParagraphStyle.HEADLINE_SMALL, caption:'Large'},
    {id: TextParagraphStyle.REGULAR, caption:'Regular'},
    {id: TextParagraphStyle.SMALL, caption:'Small'},
  ]

  constructor(private editingDisabled: EditingDisabledService) { }

  ngOnInit() {
    // console.log('mathquill in angular', (window as any).MathQuill);

    const formControls = [
      {f: this.isShowAdvancedOptions, p:'isShowAdvancedOptions'},
      {f: this.paragraphStyle, p:'paragraphStyle'},
      {f: this.elementMode, p:'elementMode'},
      {f: this.altText, p: 'altText'},
    ];

    bindFormControls(this.element, formControls, true);
  }

  convertToInput(){
    let element:IContentElementInput = <any>this.element;
    element.isShowAdvancedOptions = false;
    element.elementType = ElementType.INPUT
    element.format = InputFormat.ALGEBRA;
  }

  getElementMode() {
    if(this.element.elementMode) return this.element.elementMode
    return this.EquationBlockMode.MATH; // default 
  }


  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

}
