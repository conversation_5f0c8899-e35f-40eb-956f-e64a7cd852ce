<div>

  <fieldset [disabled]="isReadOnly()">
    <div class="field" *ngIf="isShowAdvancedOptions.value">
      <div class="control">
        <div [class.is-disabled]="isReadOnly()" class="select is-fullwidth">
          <select [formControl]="paragraphStyle">
            <option *ngFor="let option of paragraphStyles" [value]="option.id">{{option.caption}}</option>
          </select>
        </div>
      </div>
    </div>
  </fieldset>

    <!-- Mode Selection -->
  <div class="field">
    <div class="control">
      <div [class.is-disabled]="isReadOnly()" class="select is-fullwidth">
        <select [formControl]="elementMode">
          <option *ngFor="let option of elementModeOptions" [value]="option.id">{{option.caption}}</option>
        </select>
      </div>
    </div>
  </div>

  <div style="text-align:center;">
    <ng-container [ngSwitch]="getElementMode()">
      
      <!-- Math Block -->
      <ng-container *ngSwitchCase="EquationBlockMode.MATH">
        <div style="margin-bottom: 0.4em;">
          <tra slug="auth_enter_math"></tra>
        </div>
        <div style="text-align:center; font-size:150%;">
          <capture-math [class.no-pointer-events]="isReadOnly()" [obj]="element" prop="latex" [isManualKeyboard]="true"></capture-math>
        </div>
      </ng-container>

      <!-- Chemistry Block -->
      <ng-container *ngSwitchCase="EquationBlockMode.CHEMISTRY">
        <div style="margin-bottom: 0.4em;">
          <tra slug="auth_enter_chem"></tra>
        </div>
        <div style="text-align:center; font-size:150%;">
          <capture-chemistry [class.no-pointer-events]="isReadOnly()" [obj]="element" prop="latex" rawLatex="rawLatex"  ></capture-chemistry>
        </div>
      </ng-container>

    </ng-container>
  </div>

  <fieldset [disabled]="isReadOnly()">
    <div *ngIf="isShowAdvancedOptions.value" class="field" style="margin-top: 1rem;text-align:center;">
      <span>Alternative Text (for screen readers)</span>
      <div class="control">
        <input class="input" type="text" [formControl]="altText">
      </div>
    </div>
  </fieldset>

  
  <fieldset [disabled]="isReadOnly()">
    <div  *ngIf="isShowAdvancedOptions.value" style="text-align:center;">
      <hr/>
      <a [class.is-disabled]="isReadOnly()" class="button is-warning" (click)="convertToInput()">
        <span class="icon is-small">
          <i class="fa fa-keyboard-o"></i>
        </span>
        <span>Convert into an Input</span>
      </a>
    </div>
  </fieldset>

  <hr />

  <div>
    <label class="checkbox">
      <input type="checkbox" [formControl]="isShowAdvancedOptions">
      <tra slug="auth_show_advanced_options"></tra>
    </label>
  </div>

</div>