import { Component, Input, OnInit, ViewChild, ElementRef, Output, EventEmitter, OnChanges, HostListener} from '@angular/core';
import { ElementType, IContentElement } from '../../ui-testrunner/models';
import { AuthScopeSettingsService } from '../auth-scope-settings.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { TextDiffService } from '../text-diff.service';
import { SpecialKeyboardService } from '../special-keyboard.service';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { LangService } from './../../core/lang.service';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { FormControl } from '@angular/forms';
import { mtz } from '../../core/util/moment';

@Component({
  selector: 'text-diff',
  templateUrl: './text-diff.component.html',
  styleUrls: ['./text-diff.component.scss']
})
export class TextDiffComponent implements OnInit, OnChanges {

  // Takes in diffs as an input, the edit timestamp, and the path to the edit
  @Input() diffs;
  @Input() dateEdited;
  @Input() path;
  @Input() isFocused;
  @Input() element;
  @Output() refreshChanges = new EventEmitter();
  @ViewChild('editField', {static: false}) editField:ElementRef<HTMLElement>
  @ViewChild('codeeditor') private codeEditor;

  selectedEdit;
  isSelected = false;
  fieldContent = new FormControl();
  currText = "";

  problematicLines:{lineNumber:number}[] = [];

  constructor(
    private textDiffService: TextDiffService,
    public itemComponentEditService: ItemComponentEditService,
    private specialKeyboard: SpecialKeyboardService,
    public lang: LangService
  ) { }

  ngOnInit(): void {
    this.fieldContent.setValue(this.element[this.path[this.path.length-1]])
    this.fieldContent.valueChanges.subscribe( () => {
      this.element[this.path[this.path.length-1]] = this.fieldContent.value;
    });
  }

  ngOnChanges(changes) {
    if(changes.isFocused) {
      if(this.showTextArea()) {
        setTimeout(()=>{
          if(this.editField?.nativeElement) {    
            this.editField.nativeElement.focus();
          }
        }, 50);
      }
    }
  }

  showTextArea() {
    return this.isFocused && this.itemComponentEditService.usingEditingMode();
  }

  // Accept a text diff change
  onClickAccept(text) {

    this.selectedEdit = null;
    this.isSelected = false;
    
    this.itemComponentEditService.acceptTextDiff(this.getPreview(text), text, this.path);

    this.handleDelete(text);
  }

  // Rejects a text diff change
  onClickReject(text) {
    this.selectedEdit = null;
    this.isSelected = false;

    this.itemComponentEditService.rejectTextDiff(this.getPreview(text), text, this.path);
    this.handleDelete(text);
  }

  handleDelete(diff) {
    const overWrittenText = this.textDiffService.getDiff(diff);

    // Update the newly applied diff into a non edit and replace with old text
    if (overWrittenText) {
      const i = this.diffs.indexOf(diff);
      if(i !== -1) {
        this.diffs[i] = overWrittenText;
        this.diffs[i].added = undefined;
        this.diffs[i].removed = undefined;
      }
    }
    // Update diff status after adding/removing text
    else if (diff.added || diff.removed) {
      this.diffs.forEach((d, i) => {
        if (d === diff) {
          this.diffs[i].added = undefined;
          this.diffs[i].removed = undefined;
        }
      });
    } 
    
    this.diffs = [...this.diffs];

    this.refreshChanges.emit();
  }

  // Determines if a textdiff should be highlighted
  isHighlighted(diff) {
   return (diff.added || diff.removed);
  }

  // Get the styling for the type edit made
  getEditType(diff) {
    if (diff.added) {
      if (this.getPreview(diff) === undefined) {
        return { background: '#77BF70' };
      }
      return { background: '#FFDFAF' };

    } else {
      return { background: '#FFAFAF', textDecoration: 'line-through', textDecorationColor: 'rgba(243, 1, 1, 0.39)'};
    }

  }

  getPreview(diff) {
      return this.textDiffService.getDiff(diff);
  }

  // Opens the modal when an edit is clicked on
  onClickEdit(diff, event) {
    this.selectedEdit = diff
    this.isSelected = !this.isSelected;
    event.stopPropagation();
  }

  isMathElement(){
    return this.element.elementType == ElementType.MATH
  }
  // for ordering/mcq math sub blocks as they use content not latex the rest don't have a content property
  determineMathProp(): any { 
     return (this.element.content === undefined || this.element.content === null) ? this.path.includes('latex') ? 'latex' : this.path[this.path.length-1] :'content';
  }

  /** Check if the text diff is for editing the `text` prop of the `passage` elementType */
  isTargetPassageText():boolean{
    const isElemPassage = this.element.elementType == ElementType.PASSAGE
    const isPropText = this.path[this.path.length - 1] == "text"
    return (isElemPassage && isPropText)
  }

  isNonKeyboardTextField(){
    return (this.element.elementType == ElementType.IMAGE && this.path[this.path.length - 1] == "altText") || this.element.elementType == ElementType.INPUT
  }

  setTextFocus(event) {
    if (this.isNonKeyboardTextField()) return
    this.specialKeyboard.currentElement.next({element: this.element, htmlElement:this.editField.nativeElement, formControl: this.fieldContent})
  }

  focusOut() {
    this.specialKeyboard.focusOut()
  }

  processDate(rawDate){
    return mtz(rawDate).format(this.lang.tra('datefmt_timestamp'));
  }

  processAuthor(authorUid){
    const authorInfo = this.itemComponentEditService.userInfo[authorUid]
    if (!authorInfo) return;
    return (authorInfo.first_name + " " + authorInfo.last_name)
  }
  handleFocus($event: any) {
    if($event){
      const codeMirror = this.codeEditor.codeMirror.getDoc();
      this.specialKeyboard.currentElement.next({element: this.element, htmlElement: codeMirror.cm.display.input.textarea, formControl: undefined, codeMirror: codeMirror});
    }
    if(!$event) {
      this.specialKeyboard.focusOut();
    }
  }

    // If clicking anywhere outside the diffs, disappear the popover
    @ViewChild('popOver') popOverRef!: ElementRef;
    @HostListener('document:click', ['$event'])
    onDocumentClick(event: MouseEvent) {
      if (this.isSelected && !this.popOverRef?.nativeElement.contains(event.target)) {
        this.selectedEdit = null;
        this.isSelected = false;
      }
    }

  //NOTE: Based on the element-config-passage component, may need to adjust if it's updated there
  editorConfig = { 
    lineWrapping: true,
    smartIndent: false,
    indentWithTabs: false,
    lineNumbers: true,
    // mode: 'passage' // todo: implement more precise tagging for xml
    mode: 'xml' 
  };

}
