import { I } from '@angular/cdk/keycodes';
import { Injectable } from '@angular/core';
import { nodeName } from 'jquery';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { LangService } from '../core/lang.service';
import { processText, removeLeadingEllipses, StyleprofileService, StylingProcess } from '../core/styleprofile.service';
import { IContentElementCanvas } from '../ui-testrunner/element-render-canvas/model';
import { IContentElementCustomMcqOption } from '../ui-testrunner/element-render-custom-mcq/model';
import { IContentElementDndDraggable } from '../ui-testrunner/element-render-dnd/model';
import { IContentElementFrame } from '../ui-testrunner/element-render-frame/model';
import { IContentElementGroup } from '../ui-testrunner/element-render-grouping/model';
import { IContentElementDynamicImage, IContentElementImage, ImageStates } from '../ui-testrunner/element-render-image/model';
import { IContentElementInput } from '../ui-testrunner/element-render-input/model';
import { IContentElementInsertion } from '../ui-testrunner/element-render-insertion/model';
import { IContentElementMath } from '../ui-testrunner/element-render-math/model';
import { IContentElementMcq, IContentElementMcqOption, McqDisplay, IContentElementMcqOptionInTable } from '../ui-testrunner/element-render-mcq/model';
import { IContentElementMoveableDragDrop } from '../ui-testrunner/element-render-moveable-dnd/model';
import { reorderOrderOptionsToInit as reorderOrderOptions, useSetOrders } from '../ui-testrunner/element-render-order/element-render-order.component';
import { IContentElementOrder, OrderMode } from '../ui-testrunner/element-render-order/model';
import { IContentElementSelectionTable } from '../ui-testrunner/element-render-selection-table/model';
import { isSolutionHeaderVisible } from '../ui-testrunner/element-render-solution/element-render-solution.component';
import { IContentElementSolution } from '../ui-testrunner/element-render-solution/model';
import { IContentElementTable, IContentElementTableCell } from '../ui-testrunner/element-render-table/model';
import { IContentElementBookmarkLink, IContentElementText, TextParagraphStyle } from '../ui-testrunner/element-render-text/model';
import { ElementType, IContentElement, IQuestionConfig, IElementVoiceover } from '../ui-testrunner/models';
import { DEFAULT_VOICEOVER_PROP } from './element-config-mcq-option-info/element-config-mcq-option-info.component';
import { QUESTION_WORDING_OPTS } from './item-set-editor/models/assessment-framework';
import { VoiceoverStateService } from '../ui-testrunner/voiceover-state.service';
import { IContentElementSelectableText, IContentElementTextSelection } from '../ui-testrunner/element-render-select-text/model';
import { IContentElementInteractiveDiagram } from '../ui-testrunner/element-render-interactive-diagram/model';
import { extractScriptFromInteractiveDiagram } from './element-config-interactive-diagram/script-gen/interactive-diagram';

import { processSpeechMarksFromPolly } from '../ui-testrunner/utils/text-to-speech/processRawSpeechMarks';
import { AUTHORING_NODE_ID_START, AUTHORING_NODE_ID_END, getNodeCustomRefTag, getCustomNodeRefval, CT_ALT_TEXT, CUSTOM_TAG_START, CUSTOM_TAG_END, getFillerNode, getKeyIdNode, DRAG_NODE, TARGET_NODE, TARGET_NODE_KEY_ID, DRAG_NODE_KEY_ID, getCustomNode, getInstrNode, ORDER_NODE_KEY_ID } from '../ui-testrunner/utils/text-to-speech/constants';
import { IContentElementPassage } from '../ui-testrunner/element-render-passage/model';
import { IContentElementTemplate } from '../ui-testrunner/element-render-template/model';
import { IScriptNodeRef } from '../ui-testrunner/utils/text-to-speech/types';
import { createAudioEl } from '../ui-testrunner/utils/text-to-speech/dom-processing/gen-html-elements';
// import { IContentElementVirtualTools } from '../ui-testrunner/element-render-virtual-tools/model';
import { clone } from './item-set-editor/models/loft';

// const OPTION_TEXT_EN = 'Option'
export interface IScriptGenMeta {
  optionScripts:string[],
  useOldScripts?:boolean,
  useOldScriptsDecision?:boolean,
}
// export interface InteractionVoiceover {
//   script: string;
//   audioUrl: string;
//   synced: boolean;
//   elementType: ElementType;
//   entryId: string;
// }

const optionLetters = 'ABCDEFGHIJKLMNOP'.split('')

const MAX_SCRIPT_SIZE = 2500;

@Injectable({
  providedIn: 'root'
})
export class ScriptGenService {
  constructor(
    private auth:AuthService,
    private routes:RoutesService,
    private lang: LangService,
    private profile: StyleprofileService,
    private voiceoverState: VoiceoverStateService
  ) { }

  numUploadsStarted:number;
  numUploadsCompleted:number;


  // public autoGenQuestionCaptionVoiceover(sectionId: number, question: IQuestionConfig, questionTitle: string, lang:string = 'en') {
  //   if(!question.captionVoiceover) {
  //     question.captionVoiceover = {};
  //   }

  //   if(!question.captionVoiceover[sectionId]) {
  //     question.captionVoiceover[sectionId] = {};
  //   }
  //   const captionVoiceoverDef = question.captionVoiceover[sectionId];

  //   const oldCaptionVoiceover = captionVoiceoverDef.script;
  //   captionVoiceoverDef.script = questionTitle;

  //   if(oldCaptionVoiceover !== captionVoiceoverDef.script && captionVoiceoverDef.script) {
  //     for(const opt of QUESTION_WORDING_OPTS) {
  //       const qWord = this.lang.tra(opt, lang);
  //       const qWordEn = this.lang.tra(opt, 'en');
  //       const regex = new RegExp(`${qWord} (\\d+)`);
  //       const qMatch = captionVoiceoverDef.script.match(regex);
  //       if(qMatch && qMatch.length > 1) {
  //         const number = qMatch[1];
  //         const slug = `lbl_${qWordEn.toLowerCase()}_${number}`
  //         captionVoiceoverDef.url = this.lang.traVoice(slug, lang);
  //         captionVoiceoverDef.fileType = "audio/mp3";
  //         return Promise.resolve();
  //       } 
  //     }
  //     return this.uploadNewVoice(captionVoiceoverDef.script, captionVoiceoverDef, lang);
  //   }
  //   return Promise.resolve();
  // }

  public autoGenElVoiceover(el: IContentElement, lang:string = 'en', meta:IScriptGenMeta = {optionScripts: []}) {
    if(!el) {
      return;
    }
    if(!el.voiceover) {
      el.voiceover = {};
    }
    const oldScript = el.voiceover.script; 
    const script = this.extractScriptFromNode(el, lang, meta, [], []);
    this.useScriptDecision(el, meta, script)
    if(script && script !== oldScript) {
      this.uploadNewVoice(el.voiceover.script, el.voiceover, lang);
    }
  }


  //When magic wand is used
  autoGenQuestionVoiceover(question:IQuestionConfig, lang:string='en', isOverridesDisabled:boolean=false){
    this.numUploadsStarted = 0;
    this.numUploadsCompleted = 0;
    let optionScripts = [];

    // Clear existing voiceovers before generating new ones
    this.voiceoverState.clearInteractions();
    this.voiceoverState.generateNestedVoiceovers(question);
    if (question.voiceover && question.voiceover.script) {
      let lastLineWithOption = null;
      let hasNonAdjacentLines = false;
      question.voiceover.script.split('\n').forEach((lineStr, i) => {
        const optionLineStart = 'Option "';
        if (lineStr.substr(0, optionLineStart.length) === optionLineStart){
          if (lastLineWithOption && lastLineWithOption !== i-1){
            hasNonAdjacentLines = true;
          }
          optionScripts.push(lineStr);
          lastLineWithOption = i;
        }
      });
      if (!isOverridesDisabled){
        if (hasNonAdjacentLines){
          if (!confirm('The option text looks a little more complex than usual, and some text might have been missed. Would you still like to apply manual overrides from the main text to the option text?')){
            optionScripts = [];
          }
        }
        else if (optionScripts.length > 0){
          if (!confirm('Would you like to apply manual overrides from the main text to the option text?')){
            optionScripts = [];
          }
        }
      }
    }
    const meta:IScriptGenMeta = {optionScripts}; 
    if (isOverridesDisabled){
      meta.useOldScriptsDecision = true;
      meta.useOldScripts = true;
    }

    const scriptNodeRef:IScriptNodeRef[] = []
    // compute the script from the child nodes
    let script = this.extractScriptFromNodes(question.content, lang, meta, [], scriptNodeRef);
    
    if(this.profile.getStyleProfile()[lang].voiceScript.general.removeBeginningEllipses) {
      script = removeLeadingEllipses(script);
    }

    if (!question.voiceover){
      question.voiceover = {};
    }

    question.voiceover.script = script;
    question.voiceover.scriptNodeRef = scriptNodeRef;
    // question.voiceover.ssml = this.genSsmlForVoiceOver(scriptNodeRef)
    question.voiceover.scriptType = "ssml"
    
    // console.log(question.voiceover.ssml)
    this.autoGenElVoiceover(question.bannerSubtitle, lang, meta);
    this.autoGenElVoiceover(question.bannerTitle, lang, meta)

    return script;
  }


  private genSsmlForVoiceOver(scriptNodeRef: IScriptNodeRef[], isAppendStartEndTag: boolean = true, isPostProcessText: boolean = true) {

    const ssmlPre = "<speak>";
    const ssmlPost = "</speak>";

    const postProcessText = (sentence: string) => {
      // sentence = sentence.replace(/\.\.\./g, '<break time="1s"/>')
      // TODO: process reserved characters in SSML
      // sentence = sentence.replace(/"/g, '&quot;');
      sentence = sentence?.replace(/&/g, '&amp;'); //TODO:  should be coming from style profile 
      // sentence = sentence.replace(/'/g, '&apos;');
      // TODO: process <bookmarks> and other custom tags
      sentence = `<p>${sentence}</p>`;  // every node is seperated with this tag so for headings if ending delimiter is not provided this should behave the same 
      return sentence
    }

    let ssmlText = '';

    // console.log(scriptNodeRef)
    for(const scriptNode in scriptNodeRef){
      let {nodeScript, nodeRefId} = scriptNodeRef[scriptNode]
      const startTag = getNodeCustomRefTag(getCustomNodeRefval(AUTHORING_NODE_ID_START, nodeRefId))
      const endTag = getNodeCustomRefTag(getCustomNodeRefval(AUTHORING_NODE_ID_END, nodeRefId))
      if(postProcessText) nodeScript = postProcessText(nodeScript)
      if(isAppendStartEndTag) ssmlText += startTag;
      ssmlText += nodeScript;
      if(isAppendStartEndTag) ssmlText +=  endTag;
    }

    return ssmlPre + ssmlText + ssmlPost

  }

  private extractScriptFromNodes(nodes:IContentElement[], lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef, delim:string='\n', pauseAroundExpression:boolean=false) {
    let script = "";
    if(!nodes) {
      return "";
    }
    for(let i = 0; i < nodes.length; i++) {
      if(pauseAroundExpression && i !== 0 && nodes[i].elementType === ElementType.MATH && 
        nodes[i - 1].elementType === ElementType.TEXT) {
          script += " ... ";
        }
      const nodeScript = this.extractScriptFromNode(nodes[i], lang, meta, preProcesses, scriptNodeRef);
      script += nodeScript;
      if(pauseAroundExpression && i !== nodes.length - 1 && nodes[i].elementType === ElementType.MATH && 
        nodes[i + 1].elementType === ElementType.TEXT) {
        script += ` ... ${delim}`
      } else {
        script += delim;
      }
    }

    return script;
  }
  private extractScriptFromNode(node:IContentElement, lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    // console.log('check : ', node.elementType, node);
    switch(node.elementType){
      case ElementType.TEXT: return this.extractScriptFromTextNode( <IContentElementText> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.TABLE: return this.extractScriptFromTableNode( <IContentElementTable> node, lang, meta, scriptNodeRef);
      case ElementType.SELECT_TABLE: return this.extractScriptFromSelectionTableNode( <IContentElementSelectionTable> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.INSERTION: return this.extractScriptFromInsertionNode(<IContentElementInsertion>node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.MATH: return this.extractScriptFromMathNode( <IContentElementMath> node, lang,scriptNodeRef);
      case ElementType.IMAGE: return this.extractScriptFromImageNode( <IContentElementImage> node, lang, meta, scriptNodeRef);
      case ElementType.DYNAMIC_IMAGE: return this.extractScriptFromImageNode( <IContentElementImage> node, lang, meta, scriptNodeRef);
      case ElementType.MCQ: return this.extractScriptFromMcqNode( <IContentElementMcq> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.CUSTOM_MCQ: return this.extractScriptFromMcqNode( <IContentElementMcq> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.GROUPING: return this.extractScriptFromGroupNode(<IContentElementGroup> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.PASSAGE: return this.extractScriptFromPassageNode(<IContentElementPassage> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.TEMPLATE: return this.extractScriptFromTemplateNode(<IContentElementTemplate> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.ORDER: return this.extractScriptFromOrdering(<IContentElementOrder> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.INPUT: return this.extractScriptFromInputNode(<IContentElementInput> node, lang, meta, preProcesses);
      case ElementType.CANVAS: return this.extractScriptFromCanvas(<IContentElementCanvas> node, lang, meta, preProcesses, scriptNodeRef);      
      case ElementType.FRAME: return this.extractScriptFromFrame(<IContentElementFrame> node, lang, meta, preProcesses, scriptNodeRef);      
      case ElementType.MOVEABLE_DND: return this.extractScriptFromMoveableDnd(<IContentElementMoveableDragDrop> node, lang, meta, preProcesses, scriptNodeRef);      
      // case ElementType.VIRTUAL_TOOLS: return this.extractScriptFromVirtualToolsNode(<IContentElementVirtualTools> node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.SOLUTION: return this.extractScriptFromSolution(<IContentElementSolution>node, lang, meta, preProcesses);
      case ElementType.INTERACTIVE_DIAGRAM: return this.extractScriptFromInteractiveDiagram(<IContentElementInteractiveDiagram>node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.SELECT_TEXT: return this.extractScriptFromSelectText(<IContentElementTextSelection>node, lang, meta, preProcesses, scriptNodeRef);
      case ElementType.BOOKMARK_LINK: return this.extractScriptFromBookmarkLink(<IContentElementBookmarkLink>node, lang, meta, preProcesses, scriptNodeRef);
    }
    return "";
  }

  extractScriptFromSolution(node: IContentElementSolution, lang: string, meta: IScriptGenMeta, preProcesses:StylingProcess[]) {
    let script = "";

    if(isSolutionHeaderVisible(node)) {
      script += this.lang.tra('lbl_correct_answer', lang);
      script += this.lang.tra('txt_colon', lang);
      script += '\n';
    }   
    script += this.extractScriptFromNodes(node.content, lang, meta, preProcesses, []);
    this.useScriptDecision(node, meta, script);
    this.uploadNewVoice(node.voiceover.script, node.voiceover, lang);
    return ""; //Don't include solution script in overall voiceover script.
  }

  extractScriptFromMoveableDnd(node: IContentElementMoveableDragDrop, lang: string, meta: IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    let script = [];
    let nodeScript = [];

    const voice_insertion_terms = `${this.lang.tra('voice_insertion_terms', lang)}`
    script.push(`...  ${voice_insertion_terms} ... \n`);
    nodeScript.push(getFillerNode(node.entryId, voice_insertion_terms, true));

    for(const drag of node.draggables) {
      const dragNodeScript = this.extractScriptFromDraggableNode(drag, lang, meta, preProcesses, []);
      script.push(dragNodeScript);
      nodeScript.push(getKeyIdNode(node.entryId, drag.key_id, dragNodeScript, DRAG_NODE_KEY_ID))
    }
    
    if(node.targets?.length) {
      script.push(`... ${this.lang.tra('voice_dnd_num_targets', lang, {NUM_TARGETS: node.targets?.length, OPTIONAL_S: node.targets?.length > 1 ? 's' : ''})} ... \n`);
      node.targets.forEach((target, index) => {
        const _targetNodeScript = `target ${index+1}`;  // #TODO: target is hardcoded should come from style-profile
        nodeScript.push(getKeyIdNode(node.entryId, target.key_id, <string>_targetNodeScript, TARGET_NODE_KEY_ID))
      })
    }

    scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: nodeScript.join('\n')})
    return script.join(' ... \n');
  }

  extractScriptFromFrame(node:IContentElementFrame, lang, meta, preProcesses, scriptNodeRef) {
    let script = [];
    node.content.forEach((subnode, index)=>{
      script.push(this.extractScriptFromNode(subnode, lang, meta, preProcesses, scriptNodeRef));
    })
    return script.join('...\n');
  }

  extractScriptFromCanvas(node:IContentElementCanvas, lang, meta, preProcesses, scriptNodeRef) {
    let script = [];
    node.pages.forEach((page, index)=>{
      page.displayList.forEach((el)=>{
        script.push(this.extractScriptFromNode(el, lang, meta, preProcesses, scriptNodeRef));
      })
    })

    return script.join('...\n');
  }

  extractScriptFromInputNode(node:IContentElementInput, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[]) {
    let script = [];
    const inputScript = this.profile.getStyleProfile()[lang].voiceScript.input;
    // const blank = this.profile.getStyleProfile()[lang].voiceScript.general;
    // const numWords = node.maxWords;
    // const str = `${this.lang.tra(inputScript.total_words, lang)}`
    //if (node.maxWords) script.push(str.replace("{{blank}}", numWords.toString()));
    return script
  }

  extractScriptFromPassageNode(node:IContentElementPassage, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef) {
    const passageScript = this.processPlainTextScriptPre(node, 'text', lang, preProcesses, scriptNodeRef);
    // const passageScript =  this.processPlainTextScript(node.text, lang, preProcesses);
    scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: passageScript})
    return passageScript
  }

  extractScriptFromTemplateNode(node:IContentElementTemplate, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef) {
    return this.extractScriptFromNodes(node.content, lang, meta, preProcesses, scriptNodeRef)
  }

  extractScriptFromGroupNode(node:IContentElementGroup, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef) {
    // const response = [];
    // response.push('Draggable options. ')
    // node.draggables.forEach((draggable, i) => {
    //   response.push('Option 1. '+ this.extractScriptFromNode(draggable.element, lang, meta)); 
    // })
    // response.push('Draggable options. ')
    // node.draggables.forEach((draggable, i) => {
    //   response.push('Option 1. '+ this.extractScriptFromNode(draggable.element, lang, meta)); 
    // })
    let script = [];   
    let nodeScript = [];
    const groupingScript = this.profile.getStyleProfile()[lang].voiceScript.grouping;
    const voiceGroupingTerms = this.lang.tra(groupingScript?.blocks || 'voice_grouping_terms', lang)
    script.push(`... ${voiceGroupingTerms} ...`);
    nodeScript.push(getFillerNode(node.entryId, voiceGroupingTerms))

    node.draggables.forEach((drag, index)=>{
      const dragScript = this.extractScriptFromDraggableNode(drag, lang, meta, preProcesses, [])
      script.push(dragScript);
      nodeScript.push(getKeyIdNode(node.entryId, drag.key_id, dragScript, DRAG_NODE_KEY_ID))
    })
    
    if (!node.isInstructionsDisabled){
      const groupingInstr = this.lang.tra(groupingScript?.grouping_instr || 'txt_default_drag_instr', lang);
      script.push(` ... ${groupingInstr}`);
      nodeScript.push(getInstrNode(node.entryId, groupingInstr))
    } 

    node.targets.forEach((target, index)=>{
      // script.push(`... ${this.lang.tra(groupingScript?.targets || 'voice_grouping_targets', lang)} `+(1+index));
      this.getNumberedOrder(node, script, index, index==node.draggables.length-1, lang);
      const sub = this.extractScriptFromNode(target.element, lang, meta, preProcesses, []);
      this.useScriptDecision(target, meta, sub);
      this.uploadNewVoice(target.voiceover.script, target.voiceover, lang);
      script.push("...");
      script.push(target.voiceover.script);
      nodeScript.push(getKeyIdNode(node.entryId, target.key_id, <string>sub, TARGET_NODE_KEY_ID))
    })

    scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: nodeScript.join('\n')})
    return script.join(' ');
  }

  private extractScriptFromDraggableNode(node: IContentElementDndDraggable, lang: string, meta: IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef: IScriptNodeRef[]) {
    let block;
    if (node.element.voiceover && node.element.voiceover.script!=null && meta.useOldScriptsDecision) block = this.processPlainTextScript(node.element.voiceover.script, lang, preProcesses) //+" ...\n";
    else {
      block = this.extractScriptFromNode( node.element, lang, meta, preProcesses, scriptNodeRef) //+" ... \n";
    }
    this.useScriptDecision(node, meta, block);
    this.uploadNewVoice(node.voiceover.script, node.voiceover, lang);
    return " ... "+node.voiceover.script;
  }

  private extractScriptFromInsertionNode(node:IContentElementInsertion, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef: IScriptNodeRef[]) {
    let script = [];   
    let nodeScript = [];
    const insertionScript = this.profile.getStyleProfile()[lang].voiceScript.insertion;
    const blank = this.profile.getStyleProfile()[lang].voiceScript.general.blank;
    //script.push(`${this.lang.tra(insertionScript?.insertion || 'voice_insertion', lang)} ... `);

    if (node.isDragBetweenWords && (node.isShowInstructions == undefined ||node.isShowInstructions)){
      const blindIntsrText = `${this.lang.tra(insertionScript?.instr_blind || 'voice_insertion_blind_instr', lang)}`;
      script.push(`... ${blindIntsrText} ... `);
      nodeScript.push(getInstrNode(node.entryId, blindIntsrText))
    } 
    else if ((node.isShowInstructions == undefined ||node.isShowInstructions)){
      const showInstrText = `${this.lang.tra(insertionScript?.instr_blocks || 'voice_insertion_instr', lang)}`;
      script.push(`... ${showInstrText} ... `);
      nodeScript.push(getInstrNode(node.entryId, showInstrText))
    }

    const voiceTargets = ()=>{
      if (lang!="en"){
        const insertionTextPre =  this.lang.tra(insertionScript?.text || 'voice_insertion_text', lang);
        script.push(`${insertionTextPre} ... \n`);
        nodeScript.push(getFillerNode(node.entryId, insertionTextPre));
      } 
        node.textBlocks.forEach((target)=>{
          if(target.element.elementType === ElementType.TEXT) {
            const textBlockScript = this.extractScriptFromNode(target.element, lang, meta, preProcesses, [])+"...\n";
            this.useScriptDecision(target.element, meta, textBlockScript);
            this.uploadNewVoice(target.element.voiceover.script, target.element.voiceover, lang);
            script.push(target.element.voiceover.script);

            // Using the logic from element-render-insertion here
            const textElement = (<IContentElementText>target.element);
            (textElement.caption || '').split(/\s/).forEach((str,i) => {
              const textBlockScript = this.processPlainTextScript(str, lang, preProcesses);
              nodeScript.push(`<s>${textBlockScript}</s>`);
            });
          } else if ( (target.element.elementType=="" || target.element.elementType == ElementType.BLANK) && !node.isDragBetweenWords) {
            const blankTargetText = this.lang.tra(blank, lang);
            script.push("... " + `${blankTargetText} ...` );
            nodeScript.push(getFillerNode(node.entryId, blankTargetText));
          }
        })
    }

    if (node.isTargetsAbove) voiceTargets();

    const termsText =  this.lang.tra(insertionScript?.terms || 'voice_insertion_terms', lang);
    script.push(`...  ${termsText} ... \n`);
    nodeScript.push(getFillerNode(node.entryId, termsText));

    node.draggables.forEach((drag)=>{
      const dragNodeScript = this.extractScriptFromDraggableNode(drag, lang, meta, preProcesses, []);
      script.push(dragNodeScript);
      nodeScript.push(getKeyIdNode(node.entryId, drag.key_id, dragNodeScript, DRAG_NODE_KEY_ID)); //  `<s>${dragNodeScript}</s>`
    })

    if (!node.isTargetsAbove) voiceTargets();

    scriptNodeRef.push({nodeRefId: node.entryId.toString(), nodeScript: nodeScript.join()})
    return script.join(' ');
  }

  private extractScriptFromTextNode(node:IContentElementText, lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    switch(node.paragraphStyle){
      case TextParagraphStyle.ADVANCED_INLINE:
        const pauseAroundExpression = this.profile.getStyleProfile()[lang].voiceScript.advancedInline.pauseAroundExpression;
        const blank = this.profile.getStyleProfile()[lang].voiceScript.mcq.dropDown_blank;
        if (this.checkIfOneDropDown(node.advancedList)) {
          let script = [];
          let mcqNode;
          node.advancedList.forEach(node => {
            if (node.elementType==ElementType.MCQ) {
              mcqNode = node;
              if (!node["defaultDropdownText"] || node["defaultDropdownText"]=='') script.push("... " + `${this.lang.tra(blank, lang)}` + "...");
              else script.push("... "+node["defaultDropdownText"]+"...")
            } else {
              script.push(this.extractScriptFromNode(node, lang, meta, preProcesses, scriptNodeRef));
            }
          });
          const selOptions = this.profile.getStyleProfile()[lang].voiceScript.mcq.dropDown;
          // script.push("..."+`${this.lang.tra(selOptions, lang)}`+"...");
          script.push(this.extractScriptFromMcqNode(mcqNode, lang, meta, preProcesses, scriptNodeRef));
          const oneDropDownScript = script.join(' ')
          scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: oneDropDownScript})
          return oneDropDownScript;
        } 
        return this.extractScriptFromNodes(node.advancedList, lang, meta, preProcesses, scriptNodeRef, ' ', pauseAroundExpression);
      case TextParagraphStyle.BULLET:
      case TextParagraphStyle.NUMBERED:
        if (node.simpleList.length > 0){
          const numberSimpleScript = node.simpleList.map( str => this.processPlainTextScript(str, lang, preProcesses)).join('\n')
          scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: numberSimpleScript})
          return numberSimpleScript;
        }
        else if (node.advancedList){
          return this.extractScriptFromNodes(node.advancedList, lang, meta, preProcesses, scriptNodeRef)
        }
        else{
          return '';
        }
      case TextParagraphStyle.PARAGRAPHS: 
        if(!node.paragraphList?.length) {
          return '';
        } 
        const paraScript = node.paragraphList.map( par => this.processPlainTextScript(par.caption, lang, preProcesses)).join('\n')
        scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: paraScript})
        return paraScript;
      case TextParagraphStyle.HEADLINE:
      case TextParagraphStyle.HEADLINE_SMALL:
      case TextParagraphStyle.REGULAR:
      case TextParagraphStyle.SMALL:
      case TextParagraphStyle.LINK:
      case TextParagraphStyle.ANNOTATION:
      default:
        return this.processPlainTextScriptPre(node, 'caption', lang, preProcesses, scriptNodeRef)
    }
  }

  private checkIfOneDropDown(nodes:IContentElement[]) {
    let numMcq = 0;
    nodes.forEach((node)=>{
      if (node.elementType==ElementType.MCQ) {
        numMcq++;
      }
    })

    if (numMcq==1) return true;
    return false;
  }

  private processPlainTextScriptPre(node: IContentElement, prop:string, lang:string, preProcesses: StylingProcess[], scriptNodeRef: IScriptNodeRef[]){
    let nodeScript = this.processPlainTextScript(node[prop], lang, preProcesses);
    const nodeRefId = node.entryId?.toString();
    if(!nodeRefId) { console.log("No node Reference Id found while processing plain Text.", node) }
    else { scriptNodeRef.push({nodeRefId, nodeScript}) }
    return nodeScript;
  }


  private processPlainTextScript(str:string, lang:string, preProcesses: StylingProcess[]) {
    let processes =  this.profile.getStyleProfile()[lang].voiceScript.plainText.filter(style => style.slug != "APPLY_THOUSANDS_SPACES_PY" && style.slug != "PROCESS_UNITS");
    return processText(str, preProcesses.concat(<StylingProcess[]>processes));
  }

  private extractScriptFromTableCell(cell:IContentElementTableCell, i_row:number, i_col:number, node:IContentElementTable, lang:string, meta:IScriptGenMeta, inverted:boolean, scriptNodeRef) {
    const tableProfile = this.profile.getStyleProfile()[lang].voiceScript.table;
    const firstColumnIsHeader = node.isHeaderCol;
    const firstRowIsHeader = node.isHeaderRow;

    let rowColNumber = (inverted ? i_row : i_col) + 1; //we should actually be looking at the rows if inverted
    
    const isHeaderCell = (i_row === 0 && firstRowIsHeader) || (i_col === 0 && firstColumnIsHeader);
    
    if(!isHeaderCell && tableProfile.onlyReadHeaderCells) {
      return [];
    }

    const preProcesses = isHeaderCell ? tableProfile.headerProcesses : [];
    
    let str = cell.elementType === ElementType.TABLE_TEXT ? this.processPlainTextScript(cell.val, lang, preProcesses) : this.extractScriptFromNode(<IContentElement> cell, lang, meta, preProcesses, scriptNodeRef);
    
    if(isHeaderCell && tableProfile.onlyReadHeaderCells) {
      // str += " ... "
    }
    
    let cellScript = [];

    if(!tableProfile.onlyReadHeaderCells) {
      // ` ... ${this.lang.tra(inverted ? tableProfile.beginRow : tableProfile.beginColumn, lang)} ${rowColNumber} ${cell.val} ... `
      cellScript.push(`${this.lang.tra(inverted ? tableProfile.beginRow : tableProfile.beginColumn, lang)} ${rowColNumber}`);
    }
    cellScript.push(str);
    
    return cellScript;
  }

  private extractScriptFromTableNode(node:IContentElementTable, lang:string, meta:IScriptGenMeta, scriptNodeRef: IScriptNodeRef[]) {

    const getCellScriptSentence = (val: string[]) => `<s>${val.join()}</s>`;

    const nodeRefId = node.entryId.toString();
    const tableProfile = this.profile.getStyleProfile()[lang].voiceScript.table;
    
    const firstColumnIsHeader = node.isHeaderCol;
    const firstRowIsHeader = node.isHeaderRow;

    let script = [];
    let nodeScript = [];
    const beginTableSlug = node.isTableOfValues ? tableProfile.beginTableValues : tableProfile.beginTable;
    const beginTableText = this.lang.tra(beginTableSlug, lang);
    script.push(`${beginTableText} ... `);
    nodeScript.push(getFillerNode(nodeRefId, beginTableText));

    if(firstColumnIsHeader && tableProfile.columnHeaderReadRowsFirst && node.grid.length > 0) {
      for(let c = 0; c < node.grid[0].length; c++) {
        let cellScript = [];
        let colNumber = c + 1;
        
        if(!tableProfile.onlyReadHeaderCells) {
          const beginColumnText = `${this.lang.tra(tableProfile.beginColumn, lang)} ${colNumber}`
          script.push(` ... ${beginColumnText} ... `);
          nodeScript.push(getFillerNode(nodeRefId, beginColumnText));
        }

        for(let r = 0; r < node.grid.length; r++) {
          const _cell = this.extractScriptFromTableCell(node.grid[r][c], r, c, node, lang, meta, true, [])
          cellScript = cellScript.concat(_cell);
          nodeScript.push(getCellScriptSentence(_cell));
        }

        script = script.concat(cellScript)

      }
    } else {
      node.grid.forEach( (row:IContentElementTableCell[], i_row) => {
        let cellScript = [];

        let rowNumber = i_row + 1;
        if(!tableProfile.onlyReadHeaderCells) {
          const beginRowText =  `${this.lang.tra(tableProfile.beginRow, lang)} ${rowNumber}`
          script.push(` ... ${beginRowText} ... `);
          nodeScript.push(getFillerNode(nodeRefId, beginRowText));
        }

        row.forEach((cell:IContentElementTableCell, i_col) => {
          const _cell = this.extractScriptFromTableCell(cell, i_row, i_col, node, lang, meta, false, [])
          cellScript = cellScript.concat(_cell);
          nodeScript.push(getCellScriptSentence(_cell));
        });
  
        script = script.concat(cellScript)
      });
    }

    const endTableSlug = node.isTableOfValues ? tableProfile.endTableOfValues : tableProfile.endTable;
    if(endTableSlug) {
      const endTableText = `${this.lang.tra(endTableSlug, lang)}`
      script.push(` ... ${endTableText} ... `);
      nodeScript.push(getFillerNode(nodeRefId, endTableText))
    }

    scriptNodeRef.push({nodeRefId, nodeScript: nodeScript.join('\n')});
    return script.join(' ');
  }

  private extractScriptFromSelectionTableNode(node:IContentElementSelectionTable, lang:string, meta:IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef) {
    const rows = node.leftCol;
    const cols = node.topRow;
    const script = [];

    if(node.topLeftText) {
      const topLeftScript = this.extractScriptFromNode(node.topLeftText, lang, meta, preProcesses, scriptNodeRef);
      this.useScriptDecision(node, meta, topLeftScript, 'topLeft_voiceover');
      if(node.topLeft_voiceover.script) {
        script.push(node.topLeft_voiceover.script + " ... ");
      }
    }

    rows.forEach((rowHead, r)=>{
      const rowScript = this.extractScriptFromNode(rowHead.content, lang, meta, preProcesses, scriptNodeRef);
      this.useScriptDecision(rowHead, meta, rowScript);
      script.push(rowHead.voiceover.script + " ... ");
      cols.forEach((colHead, c)=>{
        const colScript = this.extractScriptFromNode(colHead.content, lang, meta, preProcesses, scriptNodeRef);
        this.useScriptDecision(colHead, meta, colScript);
        script.push(colHead.voiceover.script + " ... ")
        if (c!=cols.length-1) {
          script.push(`${this.lang.tra(this.profile.getStyleProfile()[lang].voiceScript.general.or, lang)} ... `)
        }
      });
    });

    return script.join(' ');
  }

  private getNumberedOrder(node:IContentElement, script, index:number, isLast:boolean, lang:string, nodeScript?:any[]) {
    const boxSlug = this.profile.getStyleProfile()[lang].voiceScript.ordering?.box || 'voice_box';
    const box = this.lang.tra(boxSlug);

    let { first, second, third, last } = this.profile.getStyleProfile()[lang].voiceScript.general;
    if(!last) last = 'last';
    const numSlugPreOrder = [first || '1st', second || '2nd', third || '3rd'];

    const prefix = "... "
    const postfix = lang =='en' ? 'th' : 'e';
    
    let numSlug;

    if (index < numSlugPreOrder.length) {
      numSlug = numSlugPreOrder[index]
    } 
    else if (index >= 3 ||!isLast || lang=='fr') {
      numSlug = (index + 1) + postfix; 
    } 
    else {
      numSlug = last
    }

    const numberOrder = prefix + this.lang.tra(numSlug) + " " + box;
    script.push(numberOrder);
    if(nodeScript) nodeScript.push(getFillerNode(node.entryId, numberOrder)); 
  } 

  private extractScriptFromOrdering(node:IContentElementOrder, lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    const orderProfile = this.profile.getStyleProfile()[lang].voiceScript.ordering;
    const script = [];
    let nodeScript = [];
    const isReorder = (node.orderMode == OrderMode.REORDER);
    if (node.showDefaultText || node.showDefaultText==undefined) script.push(`... ${this.lang.tra(orderProfile?.order_instr || 'txt_default_order_instr' )}`);
    let numFixed = 0;
    const item = `${this.lang.tra(orderProfile?.items || 'voice_order_items' )}`;
    script.push(" ... "+item)
    nodeScript.push(getFillerNode(node.entryId, item));

    // if(node.delimiterIcon == 'icon') {
    //   const direction = this.getOrderDirection(node.delimiterIcon);
    //   const numberOnLeft = 
    //   script.push(`stacked ${node.displayStyle} on the left are ${node.options.length - 1}`)
    // }
    
    let use_options = node.scrambledOptions

    if(useSetOrders(node)) {
      use_options = reorderOrderOptions(clone(use_options));
    }
    
    use_options.forEach((option, index)=>{
      let block;
      if (option.elementType == ElementType.TEXT) block = this.processPlainTextScript(option.caption || option.content, lang, preProcesses);
      else if (option.elementType == ElementType.MATH) block = this.extractScriptFromMathNode(option, lang, scriptNodeRef);
      else if (option.elementType == ElementType.IMAGE && option.images[ImageStates.DEFAULT]) block = this.extractScriptFromImageNode(option.images[ImageStates.DEFAULT].image, lang, meta, scriptNodeRef);
      
      if (!option.isReadOnly) {
        this.useScriptDecision(option, meta, block);
        script.push(`...Order option`);
        script.push(` ...`+option.voiceover.script)
        nodeScript.push(getFillerNode(node.entryId, `Order option`))
        nodeScript.push(getKeyIdNode(node.entryId, option.key_id, option.voiceover.script, ORDER_NODE_KEY_ID));
      }
    })

    if(!isReorder){
      use_options = node.options;
      use_options.forEach((option, index)=>{
        this.getNumberedOrder(node, script, index, index==use_options.length-1, lang, nodeScript)
        let labelMsg;
        if (option.labelType == ElementType.IMAGE && option.labelImg) {
          labelMsg = this.extractScriptFromImageNode(option.labelImg, lang, meta, scriptNodeRef);
        } else if ((!option.labelType || option.labelType==ElementType.TEXT) && option.label) {
          labelMsg = this.processPlainTextScript(option.label, lang, preProcesses);
        }       
        if (labelMsg) {
          this.useScriptDecision(option, meta, labelMsg, 'label_voiceover')
          script.push(" ..."+option.label_voiceover.script)
          nodeScript.push(getKeyIdNode(node.entryId, option.key_id, option.label_voiceover.script, TARGET_NODE_KEY_ID))
        }
  
        if (option.isReadOnly) {
          let block;
          if (option.elementType == ElementType.TEXT) block = this.processPlainTextScript(option.content || option.caption, lang, preProcesses);
          else if (option.elementType == ElementType.MATH) block = this.extractScriptFromMathNode(option, lang, scriptNodeRef);
          else if (option.elementType == ElementType.IMAGE && option.images[ImageStates.DEFAULT]) block = this.extractScriptFromImageNode(option.images[ImageStates.DEFAULT].image, lang, meta, scriptNodeRef);         
          this.useScriptDecision(option, meta, block);
          script.push(" ... "+option.voiceover.script)
          nodeScript.push(getKeyIdNode(node.entryId, option.key_id,block, ORDER_NODE_KEY_ID))
        }        
      })
    }    
    scriptNodeRef.push({nodeRefId: node.entryId, nodeScript: nodeScript.join('\n')})
    return script.join(' ');
  }

  private extractScriptFromMathNode(node:any, lang:string, scriptNodeRef) {
    let latex = node.latex || (<any>node).content; // second alt is for mcq math
    let nodeScript = '';
    let nodeRefId = node.entryId;
    if(!latex) {
      latex = "";
    }
    if(node.elementType == ElementType.MATH && node.voiceover && node.voiceover.script) {
      return node.voiceover.script;
    }

    const script = processText(latex, <StylingProcess[]>this.profile.getStyleProfile()[lang].voiceScript.math);
    nodeScript += script;
    // return processText(latex, <StylingProcess[]>this.profile.getStyleProfile()[lang].voiceScript.math);
    scriptNodeRef.push({nodeRefId, nodeScript})
    return script;
  }
  
  private extractScriptFromLatex(latex:string, lang:string): string {
    return processText(latex, <StylingProcess[]>this.profile.getStyleProfile()[lang].voiceScript.math);
  }

  

  private extractScriptFromImageNode(node:IContentElementDynamicImage, lang:string, meta:IScriptGenMeta, scriptNodeRef, isUploadVoiceover=true) {
    const script = [];
    let nodeScript = '';
    let nodeRefId = node.entryId;
    if (!node) return '';
    
    const altStartTag = getNodeCustomRefTag(getCustomNodeRefval(CT_ALT_TEXT + CUSTOM_TAG_START, nodeRefId));
    const altEndTag = getNodeCustomRefTag(getCustomNodeRefval(CT_ALT_TEXT + CUSTOM_TAG_END, nodeRefId));
    if (node.altText) {
      script.push(node.altText);
      nodeScript += altStartTag +`<s>${node.altText}</s> ` + altEndTag
    }
    
    else if (node.images) {
      const img = node.images[ImageStates.DEFAULT];
      if (img && img.image.altText) {
        script.push(img.image.altText);
        nodeScript += altStartTag +`<s>${img.image.altText}</s> ` + altEndTag
      }
      
    }
    if (node.subtexts) {
      node.subtexts.forEach((text)=>{
        script.push(text.text)
        nodeScript += `<s>${text.text}</s> `
      })
    }

    if (isUploadVoiceover) {
      this.useScriptDecision(node, meta, script.join(' '));
      this.uploadNewVoice(node.voiceover.script, node.voiceover, lang);
      scriptNodeRef.push({nodeRefId: node.entryId, nodeScript})
      return node.voiceover?.script; 
    } else {
      return script.join(' ');
    }
  }


  public extractScriptFromMcqNode(node:IContentElementMcq, lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    let scriptParts:string[] = [];
    let nodeScriptparts = [];
    if(node.displayStyle === McqDisplay.TABLE){
      const tableProfile = this.profile.getStyleProfile()[lang].voiceScript.table;
      const beginColLabel = this.lang.tra(tableProfile.beginColumn, lang);
      const beginRow = this.lang.tra(tableProfile.beginRow, lang);
      const rowBeginFillerNode = `${beginRow} 1`;
      scriptParts.push(`... ${rowBeginFillerNode} ...`);
      nodeScriptparts.push(getFillerNode(node.entryId, rowBeginFillerNode, true));

      if(!node.isRadioRowHeaderDisabled) {
        const columnIndexReader = beginColLabel + ' ' + 1;
        const rowHeaderLabel = this.lang.tra('lbl_mcq_row');
        scriptParts.push(` ... ${columnIndexReader} ... ${rowHeaderLabel} ... `);
        nodeScriptparts.push(columnIndexReader + "  " + rowHeaderLabel)
      }

      let script = ''
      node.tableCols?.forEach((colHeader, i) => {
        const columnIndex = i + 2 // column 1 is reserved for radio buttons 
        const columnIndexReader = beginColLabel + ' ' + columnIndex;
        const processedText = this.processPlainTextScript(colHeader.label, lang, preProcesses);
        script += ` ... ${columnIndexReader} ... ${processedText} ... `;
        nodeScriptparts.push(columnIndexReader + "  " + processedText) 
      });
      scriptParts.push(script);
    }
    const beginMcqOptions = `${this.lang.tra(this.profile.getStyleProfile()[lang].voiceScript.mcq.beginOptions)}`;
    scriptParts.push(beginMcqOptions);
    nodeScriptparts.push(getFillerNode(node.entryId, beginMcqOptions, true));
    node.options.forEach((option, i) => this.extractScriptFromMcqOptionNode(node, lang, meta, option, i, scriptParts, preProcesses, [],  nodeScriptparts) );
    const nodeScript = nodeScriptparts.filter(script => script != null || script != '').map(sc => `<s>${sc}</s>`).join('\n')
    scriptNodeRef.push({nodeRefId: node.entryId, nodeScript})
    return scriptParts.join('...\n')
  }

  public extractScriptFromMcqNodeAsync(node:IContentElementMcq, lang:string, meta:IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef) {
    let scriptParts:string[] = [];
    return Promise.all(
      node.options.map((option, i) => this.extractScriptFromMcqOptionNode(node, lang, meta, option, i, scriptParts, preProcesses, scriptNodeRef, []) )
    )
  }

  public useScriptDecision(option, meta, script, voiceoverProp: string = DEFAULT_VOICEOVER_PROP) {
    option[voiceoverProp] = option[voiceoverProp] || {};
    if (option[voiceoverProp].script && !meta.useOldScriptsDecision){
      meta.useOldScripts = confirm('Some of the options already have some script generated inside. Would you want to use the old script?');
      meta.useOldScriptsDecision = true;
    }
    if (!meta.useOldScripts && meta.optionScripts.length > 0){
      const injectedScript = meta.optionScripts.splice(0,1)[0];
      option[voiceoverProp].script = injectedScript
    }
    else if (option[voiceoverProp].script && meta.useOldScripts){
      if (script !== option[voiceoverProp].script){
        console.warn('Option script has been modified from original recording', [script, option[voiceoverProp].script])
      }
    }
    else {
      option[voiceoverProp].script = script;
      option[voiceoverProp].scriptSnapshot = this.voiceoverState.getSnapshotContentForElement(option);      
    }
  }

  public extractScriptFromMcqOptionNode(node:IContentElementMcq, lang:string, meta:IScriptGenMeta, option:IContentElementMcqOption, i:number, scriptParts:string[], preProcesses:StylingProcess[], scriptNodeRef, nodeScriptparts: string[]) {
    const mcqProfile = this.profile.getStyleProfile()[lang].voiceScript.mcq;
    const tableProfile = this.profile.getStyleProfile()[lang].voiceScript.table;
      // save the script into the option voice slot
      if (!option.voiceover){
        option.voiceover = {};
      }
      let script = '';

      if(node.displayStyle === McqDisplay.TABLE ){
        const beginColLabel = this.lang.tra(tableProfile.beginColumn, lang);
        const beginRow = this.lang.tra(tableProfile.beginRow, lang);
        const rowBeginFillerNode = `${beginRow} ${i + 2}`;
        script += rowBeginFillerNode;
        nodeScriptparts.push(getFillerNode(node.entryId, rowBeginFillerNode, true));
        const firstCol = `${beginColLabel} 1 ${this.lang.tra(mcqProfile.beginOption, lang)} ${i+1}`;
        script += firstCol;
        nodeScriptparts.push(firstCol);
        // insert col1 radio option read

        (<IContentElementMcqOptionInTable>option).cols?.forEach((col, colIndex) => {
          const columnIndex = colIndex + 2 // column 1 is reserved for radio buttons 
          const columnIndexReader = beginColLabel + ' ' + columnIndex;
          const processedText = this.processPlainTextScript(col.content, lang, preProcesses);
          script += ` ... ${columnIndexReader} ... ${processedText} ... `;
          nodeScriptparts.push(columnIndexReader + "  " + processedText)
        });
      } else {
        
        if (!node.isOptionLabelsDisabled && node.displayStyle != McqDisplay.DROPDOWN && node.elementType != ElementType.CUSTOM_MCQ  && node.elementType != McqDisplay.TABLE) {
          script = `${this.lang.tra(mcqProfile.beginOption, lang)}  ${optionLetters[i]}, `;
        }
  
        switch (option.elementType){
          case 'text':
            let elText:IContentElementText = <any> option;
            if (elText.paragraphStyle){
              script += this.extractScriptFromNode(option, lang, meta, preProcesses, scriptNodeRef);
            }
            else{
              script += this.processPlainTextScript(option.content, lang, preProcesses);
            }
          break;
          default: 
            if (option.elementType != ElementType.DYNAMIC_IMAGE) {
              script += this.extractScriptFromNode(option, lang, meta, preProcesses, scriptNodeRef); 
            } else {
              const dyn_option = <IContentElementCustomMcqOption> option;
              script += this.extractScriptFromNode(dyn_option.dynamicImage, lang, meta, preProcesses, scriptNodeRef);
            }
            break;
        }

        nodeScriptparts.push(script);
      }
      
      
      /*if (option.voiceover.script && !meta.useOldScriptsDecision){
        meta.useOldScripts = confirm('Some of the options already have some script generated inside. Would you want to use the old script?');
        meta.useOldScriptsDecision = true;
      }
      if (!meta.useOldScripts && meta.optionScripts.length > 0){
        const injectedScript = meta.optionScripts.splice(0,1)[0];
        option.voiceover.script = injectedScript
      }
      else if (option.voiceover.script && meta.useOldScripts){
        if (script !== option.voiceover.script){
          console.warn('Option script has been modified from original recording', [script, option.voiceover.script])
        }
      }
      else {
        option.voiceover.script = script;
      }*/
      this.useScriptDecision(option, meta, script);
      scriptParts.push(option.voiceover.script +"...");
      return this.uploadNewVoice(option.voiceover.script, option.voiceover, lang);
  }
  
  extractScriptFromInteractiveDiagram(node: IContentElementInteractiveDiagram, lang: string, meta: IScriptGenMeta, preProcesses:StylingProcess[], scriptNodeRef: IScriptNodeRef[]) {
    return extractScriptFromInteractiveDiagram(node, { 
      lang, meta, preProcesses, scriptNodeRef, 
      profile: this.profile,
      langService: this.lang,
      processPlainTextScript: (str:string, lang:string, preProcesses: StylingProcess[]) => this.processPlainTextScript(str, lang, preProcesses),
      extractScriptFromLatex: (latex:string, lang:string) => this.extractScriptFromLatex(latex, lang),
      extractScriptFromImageNode: (node:IContentElementDynamicImage, lang:string, meta:IScriptGenMeta, isUploadVoiceover=true) => this.extractScriptFromImageNode(node, lang, meta, scriptNodeRef, isUploadVoiceover),
      getNumberedOrder: (node:IContentElement, script, index:number, isLast:boolean, lang:string) => this.getNumberedOrder(node, script, index, isLast, lang),
      useScriptDecision: (object:any, meta:IScriptGenMeta, script:string, voiceoverProp:string=DEFAULT_VOICEOVER_PROP) => this.useScriptDecision(object, meta, script, voiceoverProp),
      uploadNewVoice: (script:string, element:any, lang:string, fromItemVoiceOver?:boolean) => this.uploadNewVoice(script, element, lang, fromItemVoiceOver)
    });
  }

  private extractScriptFromSelectText(node: IContentElementTextSelection, lang: string, meta: IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef: IScriptNodeRef[]): string {
    let scriptParts: string[] = [];
    let promises: Promise<any>[] = [];
  
    // Function to process individual text node
    const processTextNode = async (textNode: IContentElementSelectableText) => {
      if (!textNode.voiceover) {
        textNode.voiceover = { script: '', url: '', entryId: '' };
      }
  
      let script = '';
      if (textNode.elementType === ElementType.TEXT) {
        // Extract script for text element
        script = this.processPlainTextScript(textNode.content, lang, preProcesses);
      }
  
      // Save script to text node's voiceover
      this.useScriptDecision(textNode, meta, script);
      scriptParts.push(script);
  
      // Upload voice for this text node
      return this.uploadNewVoice(script, textNode.voiceover, lang);
    };
  
    // Process texts based on mode
    if (!node.isParagraphMode && node.texts?.length > 0) {
      // Process each text in texts array
      node.texts.forEach(textNode => {
        promises.push(processTextNode(textNode));
      });
    }
  
    // Process paragraphs if paragraph mode is active
    if (node.isParagraphMode && node.paragraphs?.length > 0) {
      node.paragraphs.forEach(paragraph => {
        paragraph.forEach((textNode: IContentElementSelectableText) => {
          promises.push(processTextNode(textNode));
        });
      });
    }
  
    // Wait for all voice uploads to complete
    Promise.all(promises).then(() => {
      const parentScript = scriptParts.join(' ');
      this.uploadNewVoice(parentScript, node.voiceover, lang);
    });
  
    // Return combined script
    return scriptParts.join(' ');
  }

  async uploadNewVoice(script, element: { url?: string, urlList?: any[], fileType?: string, index?: number, scriptType?: string, scriptNodeRef?: IScriptNodeRef[], speechMarks?: any, voiceoverList?: IElementVoiceover[], script?: string, scriptSnapshot?: string }, lang, fromItemVoiceOver?: boolean) {
    const { scriptType, scriptNodeRef } = element;
    if (!script) {
      script = element.script;
    }

    type voiceUploadResponse = { url: string, speechMarkUrl: string, speechMarkData: string };

    const singleVoiceUploadPost = (res: voiceUploadResponse) => {
      element.url = res.url;
      element.fileType = 'audio/mp3';
      if (fromItemVoiceOver) {
        if (res.speechMarkData && res.speechMarkData.trim() !== '') {
          const speechMarks = processSpeechMarksFromPolly(res);
          element.voiceoverList.push({
            audioUrl: res.url,
            speechMarks: speechMarks,
            speechMarksUrl: res.speechMarkUrl
          });
        } else {
          console.log("Speech mark data is empty, skipping processing.");
        }
      }
    }

    const splitIntoSentences = (str: string) => str.match(/[^.!?]+[.!?]/g);


    if (!scriptType || scriptType == 'text') {
      // TODO: next ticket - clarify if we even need the text based voice over
      if (script.length >= MAX_SCRIPT_SIZE) {
        const sentences = splitIntoSentences(script); // script.split('.');
        let currSentences = "";
        let partitions = [];
        sentences.forEach(sentence => {
          if (currSentences.length + sentence.length > MAX_SCRIPT_SIZE) {
            partitions.push(currSentences);
            currSentences = "";
          }
          currSentences += sentence;
        });
        if (currSentences.length > 0) {
          partitions.push(currSentences);
        }

        const promiseList = partitions.map((part, index) => {
          this.itemVoiceOverAudios = []
          return this.voiceUpload(part, element, lang);
          // promiseList.push(this.voiceUploadItemVoiceOver(part, element, lang, index, partitions.length))
        });

        element.urlList = (await Promise.all(promiseList))?.map(url => ({ url: url }));
        await this.combineAudioAndUpload(element)
      }
      else {
        const res = await this.voiceUpload(script, element, lang);
        singleVoiceUploadPost(res);
      }
    }
    else {
      element.voiceoverList = [];
      // generate voice and speech marks for SSML script     
      let generatedSSML = this.genSsmlForVoiceOver(scriptNodeRef);
      console.log("upload ssml 1", generatedSSML)

      if (generatedSSML.length >= MAX_SCRIPT_SIZE) {
        // console.log("upload ssml 2 max size")

        for (const node of scriptNodeRef) {
          const nodeRef: IScriptNodeRef[] = [node]
          const nodeSsmlScript = this.genSsmlForVoiceOver(nodeRef);

          if (nodeSsmlScript.length >= MAX_SCRIPT_SIZE) {
            // Do some kind of further Processing here (this will definitely affect the highlight syncing if not being careful)
            const sentences = splitIntoSentences(node.nodeScript) //node.nodeScript.match(/[^.!?]+[.!?]/g);
            let currSentences = "";
            let partitions = [];
            sentences.forEach(sentence => {
              if (currSentences.length + sentence.length > MAX_SCRIPT_SIZE) {
                partitions.push(currSentences);
                currSentences = "";
              }
              currSentences += sentence;
            });

            if (currSentences.length > 0) {
              partitions.push(currSentences);
            }
            let promiseList = partitions.map((partition, index) => {
              console.log(partition)
              if (index === 0) partition = getNodeCustomRefTag(getCustomNodeRefval(AUTHORING_NODE_ID_START, node.nodeRefId)) + partition
              if (index === partitions.length - 1) partition += getNodeCustomRefTag(getCustomNodeRefval(AUTHORING_NODE_ID_END, node.nodeRefId));
              const partitionSsmlScript = this.genSsmlForVoiceOver([{ nodeRefId: node.nodeRefId, nodeScript: partition }], false, false);
              console.log(partitionSsmlScript)
              return this.voiceUpload(partitionSsmlScript, element, lang)
            });
            const voicePartitions = await Promise.all(promiseList);
            const res = await this.combineAudioAndSpeechMarks(voicePartitions);
            singleVoiceUploadPost(res);
            // console.log('Node script size is bigger then allowed size.', node)

          }
          else {
            const res = await this.voiceUpload(nodeSsmlScript, element, lang);
            singleVoiceUploadPost(res);
          }
        }


      } else {
        const res = await this.voiceUpload(generatedSSML, element, lang);
        singleVoiceUploadPost(res)
        return;
      }
    }
  }


  private extractScriptFromBookmarkLink(node: IContentElementBookmarkLink, lang: string, meta: IScriptGenMeta, preProcesses: StylingProcess[], scriptNodeRef: IScriptNodeRef[]): string {
    let caption = node.caption || "";
    caption = caption.trim();
    let script = this.processPlainTextScript(caption, lang, preProcesses);
    if (node.entryId) {
      scriptNodeRef.push({ nodeRefId: node.entryId.toString(), nodeScript: script });
    }
    this.useScriptDecision(node, meta, script);
    return script;
  }

  private voiceUpload(script, element:{url?: string, fileType?:string, scriptType?: string, speechMarks?: any}, lang){
    return this.auth
        .apiCreate(
          this.routes.TEST_AUTH_TEXT_VOICE,
          { script, lang, scriptType: element.scriptType }
        ).catch(err => {
          switch (err.message) {
            case 'InvalidSsmlException':
              return alert('Generate script is not supported, please contact support!');          
            default:
              break;
          }
        })

    // return new Promise((resolve, reject) => {
    //   return this.auth
    //     .apiCreate(
    //       this.routes.TEST_AUTH_TEXT_VOICE,
    //       { script, lang, scriptType: element.scriptType }
    //     )
    //     .then((res:{url:string, speechMarkUrl: string, speechMarkData: string})=> {
    //       console.log(res)
    //       element.url = res.url;
    //       element.fileType = 'audio/mp3';
    //       element.speechMarks = processSpeechMarksFromPolly(res)
    //       setTimeout(() => {
    //         resolve();
    //       }, 500)
    //     })
    // });
  }

  itemVoiceOverAudios: {url: string, index: number}[] = [] as {url: string, index: number}[]
  private voiceUploadItemVoiceOver(script, element:{url?: string, fileType?:string, urlList?: {url: string, index: number}[] , scriptType?: string, scriptSnapshot?: string}, lang, index: number, length:number){
    return new Promise<void>((resolve, reject) => {
      return this.auth
        .apiCreate(
          this.routes.TEST_AUTH_TEXT_VOICE,
          { script, lang, scriptType: element.scriptType }
        )
        .then((res:{url:string})=> {
          console.log(res.url, index);
          element.fileType = 'audio/mp3';
          // Store the snapshot when voice is generated
          // element.scriptSnapshot = '';
          if(!this.itemVoiceOverAudios || !this.itemVoiceOverAudios.find(entry => entry.url == res.url)){
            this.itemVoiceOverAudios.push({url: res.url, index: index})
            this.itemVoiceOverAudios.sort((a, b)=>{
              if(a.index < b.index) { return -1; }
              if(a.index > b.index) { return 1; }
              return 0;
            })
            element.urlList = this.itemVoiceOverAudios;
            if(element.urlList.length == length){
              this.combineAudioAndUpload(element).then(()=>{
                resolve(void 0);
              });
            }
            else{
              setTimeout(() => {
                resolve(void 0);
              }, 500)
            }
          }     
        })
    });
  }

  async combineAudioAndUpload(element:{url?: string, fileType?:string, urlList?: {url: string, index: number}[]}){
    if(element.urlList){
      let uris = element.urlList.map(url => url.url),
      proms = uris.map(uri => fetch(uri).then(r => r.blob()));
      console.log(element.urlList);
      const blobs: any =  await Promise.all(proms);
      let blob = new Blob(blobs, {type : 'audio/mp3'}),
        blobUrl = URL.createObjectURL(blob),
        audio = new Audio(blobUrl);
        audio.duration;
      const file = new File([blob], 'voice.wav');
        return await this.auth
        .uploadFile(file, file.name, 'authoring', true)
        .then(res => {
            element.url = res.url;
            element.fileType = 'audio/mp3';
            console.log('file ready')
        })
    }
  }

  private combineAudioAndSpeechMarks = async(responses) => {

    const getAudioDuration = (audioUrl): Promise<number> => {
      const audioEl = createAudioEl(null, audioUrl);
      audioEl.load();
      audioEl.preload = "metadata";
      return new Promise((resolve, reject) => {
        audioEl.addEventListener('loadedmetadata', () => resolve(audioEl.duration))
      })
    }

    let previousAudioDuration = 0;  // MS

    const audioBlobs = [];
    const speechMarksFromBlobs = [];

    for(const res of responses){
      const {url: audioUrl, speechMarkUrl, speechMarkData} = res;
      const audioResponse = await fetch(audioUrl); 
      const audioBlob = await audioResponse.blob();
      audioBlobs.push(audioBlob);
      
      const audioDuration = await getAudioDuration(audioUrl);
      // console.log("duration of audio", audioDuration)

      const currentDuration = +((audioDuration * 1000).toFixed(0)); //parseInt(); //MS
      
      console.log(previousAudioDuration, currentDuration, audioDuration)
      if(previousAudioDuration == null) { previousAudioDuration = currentDuration}
      else {
        // add the previous duration to current speech Marks
        const preProcessedData = speechMarkData?.trim().split('\n');
        const processedSpeechMarksPart = preProcessedData.map(line => {
          const parsedLine = JSON.parse(line);
          parsedLine.time = +(parsedLine.time) + previousAudioDuration;
          return JSON.stringify(parsedLine);
        });

        // console.log(processedSpeechMarksPart)
        speechMarksFromBlobs.push(processedSpeechMarksPart.join('\n'));
        previousAudioDuration += currentDuration;
      }
    }
    
    const speechMarksData = speechMarksFromBlobs.join('\n');
    
    // Create a Blob from the merged audio Blob
    let finalAudioBlob = new Blob(audioBlobs, {type : 'audio/mp3'});
    const fileBlob = new File([finalAudioBlob], 'voice.wav');
    const fileUpload = await this.auth
      .uploadFile(fileBlob, fileBlob.name, 'authoring', true)
     
    return {url: fileUpload.url, speechMarkUrl: '', speechMarkData: speechMarksData}
  }
}
