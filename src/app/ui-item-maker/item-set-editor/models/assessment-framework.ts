import { ITestDesignPayload } from "../../../ui-testtaker/view-tt-test-runner/view-tt-test-runner.component";
// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../../services/asset-library.service';
import { AssignedUsersService } from '../../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../../auth-scope-settings.service";
import { AuthService } from '../../../api/auth.service';
import { DataGuardService } from '../../../core/data-guard.service';
import { EditingDisabledService } from '../../editing-disabled.service';
import { ItemComponentEditService } from '../../item-component-edit.service';
import { ItemMakerService } from '../../item-maker.service';
import { LangService } from '../../../core/lang.service';
import { LoginGuardService } from '../../../api/login-guard.service';
import { PrintAltTextService } from '../../print-alt-text.service';
import { PrintModeService } from '../../print-mode.service';
import { RoutesService } from '../../../api/routes.service';
import { ScriptGenService } from '../../script-gen.service';
import { EStyleProfile, StyleprofileService } from '../../../core/styleprofile.service';
import { WhitelabelService } from '../../../domain/whitelabel.service';
import { PARAMETER_LOCATIONS } from "../../view-im-parameters/view-im-parameters.component";
import { PROGRESS_BAR_STATE } from "../../widget-framework-settings/editors/progress-bar-editor/constants";

export interface IRefDoc {
  itemId:number;
  caption:string; 
  captionFr?: string;
  slug?:string;
}

export interface IAssessmentFrameworkDef {
  __id?: string,
  projectId?: string,
  caption: string,
  subcaption?: string,
  uidCreator?: string,
  uidLastTouch?: string, 
  timeCreated?: any,
  timeLastTouch?: any,
}
export interface IQuadrantConstraint { // only equality
  param: string,
  val: string,
}

export enum QuadrantConstraintErrorTypes{
  USER_INPUT = 'USER_INPUT',
  IMPOSSIBLE_CONSTRAINT = 'IMPOSSIBLE_CONSTRAINT',
}

export enum QuadrantSettingErrors{
  TRIES_BEFORE_FAIL = "Tries Before Fail",
  TRIES_BEFORE_FAIL_WHOLE = "Tries Before Fail Whole",
  NUM_ITEMS = "Num of Items",
  DISCARD_THRESHOLD = "Discard Threshold"
}

export enum INPUT_ERRORS{
  UNDEFINED = '(Undefined)',
  NOT_A_NUMBER = '(is NaN)',
  INVALID_INPUT = 'INVALID INPUT'

}

export enum QUADRANT_CONFIG_ERR_MESSAGES {
  PROBLEMATIC_QUADRANT_CONFIG = 'Following quadrant config properties are problematic:\n-',
  PROBLEMATIC_ITEM_CONFIG = 'Following quadrant config properties are problematic:\n',
  INVALID_INPUT_FOR_CONSTRAINT = 'The following constraints have invalid user input:',
  IMPOSSIBLE_CONSTRAINT_MESSAGE = 'The following constraints impossible with current item bank:',
  MISSING_PARAM_ON_CONSTRAINT = 'The Following params dont exist on this quadrant might cause issues:\n-',
  NO_MORE_TESTLETS_POSSIBLE = 'All Possible Testlets Have Been Created',
  MIN_COUNT_GREATER_THAN_ITEMS = 'Min Count > Number of items'
}

export enum TestletConstraintFunction {
  MATCH = 'MATCH',
  VARIETY = 'VARIETY',
  AVG = 'AVG',
  STDEV = 'STDEV',
}
export interface ITestletConstraintCommon {
  param: string, 
  isMin: boolean,
  isMax: boolean,
  isEqual: boolean,
  excludeEmptyParams: boolean,
  isHidden: boolean
  // isProportion: boolean,
}
export interface ITestletConstraint_MATCH extends ITestletConstraintCommon {
  val: string | number,
  count: number,
}
export interface ITestletConstrain_VARIETY extends ITestletConstraintCommon {
  count: number,
  isEmptyWildcard?: boolean
}
export interface ITestletConstrain_AVG extends ITestletConstraintCommon {
  val: number,
}
export interface ITestletConstrain_STEDEV extends ITestletConstraintCommon {
  val: number,
}
export interface ITestletConstraint {
  weight: number,
  func: TestletConstraintFunction,
  config: ITestletConstraint_MATCH | 
  ITestletConstrain_VARIETY | 
  ITestletConstrain_AVG | 
  ITestletConstrain_STEDEV,
}
export interface IQuadrantTestletConfig {
  numItems: number,
  discardThreshold: number,
  triesBeforeFail: number,
  triesBeforeFailWhole: number,
  constraints: ITestletConstraint[],
}
export interface IQuadrant {
  id: number, // dont show to user
  section: number,
  constraints: IQuadrantConstraint[],
  description: string,
  config: IQuadrantTestletConfig,
  is_post_assembly_insertion?: boolean,
  include_in_form_mapping?: boolean,
}

export interface IAssembledPanel  {
  id: number | string,
  testletIds: number[],
  lang: string,
  sections?: any[],
  multiplier?: number;
  isExcluded?: boolean,
  isProblematic?: boolean,
  numOperationalItems?: number,
  numFieldTestItems?: number,
}



export interface IQuadrantTestlet {
  id: number, // dont show to user
  section: number,
  quadrant: number,
  isReviewed?: boolean,
  isDisabled?: boolean,
  statsMap: {[key:string]:number | string},
  stats: Array<{key:string, val:number | string}>,
  isModified?: boolean,
  comments?: {
    caption: string,
    timestamp: number,
    user: string
  }[],
  quality: number,
  questions: {
    label: string,
    id?: number,
    [key:string]: any,
  }[],
  similaritySlug?: string
}

export enum TestFormConstructionMethod {
  LINEAR = 'LINEAR',
  TLOFT = 'TLOFT',
  MSCAT = 'MSCAT',
  BUCKETS = 'BUCKETS',
}

export const testletFormConstructionMethod = [TestFormConstructionMethod.MSCAT, TestFormConstructionMethod.TLOFT]

export const QUESTION_WORDING_OPTS = [
  'title_question',
  'title_page'
]

export enum ENextBtnOpt {
  NEXT = 'NEXT',
  NEXT_PREV = 'NEXT_PREV'
}

export const NEXT_BTN_OPTS = [
  {id: ENextBtnOpt.NEXT, caption: 'Next button after question'},
  {id: ENextBtnOpt.NEXT_PREV, caption: 'Next/Prev button overlay'}
]

export interface IPanelAssemblyConfig {
  startingModule: string,
  allowNumCorrect?: boolean,
  allowShuffle?: boolean,
  numStages: number,
  uniqueSpecificExpectationCode?: boolean,
  maxDifficultyLevel?:number,
  isEasyStartDisabled?:boolean,
  easyStartSettings: {
    numberOfItems: number,
    prop: string,
    isInverse: boolean,
  },
  allModules: Array<{
    id: string,
    stageNumber: number,
    difficultyLevel?: number,
    item_count: number,
    difficulty_range: number[], 
    has_ft?:boolean,
    num_ft?:number,
    ft_quadrant_id?:number,
    item_difficulty_range?: number[], 
    preambleQuestionLabel?: string,
    preambleLabelList?: string[],
    postambleLabelList?: string[],
    sidebarThumbnailEn?: string,
    sidebarThumbnailFr?: string,
  }>,
  numFtVariants?: number,
  routingRules: {
    [key:string]: Array<IRoutingRule>
  },
  constraints: Array<{
    when: Array<{
      moduleProp: string,
      comparison: string,
      value: number
    }>,
    require: Array<{
      prop: string,
      method: string,
      comparison: string,
      config: {
        value?:string,
        count?:number,
      }
    }>
  }>
}
export interface IPanelModuleConfig {
  moduleId: number,
  itemLabels: string[],
  itemIds?: number[],
  __cached_itemIds?: number[],
  // difficulty?:number,
  posOverrides?:{[itemId:number]:number}
  routingExclusions?: {[itemId:number]:boolean}
}
export interface IPanelModulesConfig {
  id: number | string,
  isDisabled?: boolean,
  // overallAgreement?: number,
  dateCreated: string,
  modules: IPanelModuleConfig[]
  testletItems?: any[]
}

export interface IAssessmentToolbarOptions {
  tts?: boolean;
  zoomIn?: boolean;
  zoomOut?: boolean;
  lineReader?: boolean;
  hiContrast?: boolean;
  toggleEditor?: boolean;
  highlighter?: boolean;
  eraser?: boolean;
  backgroundColor?: boolean;
  backgroundColor_reserve_drawing?: boolean;
  notepad?: boolean;
  infoButton?: boolean;
  freehand?: boolean;
  dictionary?: boolean;
  dictionaryDisplaySynonyms?: boolean;
  tts_v2?:boolean;
  stt?:boolean;
  ttsHoverGranularity?: TTSHoverGranularity;
}

export enum TTSHoverGranularity {
  READ_ALL = 'read-all',
  SENTENCE_BY_SENTENCE = 'sentence-by-sentence'
}

export enum ReportOptions{
  SCORE =  "score",
  CHECKMARK = "checkmark",
  STUDENT_PEN = "student_pen",
  LEGEND = "legend"
}

export interface IAssessmentReportOptions {
  hideScore?: boolean;
  showCheckmark?: boolean;
  hideStudentPen?: boolean;
  hideLegend?: boolean;
  autoCalcQuesWeight?: boolean
}

export interface ISectionItemsMap { 
  [key:number]: ISectionItems 
}
export interface ISectionItems {
  questions: {
    label: string,
    id?: number,
    QN? : number // student question number
    isAnchor?: boolean
  }[]
}
export interface IAssessmentParameterViewFilter {
  name: string;
  filter: any
}
export interface IAssessmentFrameworkDetail extends IAssessmentFrameworkDef{
  panelName?: string;
  itemPool?: string;
  notes?: string;
  cutScore?: any;
  bucketParam?: string;
  numTestTakers?: number,
  parititionIdAI: number,
  quadrantIdAI: number,
  quadrants: IQuadrant[],
  isTestletItemsReplaceDisabled?: boolean,
  isPostAssemblyTestletsAppliedToAllPanels?: boolean,
  preservedMetaParams?: string[],
  sectionItems?: ISectionItemsMap,
  panelAssembly?: IPanelAssemblyConfig,
  panels?: IPanelModulesConfig[],
  isSampleTest?: boolean,
  quadrantItems?: {
    id: number,
    questions: {
      label: string,
      id: number,
    }[],
    testletQuestions: {
      label: string,
      id: number,
    }[],
    section?: number,
    numTestlets?:number,
    numTestletsUsed?:number,
    exposureMin?:number,
    exposureMax?:number,
    numItemsUsed?:number, // in enabled testlets
    numItemsNotInGeneratedTestlet?: number, // in all testlets
    numItemsInGeneratedTestlet?: number, // in all testlets
    description:string,
    minQRequired:number,
    crossLinkedItems:{
      label:string, 
      quadrantsCrossLinks: any[]
    }[],
    // [key:string]: any,
  }[],
  testlets?: IQuadrantTestlet[],
  assembledPanels?: IAssembledPanel[];
  testletCounter?: number,
  testForms?: ITestDesignPayload[],
  partitions? : IAssessmentPartition[],
  estimatedTestTakers? : number,
  rubricDownloadLink? : string,
  assessmentName? : string,
  assessmentNameFr?: string,
  msgFinalSubmission? : string,
  msgResultsPage? : string,
  customResultPage? : string;
  customResultPageFr? : string;
  customResultPageTitle?: string;
  customResultPageTitleFr?: string;
  customNavbarResultIntroPageTitleEN?: string;
  customNavbarResultIntroPageTitleFR?: string;
  isCustomResultPageNote?: boolean;
  customResultPageNoteEN?: string;
  customResultPageNoteFR?: string;
  msgPathWarnOverride? : string,
  msgPathCnfmOverride? : string,
  msgPathCnfmCancelOverride? : string,
  msgPathCnfmProceedOverride? : string,
  isExposureComputed? : boolean,
  isResultsDetailDisabled? : boolean,
  isResultsPageEnabled?: boolean,
  isShowingSectionsInResultsPage? : boolean,
  resultsPagetype?: EResultsPageTypes,
  helpPageId? : number,
  referenceDocumentPages? : IRefDoc[],
  isTimerDisabled? : boolean,
  isSpentTimerDisabled?: boolean;
  recommendedTime?: string;
  isOrale?: boolean;
  extraTime?: string;
  isUnreadyExcluded? : boolean,
  tags? : {slug:string}[],
  similaritySlugs? : {slug:string}[],
  useQuestionLabel? : boolean,
  questionWordSlug? : string,
  useSectionCaptions? : boolean,
  testFormType? : TestFormConstructionMethod,
  testletStatCol? : Array<{
    isShown:boolean,
    key: string,
  }>,
  numOfPublishingIterations?: number,
  primaryDimensions:IAssessmentFrameworkDimensionDetail[],
  secondaryDimensions: IAssessmentFrameworkDimensionDetail[],
  standardParameters?: IAssessmentFrameworkDimensionDetail[],
  filters?: IAssessmentParameterViewFilter[]
  toolbarOptions?: IAssessmentToolbarOptions;
  reportOptions?: IAssessmentReportOptions;
  styleProfile?: EStyleProfile;
  nextButtonOpt?: ENextBtnOpt;
  showQHeader?: boolean;
  showDocumentsSplitScreen?: boolean;
  sidebarThumbnailEn?: string;
  sidebarThumbnailFr?: string;
  labelNavDdown?: string;
  isThumbnailFullWidth?: boolean;
  isAutoTestletMode?: boolean;
  accessibilitySettings?: {
    [key:string]: IAccessibilitySetting
  };
  pairableHumanScoringScales?: any;
  isAlternativeLinearTest?: boolean;
  isQuestionnaireAssessment?: boolean;
  isResourcePageOnly?: boolean
  assessmentLanguage?: string;
  assessmentTypeSlug?: string;
  assessmentType?: string;
  isRefDocumentsNestedView?: boolean;
  splitScreenSettings?: ISplitScreenSettings,
  useLangSpecificSectionProps?: boolean
  isCustomProgressBar?: boolean;
  customProgressBarConfig?: IProgressBarConfig;
  customProgressBarTemplate?: ProgressBarTemplate;
}

export type IProgressBarConfig = {
  [key in PROGRESS_BAR_STATE]: IProgressBarStageConfig
}

export enum ProgressBarTemplate {
  NONE ="lbl_none",
  PRIMARY ="lbl_primary",
  JUNIOR ="lbl_junior",
}

export interface IProgressBarStageConfig {
  background: {
    color: string
  },
  border: {
    color: string
    width?: number,
  },
  font: {
    color: string,
    weight: number
  }
}
export interface IAccessibilitySetting {
  caption: string,
  isConfigurable: boolean,
  defaultVal: boolean 
}

export interface ISplitScreenSettings {
  isSplitScreenlinksInNavbar: boolean;
  isHideReadSelLinksInQueHeader: boolean;
  isShowCutsomSplitScreenControls: boolean;
}

export interface IRoutingRule { 
  module: string, 
  minTheta:number,
  maxTheta:number,
  minPropC: number, // >=
  maxPropC: number, // <
  num_ft?: number
}

export interface IAssessmentPartition {
  id: number,
  description: string,
  buckets?: {label?:string, count?:number}[]
  preambleQuestionLabel?: string,
  preambleLabelList?: string[],
  postambleLabelList?: string[],
  mapMetaItemId?: number,
  infoCaption?: string,
  submissionText?: string,
  notepadText?: string,
  isShuffled?: boolean,
  isCalculatorEnabled?: boolean,
  isNotepadDisabled?: boolean,
  isNotepadEnabled?: boolean,
  isFormulaSheetEnabled?: boolean,
  areQuestionsSkippable?: boolean,
  isTimeLimit?: boolean,
  isConditional?: boolean,
  timeLimitMinutes?:number,
  conditionOnItem?:string,
  conditionOnOption?:string,
  disableScoring?: boolean;
  msgReqFill?: string;
  orderByParam?: string;
  disableFlagging?: boolean;
  disableLeftBar?: boolean;
  preambleThumbnail?: string,
  preambleThumbnailSelected?: string,
  preambleThumbnailText?: string,
  customSectionPopup? : boolean;
  customSectionPopupSlug?: string;
  sidebarThumbnailEn?: string;
  sidebarThumbnailFr?: string; // depricate this
  isConfigExpanded?: boolean; // only used for the config screen
  langLink?: IAssessmentPartition;
  isLangLink?: boolean // only true for `langLink` prop to avoid circular dependencies
}

export enum DimensionType {
  // MULTI_SELECT = 'multi-select',
  SELECT = 'select-sub',
  BINARY = 'binary',
  NUMERIC = 'numeric',
  LABEL = 'label',
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  COORD = 'coordinate',
  VOICE = 'voice',
} 
;

export enum DimensionCategory {
  META = 'meta',
  SCORING = 'scoring'
}
export interface IAssessmentFrameworkDimensionDetail {
  name: string, // ex: Big Ideas
  code: string, // ex: D1
  type: DimensionType,
  category?: DimensionCategory
  color?: string,
  isHidden?: boolean,
  key?: string,
  config: {
    special?: {
      [key:string]: boolean,
    },
    tags? : Array<{
      name: string, // ex: Number Sense
      code: string, // ex: NS
    }>,
  },
  storageLocation?: PARAMETER_LOCATIONS
}

// The following functions were brough over from ABED in the AUDIT migration
export const getFormPartitionObj = (partition: IAssessmentPartition, lang: string, useLangSpecificSectionProps: boolean): IAssessmentPartition => {
  
  // For language other than English, consider language-specific properties
  if (lang !== 'en' && useLangSpecificSectionProps) {
    
    // to avoid circular deps check if the current partition references to the outer most structure or inner structure for langLink
    if (partition.isLangLink) {
      return partition;
    }

    if (!partition.langLink && !partition.isLangLink) {
      partition.langLink = {
        id: partition.id,
        description: partition.description,
        isLangLink: true
      }
    }
    return partition.langLink;
  }

  return partition;
}

/**
 * Retrieve a property value from an Assessment Partition object, considering language-specific properties.
 *
 * @param partition - The Assessment Partition object from which to retrieve the property.
 * @param slug - The key (property name) to retrieve.
 * @param lang - The language for which to retrieve the property (e.g., 'en' for English).
 * @param useLangSpecificSectionProps - when turned on in the assessment framework it returns language specific partiton props (Used for backward compatibility)
 * @returns The value of the specified property, taking language-specific properties into account.
 */
export const getPartitionPropValue = <T extends IAssessmentPartition, K extends keyof T>(
  partition: T,
  slug: K,
  lang: string,
  useLangSpecificSectionProps: boolean
): T[K] => {

  return (getFormPartitionObj(partition, lang, useLangSpecificSectionProps) as T)[slug];
}

export enum EResultsPageTypes {
  EMBEDDED = 'embedded',
  EXTERNAL = 'external'
}

