<!-- All Audits summary -->
<audit-header
[auditCtrl]="auditCtrl"
[isExpanded]="isExpanded"
[refresh]="refreshAudits.bind(this)"
[auditStatus]="auditCtrl.itemAuditStatus"
[showDate]="false"
[readOnly]="isReadOnly()"
caption="ie_audit_summary"
>
</audit-header>
<!-- Individual audit listing -->
<div class="item-audit-check-container" *ngIf="isExpanded.value">
    <ng-container *ngFor="let audit of audits; let idx = index; ">
        <div
            class="item-audit-row"
            *ngIf="auditCtrl.getQuestionMemResults(audit.slug)?.length && !hiddenAudits[audit.slug]"
            (click)="toggleAuditResults(audit.slug)"
        >    
            <div class="item-audit-header">
                <i *ngIf="!expandedAudits[audit.slug]" class="fa fa-chevron-right" aria-hidden="true"></i>
                <i *ngIf="expandedAudits[audit.slug]" class="fa fa-chevron-down" aria-hidden="true"></i>
                &nbsp;
                <div class="audit-name"> 
                    {{audit.caption}} 
                </div>      
                <div [ngSwitch]="auditCtrl.runningItemLevelAuditId === itemId && auditCtrl.auditsRunning[audit.slug]">
                    <i *ngSwitchCase="true" class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                    <ng-container *ngSwitchDefault>
                        <span *ngIf="audit.isAsync" style="font-weight: lighter">
                            {{auditCtrl.getAuditLog(audit.slug)?.audited_on?.toLocaleString()?.slice(0, -5) || ''}}
                            <i class="fa fa-question" style="margin-left: 3px;" style="color: dimgray; font-size: smaller;" aria-hidden="true" [tooltip]="'ASYNC Audit'"></i>
                        </span>
                    </ng-container>
                </div>
            </div>
            <div class="audit-summary" *ngIf="auditCtrl.getQuestionMemResults(audit.slug)?.__metaData && !(auditCtrl.runningItemLevelAuditId === itemId && auditCtrl.auditsRunning[audit.slug])">
                <div *ngIf="auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.informative && isFilterActive(FilterOptions.INFORMATIVE)">
                    <i class="fa fa-info-circle" style="color: blue"></i>
                    {{auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.informative}}
                </div>
                <div *ngIf="auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.pass && isFilterActive(FilterOptions.PASSED)">
                    <i class="fa fa-check-circle" style="color: green;"></i>
                    {{auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.pass}}
                </div>
                <div *ngIf="auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.fail && isFilterActive(FilterOptions.ISSUES)">
                    <i class="fa fa-exclamation-circle" style="color: red;"></i>
                    {{auditCtrl.getQuestionMemResults(audit.slug)?.__metaData.fail}}
                </div>
            </div>

        </div>
        <!-- Audit check listing -->
        <div class="item-audit-status" *ngIf="expandedAudits[audit.slug] && !hiddenAudits[audit.slug]">
            <ng-container *ngFor="let result of auditCtrl.getQuestionMemResults(audit.slug)">
                <div class="item-audit-check-status" *ngIf="!hiddenAudits[result.id]">
                    <span>
                        <ng-container *ngIf="result.items?.length || result.num_issues">
                            <i *ngIf="!result.informativeOnly" class="fa fa-exclamation-circle" style="color: red;"></i>
                            <i *ngIf="result.informativeOnly" class="fa fa-info-circle" style="color: blue"></i>
                        </ng-container>
                        <ng-container *ngIf="!result.items?.length && !result.num_issues">
                            <i class="fa fa-check-circle" style="color: green;"></i>
                        </ng-container>
                    </span>
                    &nbsp;
                    <span>
                    {{result.caption}}
                    </span>
                </div>
            </ng-container>
        </div>
    </ng-container>
</div>