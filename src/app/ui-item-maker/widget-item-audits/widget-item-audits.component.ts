import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AuditResultType, IAuditResultSummary, ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { AuditConfigs, AuditMetaRecord, AuditTarget, IAuditConfig } from '../widget-audits/data/audits';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { WarningTagDismissType } from 'src/app/ui-partial/warning-tag/warning-tag.component';
import { ItemMakerService } from '../item-maker.service';
import { aggregateAuditMetaRecords } from '../widget-audits/util/overall-status';
import { AuditFilterOptions, AuditFilterService } from '../services/audit-filter.service';
import { EditingDisabledService } from '../editing-disabled.service';


@Component({
  selector: 'widget-item-audits',
  templateUrl: './widget-item-audits.component.html',
  styleUrls: ['./widget-item-audits.component.scss'],
  providers: [AuditFilterService]
})
export class WidgetItemAuditsComponent implements OnInit, OnDestroy {

  @Input() auditCtrl!: ItemBankAuditor;
  @Input() assetLibraryCtrl:AssetLibraryCtrl;
  @Input() itemId:number;
  @Input() isExpanded = new FormControl(false);

  AuditResultType = AuditResultType;
  WarningTagDismissType = WarningTagDismissType;
  FilterOptions = AuditFilterOptions;

  audits:IAuditConfig[];
  expandedAudits:{[key:string]:boolean} = {}; // keep track of which audits have their checks expanded
  private subscription: Subscription = new Subscription();
  
  constructor(
    private itemMaker: ItemMakerService,
    public auditFilterService: AuditFilterService,
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
    this.auditCtrl.resetAudits();
    this.ensureAuditState().then(this.initAuditFilterService.bind(this));
    this.subscription.add(this.auditCtrl.auditRanSub.subscribe((auditSlug)=>{ // on completion of audit
      this.updateFilters(auditSlug)
    }));
    this.subscription.add(this.itemMaker.assesmentTypePubSub.subscribe(async () =>{
      this.audits = (await this.auditCtrl.getItemLevelAudits()).sort((a, b) => a.slug.localeCompare(b.slug));
      this.initAuditFilterService()
    }));
  }

  get filterControl() {
    return this.auditFilterService.filterControl
  }

  get hiddenAudits() {
    return this.auditFilterService.hiddenAudits
  }

  get ItemMem(): Partial<AuditMetaRecord> {
    return this.auditCtrl.itemAuditQuestionMem;
  }

  /**
   * Aggregates the AuditMetaRecord records into a single IAuditResultSummary
   */
  get AggregatedItemMeta(): IAuditResultSummary {
    return aggregateAuditMetaRecords(this.ItemMem)
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  initAuditFilterService(){
    this.auditFilterService.initAuditFilterService(this.audits, this.auditCtrl, true);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this.auditCtrl.destroy(); // Clear audits as this context only needs to exist at the item level
  }

  updateFilters(auditSlug?:string | boolean) {
    this.auditFilterService.updateFilters(auditSlug)
  }

  async ensureAuditState(){
    this.auditCtrl.initAudits();
    this.auditCtrl.resetAudits();
    this.audits = (await this.auditCtrl.getItemLevelAudits()).sort((a, b) => a.slug.localeCompare(b.slug));
    this.auditCtrl.getAuditLogs(this.itemId);
  }

  refreshAudits($event:any){
    $event.stopPropagation();
    this.auditCtrl.runAllItemLevelAudits().then(() => {
      // Ensure filters are applied after audits run
      this.updateFilters();
    });
  }
  /**
   * Individual audit toggle (view their individual checks)
   * @param slug 
   */
  toggleAuditResults(slug:string){
    this.expandedAudits[slug] = !this.expandedAudits[slug]
  }

  isFilterActive(option:AuditFilterOptions): boolean{
    return this.auditFilterService.filterControlbyOption(option) 
  }
  
}
