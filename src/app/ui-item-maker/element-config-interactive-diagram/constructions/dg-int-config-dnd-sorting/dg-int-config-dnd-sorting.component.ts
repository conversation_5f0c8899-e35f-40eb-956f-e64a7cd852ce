import { Component, ElementRef, Input, OnInit, Query<PERSON>ist, ViewChild, ViewChildren } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { IDgIntElementDnDSorting, resolveDnDSortingOptions, FLOW_LAYOUT, SORTING_PROPORTIONAL_SCORING_MODE, FLOW_SYMBOL, IDgIntElementDnDSortingOption } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-sorting';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { createDefaultElement, generateDefaultElement, generateDefaultElementText } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { diagramLabelPositionOptions, homeLabelPositionOptions, unusedId } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-config-dnd-sorting',
  templateUrl: './dg-int-config-dnd-sorting.component.html',
  styleUrls: ['./dg-int-config-dnd-sorting.component.scss']
})
export class ElementConfigDgIntDndSortingComponent implements OnInit {
  @Input() element: IDgIntElementDnDSorting;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChild('startLabel') startLabel: ElementRef;
  @ViewChild('endLabel') endLabel: ElementRef;
  @ViewChild('label') label: ElementRef;
  @ViewChildren('options') options: QueryList<ElementRef>;
  @ViewChildren('extraOptions') extraOptions: QueryList<ElementRef>;

  twiddleConfig = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  optionsImageTwiddleConfig : TwiddleState[];
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  homeIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  
  isShowAnswerForm = new FormControl(false);
  flowSymbolForm = new FormControl(FLOW_SYMBOL.ARROW)
  flowSymbolOptions = [
    {id: FLOW_SYMBOL.NONE, caption:'None'},
    {id: FLOW_SYMBOL.ARROW, caption:'Arrow'},
  ]
  flowLayoutForm = new FormControl(FLOW_LAYOUT.LEFT_TO_RIGHT)
  flowLayoutOptions = [
    {id: FLOW_LAYOUT.LEFT_TO_RIGHT, caption:'Left to Right'},
    {id: FLOW_LAYOUT.RIGHT_TO_LEFT, caption:'Right to Left'},
    {id: FLOW_LAYOUT.TOP_TO_BOTTOM, caption:'Top to Bottom'},
    {id: FLOW_LAYOUT.BOTTOM_TO_TOP, caption:'Bottom to Top'},
  ]
  proportionalScoringModeForm = new FormControl(SORTING_PROPORTIONAL_SCORING_MODE.LONGEST_COMMON_SUBSEQUENCE)
  proportionalScoringModeOptions = [
    {id: SORTING_PROPORTIONAL_SCORING_MODE.LONGEST_COMMON_SUBSEQUENCE, caption:'Longest Common Subsequence'},
    {id: SORTING_PROPORTIONAL_SCORING_MODE.POSITIONAL, caption: 'Positional'}
  ]
  homeLabelPositionOptions = homeLabelPositionOptions;
  diagramLabelPositionOptions = diagramLabelPositionOptions;
  startLabelForm = new FormControl("");
  endLabelForm = new FormControl("");
  labelForm = new FormControl("");
  form: FormGroup;
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ) { }
  
  ngOnInit(): void {
    this.validateConfig();

    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    bindFormControls(this.element, [
      {f: this.flowSymbolForm, p:'flowSymbol'}, 
    ]);
    bindFormControls(this.element.targetLabels.start, [
      {f: this.startLabelForm, p:'caption'}, 
    ]);
    bindFormControls(this.element.targetLabels.end, [
      {f: this.endLabelForm, p:'caption'}, 
    ]);
    bindFormControls(this.element.label, [
      {f: this.labelForm, p: 'text'}
    ])
    bindFormControls(this.element.config, [
      {f: this.flowLayoutForm, p:'flowLayout'}, 
      {f: this.proportionalScoringModeForm, p:'proportionalScoringMode'}, 
    ]);

    this.form = new FormGroup({
      options: new FormArray([]),
      extraOptions: new FormArray([]),
    });

    this.addFormControls();
    this.bindOptionsAndExtraOptions()
  }

  bindOptionsAndExtraOptions() {
    // for each option bind form control to element label
    this.element.options.forEach((option, idx) => {
      bindFormControls(option, [
        {f: this.optionsFormArray.controls[idx] as FormControl, p: 'label'}, 
      ])
    });
    
    // for each extra option bind form control to element label
    this.element.extraOptions.forEach((option, idx) => {
      bindFormControls(option, [
        {f: this.extraOptionsFormArray.controls[idx] as FormControl, p: 'label'}, 
      ])
    });
  }

  toggleTargetLabelMath(option: IDgIntElementDnDSortingOption) {
    option.isTargetLabelMath = !option.isTargetLabelMath;
    option.targetLabel.elementType = option.isTargetLabelMath ? ElementType.MATH : ElementType.TEXT;
    this.rerender();
  }

  get optionsFormArray() {
    return (this.form.get('options') as FormArray);
  }

  get extraOptionsFormArray() {
    return (this.form.get('extraOptions') as FormArray);
  }

  addFormControls() {
    // Loop through options and create FormControls for each
    this.element.options.forEach(option => {
      this.optionsFormArray.push(new FormControl(option.label));
    });

    // Loop through extraOptions and create FormControls for each
    this.element.extraOptions.forEach(option => {
      this.extraOptionsFormArray.push(new FormControl(option.label));
    });
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to images mode?");
      if (!confirm) return;
      this.element.config.isUseImage = true;
    } else {
      const confirm = window.confirm("Are you sure you want to change to text only mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let option of this.element.options) delete option.image;
    }
    this.validateConfig();
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  isUseImage() : boolean {
    return this.element.config.isUseImage ?? false;
  }
  elementEditSetOptionMath(option: IDgIntElementDnDSortingOption, value: boolean) {
    option.isMath = value;
    option.elementType = value ? ElementType.MATH : ElementType.TEXT;
    this.rerender();
  }

  toggleLabelMath() {
    this.element.label.isMath = !this.element.label.isMath;
    this.element.label.elementType = this.element.label.isMath ? ElementType.MATH : ElementType.TEXT;
    this.rerender();
  }
  
  validateConfig() {
    this.resolveData();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  resolveData() {
    resolveDnDSortingOptions(this.element);
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      startLabel: this.startLabel,
      endLabel: this.endLabel,
      label: this.label,
    }

    this.element.options.forEach((option, idx) => {
      elements[`option_${idx}`] = this.options.toArray()[idx];
    })

    this.element.extraOptions.forEach((option, idx) => {
      elements[`extraOption_${idx}`] = this.extraOptions.toArray()[idx];
    })

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
  
  dropOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.ordering, event.previousIndex, event.currentIndex);
    this.rerender();
  }
  
  dropOptionOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.options, event.previousIndex, event.currentIndex);
    moveItemInArray(this.optionsFormArray.controls, event.previousIndex, event.currentIndex);
    this.rerender();
  }
  
  updateSortedOptionIdList() {
     this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
   }
   updateIdToLabelMap() {
     this.targetIdList = [];
     this.optionIdList = [];
     this.idToLabelMap.clear();
     this.idIsMathSet.clear();
     for (let option of this.element.options){
       this.idToLabelMap.set(option.id, option.label);
       this.idToLabelMap.set(option.targetId, option.label);
       this.idToUrlMap.set(option.id, option.image?.url);
       if (option.isMath) this.idIsMathSet.add(option.id);
       this.targetIdList.push(option.targetId);
       this.optionIdList.push(option.id);
     }
     this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
   }
   getOptionLabelFromId(id: string) : string {
     return this.idToLabelMap.get(id);
   }
   getOptionImageURLFromId(id: string) : string {
     return this.idToUrlMap.get(id);
   }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  elementEditDeleteOption(optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    this.element.options.splice(optionIndex, 1);
    this.optionsFormArray.removeAt(optionIndex);
    this.validateConfig();
  }
  
  elementEditAddOption(){
    this.element.options.push({
      label: "",
      isPreFilled: false,
      ...generateDefaultElement(ElementType.TEXT),
      targetLabel: generateDefaultElementText(ElementType.TEXT)
    });

    const numOptions = this.element.options.length;
    const newOption = new FormControl("");
    this.optionsFormArray.push(newOption);
    bindFormControls(this.element.options[numOptions - 1], [
      {f: newOption, p: 'label'}
    ])

    this.validateConfig();
  }
  
  elementEditDeleteExtraOption(optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    this.element.extraOptions.splice(optionIndex, 1);
    this.extraOptionsFormArray.removeAt(optionIndex);
    this.validateConfig();
  }
  
  elementEditAddExtraOption(){
    this.element.extraOptions.push({
      label: "",
      isExtra: true,
      ...generateDefaultElement(ElementType.TEXT),
      targetLabel: generateDefaultElementText(ElementType.TEXT)
    });

    const numOptions = this.element.extraOptions.length;
    const newOption = new FormControl("");
    this.extraOptionsFormArray.push(newOption);
    bindFormControls(this.element.extraOptions[numOptions - 1], [
      {f: newOption, p: 'label'}
    ])

    this.validateConfig();
  }
  
  getImageElement(optionIndex : number) : IContentElementImage {
    const option = this.element.options[optionIndex];
    if (option?.image == undefined) {
      option.image = createDefaultElement(ElementType.IMAGE)
      option.image.scale = 25;
    }
    return option.image;
  }
  getImageExtraElement(optionIndex : number) : IContentElementImage {
    const option = this.element.extraOptions[optionIndex];
    if (option?.image == undefined) {
      option.image = createDefaultElement(ElementType.IMAGE)
      option.image.scale = 25;
    }
    return option.image;
  }
  
  elementEditRemoveImage(optionIndex : number){
    const confirm = window.confirm("Are you sure you want to clear this image?");
    if (!confirm) return;
    const option = this.element.options[optionIndex];
    if (option) delete option.image;
    this.rerender();
  }
  elementEditRemoveExtraImage(optionIndex : number){
    const confirm = window.confirm("Are you sure you want to clear this image?");
    if (!confirm) return;
    const option = this.element.extraOptions[optionIndex];
    if (option) delete option.image;
    this.rerender();
  }
  
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return true;
    return !this.optionIdList.includes(optionId);
  }
  
  isShowConfigPreview(): boolean {
    return true;
  }
  
}
