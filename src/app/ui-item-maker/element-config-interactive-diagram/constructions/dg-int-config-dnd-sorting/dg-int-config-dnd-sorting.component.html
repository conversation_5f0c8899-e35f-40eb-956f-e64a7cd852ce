<fieldset [disabled]="isReadOnly()" (change)="rerender()">
  <div class="layout-config-container">
    <span>Start Label:</span>
    <textarea
      textInputTransform
      #startLabel
      [formControl]="startLabelForm"
      style="min-width: 10em; max-width: 20em;"
      class="textarea is-small target-option-textarea"
      cdkTextareaAutosize
      (input)="rerender()"
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(startLabelForm, 'startLabel')"
    ></textarea>
    
    <span>End Label:</span>
    <textarea
      textInputTransform
      #endLabel
      [formControl]="endLabelForm"
      style="min-width: 10em; max-width: 20em;"
      class="textarea is-small target-option-textarea"
      cdkTextareaAutosize
      (input)="rerender()"
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(endLabelForm, 'endLabel')"
    ></textarea>
    
    <span>Layout:</span>
    <div class="select">
      <select [formControl]="flowLayoutForm" style="width:100%">
        <option *ngFor="let option of flowLayoutOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </div>
    
    <span>Symbol:</span>
    <div class="select">
      <select [formControl]="flowSymbolForm" style="width:100%">
        <option *ngFor="let option of flowSymbolOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </div>
    
  </div>
    
</fieldset>

<hr/>

<fieldset (change)="rerender()">
  <label class="label">Label</label>
  <div style="margin-top: 0.5em">
    <label>Label: </label> 
    <textarea
      *ngIf="!element.label.isMath"
      textInputTransform
      #label
      [formControl]="labelForm"
      (input)="rerender()"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em; display: inline-block;"
      class="textarea is-small target-option"
      cdkTextareaAutosize
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(labelForm, 'label')"
    ></textarea>
    <div *ngIf="element.label.isMath" class="capture-math-container" 
      style="width: 11em; display: inline-block; vertical-align: top;"
    >
      <capture-math 
        [obj]="element.label" 
        (onChange)="rerender()"
        [class.is-disabled]="isReadOnly()"
        [class.is-disabled]="isReadOnly()"
        prop="text" [isManualKeyboard]="true">
      </capture-math>
    </div>
    <div *ngIf="!element.label.isMath"
      (click)="toggleLabelMath()" 
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-square-root-alt" aria-hidden="true"></i>
    </div>
    <div *ngIf="element.label.isMath"
      (click)="toggleLabelMath()"
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-font" aria-hidden="true"></i>
    </div>
    <ng-container *ngIf="element.label.text">
      <br>Label Position:
      <select [(ngModel)]="element.label.position">
        <option *ngFor="let option of diagramLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </ng-container>
  </div>
</fieldset>

<hr />
<!-- <fieldset [disabled]="isReadOnly()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm"/>
    Show Answer
  </label>
</fieldset> -->

  
<fieldset [disabled]="isReadOnly()">
  <label class="label">Options</label>
  
  <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
    <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
    <span>Image</span>
  </button>
  <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
    <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
    <span>Text</span>
  </button>
  
  <div>
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
      Show Advanced Options
    </label>
  </div>
  
  <div class="list" cdkDropList (cdkDropListDropped)="dropOptionOrdering($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let option of element.options; let i = index; trackBy: trackByFn" cdkDrag>
      <div class="target-form-container">
        <div class="target-index">{{ i + 1 }}</div>
        <div class="target-form-inner-container">
          <div class="flex-container">
            <ng-container *ngIf="!option.isMath">
              <textarea
                textInputTransform
                #options
                (mousedown)="$event.stopPropagation()"
                [formControl]="this.optionsFormArray.controls[i]"
                (input)="validateConfig()"
                style="min-width: 10em;"
                class="textarea is-small target-option-textarea"
                cdkTextareaAutosize
                [cdkTextareaAutosize]="true"
                [cdkAutosizeMinRows]="2"
                (focus)="setTextFocus(this.optionsFormArray.controls[i], 'option_' + i)"
              ></textarea>
              <div
                (click)="elementEditSetOptionMath(option,true)" class="target-option-button"
                [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-square-root-alt" aria-hidden="true"></i>
              </div>
            </ng-container>
            <ng-container *ngIf="option.isMath">
              <div class="capture-math-container">
                <capture-math 
                  #options
                  [obj]="option" 
                  (onChange)="validateConfig()"
                  [class.is-disabled]="isReadOnly()"
                  prop="label" [isManualKeyboard]="true">
                </capture-math>
              </div>
              <div
                (click)="elementEditSetOptionMath(option,false)" class="target-option-button"
                [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-font" aria-hidden="true"></i>
              </div>
            </ng-container>
            
            <div (click)="elementEditDeleteOption(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-trash" aria-hidden="true"></i>
            </div>
          </div>
          <label class="checkbox">
            <input type="checkbox" [(ngModel)]="option.isPreFilled" (change)="validateConfig()"/>
            Pre Filled
          </label>
          
          <div *ngIf="isUseImage()">
            <div *ngIf="!element.isShowAdvancedOptions && getImageElement(i).url">
              <div class="simple-image-container">
                <img [src]="getImageElement(i).url" style="max-width: 10em; max-height: 10em;">
              </div >
              <div>
                scale:
                <input type="number" [(ngModel)]="getImageElement(i).scale" class="small-input">
              </div>
              <button (click)="elementEditRemoveImage(i)">Clear Image</button>
            </div>
            <div *ngIf="element.isShowAdvancedOptions || !getImageElement(i).url">
              <capture-image [element]="getImageElement(i)" (change)="rerender()"></capture-image>
              <asset-library-link [element]="getImageElement(i)"></asset-library-link>     
            </div>
          </div>
          
          <div *ngIf="element.isShowAdvancedOptions || option.targetLabel">
            <hr style="margin: 1em; background-color: #ccc; height: 1px;">
            <label>Target Label:</label>
            <div class="flex-container">
              <ng-container *ngIf="!option.isTargetLabelMath">
                <textarea
                  textInputTransform
                  (mousedown)="$event.stopPropagation()"
                  [(ngModel)]="option.targetLabel.caption"
                  (input)="rerender()"
                  style="min-width: 10em;"
                  class="textarea is-small target-option-textarea"
                  cdkTextareaAutosize
                  [cdkTextareaAutosize]="true"
                  [cdkAutosizeMinRows]="2"
                ></textarea>
                <div
                  (click)="toggleTargetLabelMath(option)" class="target-option-button"
                  [class.no-pointer-events]="isReadOnly()">
                  <i class="fas fa-square-root-alt" aria-hidden="true"></i>
                </div>
              </ng-container>
              <ng-container *ngIf="option.isTargetLabelMath">
                <div class="capture-math-container">
                  <capture-math 
                    [obj]="option.targetLabel" 
                    (onChange)="validateConfig()"
                    [class.is-disabled]="isReadOnly()"
                    prop="caption" [isManualKeyboard]="true">
                  </capture-math>
                </div>
                <div
                  (click)="toggleTargetLabelMath(option)" class="target-option-button"
                  [class.no-pointer-events]="isReadOnly()">
                  <i class="fas fa-font" aria-hidden="true"></i>
                </div>
              </ng-container>
            </div>
          </div>
          
          
        </div>
        
        
      </div>
    </div>
  </div> 
</fieldset>

<button 
  (click)="elementEditAddOption()" 
  class="button is-small has-icon"
>
  <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
  <span>Add Option</span>
</button>

<fieldset [disabled]="isReadOnly()" style="margin-top: 2em">
  <label class="label">Extra Options</label>

  <div class="list-item" style="cursor: default;" *ngFor="let option of element.extraOptions; let i = index">
    <div class="target-form-container">
      <div class="target-form-inner-container">
        <div class="flex-container">
          <ng-container *ngIf="!option.isMath">
            <textarea
              textInputTransform
              #extraOptions
              (mousedown)="$event.stopPropagation()"
              [formControl]="this.extraOptionsFormArray.controls[i]"
              (input)="validateConfig()"
              style="min-width: 10em;"
              class="textarea is-small target-option-textarea"
              cdkTextareaAutosize
              [cdkTextareaAutosize]="true"
              [cdkAutosizeMinRows]="2"
              (focus)="setTextFocus(this.extraOptionsFormArray.controls[i], 'extraOption_' + i)"
            ></textarea>
            <div
              (click)="elementEditSetOptionMath(option,true)" class="target-option-button"
              [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-square-root-alt" aria-hidden="true"></i>
            </div>
          </ng-container>
          <ng-container *ngIf="option.isMath">
            <div class="capture-math-container">
              <capture-math 
                #extraOptions
                [obj]="option" 
                (onChange)="validateConfig()"
                [class.is-disabled]="isReadOnly()"
                prop="label" [isManualKeyboard]="true">
              </capture-math>
            </div>
            <div
              (click)="elementEditSetOptionMath(option,false)" class="target-option-button"
              [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-font" aria-hidden="true"></i>
            </div>
          </ng-container>
          
          <div (click)="elementEditDeleteExtraOption(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
            <i class="fas fa-trash" aria-hidden="true"></i>
          </div>
        </div>
        
        <div *ngIf="isUseImage()">
          <div *ngIf="!element.isShowAdvancedOptions && getImageExtraElement(i).url">
            <div class="simple-image-container">
              <img [src]="getImageExtraElement(i).url" style="max-width: 10em; max-height: 10em;">
            </div >
            <div>
              scale:
              <input type="number" [(ngModel)]="getImageExtraElement(i).scale" class="small-input">
            </div>
            <button (click)="elementEditRemoveExtraImage(i)">Clear Image</button>
          </div>
          <div *ngIf="element.isShowAdvancedOptions || !getImageExtraElement(i).url">
            <capture-image [element]="getImageExtraElement(i)" change="rerender()"></capture-image>
            <asset-library-link [element]="getImageExtraElement(i)"></asset-library-link>     
          </div>
        </div>
        
      </div>
      
    </div>
  </div>
  
  <button 
    (click)="elementEditAddExtraOption()" 
    class="button is-small has-icon"
  >
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span>Add Option</span>
  </button>
</fieldset>


<hr/>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="optionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br>Target Background Colour:
      <input type="color" [(ngModel)]="element.config.targetBgColor">
      <br>Draggable Background Colour:
      <input type="color" [(ngModel)]="element.config.draggableBgColor">
      <br>
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <div *ngIf="parentElement.enableProportionalScoring">
        Proportional Scoring Mode:
        <select [formControl]="proportionalScoringModeForm">
          <option *ngFor="let option of proportionalScoringModeOptions" [value]="option.id">
            <tra [slug]="option.caption"></tra>
          </option>
        </select>
      </div>
      <br>
      <br> Maximum Total Width:
      <input type="number" [(ngModel)]="element.config.totalMaxWidth" class="small-input">
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
      <br>
      <br>
      <label>Diagram Padding</label>
      <br> top:
      <input type="number" [(ngModel)]="element.diagramPadding.top" class="small-input">
      <br> bottom:
      <input type="number" [(ngModel)]="element.diagramPadding.bottom" class="small-input">
      <br> left:
      <input type="number" [(ngModel)]="element.diagramPadding.left" class="small-input">
      <br> right:
      <input type="number" [(ngModel)]="element.diagramPadding.right" class="small-input">
    </div>
  </fieldset>
</div>
