import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { IDgIntElementMcqHotspot, IDgIntDataUpdateParamMcqHotspot, IDgIntDataUpdateCallbackObjectMcqHotspot, generateIdOnHotspots, DgIntPolygonEditType, presetHotspotShape, presetHotspotShapePoints, isPreventEditVertex, DgIntMcqMode, getHotspotPosition, moveHotspotToPosition } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/mcq-hotspot';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { createDefaultElement, generateDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { TwiddleState } from 'src/app/ui-partial/twiddle/twiddle.component';
import { cloneDeep } from 'lodash';
import { numberToAlphabet } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { homeLabelPositionOptions, HOME_LABEL_POSITION } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { AbstractControl, FormControl } from '@angular/forms';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { bindFormControls } from 'src/app/ui-item-maker/services/data-bind';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';


@Component({
  selector: 'dg-int-config-mcq-hotspot',
  templateUrl: './dg-int-config-mcq-hotspot.component.html',
  styleUrls: ['./dg-int-config-mcq-hotspot.component.scss']
})
export class ElementConfigDgIntMcqHotspotComponent implements OnInit, AfterViewInit {
  @Input() element: IDgIntElementMcqHotspot;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChild('label') label: ElementRef;
  
  numberToAlphabet = numberToAlphabet;
  twiddleHotspots = new TwiddleState(true);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  editIndex: number|null = null;
  editType: DgIntPolygonEditType|null = null;
  mcqMode = DgIntMcqMode;
  labelForm = new FormControl('');
  
  homeLabelPositionOptions = homeLabelPositionOptions;
  isLockAspectRatio = true;
  isAllowAddNewVertex = true;
  
  polygonPresetShapeOptions = [
    {id: presetHotspotShape.SQUARE, caption: 'Square'},
    {id: presetHotspotShape.CIRCLE, caption: 'Circle'},
  ]
  mcqModeOptions = [
    {id: DgIntMcqMode.MCQ, caption: 'MCQ (Single Choice)'},
    {id: DgIntMcqMode.MSEL, caption: 'MSEL (Multiple Selection)'},
  ]
  
  polygonEditType = DgIntPolygonEditType;
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ) { }

  ngOnInit(): void {
    this.reindexHotspot();
    this.ensureValidConfig();

    bindFormControls(this.element.label, [
      {f: this.labelForm, p: 'text'}, 
    ]);
  }
  ngAfterViewInit(): void {
    this.rerender();
  }
  
  reindexHotspot(): void {
    generateIdOnHotspots(this.element.hotspots);
  }
  ensureValidConfig() {
    if (this.element.label == undefined) this.element.label = {text: '', position: HOME_LABEL_POSITION.TOP, ...generateDefaultElement(ElementType.TEXT)};
    if (this.element.styleConfig == undefined){
      this.element.styleConfig = {
        normal: {
          off: { color: '#c7c7c7', opacity: 0.5 },
          on: { color: '#aec7e8', opacity: 0.5 },
        },
        hover: {
          off: { color: '#c7c7c7', opacity: 0.7 },
          on: { color: '#aec7e8', opacity: 0.7 },
        },
        isSeparateHoverColor: false,
      }
    }
    for (let hotspot of this.element.hotspots){
      hotspot.position = getHotspotPosition(hotspot, true);
    }
  }
  
  getBackgroundImageElement(): IContentElementImage {
    if (this.element.backgroundImage == undefined)
      this.element.backgroundImage = {};
    if (this.element.backgroundImage.image == undefined) {
      this.element.backgroundImage.image = createDefaultElement(ElementType.IMAGE);
    }
    return this.element.backgroundImage.image as IContentElementImage;
  }
  
  elementEditAddHotspot() {
    this.element.hotspots.push({
      points: [
        { x:-1, y:-1},
        { x: 1, y:-1},
        { x: 1, y: 1},
        { x:-1, y: 1},
      ],
      isCorrect: false
    });
    this.reindexHotspot();
    this.rerender();
  }
  elementEditDeleteHotspot(index: number) {
    const confirm = window.confirm('Are you sure you want to delete this hotspot?');
    if (!confirm) return;
    this.endEditHotspot();
    this.element.hotspots.splice(index, 1);
    this.reindexHotspot();
    this.rerender();
  }
  elementEditDuplicateHotspot(index: number) {
    const hotspot = this.element.hotspots[index];
    this.element.hotspots.splice(index, 0, cloneDeep(hotspot));
    this.reindexHotspot();
    this.rerender();
  }
  elementEditLoadPresetShape(shape: presetHotspotShape, hotspotIndex: number) {
    const confirm = window.confirm('Are you sure you want to change the hotspot shape?');
    if (!confirm) return;
    const hotspot = this.element.hotspots[hotspotIndex];
    const points = presetHotspotShapePoints[shape];
    const _isPreventEditVertex = isPreventEditVertex[shape];
    hotspot.points = points;
    hotspot._isPreventEditVertex = _isPreventEditVertex ?? false;
    this.updateEditHotspot(hotspotIndex);
    this.rerender();
  }
  
  toggleOptionCorrect(index: number) {
    const hotspot = this.element.hotspots[index];
    hotspot.isCorrect = !hotspot.isCorrect;
    this.rerender();
  }
  isOptionCorrect(index: number) {
    return this.element.hotspots[index].isCorrect;
  }
  
  beginEditHotspotBBox(hotspotIndex: number) {
    this.beginEditHotspot(hotspotIndex, DgIntPolygonEditType.BBOX);
  }
  beginEditHotspotVertex(hotspotIndex: number) {
    this.beginEditHotspot(hotspotIndex, DgIntPolygonEditType.VERTEX);
  }
  updateEditHotspot(hotspotIndex: number) {
    const type = this.editType;
    this.beginEditHotspot(hotspotIndex, type);
  }
  beginEditHotspot(hotspotIndex: number, editType: DgIntPolygonEditType) {
    if (this.editIndex != null){
      this.endEditHotspot();
    }
    
    this.sharedObjectMap.update(this.element, <IDgIntSharedObject>{
      dataChangeParams: <IDgIntDataUpdateParamMcqHotspot>{
        hotspotIndex, 
        editType,
        isAllowAddNewVertex: this.isAllowAddNewVertex,
        isLockAspectRatio: this.isLockAspectRatio,
      },
      dataChangeCallback: (param: IDgIntDataUpdateCallbackObjectMcqHotspot) => {
        const hotspot = this.element.hotspots[param.hotspotIndex];
        hotspot.points = param.hotspotPoints;
        hotspot.position = getHotspotPosition(hotspot, true);
        // this.element.hotspots[param.hotspotIndex].points = param.hotspotPoints;
      },
    });
    this.editIndex = hotspotIndex;
    this.editType = editType;
    this.rerender();
  }
  endEditHotspot(){
    this.sharedObjectMap.update(this.element, <IDgIntSharedObject>{
      dataChangeParams: undefined,
      dataChangeCallback: undefined
    });
    this.editIndex = null;
    this.resetHoveredHotspot();
  }
  isEditIndex(index: number): boolean {
    return index == this.editIndex;
  }
  moveToPosition(index: number, position: {x: number, y: number}) {
    const hotspot = this.element.hotspots[index];
    if (isNaN(position.x) || position.x == undefined) return;
    if (isNaN(position.y) || position.y == undefined) return;
    moveHotspotToPosition(hotspot, position);
    this.rerender();
  }
  moveToPositionX(index: number, x: string) {
    const hotspot = this.element.hotspots[index];
    this.moveToPosition(index, {
      x: parseFloat(x),
      y: hotspot.position.y
    });
  }
  moveToPositionY(index: number, y: string) {
    const hotspot = this.element.hotspots[index];
    this.moveToPosition(index, {
      x: hotspot.position.x,
      y: parseFloat(y)
    });
  }
  
  dropHotspotOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.hotspots, event.previousIndex, event.currentIndex);
    this.rerender();
  }
  
  setHoveredHotspot(hotspotIndex: number) {
    this.element._hoveredIndex = hotspotIndex;
    this.rerender();
  }
  resetHoveredHotspot() {
    delete this.element._hoveredIndex;
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      label: this.label
    }

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
}
