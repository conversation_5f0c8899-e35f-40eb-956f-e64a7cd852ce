import { Component, Input, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { 
  IDgIntElementDnDInline,  
  resolveDnDInlineTargets, groupDnDTarget, DND_INLINE_CONTENT_TYPE, contentToContentArray
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-inline';
import { 
    HOME_CONFIG_LAYOUT,
    HOME_LABEL_POSITION,
    IDgIntElementDnDAnswerSet,
  IDgIntElementDnDTarget, LAYOUT_HOME_POSITION, generateDefaultAnswerSet, homeLabelPositionOptions, isUnusedId, DND_OPTION_TEXT_MODE, IDgIntElementDnDOption, unusedId
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { 
    contentJustifyOptions,
    CONTENT_JUSTIFY,
  flattenArray,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { bindFormControls } from 'src/app/ui-item-maker/services/data-bind';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { generateDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';

const FOCUS_TIMEOUT = 100;

@Component({
  selector: 'dg-int-config-dnd-inline',
  templateUrl: './dg-int-config-dnd-inline.component.html',
  styleUrls: ['./dg-int-config-dnd-inline.component.scss']
})
export class ElementConfigDgIntDndInlineComponent implements OnInit {
  @Input() element: IDgIntElementDnDInline;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;

  
  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  homeIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  nonUniqueOptionTexts: string[] = [];
  
  isUnusedId = isUnusedId;
  groupIds = new Set<number>();
  
  isGroupHomeForm = new FormControl(false);
  isShowAnswerForm = new FormControl(false);
  homeLabelForm = new FormControl('');
  homeLabelPositionOptions = homeLabelPositionOptions;
  homeLabelPositionForm = new FormControl(HOME_LABEL_POSITION.TOP);
  textContentForm : FormControl;
  isUsingAnswerGroupForm : FormControl;
  isSyncGroupForm: FormControl;
  homePositionForm = new FormControl(LAYOUT_HOME_POSITION.BOTTOM)
  homePositionOptions = [
    {id: LAYOUT_HOME_POSITION.LEFT, caption:'Left'},
    {id: LAYOUT_HOME_POSITION.RIGHT, caption:'Right'},
    {id: LAYOUT_HOME_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_HOME_POSITION.BOTTOM, caption:'Bottom'},
  ]
  homeLayoutForm = new FormControl(HOME_CONFIG_LAYOUT.DEFAULT);
  homeLayoutOptions = [
    {id: HOME_CONFIG_LAYOUT.DEFAULT, caption:'Default'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT, caption:'Default (Individual)'},
    {id: HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN, caption:'Column'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN, caption:'Column (Individual)'},
    {id: HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM, caption:'Top and Bottom'},
    {id: HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED, caption:'Advanced'},
    // {id: HOME_CONFIG_LAYOUT.ROW, caption:'[TBD] Row'},
    // {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_ROW, caption:'[TBD] Row (Individual)'},
  ]
  contentJustifyOptions = contentJustifyOptions;
  
  contentArrayTypeForm = new FormControl(DND_INLINE_CONTENT_TYPE.TEXT);
  contentArrayTypeOptions = [
    {id: DND_INLINE_CONTENT_TYPE.TEXT, caption:'Text'},
    {id: DND_INLINE_CONTENT_TYPE.TARGET, caption:'Target'},
    {id: DND_INLINE_CONTENT_TYPE.MATH, caption:'Math'},
  ]
  
  groupOptions : {id:string, caption:string}[] = [];
  
  public dndTargetOptionsForm : {
    label : string, 
    content : FormControl[],
    group : FormControl, 
  }[] = [];
  isNewTargetButtonHovered = false;
  isHovered = false;
  isFocus: boolean = false;
  cursorPosition: number = 0;
  focusTimer = null;
  
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  DND_INLINE_CONTENT_TYPE = DND_INLINE_CONTENT_TYPE;
  constructor(
    private cd: ChangeDetectorRef,
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService
  ) {}
  
  ngOnInit(): void {
    this.validateConfig();
    this.updateGroupOptions();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    bindFormControls(this.element.config, [
      {f: this.homePositionForm, p:'homePosition'},
    ]);
    bindFormControls(this.element.homeConfig, [
      {f: this.homeLayoutForm, p:'layout'},
      {f: this.homeLabelPositionForm, p:'labelPosition'},
      {f: this.homeLabelForm, p:'label'},
    ]);
    
    this.textContentForm = new FormControl(this.element?.content);
    this.textContentForm.valueChanges.subscribe(() => {
      this.element.content = this.textContentForm.value;
      this.validateConfig();
    });
    
    this.isUsingAnswerGroupForm = new FormControl(this.element?.config.isUsingAnswerGroup);
    this.isUsingAnswerGroupForm.valueChanges.subscribe(() => {
      if (!this.isUsingAnswerGroupForm.value){
        const confirm = window.confirm("Are you sure you want to disable answer group? This will remove all groups.");
        if (!confirm) {
          this.isUsingAnswerGroupForm.setValue(true);
          return;
        }
        this.elementEditRemoveAllGroups();
      }
      this.element.config.isUsingAnswerGroup = this.isUsingAnswerGroupForm.value;
      this.validateConfig();
    });
    
    this.isGroupHomeForm.setValue(this.element.homeConfig.isGroupHome, {emitEvent: false});
    this.isGroupHomeForm.valueChanges.subscribe(() => {
      if (!this.isGroupHomeForm.value) {
        const confirm = window.confirm('Are you sure you want to turn off home group?');
        if (!confirm) {
          this.isGroupHomeForm.setValue(true, { emitEvent: false });
          return;
        }
      }
      this.element.homeConfig.isGroupHome = this.isGroupHomeForm.value;
      this.validateConfig();
    });
    
    this.isSyncGroupForm = new FormControl(this.element.config.isSyncHomeGroupAndAnswerGroup);
    this.isSyncGroupForm.valueChanges.subscribe(() => {
      this.element.config.isSyncHomeGroupAndAnswerGroup = this.isSyncGroupForm.value;
      this.validateConfig();
    });
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  onFocus(){
    clearTimeout(this.focusTimer);
    this.isFocus = true;
  }
  
  onBlur(){
    this.focusTimer = setTimeout(() => {
      if (!this.isNewTargetButtonHovered){
        this.isFocus = false;
      } else {
        this.onBlur();
      }
    }, FOCUS_TIMEOUT);
  }
  
  setNewTargetButtonHoverState(state: boolean) {
    this.isNewTargetButtonHovered = state;
  }
  
  updateCursorPosition(ev: any){
    if (ev.target?.selectionStart){
      this.cursorPosition = ev.target.selectionStart;
    }
  }
  
  appendTextAtCursor(text: string){
    if (this.cursorPosition == undefined) return;
    const prevContent = this.textContentForm.value;
    this.textContentForm.setValue(
      prevContent.slice(0, this.cursorPosition) + text + prevContent.slice(this.cursorPosition)
    );
  }
  
  newTargetText(){
    if (this.isCursorInsideTarget()) {
      alert("Cannot create a new target. Your cursor is inside a target.\n(* Or your input is invalid)");
      return;
    }
    this.appendTextAtCursor(`[${this.getNewTargetLabel()}]`);
  }
  
  isCursorInsideTarget() : boolean{
    // example: "This is a [target] text"
    // check if cursor is inside the target
    const content = this.textContentForm.value;
    const cursor = this.cursorPosition;
    // if going right, we found ] before [, then the cursor is inside the target
    for (let i = cursor; i < content.length; i++){
      if (content[i] == ']') return true;
      if (content[i] == '[') break;
    }
    // if going left, we found [ before ], then the cursor is inside the target
    for (let i = cursor-1; i >= 0; i--){
      if (content[i] == '[') return true;
      if (content[i] == ']') break;
    }
    return false;
  }
  
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      
      let groupForm = new FormControl(String(target.group));
      groupForm.valueChanges.subscribe(obs => {
        if (groupForm.value == "-1"){
          const newVal = this.addNewGroup();
          groupForm.setValue(newVal);
        } else {
          target.group = parseInt(groupForm.value);
          this.validateConfig();
        }
      });
      
      this.dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm,
        group: groupForm,
      })
    });
  }
  
  isAllowDeleteOption(targetIndex: number, _optionIndex : number) : boolean {
    const target = this.element.dndTargets[targetIndex];
    return isUnusedId(target.label);
  }
  
  isAllowAddOption(targetIndex : number) : boolean {
    const target = this.element.dndTargets[targetIndex];
    return isUnusedId(target.label);
  }
  
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  
  isEmptyHomeGroup(index: number) : boolean {
    const target = this.element.homeConfig.element[index];
    return target.length == 0;
  }
  
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        if (targetData.optionIds.length == 0) return true;
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return true;
    return !this.optionIdList.includes(optionId);
  }
  
  elementEditDeleteOption(targetIndex: number, optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    const target = this.element.dndTargets[targetIndex];
    target.content.splice(optionIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddOption(targetIndex: number){
    const target = this.element.dndTargets[targetIndex];
    if (!isUnusedId(target.label)) return;
    target.content.push({...generateDefaultElement(ElementType.TEXT), value:""});
    this.validateConfig();
  }
  
  elementEditRemoveAllGroups(){
    for (let target of this.element.dndTargets) target.group = 1;
    this.validateConfig();
  }
  
  elementEditAddHomeGroup(){
    this.element.homeConfig.element.push([]);
    this.validateConfig();
  }
  elementEditDeleteHomeGroup(index: number) {
    const confirm = window.confirm("Are you sure you want to delete this group?");
    if (!confirm) return;
    this.element.homeConfig.element.splice(index, 1);
    this.validateConfig();
  }
  
  elementEditAddAnswerSet() {
    if (!this.element.config.isAllowMultipleAnswer) return;
    const answerSet = this.targetIdList.map((id) => { return { targetId: id, optionIds: [] } });
    this.element.altAnswers.push(answerSet);
    this.validateConfig();
  }
  elementEditDeleteAnswerSet(index: number){
    const confirm = window.confirm("Are you sure you want to delete this answer set?");
    if (!confirm) return;
    this.element.altAnswers.splice(index, 1);
    this.validateConfig();
  }
  elementEditSetOptionTextMode(targetIndex: number, optionIndex: number, mode: DND_OPTION_TEXT_MODE) {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.textMode = mode;
    option.elementType = mode == DND_OPTION_TEXT_MODE.TEXT ? ElementType.TEXT : ElementType.MATH;
    this.validateConfig();
  }
  
  elementEditDeleteContentArray(contentIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this element?");
    if (!confirm) return;
    this.element.contentArray.splice(contentIndex, 1);
    this.validateConfig();
  }
  elementEditAddContentArray(){
    const type = this.contentArrayTypeForm.value;
    if (type == DND_INLINE_CONTENT_TYPE.TARGET){
      this.element.contentArray.push({...generateDefaultElement(ElementType.DND_TARGET), type: type, value: this.getNewTargetLabel()});
    } else {
      this.element.contentArray.push({...generateDefaultElement(type == DND_INLINE_CONTENT_TYPE.MATH ? ElementType.MATH : ElementType.TEXT), type: type, value: ""});
    }
    this.validateConfig();
  }
  
  getNewTargetLabel(): string {
    const targetLabels = this.element.dndTargets.map(target => target.label);
    // get all target labels in the form `target{number}`
    const standardTargetLabels = targetLabels.filter(label => label.match(/^target\d+$/));
    // get only the numbers
    const targetNumbers = standardTargetLabels.map(label => parseInt(label.slice(6)));
    const maxNumber = Math.max(...targetNumbers,0);
    return `target${maxNumber+1}`;
  }
  
  setListView() {
    const confirm = window.confirm("Are you sure you want to change to the list editor view? (make sure your element is valid)");
    if (!confirm) return;
    this.element.contentArray = contentToContentArray(this.element.content);
    this.element.config.isUseArrayEditor = true;
    this.rerender();
  }
  setInlineView() {
    const confirm = window.confirm("Are you sure you want to change to the inline editor view?");
    if (!confirm) return;
    this.element.config.isUseArrayEditor = false;
    this.textContentForm.setValue(this.element.content);
    this.rerender();
  }
  
  dropContentArray(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.contentArray, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  
  // return the id of the new group
  addNewGroup() : string {
    this.updateGroupOptions();
    let groupOptions = this.groupOptions;
    let secondToLast = groupOptions[groupOptions.length-2];
    // this newId assumes that the groupIds are sorted
    const newId = secondToLast ? String(parseInt(secondToLast.id) + 1) : "1";
    // push to groupOptions one index before the last one
    groupOptions.splice(groupOptions.length-1, 0, {id:newId, caption:newId})
    this.groupOptions = groupOptions;
    this.cd.detectChanges();
    return newId;
  }
  
  validateConfig() {
    this.resolveData();
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  updateGroupOptions(){
    let groupIds = new Set<number>();
    for (let target of this.element.dndTargets) groupIds.add(target.group ?? 1);
    groupIds = new Set(Array.from(groupIds).sort((a,b) => a-b));
    this.groupIds = groupIds;
    
    let groupOptions = Array.from(groupIds).map(x => {return {id:x.toString(), caption:x.toString()}})
    groupOptions.push({id:"-1", caption:'add new group'})
    this.groupOptions = groupOptions;
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  
  getOptionTextMode(targetIndex: number, optionIndex: number) : DND_OPTION_TEXT_MODE {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    return option.textMode ?? DND_OPTION_TEXT_MODE.TEXT;
  }
  getOptionObject(targetIndex: number, optionIndex: number): IDgIntElementDnDOption {
    const target = this.element.dndTargets[targetIndex];
    return target.content[optionIndex];
  }
  
  resolveData() {
    resolveDnDInlineTargets(this.element);
  }
  
  dropHomeLayout(event: CdkDragDrop<number[]>){
    this.requestResetState();
    if (event.previousContainer === event.container){
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else if (!this.element.config.isSyncHomeGroupAndAnswerGroup) {
      // if the home layout and answer group are synced, element can't be moved between them
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }
    this.rerender();
  }
  
  isNonUniqueOption(option: string) {
    if (option.trim() == "") return false;
    return this.nonUniqueOptionTexts.includes(option);
  }
  
  isUsingAnswerGroup() : boolean {
    return this.element.config.isUsingAnswerGroup ?? false;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
}
