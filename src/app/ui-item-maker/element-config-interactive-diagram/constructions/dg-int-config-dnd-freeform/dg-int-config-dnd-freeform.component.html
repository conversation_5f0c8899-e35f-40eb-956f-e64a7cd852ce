<fieldset [disabled]="isReadOnly()">
  <label class="label">Background Image</label>
  <div class="background-config-container">
    <capture-image [element]="getBackgroundImageElement()" (change)="rerender()"></capture-image>
    <asset-library-link [element]="getBackgroundImageElement()"></asset-library-link>     
  </div >
</fieldset>

<fieldset (change)="rerender()" style="margin: 1em 0" [disabled]="isReadOnly()">
  <label class="label">Label</label>
  <div style="margin-top: 0.5em">
    <label>Label: </label> 
    <textarea
      *ngIf="!element.label.isMath"
      textInputTransform
      #label
      [formControl]="labelForm"
      (input)="rerender()"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em; display: inline-block;"
      class="textarea is-small"
      cdkTextareaAutosize
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(labelForm, 'label')"
    ></textarea>
    <div *ngIf="element.label.isMath" class="capture-math-container" 
      style="width: 11em; display: inline-block; vertical-align: top;"
    >
      <capture-math 
        [obj]="element.label" 
        (onChange)="rerender()"
        [class.is-disabled]="isReadOnly()"
        [class.is-disabled]="isReadOnly()"
        prop="text" [isManualKeyboard]="true">
      </capture-math>
    </div>
    <div *ngIf="!element.label.isMath"
      (click)="toggleLabelMath()" 
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-square-root-alt" aria-hidden="true"></i>
    </div>
    <div *ngIf="element.label.isMath"
      (click)="toggleLabelMath()"
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-font" aria-hidden="true"></i>
    </div>
    <ng-container *ngIf="element.label.text">
      <br>Label Position:
      <select [(ngModel)]="element.label.position">
        <option *ngFor="let option of diagramLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </ng-container>
  </div>
</fieldset>

<hr/>

<fieldset [disabled]="isReadOnly()">
  <label class="label">Targets</label>
  
  <div *ngFor="let target of element.dndTargets | slice:0:-1; let i = index; trackBy: trackByFn">
    <div 
      class="target-config-container"
      (mouseenter)="setHoveredTarget(i)"
      (mouseleave)="resetHoveredTarget()"
    >
      
      <div class="target-label-container">
        <div class="flex-container">
          <textarea
            textInputTransform
            #targets
            [formControl]="dndTargetLabelForm[i]"
            style="min-width: 10em;"
            class="textarea is-small target-option-textarea"
            cdkTextareaAutosize
            [cdkTextareaAutosize]="true"
            [cdkAutosizeMinRows]="2"
            (focus)="setTextFocus(dndTargetLabelForm[i], 'target_' + i)"
          ></textarea>
          <div (click)="elementEditDeleteTarget(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
            <i class="fas fa-trash" aria-hidden="true"></i>
          </div>
        </div >
      </div>
      
      <!-- <button (click)="requestMoveTarget($event, i)">move target</button> -->
      <button 
        (click)="requestMoveTarget($event, i)" 
        class="button is-small has-icon"
      >
        <span class="icon"><i class="fa fa-map-marker-alt" aria-hidden="true"></i></span>
        <span>Move Target Position</span>
      </button>
      
      <span *ngIf="isMovingTarget(i)">
        Right Click to cancel
      </span >
      
      <ng-container *ngIf="element.isShowAdvancedOptions">
        <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em">
          <div>X: </div>
          <input 
              type="number" 
              class="input is-small" 
              style="width:8em; text-align:center" 
              [(ngModel)]="element.targetData[i].position.x"
              step="0.01"
          >
          <div>Y: </div>
          <input 
              type="number" 
              class="input is-small" 
              style="width:8em; text-align:center" 
              [(ngModel)]="element.targetData[i].position.y"
              step="0.01"
          >
        </div>
      </ng-container>
      
    </div>
  </div>
  
  <button 
    (click)="elementEditAddTarget()" 
    class="button is-small has-icon"
  >
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span>Add Target</span>
  </button>
  
  <div style="margin-bottom: 1em">
    <!-- <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
      <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
      <span>Allow Images</span>
    </button> -->
    <!-- <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Text Only</span>
    </button> -->
    
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
      Show Advanced Options
    </label>
  </div>
  
  
</fieldset>

<hr />

<fieldset [disabled]="isReadOnly()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm" (change)="rerender()"/>
    Show Answer
  </label>
</fieldset>

<twiddle caption="Target Options" [state]="twiddleTargets"></twiddle>
<div *ngIf="twiddleTargets.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div style="margin: 1em 0">
      <ng-container *ngIf="!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer"/>
        Accept Multiple Answers
      </label><br>
      </ng-container>
      <label class="checkbox">
        <input type="checkbox" [formControl]="isUsingReusableDraggableForm"/>
        Reusable Draggables
      </label>
    </div>
    <div>
      <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
        <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
        <span>Image</span>
      </button>
      <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
        <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
        <span>Text</span>
      </button>
    </div>
    
    <div *ngIf="isUseImage()">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
        Show Advanced Options
      </label>
    </div>
  </fieldset>
  <br/>

  
  <dg-int-dnd-target-options
    [dndTargets]="element.dndTargets"
    [dndTargetOptionsForm]="dndTargetOptionsForm"
    [nonUniqueOptionTexts]="nonUniqueOptionTexts"
    [isUsingReusableDraggable]="element.config.isUsingReusableDraggable"
    [isAlwaysSeparateAnswerSetup]="element._isAlwaysSeparateAnswerSetup"
    [isAllowGrouping]="element.config.isAllowGrouping"
    [isUseImage]="element.config.isUseImage"
    [isShowAdvancedOptions]="element.isShowAdvancedOptions"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    (onSetIsAllowEmptyTarget)="element.config.isAllowEmptyTarget = $event"
    (onChange)="validateConfig()"
  ></dg-int-dnd-target-options>
  
  <div style="margin-top: 1em; background-color: #eee; padding: 1em; border-radius: 0.5em;">
    <span>The item is considered to be answered if:</span>
    <div class="select">
      <select [(ngModel)]="element.dndIsFilledMode" (change)="validateConfig()">
        <option *ngFor="let option of dndIsFilledModeOptions" [value]="option.id">
          <span *ngIf="option.id == 'auto'">Auto ({{dndIsFilledCaptionMap[element._autoDnDIsFilledMode]}})</span>
          <span *ngIf="option.id != 'auto'">{{option.caption}}</span>
        </option>
      </select>
    </div>
  </div>
  
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle 
    *ngIf="element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup"
    caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value 
    && (element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup)
    && element.altAnswers">
  <div style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
      Accept Multiple Answers
    </label>
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowEmptyTarget" (change)="rerender()"/>
      Allow empty target
    </label>
  </div>
  <dg-int-dnd-answers
    [defaultAnswerSet]="defaultAnswerSet"
    [altAnswers]="element.altAnswers"
    [isShowDefaultAnswerSet]="!this.element.config.isUsingReusableDraggable && !this.element._isAlwaysSeparateAnswerSetup"
    [isAllowMultipleAnswer]="element.config.isAllowMultipleAnswer"
    [isAllowEmptyTarget]="element.config.isAllowEmptyTarget"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    [targetIdList]="targetIdList"
    [optionIdList]="sortedOptionIdList"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [targets]="element.dndTargets"
    (onChange)="validateConfig()"
  ></dg-int-dnd-answers>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <fieldset style="margin-bottom: 0.5em" (change)="rerender()">
    <div>
      <span *ngIf="element.config.isFixedOptionWidth">
        Option Width:
      </span>
      <span *ngIf="!element.config.isFixedOptionWidth">
        Maximum Option Width:
      </span>
      <input type="number" [(ngModel)]="element.config.optionMaxWidth" class="small-input">
    </div>
    <div>
      <span>Content Justify: </span>
      <select [(ngModel)]="element.config.contentJustify">
        <option *ngFor="let option of contentJustifyOptions" [value]="option.id">{{option.caption}}</option>
      </select>
    </div>
  </fieldset>
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br>
        
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isFixedOptionWidth"/>
        Fixed Option Width
      </label>
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAutoPadding"/>
        Apply Auto Padding
      </label>
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.backgroundImage.padding.isEnabled"/>
        Add Padding to the Image
      </label>
      <br>
      <div *ngIf="element.backgroundImage.padding.isEnabled">
        top:
        <input type="number" [(ngModel)]="element.backgroundImage.padding.top" class="small-input">
        <br> bottom:
        <input type="number" [(ngModel)]="element.backgroundImage.padding.bottom" class="small-input">
        <br> left:
        <input type="number" [(ngModel)]="element.backgroundImage.padding.left" class="small-input">
        <br> right:
        <input type="number" [(ngModel)]="element.backgroundImage.padding.right" class="small-input">
      </div>
      
      
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
        
      <br>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowNonScoringTarget" (change)="validateConfig()"/>
        Allow Non Scoring Target
      </label>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element._isAlwaysSeparateAnswerSetup" (change)="validateConfig()"/>
        Always Separate Answer Setup
      </label>
    </div>
  </fieldset>
</div>
