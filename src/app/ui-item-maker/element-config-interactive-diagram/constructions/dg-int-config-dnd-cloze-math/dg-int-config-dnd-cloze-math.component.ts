import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { 
  isUnusedId,
  IDgIntElementDnDAnswerSet,
  generateDefaultAnswerSet,
  DND_OPTION_TEXT_MODE,
  homeLabelPositionOptions,
  HOME_LABEL_POSITION,
  unusedId,
  diagramLabelPositionOptions,
  dndIsFilledModeOptions,
  dndIsFilledModeCaptionMap,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { contentJustifyOptions, flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { 
  IDgIntElementDnDClozeMath, resolveDnDClozeMathTargets
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-cloze-math';
import { CaptureMathComponent } from 'src/app/widget-math/capture-math/capture-math.component';
import { numberToAlphabet, alphabetToNumber } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';
import { ElementType } from 'src/app/ui-testrunner/models';


const FOCUS_TIMEOUT = 100;

@Component({
  selector: 'dg-int-config-dnd-cloze-math',
  templateUrl: './dg-int-config-dnd-cloze-math.component.html',
  styleUrls: ['./dg-int-config-dnd-cloze-math.component.scss']
})
export class ElementConfigDgIntDndClozeMathComponent implements OnInit {
  @Input() element: IDgIntElementDnDClozeMath;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  
  @ViewChild('captureMath') captureMath!: CaptureMathComponent;
  @ViewChild('label') label: ElementRef;

  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  homeIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  
  homeLabelPositionOptions = homeLabelPositionOptions;
  diagramLabelPositionOptions = diagramLabelPositionOptions;
  contentJustifyOptions = contentJustifyOptions;
  dndIsFilledModeOptions = dndIsFilledModeOptions;
  dndIsFilledCaptionMap = dndIsFilledModeCaptionMap;
  isShowAnswerForm = new FormControl(false);
  labelForm = new FormControl("");
  isUsingReusableDraggableForm: FormControl;
  public dndTargetOptionsForm : {label:string, content:FormControl[]}[] = [];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ){}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    this.isUsingReusableDraggableForm = new FormControl(this.element.config.isUsingReusableDraggable);
    this.isUsingReusableDraggableForm.valueChanges.subscribe(() => {
      if (this.isUsingReusableDraggableForm.value) {
        const confirm = window.confirm('Are you sure you want to enable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(false, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = true;
      } else {
        const confirm = window.confirm('Are you sure you want to disable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(true, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = false;
      }
      this.validateConfig();
    });

    bindFormControls(this.element.label, [
      {f: this.labelForm, p: 'text'}, 
    ]);
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }

  toggleLabelMath() {
    this.element.label.isMath = !this.element.label.isMath;
    this.element.label.elementType = this.element.label.isMath ? ElementType.MATH : ElementType.TEXT;
    this.rerender();
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      label: this.label,
    }

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      this.dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm
      })
    });
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        if (targetData.optionIds.length == 0) return !this.element.config.isAllowEmptyTarget;
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return !this.element.config.isAllowEmptyTarget;
    return !this.optionIdList.includes(optionId);
  }
  
  validateConfig() {
    this.resolveData();
    this.updateForms();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter += 1;
  }
  
  updateForms() {
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
  }
  resolveData() {
    resolveDnDClozeMathTargets(this.element);
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index)
      .filter(x => x);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        this.idToUrlMap.set(option.id, option.image?.url);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
    // idIsMathSet get value from combined targetIdList and optionIdList
    this.idIsMathSet = new Set<string>([...this.targetIdList, ...this.optionIdList]);
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string) : string {
    return this.idToUrlMap.get(id);
  }
  
  isUsingReusableDraggable() {
    return this.element.config.isUsingReusableDraggable ?? false;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  isShowConfigPreview(){
    return true;
  }
  
  insertNewTarget() {
    const targetLabels = this.element.dndTargets.map(t => t.label);
    // filter the target labels that only contain uppercase alphabet
    const alphabetLabels = targetLabels.filter(label => /^[A-Z]+$/.test(label));
    const numericLabels = alphabetLabels.map(s => alphabetToNumber(s));
    const newLabel = numberToAlphabet(Math.max(...numericLabels, -1) + 1);
    this.captureMath?.mathField?.insert(`\\boxed{${newLabel}}`)
  }
  
  isNewTargetButtonHovered = false;
  isFocus: boolean = false;
  cursorPosition: number = 0;
  focusTimer = null;
  setNewTargetButtonHoverState(state: boolean) {
    this.isNewTargetButtonHovered = state;
  }
  onFocus(){
    clearTimeout(this.focusTimer);
    this.isFocus = true;
  }
  onBlur(){
    this.focusTimer = setTimeout(() => {
      if (!this.isNewTargetButtonHovered){
        this.isFocus = false;
      } else {
        this.onBlur();
      }
    }, FOCUS_TIMEOUT);
  }
}
