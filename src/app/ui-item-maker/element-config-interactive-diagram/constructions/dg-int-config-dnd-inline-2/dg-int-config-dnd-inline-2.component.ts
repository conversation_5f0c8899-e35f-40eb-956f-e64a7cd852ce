import { Component, Input, OnInit, ChangeDetectorRef, ViewChildren, QueryList, ElementRef, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup } from '@angular/forms';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { 
  IDgIntElementDnDInline2,  
  resolveDnDInline2Targets, DND_INLINE_2_CONTENT_TYPE, contentToContentArray
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-inline-2';
import { 
    HOME_CONFIG_LAYOUT,
    HOME_LABEL_POSITION,
    IDgIntElementDnDAnswerSet,
  IDgIntElementDnDTarget, LAYOUT_HOME_POSITION, generateDefaultAnswerSet, homeLabelPositionOptions, isUnusedId, DND_OPTION_TEXT_MODE, IDgIntElementDnDOption, unusedId, dndIsFilledModeOptions, dndIsFilledModeCaptionMap
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { 
    contentJustifyOptions,
    CONTENT_JUSTIFY,
  flattenArray,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { bindFormControls } from 'src/app/ui-item-maker/services/data-bind';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';
import { generateDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';

const FOCUS_TIMEOUT = 100;

@Component({
  selector: 'dg-int-config-dnd-inline-2',
  templateUrl: './dg-int-config-dnd-inline-2.component.html',
  styleUrls: ['./dg-int-config-dnd-inline-2.component.scss']
})
export class ElementConfigDgIntDndInline2Component implements OnInit {
  @Input() element: IDgIntElementDnDInline2;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChildren('contentArray') contentArray: QueryList<ElementRef>;
  @ViewChild('inlineEditor') inlineEditor: ElementRef;

  
  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  homeIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  
  isUnusedId = isUnusedId;
  
  form: FormGroup;
  isGroupHomeForm = new FormControl(false);
  isShowAnswerForm = new FormControl(false);
  isUsingReusableDraggableForm: FormControl;
  homeLabelForm = new FormControl('');
  homeLabelPositionOptions = homeLabelPositionOptions;
  homeLabelPositionForm = new FormControl(HOME_LABEL_POSITION.TOP);
  textContentForm : FormControl;
  homePositionForm = new FormControl(LAYOUT_HOME_POSITION.BOTTOM)
  homePositionOptions = [
    {id: LAYOUT_HOME_POSITION.LEFT, caption:'Left'},
    {id: LAYOUT_HOME_POSITION.RIGHT, caption:'Right'},
    {id: LAYOUT_HOME_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_HOME_POSITION.BOTTOM, caption:'Bottom'},
  ]
  homeLayoutForm = new FormControl(HOME_CONFIG_LAYOUT.DEFAULT);
  homeLayoutOptions = [
    {id: HOME_CONFIG_LAYOUT.DEFAULT, caption:'Default'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT, caption:'Default (Individual)'},
    {id: HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN, caption:'Column'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN, caption:'Column (Individual)'},
    {id: HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM, caption:'Top and Bottom'},
    {id: HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED, caption:'Advanced'},
    // {id: HOME_CONFIG_LAYOUT.ROW, caption:'[TBD] Row'},
    // {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_ROW, caption:'[TBD] Row (Individual)'},
  ]
  contentJustifyOptions = contentJustifyOptions;
  dndIsFilledModeOptions = dndIsFilledModeOptions;
  dndIsFilledCaptionMap = dndIsFilledModeCaptionMap;
  
  contentArrayTypeForm = new FormControl(DND_INLINE_2_CONTENT_TYPE.TEXT);
  contentArrayTypeOptions = [
    {id: DND_INLINE_2_CONTENT_TYPE.TEXT, caption:'Text'},
    {id: DND_INLINE_2_CONTENT_TYPE.TARGET, caption:'Target'},
    {id: DND_INLINE_2_CONTENT_TYPE.MATH, caption:'Math'},
    {id: DND_INLINE_2_CONTENT_TYPE.BOOKMARK_LINK, caption:'Bookmark Link'},
  ]
  
  public dndTargetOptionsForm : {
    label : string, 
    content : FormControl[],
  }[] = [];
  isNewTargetButtonHovered = false;
  isHovered = false;
  isFocus: boolean = false;
  cursorPosition: number = 0;
  focusTimer = null;
  
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  DND_INLINE_CONTENT_TYPE = DND_INLINE_2_CONTENT_TYPE;
  constructor(
    private cd: ChangeDetectorRef,
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ) {}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    bindFormControls(this.element.config, [
      {f: this.homePositionForm, p:'homePosition'},
    ]);
    bindFormControls(this.element.homeConfig, [
      {f: this.homeLayoutForm, p:'layout'},
      {f: this.homeLabelPositionForm, p:'labelPosition'},
      {f: this.homeLabelForm, p:'label'},
    ]);
    
    this.textContentForm = new FormControl(this.element?.content);
    this.textContentForm.valueChanges.subscribe(() => {
      this.element.content = this.textContentForm.value;
      this.validateConfig();
    });

    this.form = new FormGroup({
      contentArray: new FormArray([]),
    });

    this.addFormControls();
    this.bindContentArray();
    
    this.isGroupHomeForm.setValue(this.element.homeConfig.isGroupHome, {emitEvent: false});
    this.isGroupHomeForm.valueChanges.subscribe(() => {
      if (!this.isGroupHomeForm.value) {
        const confirm = window.confirm('Are you sure you want to turn off home group?');
        if (!confirm) {
          this.isGroupHomeForm.setValue(true, { emitEvent: false });
          return;
        }
      }
      this.element.homeConfig.isGroupHome = this.isGroupHomeForm.value;
      this.validateConfig();
    });
    this.isUsingReusableDraggableForm = new FormControl(this.element.config.isUsingReusableDraggable);
    this.isUsingReusableDraggableForm.valueChanges.subscribe(() => {
      if (this.isUsingReusableDraggableForm.value) {
        const confirm = window.confirm('Are you sure you want to enable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(false, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = true;
      } else {
        const confirm = window.confirm('Are you sure you want to disable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(true, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = false;
      }
      this.validateConfig();
    });
  }

  bindContentArray() {
    // for each option bind form control to element label
    this.element.contentArray.forEach((content, idx) => {
      bindFormControls(content, [
        {f: this.contentFormArray.controls[idx] as FormControl, p: 'value'}, 
      ])
    });

    // validate config every time the input value changes
    this.contentFormArray.controls.forEach(contentControl => {
      contentControl.valueChanges.subscribe(() => this.validateConfig());
    })
  }

  get contentFormArray() {
    return (this.form.get('contentArray') as FormArray);
  }

  addFormControls() {
    // Loop through options and create FormControls for each
    this.contentFormArray.clear();
    this.element.contentArray.forEach(content => {
      this.contentFormArray.push(new FormControl(content.value));
    });
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  onFocus(){
    clearTimeout(this.focusTimer);
    this.isFocus = true;
  }
  
  onBlur(){
    this.focusTimer = setTimeout(() => {
      if (!this.isNewTargetButtonHovered){
        this.isFocus = false;
      } else {
        this.onBlur();
      }
    }, FOCUS_TIMEOUT);
  }
  
  setNewTargetButtonHoverState(state: boolean) {
    this.isNewTargetButtonHovered = state;
  }
  
  updateCursorPosition(ev: any){
    if (ev.target?.selectionStart){
      this.cursorPosition = ev.target.selectionStart;
    }
  }
  
  appendTextAtCursor(text: string){
    if (this.cursorPosition == undefined) return;
    const prevContent = this.textContentForm.value;
    this.textContentForm.setValue(
      prevContent.slice(0, this.cursorPosition) + text + prevContent.slice(this.cursorPosition)
    );
  }
  
  newTargetText(){
    if (this.isCursorInsideTarget()) {
      alert("Cannot create a new target. Your cursor is inside a target.\n(* Or your input is invalid)");
      return;
    }
    this.appendTextAtCursor(`[${this.getNewTargetLabel()}]`);
  }
  
  isCursorInsideTarget() : boolean{
    // example: "This is a [target] text"
    // check if cursor is inside the target
    const content = this.textContentForm.value;
    const cursor = this.cursorPosition;
    // if going right, we found ] before [, then the cursor is inside the target
    for (let i = cursor; i < content.length; i++){
      if (content[i] == ']') return true;
      if (content[i] == '[') break;
    }
    // if going left, we found [ before ], then the cursor is inside the target
    for (let i = cursor-1; i >= 0; i--){
      if (content[i] == '[') return true;
      if (content[i] == ']') break;
    }
    return false;
  }
  
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      
      this.dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm,
      })
    });
  }
  
  isAllowDeleteOption(targetIndex: number, _optionIndex : number) : boolean {
    const target = this.element.dndTargets[targetIndex];
    return isUnusedId(target.label);
  }
  
  isAllowAddOption(targetIndex : number) : boolean {
    const target = this.element.dndTargets[targetIndex];
    return isUnusedId(target.label);
  }
  
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  
  isEmptyHomeGroup(index: number) : boolean {
    const target = this.element.homeConfig.element[index];
    return target.length == 0;
  }
  
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        if (targetData.optionIds.length == 0) return !this.element.config.isAllowEmptyTarget;
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return !this.element.config.isAllowEmptyTarget;
    return !this.optionIdList.includes(optionId);
  }
  
  elementEditDeleteOption(targetIndex: number, optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    const target = this.element.dndTargets[targetIndex];
    target.content.splice(optionIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddOption(targetIndex: number){
    const target = this.element.dndTargets[targetIndex];
    if (!isUnusedId(target.label)) return;
    target.content.push({...generateDefaultElement(ElementType.TEXT), value: ""});
    this.validateConfig();
  }
  
  elementEditAddHomeGroup(){
    this.element.homeConfig.element.push([]);
    this.validateConfig();
  }
  elementEditDeleteHomeGroup(index: number) {
    const confirm = window.confirm("Are you sure you want to delete this group?");
    if (!confirm) return;
    this.element.homeConfig.element.splice(index, 1);
    this.validateConfig();
  }
  
  elementEditSetOptionTextMode(targetIndex: number, optionIndex: number, mode: DND_OPTION_TEXT_MODE) {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.textMode = mode;
    this.validateConfig();
  }
  
  elementEditDeleteContentArray(contentIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this element?");
    if (!confirm) return;
    this.element.contentArray.splice(contentIndex, 1);
    this.contentFormArray.removeAt(contentIndex);
    this.validateConfig();
  }

  elementEditAddContentArray(){
    const type = this.contentArrayTypeForm.value;
    if (type == DND_INLINE_2_CONTENT_TYPE.TARGET){
      this.element.contentArray.push({type: type, value: this.getNewTargetLabel(), ...generateDefaultElement(ElementType.DND_TARGET)});
    } else {
      this.element.contentArray.push({type: type, value: "", ...generateDefaultElement(type)});
    }

    const numOptions = this.element.contentArray.length;
    const newOption = new FormControl(this.element.contentArray[numOptions - 1].value);
    this.contentFormArray.push(newOption);
    bindFormControls(this.element.contentArray[numOptions - 1], [
      {f: newOption, p: 'value'}
    ])
    this.contentFormArray.controls[numOptions - 1].valueChanges.subscribe(() => {
      this.validateConfig();
    })

    this.validateConfig();
  }
  
  getNewTargetLabel(): string {
    const targetLabels = this.element.dndTargets.map(target => target.label);
    // get all target labels in the form `target{number}`
    const standardTargetLabels = targetLabels.filter(label => label.match(/^target\d+$/));
    // get only the numbers
    const targetNumbers = standardTargetLabels.map(label => parseInt(label.slice(6)));
    const maxNumber = Math.max(...targetNumbers,0);
    return `target${maxNumber+1}`;
  }
  
  setListView() {
    const confirm = window.confirm("Are you sure you want to change to the list editor view? (make sure your element is valid)");
    if (!confirm) return;
    this.element.contentArray = contentToContentArray(this.element.content);
    this.element.config.isUseArrayEditor = true;
    this.addFormControls();
    this.bindContentArray();
    this.validateConfig();
  }
  setInlineView() {
    const confirm = window.confirm("Are you sure you want to change to the inline editor view?");
    if (!confirm) return;
    this.element.config.isUseArrayEditor = false;
    this.textContentForm.setValue(this.element.content);
    this.validateConfig();
  }
  
  dropContentArray(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.contentArray, event.previousIndex, event.currentIndex);
    moveItemInArray(this.contentFormArray.controls, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  
  validateConfig() {
    this.resolveData();
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter += 1;
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        if (option.textMode == DND_OPTION_TEXT_MODE.MATH) this.idIsMathSet.add(option.id);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  
  getOptionTextMode(targetIndex: number, optionIndex: number) : DND_OPTION_TEXT_MODE {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    return option.textMode ?? DND_OPTION_TEXT_MODE.TEXT;
  }
  getOptionObject(targetIndex: number, optionIndex: number): IDgIntElementDnDOption {
    const target = this.element.dndTargets[targetIndex];
    return target.content[optionIndex];
  }
  
  resolveData() {
    resolveDnDInline2Targets(this.element);
  }
  
  dropHomeLayout(event: CdkDragDrop<number[]>){
    this.requestResetState();
    if (event.previousContainer === event.container){
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }
  }
  
  isNonUniqueOption(option: string) {
    if (option.trim() == "") return false;
    return this.nonUniqueOptionTexts.includes(option);
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      inlineEditor: this.inlineEditor
    }

    this.element.contentArray.forEach((content, idx) => {
      elements[`content_${idx}`] = this.contentArray.toArray()[idx];
    })

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
  
}
