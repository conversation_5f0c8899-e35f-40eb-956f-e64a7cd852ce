import { Component, ElementRef, Input, OnInit, Query<PERSON>ist, ViewChild, ViewChildren } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { IDgIntElementDnDVenn, resolveDnDVennTargets } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-venn';
import { IDgIntElementDnDTarget, isUnusedId, DND_OPTION_TEXT_MODE } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement, generateDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-config-dnd-venn',
  templateUrl: './dg-int-config-dnd-venn.component.html',
  styleUrls: ['./dg-int-config-dnd-venn.component.scss']
})
export class ElementConfigDgIntDndVennComponent implements OnInit {
  @Input() element: IDgIntElementDnDVenn;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChild('leftLabel') leftLabel: ElementRef;
  @ViewChild('rightLabel') rightLabel: ElementRef;
  @ViewChildren('targets') targets: QueryList<ElementRef>;

  twiddleHome = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  
  isShowAnswerForm = new FormControl(false);
  leftLabelForm = new FormControl('');
  rightLabelForm = new FormControl('');
  isLimitMaxCapacityForm: FormControl;
  
  public dndTargetOptionsForm : {label:string, content:FormControl[]}[] = [];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ) {}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    bindFormControls(this.element.label.left, [
      {f: this.leftLabelForm, p:'caption'},
    ]);
    bindFormControls(this.element.label.right, [
      {f: this.rightLabelForm, p:'caption'},
    ]);
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      
      this.dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm
      })
    });
  }
  
  isAllowDeleteOption(_targetIndex: number, _optionIndex : number) : boolean {
    return true;
  }
  
  isAllowAddOption(_targetIndex : number) : boolean {
    return true;
  }
  
  isAllowDeleteTarget(targetIndex: number) : boolean {
    return !this.isUnusedTarget(this.element.dndTargets[targetIndex]);
  }
  
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        this.idToUrlMap.set(option.id, option.image?.url);
        if (option.textMode == DND_OPTION_TEXT_MODE.MATH) this.idIsMathSet.add(option.id);
      }
    }
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string) : string {
    return this.idToUrlMap.get(id);
  }
  
  
  elementEditDeleteOption(targetIndex: number, optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    const target = this.element.dndTargets[targetIndex];
    target.content.splice(optionIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddOption(targetIndex: number){
    const target = this.element.dndTargets[targetIndex];
    target.content.push({'value':"", ...generateDefaultElement(ElementType.TEXT)});
    this.validateConfig();
  }
  
  elementEditInitOptionImage(targetIndex:number, optionIndex:number){
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.image = createDefaultElement(ElementType.IMAGE);
    option.image.scale = 25;
  }
  
  elementEditRemoveOptionImage(targetIndex:number, optionIndex:number){
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    delete option.image;
    delete option.imageLabelPosition;
    this.rerender();
  }
  
  getOptionImageElement(targetIndex: number, optionIndex: number) : IContentElementImage {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    if (option?.image == undefined) {
      this.elementEditInitOptionImage(targetIndex, optionIndex);
    }
    return option.image;
  }
  
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return true;
    return !this.optionIdList.includes(optionId);
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to image mode?");
      if (!confirm) return;
      this.element.config.isUseImage = true;
    } else {
      const confirm = window.confirm("Are you sure you want to change to text mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let target of this.element.dndTargets){
        for (let option of target.content){
          delete option.image;
          delete option.imageLabelPosition;
        }
      }
    }
    this.validateConfig();
  }
  
  isUseImage() : boolean {
    return this.element.config.isUseImage ?? false;
  }
  
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  
  validateConfig() {
    this.resolveData();
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  resolveData() {
    resolveDnDVennTargets(this.element);
  }
  
  dropOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.ordering, event.previousIndex, event.currentIndex);
    this.rerender();
  }
  dropTarget(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.dndTargets, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  
  isNonUniqueOption(option: string) {
    if (option.trim() == "") return false;
    return this.nonUniqueOptionTexts.includes(option);
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      leftLabel: this.leftLabel,
      rightLabel: this.rightLabel,
    }

    let targetCount = 0;
    this.element.dndTargets.forEach((target, i) => {
      target.content.forEach((targetOption, j) => {
        elements[`target_${i}_${j}`] = this.targets.toArray()[targetCount];
        targetCount += 1;
      })
    })

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
}
