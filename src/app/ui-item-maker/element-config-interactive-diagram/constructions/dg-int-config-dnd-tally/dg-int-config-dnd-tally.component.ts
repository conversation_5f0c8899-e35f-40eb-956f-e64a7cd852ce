import { Component, ElementRef, Input, OnInit, QueryList, ViewChildren } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { IDgIntElementDnDTally, resolveDnDTally } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-tally';
import { IDgIntElementDnDTarget, DND_UNUSED_ID, LAYOUT_HOME_POSITION, isUnusedId } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { createDefaultElement, generateDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IContentElementInteractiveDiagram } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-config-dnd-tally',
  templateUrl: './dg-int-config-dnd-tally.component.html',
  styleUrls: ['./dg-int-config-dnd-tally.component.scss']
})
export class ElementConfigDgIntDndTallyComponent implements OnInit {
  @Input() element: IDgIntElementDnDTally;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChildren('targets') targets: QueryList<ElementRef>;
  @ViewChildren('options') options: QueryList<ElementRef>;

  twiddleConfig = new TwiddleState(false);
  optionsImageTwiddleConfig : TwiddleState[];
  
  isShowAnswerForm = new FormControl(false);
  homePositionForm = new FormControl(LAYOUT_HOME_POSITION.BOTTOM)
  homePositionOptions = [
    {id: LAYOUT_HOME_POSITION.LEFT, caption:'Left'},
    {id: LAYOUT_HOME_POSITION.RIGHT, caption:'Right'},
    {id: LAYOUT_HOME_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_HOME_POSITION.BOTTOM, caption:'Bottom'},
  ]
  
  public targetForms : {
    label: FormControl,
    targetValue: FormControl,
    isShowLabel: FormControl,
  }[] = [];
  public optionForms : {
    label: FormControl,
    value: FormControl,
  }[] = [];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private specialKeyboard: SpecialKeyboardService
  ) { }
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    const configFormControls = [
      {f: this.homePositionForm, p:'homePosition'},
    ];
    bindFormControls(this.element.config, configFormControls);
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  initForms() {
    this.initTargetsForm();
    this.initOptionsForm();
    if (this.isUseImage()) this.initOptionsTwiddle();
  }
  
  initOptionsTwiddle() {
    // dont create a new twiddle state if it already exists and has the same length
    if (this.optionsImageTwiddleConfig && this.optionsImageTwiddleConfig.length === this.element.options.length) return;
    let optionTwiddleStates = [];
    for (let option of this.element.options){
      const url = option.image?.url;
      const initVal = url ? false : true;
      const twiddleState = new TwiddleState(initVal);
      optionTwiddleStates.push(twiddleState);
    }
    this.optionsImageTwiddleConfig = optionTwiddleStates;
  }
  initOptionsForm(){
    let optionForms = [];
    for (let option of this.element.options){
      const labelForm = new FormControl(option.label);
      const valueForm = new FormControl(option.value);
      labelForm.valueChanges.subscribe(obs => { option.label = labelForm.value; });
      valueForm.valueChanges.subscribe(obs => { option.value = valueForm.value; });
      optionForms.push({label: labelForm, value: valueForm});
    }
    this.optionForms = optionForms;
  }
  
  initTargetsForm(){
    let targetForms = [];
    for (let target of this.element.targets){
      const labelForm = new FormControl(target.label);
      const targetValueForm = new FormControl(target.targetValue);
      const isShowLabelForm = new FormControl(target.isShowLabel);
      labelForm.valueChanges.subscribe(obs => { target.label = labelForm.value; });
      targetValueForm.valueChanges.subscribe(obs => { target.targetValue = targetValueForm.value; });
      isShowLabelForm.valueChanges.subscribe(obs => { target.isShowLabel = isShowLabelForm.value; });
      targetForms.push({label: labelForm, targetValue: targetValueForm, isShowLabel: isShowLabelForm});
    }
    this.targetForms = targetForms;
  }
  
  elementEditDeleteOption(optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    this.element.options.splice(optionIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddOption(){
    this.element.options.push({
      label: "",
      value: 0,
      ...generateDefaultElement(ElementType.TEXT),
    });
    this.validateConfig();
  }
  
  elementEditDeleteTarget(targetIndex: number) {
    const targetLabel = this.element.targets[targetIndex].label;
    const confirm = window.confirm(`Are you sure you want to delete this target? (${targetLabel})`);
    if (!confirm) return;
    this.element.targets.splice(targetIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddTarget(){
    this.element.targets.push({
      label: "",
      targetValue: 0,
      ...generateDefaultElement(ElementType.TEXT),
    });
    this.validateConfig();
  }
  
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  
  dropTargetOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.targets, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  dropOptionOrdering(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.options, event.previousIndex, event.currentIndex);
    if (this.isUseImage) moveItemInArray(this.optionsImageTwiddleConfig, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to images mode? (this will remove your labels)");
      if (!confirm) return;
      this.element.config.isUseImage = true;
      // delete all labels
      for (let option of this.element.options) option.label = "";
    } else {
      const confirm = window.confirm("Are you sure you want to change to text mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let option of this.element.options) delete option.image;
    }
    this.validateConfig();
  }
  
  isUseImage() : boolean {
    return this.element.config.isUseImage ?? false;
  }
  getImageElement(optionIndex : number) : IContentElementImage {
    const option = this.element.options[optionIndex];
    if (option?.image == undefined) {
      option.image = createDefaultElement(ElementType.IMAGE)
      option.image.scale = 25;
    }
    return option.image;
  }
  
  validateConfig() {
    this.resolveData();
    this.initForms();
    this.rerender();
  }
  rerender() {
    this.parentElement._changeCounter++;
  }
  resolveData() {
    resolveDnDTally(this.element);
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {}

    this.element.targets.forEach((target, idx) => {
      elements[`target_${idx}`] = this.targets.toArray()[idx];
    })

    this.element.options.forEach((option, idx) => {
      elements[`option_${idx}`] = this.options.toArray()[idx];
    })

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
}
