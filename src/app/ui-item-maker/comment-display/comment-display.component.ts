import { Component, OnInit, Input, Output, EventEmitter, OnChanges} from '@angular/core';
import { AuthService } from '../../api/auth.service';
import {IItemAuthNote, IItemAuthNoteFile} from '../element-notes/element-notes.component';
import { AssignedUsersService } from '../assigned-users.service';
import { RoutesService } from 'src/app/api/routes.service';
import {EditingDisabledService} from "../editing-disabled.service";
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { LangService } from 'src/app/core/lang.service';
import { UserRoles } from 'src/app/api/models/roles';
import { MyCtrlOrgService } from '../../ui-testctrl/my-ctrl-org.service';
import { ENoteItemType } from '../element-notes/types';
import {ItemBankCtrl} from "../item-set-editor/controllers/item-bank";
import { HighlighterService } from './../../ui-testrunner/highlighter.service';
import { ItemComponentEditService } from './../item-component-edit.service';
import { Subscription } from 'rxjs';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {CommentModalType} from "./../element-notes/element-notes.component"
import { LoginGuardService } from 'src/app/api/login-guard.service';
import {NUM_FIRST_REPLIES_EXPANDED,MAX_NESTING_DEPTH, HIGHLIGHT_COMMENT_TEMP_BLOCKED } from './../element-notes/constants'

@Component({
  selector: 'comment-display',
  templateUrl: './comment-display.component.html',
  styleUrls: ['./comment-display.component.scss']
})
export class CommentDisplayComponent implements OnInit {

  @Input() comment: IItemAuthNote;
  @Input() isSub: boolean;
  @Input() itemId: number;
  @Input() showResolved: boolean;
  @Input() delEnabled: boolean;
  @Input() groupId: number;
  @Input() singleGroupId?: number;
  @Input() resolveAllTrigger: number;
  @Input() isGraphicRequestIsSignedOff: boolean;
  @Input() isPinnedDisplay: boolean; //for future use
  @Input() isZoomView: boolean = false;
  
  @Input() isExpanded: boolean;
  @Input() nestingVisibleDepth: number;

  @Output() assignHandler = new EventEmitter();
  @Output() unassignHandler = new EventEmitter();
  @Output() refreshPinned = new EventEmitter();
  @Output() refreshCount = new EventEmitter();
  @Output() exportSingleComment = new EventEmitter();
  @Output() graphicReqRemoveSignedOff = new EventEmitter();

  @Output() nestedZoomTrigger = new EventEmitter();

  subscription = new Subscription();

  NOTES_FILE_ENDPOINT = 'public/test-auth/note-files';
  canExportComment: boolean = false;
  isSelectedHighlight: boolean = false;
  CommentModalType = CommentModalType;
  pageModal: PageModalController;
  newCommentText: string;
  newCommentUploads: IItemAuthNoteFile[] = []

  HIGHLIGHT_COMMENT_TEMP_BLOCKED = HIGHLIGHT_COMMENT_TEMP_BLOCKED;

  constructor(
    private auth:AuthService,
    private assignedUsersService:AssignedUsersService,
    private routes:RoutesService,
    private editingDisabled: EditingDisabledService,
    private authScopeSettings: AuthScopeSettingsService,
    private myCtrlOrg: MyCtrlOrgService,
    private lang: LangService,
    private itemBankCtrl: ItemBankCtrl,
    public highlighter: HighlighterService,
    private itemComponentEdit: ItemComponentEditService,
    private pageModalService: PageModalService,
    private  loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.canExportComment = this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_COMMENT_IMPORT);
    if (this.isHighlightComment()) this.setupHighlightComment();
  }

  ngOnChanges(change) {
    // In graphic requests, signing off should resolve all comments that belong to the request
    if (change.resolveAllTrigger && !change.resolveAllTrigger.firstChange){
      if (!this.comment.is_resolved) this.resolveNote()
    }
  }

  setupHighlightComment(){
    if (this.comment.question_config_map) {
      const {entryId, prop, start, end} = this.comment.question_config_map;
      const entryIdElem = this.itemComponentEdit.deepFind(this.itemComponentEdit.originalQuestionState.content, 'entryId', entryId);
      const rawTargetString = entryIdElem[prop]
      const highlightHtml = this.highlighter.splitHtmlOnHighlight({rawString: rawTargetString, startRaw: start, endRaw: end}).highlight
      this.comment.highlightRef = {
        entryId,
        prop,
        selection: {start, end, highlightHtml}
      }
    }
    // If anything other than this comment is highlighted, unmark this comment as highlighted
    this.subscription.add(this.highlighter.applyHighlight.subscribe(newHighlight => {       
      if (newHighlight?.noteId !== this.comment.id) {
        this.isSelectedHighlight = false;
      }
    }))

    // When trying to change highlight interval and a user window selection is processed, proceed with it if it applies to this comment
    this.subscription.add(this.highlighter.newValidSelectedText.subscribe(validSelection => {
      if (validSelection && !this.highlighter.isHighlightCommentButtonActive && this.highlighter.changeIntervalAttemptNoteId == this.comment.id){
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('auth_highlight_change_interval_confirm'),
          confirm: () => {
            this.highlighter.changeHighlightInterval(this.comment.id, validSelection)
            this.highlighter.changeIntervalAttemptNoteId = undefined;
          },
          close: () => this.highlighter.changeIntervalAttemptNoteId = undefined
      });
      }
    }))
  }

  canEditComment(){
    return this.isCurrentUserAuthor() || this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_COMMENT_EDIT);
  }

  /** Changing interval is only permitted on interval-based highlight comments and only the comment's author can do it*/
  canChangeHighlightInterval(){
    return this.isHighlightComment() && this.isCurrentUserAuthor()
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
 

  genParams() {
    return {query: {
      item_id: this.comment.item_id,
      note_item_type: this.comment.item_type
    }}
  }

  handleNestedZoomTargetDel(){
    // If going one level up will not reach the very top level, zoom in on the parent of the deleted comment
    if(this.comment.parent?.parent) {
      this.setCommentAsNestedZoom(this.comment.parent)
    }
    // Otherwise, exit zoom view 
    else {
      this.nestedZoomTrigger.emit();
    }
  }

  resolveNote(isDelete: boolean = false) {
    const newResolved = this.comment.is_resolved ? 0 : 1;
    const data = isDelete ? {
      is_deleted: 1
    } : {
      is_resolved: newResolved
    };
    this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, this.comment.id, data, this.genParams())
        .then(_ => {
          // If deleting the top/zoomed comment in zoom view, go one level higher
          if(this.isZoomView && isDelete && this.nestingVisibleDepth == 0) {
            this.handleNestedZoomTargetDel();
          }
          if(newResolved == 1 || isDelete) {
            this.resolveNoteAndChildren(this.comment, isDelete);
          } else {
            this.unresolveNoteAndParents(this.comment);
            this.unresolveNoteAndChildren(this.comment)
          }
          //Otherwise, this was already done 
          if(this.shouldRefreshPins(this.comment)) {
            this.refreshPins();
          }
          this.refreshCountOfNotes();
        })
        .catch(e => {
          console.error(e);
        });
  }

  shouldRefreshPins(note: IItemAuthNote) {
    return this.isChildOfPinned(note);
  }

  isChildOfPinned(note: IItemAuthNote) {

    if(!note) {
      return false;
    }

    if(note.is_pinned) {
      return true;
    } 

    return this.isChildOfPinned(note.parent);
  }
  
  resolveNoteAndChildren(note: IItemAuthNote, isDelete: boolean) {
    const prop = isDelete ? 'is_deleted' : 'is_resolved'; //Just for temporary deletion, on refresh will no longer be loaded.
    note[prop] = 1;
    note.sub?.forEach(n => {
      this.resolveNoteAndChildren(n, isDelete);
    });
  }

  unresolveNoteAndChildren(note: IItemAuthNote){
    note.is_resolved = 0;
    note.sub?.forEach(n => {
      this.unresolveNoteAndChildren(n);
    });
  }
  
  unresolveNoteAndParents(note: IItemAuthNote) {
    note.is_resolved = 0;
    if(note.parent) {
      this.unresolveNoteAndParents(note.parent);
    }
  }

  assignHandlerPass($event) {
      this.assignHandler.emit($event);
  }

  assignComment() {
      this.assignHandler.emit(this.comment);
  }

  getAssignedUserName(short: boolean) { 
    const assignedUser = this.assignedUsersService.getAssignedUser(this.comment.id);
    if(assignedUser ) {
      return short ? assignedUser.shortName : assignedUser.name;
    } 
    return "";
  }

  getAssignedAccessLevelName(short: boolean) {
    const assignedAccessLevel = this.assignedUsersService.getAssignedAccessLevel(this.comment.id);
    if(assignedAccessLevel) {
      return this.assignedUsersService.getAccessLevelCaption(assignedAccessLevel, short);
    }
    return "";
  }
  
  async unassignComment() {
    await this.assignedUsersService.unassignUser(this.comment.id)
    this.unassignHandler.emit();
  }

  resolveComment() {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('comment_res_conf'),
      confirm: () => this.resolveNote(false)
    })
  }

  /** First get confirmation to unresolve, then handle special case inside signed-off graphic requests if needed or proceed to unresolve */
  unresolveComment() {
    
    const doUnresolve = () => {
      if (this.comment.item_type === ENoteItemType.GRAPHIC_REQUEST && this.isGraphicRequestIsSignedOff) {
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('auth_graphic_req_unresolve_comment_confirm'),
          confirm: () => {
            this.graphicReqRemoveSignedOff.emit()
            setTimeout(() => this.resolveNote())
          }
        })
      } 
      else this.resolveNote();
    }

    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('comment_unres_conf'),
      confirm: () => {
        setTimeout(doUnresolve)
      }
    })
  }

  /**
   * @param author 
   * @param text 
   * @param isRemoveSignOffOnSubmit - for comments inside graphic request, whether should signal up to remove sign off from request
   */
  respondToComment(author, text:string, uploads:IItemAuthNoteFile[], isRemoveSignOffOnSubmit?:boolean) {
    if (!text) return;
    const data: IItemAuthNote = {
      text,
      parent_note_id: this.comment.id,
      item_id: this.comment.item_id,
      // Highlight comments can only receive regular replies
      item_type: this.comment.item_type == ENoteItemType.HIGHLIGHT ? ENoteItemType.QUESTION : this.comment.item_type,
      graphic_request_id: this.comment.graphic_request_id,
      has_child: 0,
      is_resolved: 0,
      assigned_uid: null,
      created_by_first_name: author.firstName, // get user info from auth in API.
      created_by_last_name: author.lastName,
      created_by_uid: author.uid,
    };

    this.auth.apiCreate(this.routes.TEST_AUTH_NOTES, {itemNote: data}, {query: {group_id: this.groupId}})
        .then(result => {
          if (!this.comment.sub) {
            this.comment.sub = [];
          }

          const newComment:IItemAuthNote = {
            ...result,
            parent: this.comment,
            isNewlyAdded: true
          }

          // Save any uploads added for the new comment to the db
          this.saveNewCommentUploads(newComment, uploads)
          .then(() => {
            // Add new comment to the reply list
            this.comment.sub.push(newComment);
            // Receiving a reply should unresolve this comment (and its parents and other children)
            if (this.comment.is_resolved) this.resolveNote(false);
            if (isRemoveSignOffOnSubmit) this.graphicReqRemoveSignedOff.emit();
  
            //If adding a new reply will make the comment tree view deeper than allowed, zoom in on the current comment after replying
            if (this.nestingVisibleDepth + 1 > MAX_NESTING_DEPTH){
              this.setCommentAsNestedZoom(this.comment)
            }
          })

        })
        .catch(e => {
          console.error(e);
        });

    if (!this.comment.has_child) {
      const patchData = {has_child: 1};
      this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, this.comment.id, patchData, this.genParams())
        .then(() => this.refreshPins());
    }

    if(this.shouldRefreshPins(this.comment)) {
      this.refreshPins();
    }
  }

  /** Save any uploads for the newly created comment */
  async saveNewCommentUploads(newComment:IItemAuthNote, uploads: IItemAuthNoteFile[]){
    newComment.uploads = [];

    for (let upload of uploads){
      if(!upload.file_url) return;
      const newUpload: IItemAuthNoteFile = {
        ...upload,
        test_question_auth_note_id: newComment.id,
      };
      const savedUpload = await this.auth.apiCreate(this.routes.TEST_AUTH_NOTE_FILES, newUpload)
      newComment.uploads.push(savedUpload)
    }

  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  startEditCommentModal(){
    this.newCommentText = this.comment.text;
    const config = {};
    this.pageModal.newModal({
      type: CommentModalType.COMMENT_EDIT,
      config,
      cancel: () => {
      },
      finish: () => {
        this.editComment(this.newCommentText)
      }
    })
  }

  /** Handle special case if replying inside a signed-off graphic request, otherwise proceed normally */
  initReplyComment(){
    if (this.comment.item_type === ENoteItemType.GRAPHIC_REQUEST && this.isGraphicRequestIsSignedOff) {
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('auth_graphic_req_new_comment_confirm'),
        confirm: () => {
          setTimeout(() => this.startReplyCommentModal(true))
        }
      })
    }
    else this.startReplyCommentModal();
  }

  startReplyCommentModal(isRemoveSignOffOnSubmit?:boolean){
    this.newCommentUploads = [];
    this.newCommentText = "";
    const config = {};
    this.pageModal.newModal({
      type: CommentModalType.NEW_REPLY_COMMENT,
      config,
      cancel: () => {
      },
      finish: () => {
        const author = this.auth.user().getValue();
        this.respondToComment(author, this.newCommentText, this.newCommentUploads, isRemoveSignOffOnSubmit);
      }
    })
  }

  // Edit comment...
  editComment(newText:string) {
    this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, this.comment.id, {text: newText}, this.genParams()).then((res) => {
      this.comment.text = newText;
      this.comment.is_text_edited = 1;
    })
  }

  exportComment(comment) {
    this.exportSingleComment.emit(comment);
  }

  insertUpload() {
    if (!this.comment.uploads) {
      this.comment.uploads = [];
    }
    // @ts-ignore
    this.comment.uploads.push({});
  }

  insertNewCommentUploads(){
    // @ts-ignore
    this.newCommentUploads.push({})
  }

  getUploads(): IItemAuthNoteFile[] {
    return this.comment.uploads || [];
  }

  isAssigned() {
    return this.isAssignedToUser() || this.isAssignedToAccessLevel();
  }

  isAssignedToUser() {
    return this.assignedUsersService.isAssignedToUser(this.comment.id);
  }

  isAssignedToAccessLevel() {
    return this.assignedUsersService.isAssignedToAccessLevel(this.comment.id);
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  deleteComment() {
    // If comment was selected to highlight the config, unhilight if it's being deleted
    if (this.isSelectedHighlight) this.unhighlightSource();
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('comment_del_conf'),
      confirm: () => this.resolveNote(true)
    })
  }

  isCommentViewable() {
    return this.authScopeSettings.isCommentViewable(this.comment)
  }

  isShared() {
    return this.authScopeSettings.isShared(this.comment)
  }

  isDelEnabled() {
    if(this.delEnabled != null) {
      return this.delEnabled
    }
    return this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_COMMENT_DEL);
  }

  isPinEnabled() {
    return !this.comment.parent_note_id;
  }

  isShareEnabled() {
    return this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_COMMENT_SEE_ALL);
  }

  isTopLevel() {
    return !this.comment.parent_note_id;
  }

  isCurrentUserAuthor(){
    return this.comment.created_by_uid == this.auth.user().getValue().uid
  }


  shareOrUnshareComment() {
      let shared = 1;
      let msg = this.lang.tra("bc_share_comment_msg")
      if (this.comment.is_shared) {
        shared = 0;
        msg = this.lang.tra("bc_unshare_comment_msg")
      }

      this.loginGuard.confirmationReqActivate({
        caption: msg,
        confirm: () => {
          this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, this.comment.id, {is_shared: shared}, this.genParams()).then((res)=>{
            this.comment.is_shared = shared
          })
        }
      })
  }


  togglePinComment() {
    const newPinned = this.comment.is_pinned ? 0 : 1;
    const doToggle = () => {
      this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, this.comment.id, {is_pinned: newPinned}, this.genParams())
      .then(() => {
        this.comment.is_pinned = newPinned;
        this.refreshPins(); //unpinning on pinned section should also remove the comment from its own section.
      })
    }
    if (newPinned) doToggle();
    else {
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('comment_unpin_conf'),
        confirm: doToggle
      })
    }
  }


  refreshPins() {
    this.refreshPinned.emit();
  }

  refreshCountOfNotes() {
    this.refreshCount.emit();
  }

  isHighlightComment(){
    return this.comment.item_type == ENoteItemType.HIGHLIGHT;
  }

  isGraphicReqComment(){
    return this.comment.item_type == ENoteItemType.GRAPHIC_REQUEST;
  }

  //* Continuously get info on how the comment currently maps to the text, and apply the highlight if the comment is selected for this */
  highlightSource(){
    this.isSelectedHighlight = true;
    const commentDetail = this.highlighter.getCommentDetailById(this.comment.id)
    if (!commentDetail) return;
    const targets = commentDetail
    .filter(d => !d.isInvalid)
    .map(d => {
      const {entryId, prop, start, end, isWhole, isPassage} = d;
      return {entryId, prop, selection: {start, end, isWhole, isPassage}}
    })
    this.highlighter.initApplyHighlight({
      targets, 
      noteId: this.comment.id, 
      isResolved: !!this.comment.is_resolved,
      targetLanguage: this.lang.getCurrentLanguage()
    })
  }

  unhighlightSource(){
    this.highlighter.initApplyHighlight({});
  }

  toggleHighlightSource(){
    if (!this.isSelectedHighlight) this.highlightSource()
    else this.unhighlightSource();
  }

  disableCommentSubmit(){
    if (!this.newCommentText) return true;
    // If editing, require a change to the comment
    if (this.cModal().type == CommentModalType.COMMENT_EDIT) return this.comment.text === this.newCommentText
    else return false;
  }

  /** Based on comment Id, find where it maps to. Returns undefined if no longer valid. */
  getHighlightSelection(){
    // If the comment maps to an image, return that with an annotation
    const highlightImage = this.highlighter.getTargetImageByCommentId(this.comment.id);
    if (highlightImage) return {isImage: true, content: highlightImage}
    // Otherwise compile text targets
    const highlightHtml = this.highlighter.getHighlightHtmlByCommentId(this.comment.id);
    if (highlightHtml) return {content: highlightHtml}
  }

  initChangeHighlightInterval(){
    if (this.highlighter.changeIntervalAttemptNoteId !== this.comment.id)  {
      this.highlighter.changeIntervalAttemptNoteId = this.comment.id;
      // Turn off activation from the new highlight comment button so that active buttons don't compete
      this.highlighter.isHighlightCommentButtonActive = false;
    }
    else this.highlighter.changeIntervalAttemptNoteId = undefined;
    this.highlighter.initHighlightCommentAttempt();
  }

  isReplyExpanded(comment: IItemAuthNote, replyIndex:number):boolean{
    return comment.isNewlyAdded || replyIndex < NUM_FIRST_REPLIES_EXPANDED;
  }

  /** Show 'Read More' if comment is at the maximum visible depth, and has replies to be seen (not deleted and not resolved if resolved comments are hidden) */
  showReadMoreReplies(){
    return (
      (this.nestingVisibleDepth == MAX_NESTING_DEPTH) && 
      this.comment.sub?.filter((reply:IItemAuthNote) => !reply.is_deleted && (this.showResolved || !reply.is_resolved)).length
    )
  }

  /**
   * Zoom in on some comment to be displayed as the top level with it replies under, and nothing else
   * @param targetComment - The comment to be seen at the top level 
   */
  setCommentAsNestedZoom(targetComment:IItemAuthNote){
    this.nestedZoomTrigger.emit(targetComment);
  }

}
