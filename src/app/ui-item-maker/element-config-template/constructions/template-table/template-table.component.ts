import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { mcqStyleSub, StyleprofileService } from 'src/app/core/styleprofile.service';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement, generateDefaultElementTextLink } from 'src/app/ui-item-maker/item-set-editor/models';
import { IContentElementInput } from 'src/app/ui-testrunner/element-render-input/model';
import { IContentElementMcq, IContentElementMcqOption, McqDisplay } from 'src/app/ui-testrunner/element-render-mcq/model';
import { IContentElementTable, IContentElementTableCell } from 'src/app/ui-testrunner/element-render-table/model';
import { TemplateConfigDefRow } from 'src/app/ui-testrunner/element-render-template/model';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { ElementType } from 'src/app/ui-testrunner/models';

const RESTRICTED = 'Restricted';

@Component({
  selector: 'template-table',
  templateUrl: './template-table.component.html',
  styleUrls: ['./template-table.component.scss']
})
export class TemplateTableComponent implements OnInit {
  
  @Input() config: TemplateConfigDefRow;
  @Input() elementRef: string;
  @Input() tableElement: IContentElementTable;
  @Input() hasValidator: boolean = false;
  @Input() twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  @Input() isNonInteractive: boolean = false;
  @Input() allowBookmarkLink: boolean = true;
  @Input() mcqOptionNumber: number = -1;
  @Input() isStaticElementOnly: boolean = false;
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onValidatorAlign = new EventEmitter<any>();
  @Output() onAssignEntryId = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  @Output() onTwiddleToggle = new EventEmitter<any>();
  
  elementTypes = ElementType;
  tableElements: {id: string, label: string}[];
  
  tableElementsInteractive: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'dropdown_text', label: 'Dropdown Text'},
    {id: 'dropdown_math', label: 'Dropdown Math'},
    {id: 'dropdown_advanced_inline', label: 'Dropdown - Advanced Inline'},
    {id: 'vertical_text', label: 'Vertical MCQ - Text'},
    {id: 'vertical_math', label: 'Vertical MCQ - Math'},
    {id: 'vertical_advanced_inline', label: 'Vertical MCQ - Advanced Inline'},
    {id: 'vertical_advanced_text_bullet', label: 'Vertical MCQ - Advanced Text (Bullet)'},
    {id: 'advanced_inline', label: 'Advanced Inline'},
    {id: 'keyboard_input', label: 'Keyboard Input'}
  ]
  tableElementsNonInteractive: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'advanced_inline', label: 'Advanced Inline'},
  ]
  // alignment options
  public alignments = [
    {id:'left', icon:'fa-align-left'},
    {id:'center', icon:'fa-align-center'},
    {id:'right', icon:'fa-align-right'},
  ]

  public vertical_alignments = [
    {id:'top', icon:'fa fa-arrow-up'},
    {id:'middle', icon:'fas fa-arrows-alt-v'},
    {id:'bottom', icon:'fa fa-arrow-down'},
  ]

  constructor(
    private styleProfileService: StyleprofileService,
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
    this.tableElements = this.isNonInteractive ? this.tableElementsNonInteractive : this.tableElementsInteractive;
    
    if (this.tableElement && this.tableElement.grid) {
      this.tableElement.grid.forEach(row => {
        row.forEach(cell => {
          if (!cell.elementType) {
            const oldVal = cell.val;
            const textElement = createDefaultElement(ElementType.TABLE_TEXT);
            this.objectClearAndAssign(cell, textElement);
            cell.val = oldVal;
          }
        });
      });
    }    
  }
  
  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }
  onImageUploadEmit() {
    this.onImageUploaded.next();
  }
  onValidatorAlignEmit() {
    this.onValidatorAlign.next();
  }
  onAssignEntryIdEmit() {
    this.onAssignEntryId.next();
  }
  onTwiddleToggleEmit(elementRef: string, key: string, id: number, toggle: boolean) {
    this.onTwiddleToggle.next({elementRef, key, id, toggle});
  }  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  getMcqOptionNumber() {
    if (this.mcqOptionNumber === -1) {
      return "";
    } else {
      return `_${this.mcqOptionNumber}`
    }
  }
  
  tableDeleteRow(element: IContentElementTable, index:number){
    if (this.isReadOnly()) return;
    if(element.grid.length < 2) {
      alert("Table must have at least one row.");
      return;
    }
    if (confirm('Are you sure you want to remove the row?')){
      const affectedMergedCellGroupIds = [];
      const cellsToBeRemoved = element.grid[index];

      // cellsToBeRemoved.forEach(cellToRemove => {
      //   if (cellToRemove.mergedCellId && !affectedMergedCellGroupIds.includes(cellToRemove.mergedCellId)){
      //     affectedMergedCellGroupIds.push(cellToRemove.mergedCellId);
      //   }
      // })

      affectedMergedCellGroupIds.forEach(groupId => {
        const cells = this.tableFindMergedCellGroup(element, groupId);
        cells.forEach(cell => {
          cell.mergedCellRowSpan -= 1
        })
      })

      element.grid.splice(index, 1);
    }
    this.tableCleanUpMergedCellGroup(element);
    
    if(this.hasValidator) {
      this.onValidatorAlignEmit();
    }
  }

  tableDeleteCol(element: IContentElementTable, index:number){
    if (this.isReadOnly()) return;
    if(element.grid[0].length < 2) {
      alert("Table must have at least one column.");
      return;
    }
    if (confirm('Are you sure you want to remove the column?')){
      const affectedMergedCellGroupIds = [];
      element.grid.forEach((row, i) => {

        // Collect merged cell groups that are affected by the deletion
        const cellToRemove = this.tableGetCell(element, i, index);
        if (cellToRemove.mergedCellId && !affectedMergedCellGroupIds.includes(cellToRemove.mergedCellId)){
          affectedMergedCellGroupIds.push(cellToRemove.mergedCellId);
        }

        row.splice(index, 1);
      })

      affectedMergedCellGroupIds.forEach(groupId => {
        const cells = this.tableFindMergedCellGroup(element, groupId);
        cells.forEach(cell => {
          cell.mergedCellColSpan -= 1
        })
      })
    }
    this.tableCleanUpMergedCellGroup(element);
    
    if(this.hasValidator) {
      this.onValidatorAlignEmit();
    }
  }

  tableAddRow(element: any){
    const numCols = element.grid.length ? element.grid[0].length : 0;
    const modelRow = [];
    for (let i=0; i<numCols; i++){
      const cell = <IContentElementTableCell>createDefaultElement(ElementType.TABLE_TEXT);
      modelRow.push(cell);
    }
    element.grid.push(modelRow);

    if(this.hasValidator) {
      this.onValidatorAlignEmit();
    }
  }

  tableAddCol(element: any){
    element.grid.forEach((inputRow, i) => {
      const cell = <IContentElementTableCell>createDefaultElement(ElementType.TABLE_TEXT);
      element.grid[i].push(cell);
    })

    if(this.hasValidator) {
      this.onValidatorAlignEmit();
    }
  }
  
  getRowNumber(row_i: number) {
    return `Row ${row_i + 1}`
  }
  
  getColNumber(col_i: number) {
    return `Column ${col_i + 1}`;
  }

  tableGetCell(element: any, row_i:number, col_i:number){
    return element.grid[row_i][col_i];
  }

  tableGetCellRowSpan(element: any, row_i: number, col_i: number){
    const cell = this.tableGetCell(element, row_i, col_i);
    if (cell.mergedCellId){
        return cell.mergedCellRowSpan + 1;
    }
    return 1;
  }

  tableGetCellColSpan(element: any, row_i: number, col_i: number){
    const cell = this.tableGetCell(element, row_i, col_i);
    if (cell.mergedCellId){
        return cell.mergedCellColSpan + 1;
    }
    return 1;
  }

  tableFindMergedCellGroup(element: any, targetMergedCellId: number) {
    const cells = [];
    element.grid.forEach((row, row_i) => {
      row.forEach((column, col_i) => {
        const cell = this.tableGetCell(element, row_i, col_i)
        if (cell.mergedCellId === targetMergedCellId) cells.push(cell);
      })
    })
    return cells;
  }

  tableClearMergedCellDataInCellElement(elementCell: IContentElementTableCell){
    // elementCell.mergedCellId = undefined;
    // elementCell.mergedCellTopLeft = undefined;
    // elementCell.mergedCellBottomRight = undefined;
    // elementCell.mergedCellRowSpan = undefined;
    // elementCell.mergedCellColSpan = undefined;
  }

  tableCleanUpMergedCellGroup(element: any){
    const mergeCellGroupIds = new Map<number, IContentElementTableCell[]>();
    element.grid.forEach((row, row_i) => {
      row.forEach((column, col_i) => {
        const cell = this.tableGetCell(element, row_i, col_i)
        if (cell.mergedCellId !== undefined){
          const mapEntryValue = mergeCellGroupIds.get(cell.mergedCellId);
          if (mapEntryValue) mapEntryValue.push(cell);
          else mergeCellGroupIds.set(cell.mergedCellId, [cell]);
        }
      })
    })

    const mergedGroupsWithOnlyOneCell = Array.from(mergeCellGroupIds.values()).filter((cells) => cells.length === 1);
    mergedGroupsWithOnlyOneCell.forEach(cell => {
      this.tableClearMergedCellDataInCellElement(cell[0]);
    })
  }

  getTableIndividualColWidthConstArray(element: IContentElementTable){
    // const colCount = element.grid?.[0]?.length ?? 0;
    // if (element.individualColWidthConst == undefined){
    //   element.individualColWidthConst = [];
    // }
    // element.individualColWidthConst.length = colCount;
    // return element.individualColWidthConst;
  }
  
  getTableElement(element: IContentElementMcq) {
    if(element.elementType == 'math') {
      return 'math';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.CUSTOM_DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'dropdown_advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'vertical_advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'bullet') {
      return 'vertical_advanced_text_bullet';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT) {
      return 'dropdown_text';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.CUSTOM_DROPDOWN && element.options[0].elementType == this.elementTypes.MATH) {
      return 'dropdown_math';
    } else if(element.elementType == 'text' && element.paragraphStyle == TextParagraphStyle.ADVANCED_INLINE) {
      return 'advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT) { 
      return 'vertical_text';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.MATH) { 
      return 'vertical_math';
    } else if (element.elementType == 'text') {
      return 'text';
    } else if (element.elementType == 'input') {
      return 'keyboard_input';
    } else if(element.elementType === 'image') {
      return 'image';
    }

    return 'text';
  }
  
  onTableElementChange(newElement: string, cell: any) {
    const align = cell.align;
    if(newElement == 'dropdown_text') {
      const mcqDropdownText: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqDropdownText.displayStyle = McqDisplay.DROPDOWN
      // mcqDropdownText.isDropdownFullWidth = true;
      this.objectClearAndAssign(cell, mcqDropdownText);
    } else if(newElement == 'dropdown_math') {
      const mcqDropdownMath: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqDropdownMath.displayStyle = McqDisplay.CUSTOM_DROPDOWN
      // mcqDropdownMath.isDropdownFullWidth = true;
      mcqDropdownMath.options = [
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()},
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:false, optionId: -1, link: generateDefaultElementTextLink()},
      ];
      mcqDropdownMath.options.push()
      this.objectClearAndAssign(cell, mcqDropdownMath)
    } else if (newElement === 'dropdown_advanced_inline') {
      // Dropdown needs to be custom since default cannot handle advanced inline
      const mcqDropdownAdvancedInline: IContentElementMcq = this.buildAdvancedInlineMCQ(McqDisplay.CUSTOM_DROPDOWN);
      // mcqDropdownAdvancedInline.isDropdownFullWidth = true;
      this.setMcqStyle(mcqDropdownAdvancedInline);
      this.objectClearAndAssign(cell, mcqDropdownAdvancedInline);
    } else if (newElement == 'text') {
      const textElement = createDefaultElement(ElementType.TABLE_TEXT);
      this.objectClearAndAssign(cell, textElement);
    } else if(newElement == 'advanced_inline') {
      const advCell = this.createDefaultInlineCell();
      advCell.advancedList = [this.createDefaultInlineCell()];
      this.objectClearAndAssign(cell, advCell);
    } else if (newElement === 'vertical_text') {
      const mcqVertText: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      this.setMcqStyle(mcqVertText);
      this.objectClearAndAssign(cell, mcqVertText);
    } else if (newElement === 'vertical_math') {
      const mcqVertMath: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqVertMath.options = [
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()},
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:false, optionId: -1, link: generateDefaultElementTextLink()},
      ];
      this.setMcqStyle(mcqVertMath);
      this.objectClearAndAssign(cell, mcqVertMath);
    } else if (newElement === 'vertical_advanced_inline') {
      const mcqVerticalAdvancedInline: IContentElementMcq = this.buildAdvancedInlineMCQ(McqDisplay.VERTICAL);
      this.setMcqStyle(mcqVerticalAdvancedInline);
      this.objectClearAndAssign(cell, mcqVerticalAdvancedInline);
    } else if (newElement === 'vertical_advanced_text_bullet') {
      const mcqVerticalAdvancedInline: IContentElementMcq = this.buildAdvancedInlineMCQ(McqDisplay.VERTICAL, 4, TextParagraphStyle.BULLET, true, true);
      this.setMcqStyle(mcqVerticalAdvancedInline);
      this.objectClearAndAssign(cell, mcqVerticalAdvancedInline);
    } else if(newElement == 'math') {
      // cell.advancedList.push(createDefaultElement(ElementType.MATH));
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.MATH));
    } else if(newElement == 'keyboard_input') {
      const inputElement: IContentElementInput = createDefaultElement(ElementType.INPUT) as IContentElementInput;
      inputElement.inputNumFormat = RESTRICTED;
      this.objectClearAndAssign(cell, inputElement);
    }

    // The alignment should remain the same when element type is changed
    cell.align = align;
    cell.alignment = align;
    if (cell.elementType === ElementType.TEXT && cell.paragraphStyle === TextParagraphStyle.ADVANCED_INLINE) {
      cell.advancedList.forEach(sub_text => {
        sub_text.alignment = align;
      })
    }

    if(this.hasValidator) {
      this.onValidatorAlignEmit();
    }

  }

  objectClearAndAssign(target: any, source: any) {
    for (let key in target) {
      if (target.hasOwnProperty(key)) delete target[key];
    }
    return Object.assign(target, source);
  }
  
  /*
    Build an mcq element with options of type paragraphStyle.
    Customize display style, number of options, whether radio
    and label is disabled or not.
  */
  buildAdvancedInlineMCQ(
    displayStyle: McqDisplay,
    numOptions: number = 4,
    paragraphStyle: TextParagraphStyle = TextParagraphStyle.ADVANCED_INLINE,
    isRadioDisabled: boolean = false,
    isOptionLabelsDisabled: boolean = false
  ): IContentElementMcq {
    const advancedInlineMcqElement: IContentElementMcq = <IContentElementMcq> createDefaultElement(ElementType.MCQ);
    advancedInlineMcqElement.displayStyle = displayStyle;
    advancedInlineMcqElement.isRadioDisabled = isRadioDisabled;
    advancedInlineMcqElement.isOptionLabelsDisabled = isOptionLabelsDisabled;
    advancedInlineMcqElement.options = [];
    for (let i = 0; i < numOptions; i++) {
      advancedInlineMcqElement.options.push(
        {...this.createDefaultInlineCell(paragraphStyle), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()}
      );
    }
    advancedInlineMcqElement.options.push();
    return advancedInlineMcqElement;
  }
  
  setMcqStyle(mcqElement: IContentElementMcq) {
    const defaultStyle = this.styleProfileService.getDefaultMcqStyles();
    const style = defaultStyle?.[0]
    if (!style) return;
    const displayStyle: mcqStyleSub = style[mcqElement.displayStyle];
    if (displayStyle) this.setObjectStyle(mcqElement, displayStyle);
  }
  
  createDefaultInlineCell = (paragraphStyle: TextParagraphStyle = TextParagraphStyle.ADVANCED_INLINE) => {
    const cell:IContentElementText = <IContentElementText>createDefaultElement(ElementType.TEXT);
    cell.paragraphStyle = paragraphStyle;
    cell.advancedList = [];
    cell.advancedList.push(createDefaultElement(ElementType.TEXT));

    return cell;
  }
  
  setObjectStyle(obj: any, style : any){
    for (let key in style) obj[key] = style[key];
  }
  
  getTwiddleToggleInfo(elementRef: string, key: string, id: number) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      this.twiddleToggleMap.get(elementRef).set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).get(key).has(id)) {
      this.twiddleToggleMap.get(elementRef).get(key).set(id, false);
    }

    return this.twiddleToggleMap.get(elementRef).get(key).get(id);
  }
  
  isElementAdvancedInline(paragraphStyle) {
    return [TextParagraphStyle.ADVANCED_INLINE, TextParagraphStyle.BULLET].includes(paragraphStyle);
  }
  
  toggleValue(obj: any, key: string) {
    obj[key] = !obj[key];
  }
  
  insertMCQEntry(
    options:any[],
    elementType: ElementType | string,
    paragraphStyle: TextParagraphStyle | string = TextParagraphStyle.REGULAR) {
    let content:any = '';
    const optionElement:IContentElementMcqOption = {
      ... createDefaultElement(elementType),
      elementType,
      optionType: ElementType.MCQ_OPTION,
      content,
      isCorrect: false,
      optionId: -1,
      link: generateDefaultElementTextLink()
    };

    if (paragraphStyle === TextParagraphStyle.ADVANCED_INLINE) {
      optionElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    } else if (paragraphStyle === TextParagraphStyle.BULLET) {
      optionElement.paragraphStyle = TextParagraphStyle.BULLET;
    } else {
      delete optionElement.paragraphStyle;
    }

    options.push(optionElement)
  }
  
  getMCQOptionLabel(index: number) {
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if(index >= alphabet.length) {
      return 'Index out of bounds.';
    }

    return alphabet[index];
  }
  
  getMcqTypeFromOption(mcq: IContentElementMcq) {
    const firstOption = mcq.options?.[0];
    if (firstOption == undefined) return 'text';
    
    if (firstOption.elementType == 'text') {
      if (firstOption.paragraphStyle == 'advanced-inline' || firstOption.paragraphStyle == 'bullet') {
        return 'advanced-inline';
      } else {
        return 'text';
      }
    } else {
      return firstOption.elementType;
    }
  }
  
  /*
    Align all elememts in column col_i to alignId
  */
  setColAlignment(element, col_i: number, alignId: string) {
    element.grid.forEach((row: any) => {
      row[col_i].align = alignId;
      row[col_i].alignment = alignId;
      if (row[col_i].elementType === ElementType.TEXT && row[col_i].paragraphStyle === TextParagraphStyle.ADVANCED_INLINE) {
        row[col_i].advancedList.forEach(sub_text => {
          sub_text.alignment = alignId;
        })
      }
    })
  }
  /*
    Vertically align all elememts in column col_i to alignId
  */
  setColVerticalAlignment(element, col_i, alignId) {
    element.grid.forEach((row: any) => {
      row[col_i].alignVertical = alignId;
    })
  }
}
