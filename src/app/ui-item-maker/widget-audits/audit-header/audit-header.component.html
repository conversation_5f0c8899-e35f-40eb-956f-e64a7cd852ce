<div (click)="onToggleExpanded()" class="audit-row">
  <div class="audit-header">
    <h3>
      <tra [slug]="caption"></tra>
    </h3>
    <ng-container *ngIf="isExpanded.value && !!auditStatus">
      <audit-filter></audit-filter>
    </ng-container>
  </div>
  &nbsp;
  <span class="audit-header-actions">
    <div class="audit-status-msg">
      <ng-container *ngIf="auditCtrl.runningItemLevelAuditId || auditCtrl.runningFrameworkLevelAuditId || auditCtrl.retrievingAuditLogs">
        <tra [slug]="auditCtrl.retrievingAuditLogs ? 'ie_audit_loading_item_logs' : 'ie_audit_running_item_audits'"></tra>
      </ng-container>
      <ng-container *ngIf="!auditCtrl.retrievingAuditLogs && auditCtrl.lastItemLevelRunBlocked">
        <tra style="color: red;" slug="ie_item_audit_run_blocked"></tra>
      </ng-container>
    </div>

    <widget-framework-status
      [auditCtrl]="auditCtrl"
      [auditStatus]="auditStatus"
      [filterControl]="filterControl.bind(this)"
      [showDate]="showDate"
    ></widget-framework-status>

    <button
      class="button is-small is-info refresh-button"
      *ngIf="refresh"
      (click)="onRefresh($event)"
      [disabled]="auditCtrl.retrievingAuditLogs || auditCtrl.runningItemLevelAuditId || auditCtrl.runningFrameworkLevelAuditId || readOnly"
    >
      <ng-container [ngSwitch]="!!auditCtrl.retrievingAuditLogs || !!auditCtrl.runningItemLevelAuditId || !!auditCtrl.runningFrameworkLevelAuditId">
        <i *ngSwitchCase="true" class="fa fa-spinner fa-spin" aria-hidden="true"></i>
        <i *ngSwitchDefault class="fa fa-sync" aria-hidden="true"></i>
      </ng-container>
    </button>

    <i *ngIf="!isExpanded.value" class="fa fa-chevron-right" aria-hidden="true"></i>
    <i *ngIf="isExpanded.value" class="fa fa-chevron-down" aria-hidden="true"></i>
  </span>
  <hr *ngIf="isExpanded.value">
</div>

