import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ItemBankAuditor } from '../../item-set-editor/controllers/audits';
import { AuditConfigs, AuditMetaRecord, AuditQuestionScope, AuditTarget, AuditType, IAuditConfig, PANEL_AUDIT_TYPE_TO_TARGET_DICT } from '../data/audits';
import { AssetLibraryCtrl } from '../../item-set-editor/controllers/asset-library';
import { EditingDisabledService } from '../../editing-disabled.service';
import { AuditFilterOptions, AuditFilterService } from '../../services/audit-filter.service';
import { AuditCacheService } from '../../services/audit-cache.service';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { skip } from 'rxjs/operators';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { TestFormConstructionMethod } from '../../item-set-editor/models/assessment-framework';
import { LoginGuardService } from 'src/app/api/login-guard.service';

export const auditTypeWithoutSummary = new Set(["PUBLISH"]);
@Component({
  selector: 'audit-table',
  templateUrl: './audit-table.component.html',
  styleUrls: ['./audit-table.component.scss'],
  providers: [AuditFilterService]
})
export class AuditTableComponent implements OnInit, OnDestroy {
  
  @Input() auditCtrl:ItemBankAuditor;
  @Input() assetLibraryCtrl:AssetLibraryCtrl;
  @Input() auditType: AuditType;
  @Input() audits:IAuditConfig[];
  @Input() caption: string;
  @Input() twiddleState =  new FormControl(false);
  @Input() auditCacheService: AuditCacheService;

  auditScopeDictionary: { [key in AuditQuestionScope]: string } = {
    [AuditQuestionScope.ITEMS_SCORED]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SCORED_MINUS_FT]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_HUMAN_SCORED]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SCORED_MINUS_HS]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SURVEY]: 'audit_assessment',
    [AuditQuestionScope.SCREENS]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_NONSCORED]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_PASSAGES]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_NON_PASSAGES]: 'audit_assessment',
    [AuditQuestionScope.ITEM_BANK]: 'ie_audit_item_bank',
    [AuditQuestionScope.ITEM_BANK_SCORED]: 'ie_audit_item_bank',
    [AuditQuestionScope.SCREENS_INCLUDE_DISABLED]: 'audit_assessment_disabled',
    [AuditQuestionScope.PASSAGE_ITEMS]: 'audit_assessment',
    [AuditQuestionScope.ITEM_BANK_PASSAGES]: 'ie_audit_item_bank',
    [AuditQuestionScope.NONE]: 'audit_assessment'
  }; // slugs for translation
  auditCount = 0;
  autoFixSet = new Set();;
  AuditTarget = AuditTarget;
  private subscription: Subscription = new Subscription();
  pageModal: PageModalController;
  runAsync: boolean = true;
  auditLocalToggle: {[key: string]: any} = {}; // primarly used for diff check so that we can toggle by lang

  constructor(
    private editingDisabled: EditingDisabledService,
    private pageModalService: PageModalService,
    public auditFilterService: AuditFilterService,
    private loginGuard: LoginGuardService
  ) { }

  cModal() { return this.pageModal?.getCurrentModal(); }

  ngOnInit(): void {
    this.setAuditAutoFixSet();
    if(!auditTypeWithoutSummary.has(this.auditType)){
      this.initAuditFilterService();
      this.subscription.add(this.auditCtrl.auditRanSub.subscribe((auditSlug)=>{ // on completion of audit
        this.updateFilters(auditSlug);
      }));
      this.subscription.add(this.auditCtrl.bulkAuditCompleted.subscribe(ran =>{
        if(ran){
          this.pageModal?.closeModal();
        }
      }));
    }
    this.subscription.add(this.twiddleState.valueChanges.pipe(skip(1)).subscribe((value) => {
      if(!value)this.auditFilterService.resetFilters();
    }));
    this.pageModal = this.pageModalService.defineNewPageModal();
    
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    
  }

  get auditTypeWithoutSummary() {
    return auditTypeWithoutSummary
  }

  get filterControl() {
    return this.auditFilterService.filterControl
  }

  get hiddenAudits() {
    return this.auditFilterService.hiddenAudits
  }

  get showIssues() {
    return this.isFilterActive(AuditFilterOptions.ISSUES);
  }

  getIssueColor(auditSlug:string) {
    if(this.showIssues && this.getNumberOfIssues(auditSlug)){
      return 'red'
    } else if (!this.showIssues) {
      return 'dimgrey'
    } else {
      return 'initial'
    }
  }

  isFilterActive(filterType:AuditFilterOptions){
    return this.auditFilterService.isFilterActive(filterType)
  }


  setAuditAutoFixSet(){
    this.audits.forEach(audit =>{
      audit.checks?.forEach(c =>{
        if(c.autoFix){
          this.autoFixSet.add(c.id)
        }
      })
    })
  }

  initAuditFilterService(){
    if (this.auditCacheService) {
      // Use cached filter control for this audit type
      const cachedFilterControl = this.auditCacheService.getFilterControl(this.auditType);
      this.auditFilterService.filterControl = cachedFilterControl;
    } else {
      // Fallback to original method if cache service is not available
      this.auditFilterService.initAuditFilterService(this.audits, this.auditCtrl);
      return;
    }
    
    // Initialize the service with audits and controller
    this.auditFilterService.audits = this.audits;
    this.auditFilterService.auditCtrl = this.auditCtrl;
    
    // Ensure filter options are properly set (framework level, not item level)
    this.auditFilterService.filterOption.splice(0, this.auditFilterService.filterOption.length);
    this.auditFilterService.filterOption.push(
      AuditFilterOptions.INFORMATIVE,
      AuditFilterOptions.PASSED,
      AuditFilterOptions.ISSUES,
      AuditFilterOptions.NOT_RAN
    );
    
    // Update filters with the current state
    this.auditFilterService.updateFilters();
  }

  updateFilters(auditSlug?:string | boolean) {
    this.auditFilterService.updateFilters(auditSlug)
  }

  getNumberOfIssues(slug:string){
    return this.auditCtrl.getAuditLog(slug)?.num_issues ?? 0
  }
  
  activateAuditItems(auditSlug:string, resultId:string, auditTarget:AuditTarget){
    const currentItemMemId = this.auditCtrl.activeAuditItemMemId
    // Clear previous values
    this.clearPreviousListedValue();
    // if clicking on same item list clear and return
    const slug = this.renderAuditResultSlug(auditSlug, resultId);
    if(currentItemMemId === slug){
      // this is technically being done in clearPreviousListedValue but adding here to be explicit
      this.auditCtrl.activeAuditItemMemId = null;
      return
    }
    try{
    switch(auditTarget){
      case AuditTarget.MSCAT_PANELS:
      case AuditTarget.TLOFT_PANELS:
      case AuditTarget.LINEAR_SECTIONS:
        this.auditCtrl.activateAuditPanels(slug, auditTarget);
        break;
      case AuditTarget.PANELS:
        this.auditCtrl.activateAuditPanels(slug, PANEL_AUDIT_TYPE_TO_TARGET_DICT[this.auditCtrl.frameworkCtrl.asmtFmrk.testFormType])
        break;
      case AuditTarget.QUESTIONS:
      default:
        this.auditCtrl.activateAuditQuestions(slug);
        break;
      }
    } catch(e){
      this.auditCtrl.auditItemListingMode = null;
      this.clearPreviousListedValue();
      this.loginGuard.quickPopup("Flagged items could not be shown please try re-running audits and try again");
    }
  }

  clearPreviousListedValue(){
    this.auditCtrl.clearAuditQuestions();
    this.auditCtrl.clearAuditPanels();
    this.auditCtrl.auditItemListingMode = null;
  }

  checkActiveQuestionMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.checkActiveQuestionMem(slug)
  }
  renderAuditResultSlug(auditSlug:string, resultId:string){
    return auditSlug+'_'+resultId
  }
  canFixAuditQuestions(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)

  }

  getAuditQMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.auditQuestionMem[slug]
  }
  canSelectAuditQuestions(auditSlug:string, resultId:string){
    const auditQMem = this.getAuditQMem(auditSlug, resultId);
    const audit = this.auditCtrl.audits.find(audit => audit.slug === auditSlug);
    if(audit.auditTarget === AuditTarget.PANELS && this.auditCtrl.FrameworkConstructionType === TestFormConstructionMethod.LINEAR){
      return false;
    }
    return (auditQMem && auditQMem.length > 0)
  }

  canAutoFix(auditSlug:string){
    const checks = this.audits.find(audit => audit.slug === auditSlug)?.checks || [];
    for (const check of checks) {
      if (check.autoFix && this.canSelectAuditQuestions(auditSlug, check.id)) {
      return true;
      }
    }
    return false;
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);
  resetDiffLangs(auditSlug: string) {
    this.auditLocalToggle[`${auditSlug}_isDiffEn`] = false;
    this.auditLocalToggle[`${auditSlug}_isDiffFr`] = false;
  }
  
  toggleBothLangs(auditSlug: string) {
    this.resetDiffLangs(auditSlug);
  }

  toggleDiff(auditSlug: string, lang: "en" | "fr") {
    const key = auditSlug + "_" + (lang === "en" ? 'isDiffEn' : 'isDiffFr' );
    const updatedVal = !this.auditLocalToggle[key];
    this.resetDiffLangs(auditSlug);
    this.auditLocalToggle[key] = updatedVal;
  }

  get auditTypeSummary(){
    return auditTypeWithoutSummary.has(this.auditType) ? null : this.auditCtrl.frameworkAuditQuestionMem?.[this.auditType];
  }

  getAuditNumFlaggedItems(slug: string){
    return this.auditCtrl.auditQuestionMem[slug]?.items?.length ?? 0
  }

  get getOpenRunAllAuditsModal(){
    return !auditTypeWithoutSummary.has(this.auditType) ? () => this.openRunAllAuditsModal() : null
  }

  openRunAllAuditsModal(){
    this.pageModal.newModal({
      type: "RUN_FRAMEWORK_AUDITS",
      config: null,
      finish: null
    });
  }

  runAllAudits(){
    this.auditCtrl.runAllFrameworkLevelAudits(this.runAsync, undefined, this.auditType)
  }

  getAuditHideSameContent(auditSlug: string) {
    return this.auditLocalToggle[auditSlug + '_hideSameContent'];
  }

  getAuditRanValue(auditSlug: string, key: string) {

    return this.auditLocalToggle[auditSlug + '_' + key];
  }

  toggleIsAuditHideSameContent(auditSlug: string){
    this.auditLocalToggle[auditSlug + '_hideSameContent'] = !this.getAuditRanValue(auditSlug, 'hideSameContent');
  }

  shouldShowDiffLang(auditSlug: string, lang: string) {
    const isDiffEn = this.getAuditRanValue(auditSlug, 'isDiffEn');
    const isDiffFr = this.getAuditRanValue(auditSlug, 'isDiffFr');
    return (
      (!isDiffEn && !isDiffFr) ||
      (isDiffEn && lang === 'en') ||
      (isDiffFr && lang === 'fr')
    );
  }

}
