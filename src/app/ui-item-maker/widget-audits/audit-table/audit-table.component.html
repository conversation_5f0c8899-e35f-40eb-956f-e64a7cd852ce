<audit-header
[auditCtrl]="auditCtrl"
[isExpanded]="twiddleState"
[caption]="caption"
[auditStatus]="auditTypeSummary"
[refresh]="getOpenRunAllAuditsModal"
[readOnly]="isReadOnly()"
>
</audit-header>
<table class="audit-table">
<!-- Brought over from ABED before each audit was hard coded now they are generated programatically -->
  <ng-container *ngIf="twiddleState.value">
    <ng-container *ngFor="let audit of audits; let idx = index">
      <tr *ngIf="!audit.isHidden && !hiddenAudits[audit.slug]" [class.is-disabled]="audit.isDisabled">
  
        <th class="audit-table-col-num"> {{idx + 1}} </th>
        <th class="audit-table-col-name"> 
          {{audit.caption}} 
          <p *ngIf="auditCtrl.auditsWithError[audit.slug]" style="color: red; font-size: x-small;">Error Running
        </th>
        <th class="audit-table-col-btn">
          <div class="audit-table-run-audit-buttons">
            <button
              *ngFor="let scope of audit.itemScope"
              (click)="auditCtrl.runCustomAudit(audit.slug, scope, audit.isLangSensitive, audit.auditTarget)" 
              class="button is-small is-light"
              [class.is-info]="auditCtrl.auditsRunning[audit.slug]"
              [disabled]="audit.isDisabled || (auditCtrl.auditsRan[audit.slug] && auditCtrl.getAuditLog(audit.slug))|| isReadOnly() || auditCtrl.auditsRunning[audit.slug]" 
            > <tra [slug]="auditScopeDictionary[scope]"></tra> </button>
          </div>
        </th>
        <th style="border-width: 1px; width: 18em">
          <span style="display: flex; flex-direction: column;">
            <span *ngIf="auditCtrl.getAuditLog(audit.slug)">
              <table class="audit-log-list">
                <tr [style.color]="(auditCtrl.getAuditLog(audit.slug).is_new) ? 'green' : 'initial'">
                  <th><tra slug="pre_publish_audits_audited_on"></tra></th> 
                  <td>
                    {{auditCtrl.getAuditLog(audit.slug).audited_on | date:'short'}}
                  </td>
                </tr>
                <tr>
                  <th><tra slug="pre_publish_audits_audited_by"></tra></th>
                  <td>
                    {{auditCtrl.getAuditLog(audit.slug)?.name}}<br>
                    <a [href]="'mailto:'+ auditCtrl.getAuditLog(audit.slug)?.email">
                      {{auditCtrl.getAuditLog(audit.slug)?.email}}
                    </a>
                  </td>
                </tr>
                <tr [style.color]="getIssueColor(audit.slug)">
                  <th><tra slug="pre_publish_audits_number_of_issues"></tra></th> 
                  <td>{{getNumberOfIssues(audit.slug)}}</td>
                </tr>
              </table>
            </span>
            <span *ngIf="!auditCtrl.getAuditLog(audit.slug) && !auditCtrl.retrievingAuditLogs">
              <b><tra slug="pre_publish_audit_no_logs_found"></tra></b>
            </span>
            <span *ngIf="!auditCtrl.getAuditLog(audit.slug) && auditCtrl.retrievingAuditLogs">
              <b><i class="fa fa-spinner fa-spin" aria-hidden="true"></i></b>
            </span>
            <span *ngIf="auditCtrl.auditsRan[audit.slug]  && !auditCtrl.retrievingAuditLogs && auditCtrl.getAuditLog(audit.slug) " style="display: flex; justify-content: center;">
              <button  class="button is-small is-danger" (click)="auditCtrl.refreshAudit([audit.slug])" [disabled]="isReadOnly()" > 
                <span><tra slug="ie_refresh"></tra></span>
              </button>
            </span>
          </span>
        </th>
        <td class="audit-table-col-result">
          <button class="button is-small" *ngIf="auditCtrl.getAuditLog(audit.slug) && !auditCtrl.auditsRan[audit.slug]" (click)="auditCtrl.auditsRan[audit.slug] = true">
            <tra slug="pre_publish_audits_toggle_history"></tra>
          </button>
          <span *ngIf="auditCtrl.auditsRan[audit.slug] && auditCtrl.getAuditLog(audit.slug)">
            <p *ngIf="showIssues" style="padding-left: 1em; font-weight: bold;">
              <tra slug="pre_publish_audits_audited_on"></tra>&nbsp;{{auditCtrl.getAuditLog(audit.slug).audited_on | date:'short'}}
            </p>
            <p *ngIf="showIssues" style="padding-left: 1em;" [ngStyle]="{'color': getNumberOfIssues(audit.slug) ? 'red' : 'initial'}">
              <tra slug="ie_issues_detected"></tra>: {{getNumberOfIssues(audit.slug)}}&nbsp;
            </p>
            <ng-container *ngIf="audit.autoFixes && getNumberOfIssues(audit.slug) && showIssues && canAutoFix(audit.slug)">
              <div style="padding-left: 1em;">
                <button  
                  class="button is-small is-warning has-icon" 
                  (click)="auditCtrl.runCustomCleaningPatch(audit, audit.autoFixes.slug)" 
                  [disabled]="isReadOnly()" 
                > 
                  <span class="icon"><i class="fa fa-wrench" aria-hidden="true"></i> </span>
                  <span><tra [slug]="audit.autoFixes.caption ? audit.autoFixes.caption : 'ie_apply_auto_fix'"></tra></span>
                </button>
                <span *ngIf="auditCtrl.auditQuestionMem[audit.autoFixes.slug]">
                  <tra slug="ie_applying_auto_fix"></tra> {{auditCtrl.auditQuestionMem[audit.autoFixes.slug].i}} / {{auditCtrl.auditQuestionMem[audit.autoFixes.slug].n}}
                </span>
              </div>
            </ng-container>
    
            <ul *ngIf="!audit.isCustomResult">
              <ng-container *ngFor="let result of auditCtrl.auditQuestionMem[auditCtrl.getAuditResultSlug(audit.slug)]">
                <li *ngIf="!hiddenAudits[result.id]">
                  <button 
                    class="button is-small has-icon" 
                    (click)="activateAuditItems(audit.slug, result.id, audit.auditTarget)" 
                    [class.is-info]="checkActiveQuestionMem(audit.slug, result.id)"
                    style="vertical-align: middle;"
                    [disabled]="!canSelectAuditQuestions(audit.slug,result.id)"
                  >
                    <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                    <span>{{result.items.length}}
                      <ng-container [ngSwitch]="audit.auditTarget">
                        <ng-container *ngSwitchCase="AuditTarget.MSCAT_PANELS">
                          <tra slug="ie_panel"></tra> 
                        </ng-container>
                        <ng-container *ngSwitchCase="AuditTarget.TLOFT_PANELS">
                          <tra slug="ie_panel"></tra> 
                        </ng-container>
                        <ng-container *ngSwitchCase="AuditTarget.LINEAR_SECTIONS">
                          <tra slug="ie_panel"></tra> 
                        </ng-container>
                        <ng-container *ngSwitchCase="AuditTarget.PANELS">
                          <tra slug="ie_panel"></tra> 
                        </ng-container>
                        <ng-container *ngSwitchDefault>
                          <tra slug="ie_questions"></tra> 
                        </ng-container>
                      </ng-container>
                    </span>
                  </button>
                  <span [style]="result.items.length && !result.informativeOnly? 'color: red': ''">
                    {{result.caption}}
                  </span>
                  <i *ngIf="autoFixSet.has(result.id)" class="fa fa-wrench" aria-hidden="true" style="margin-left:1em;"></i>
                </li>
              </ng-container>
            </ul>
    
            <ng-container *ngIf="audit.isCustomResult" >
              <div *ngIf="audit.isContentDiff">
                <div style="margin-left: 1em">
                  <h4 style="margin-bottom: 0;">{{auditCtrl.auditResultHeader[audit.slug]}}</h4>
                  <label style="display: flex; gap:0.2em"> Only show different content:
                    <!-- todo: consider attaching this model to a different object -->
                    <input  type="checkbox"  (click)="toggleIsAuditHideSameContent(audit.slug)" [checked]="getAuditHideSameContent(audit.slug)">
                  </label>
                </div>
                <ng-container *ngIf="auditCtrl.auditQuestionMem[audit.slug]?.length && audit.slug == 'CONTENT_DIFF_AUDIT'">
                  <div>
                    <button class="button is-small" [class.is-info]="!getAuditRanValue(audit.slug, 'isDiffEn') && !getAuditRanValue(audit.slug, 'isDiffFr')" (click)="toggleBothLangs(audit.slug)">EN | FR</button>
                    <button class="button is-small" [class.is-info]="getAuditRanValue(audit.slug, 'isDiffEn')" (click)="toggleDiff(audit.slug, 'en')">EN</button>
                    <button class="button is-small" [class.is-info]="getAuditRanValue(audit.slug, 'isDiffFr')" (click)="toggleDiff(audit.slug, 'fr')">FR</button>
                  </div>
                </ng-container>
                <ul>
                  <div *ngFor="let qDiff of auditCtrl.auditQuestionMem[audit.slug]" style="margin-bottom: 0.5em;">
                    <ng-container *ngIf="(getAuditHideSameContent(audit.slug) ? qDiff.hasDiff : true) && shouldShowDiffLang(audit.slug, qDiff.lang)">
                      <widget-item-diff
                        *ngIf="audit.slug == 'CONTENT_DIFF_AUDIT'"
                        [questionId]= "qDiff.questionId"
                        [questionLabel] = "qDiff.questionLabel"
                        [description] = "qDiff.description"
                        [content] = "qDiff.content"
                        [style] = "qDiff.style"
                        [currentContent]="qDiff.currentContent"
                        [previousContent]="qDiff.previousContent"
                        [itemBankCtrl]="auditCtrl.itemBankCtrl"
                        [lang] = "qDiff.lang"
                      >
                      </widget-item-diff>
                    <!-- 18 has not yet been updated, preserving old view -->
                      <expansion-panel
                        *ngIf="audit.slug != 'CONTENT_DIFF_AUDIT'"
                        [title]= "qDiff.title"
                        [subTitle] = "qDiff.subTitle"
                        [description] = "qDiff.description"
                        [content] = "qDiff.content"
                        [style] = "qDiff.style"
                      ></expansion-panel>
                    </ng-container>
                  </div>
                </ul>
              </div>
    
              <ng-container [ngSwitch]="audit.slug">
                <div *ngSwitchCase="'COMMENTS'">
                  <ul>
                    <li>
                      <div style="display: flex; align-items: center">
                        <button 
                        [class.is-info]="auditCtrl.checkActiveQuestionMem('COMMENTS_QS_W_OUTSTANDING_CMTS')"
                        (click)="auditCtrl.activateAuditQuestions('COMMENTS_QS_W_OUTSTANDING_CMTS')"
                        class="button is-small has-icon" 
                        [disabled]="!canSelectAuditQuestions(audit.slug, 'QS_W_OUTSTANDING_CMTS') || isReadOnly()"
                        >
                          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                          <span>{{getAuditQMem(audit.slug, 'QS_W_OUTSTANDING_CMTS').length || 0}} items</span>
                        </button>  
                        <span [ngStyle]="{'color': canSelectAuditQuestions(audit.slug, 'QS_W_OUTSTANDING_CMTS') ? 'red' : 'initial'}">have outstanding comments</span>
                      </div>
                    </li>
                    <li>
                      <div style="display: flex; align-items: center">
                        <button 
                        class="button is-small has-icon" 
                        (click)="assetLibraryCtrl.openAssetLibraryToIds(auditCtrl.auditQuestionMem['COMMENTS_ASSETS_W_OUTSTANDING_CMTS'])"
                        [disabled]="!canSelectAuditQuestions(audit.slug, 'ASSETS_W_OUTSTANDING_CMTS') || isReadOnly()"
                        >
                          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
                          <span>{{getAuditQMem(audit.slug, 'ASSETS_W_OUTSTANDING_CMTS').length || 0}} assets</span>
                        </button>  
                        <span [ngStyle]="{'color': canSelectAuditQuestions(audit.slug, 'ASSETS_W_OUTSTANDING_CMTS') ? 'red' : 'initial'}" >have outstanding comments</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </ng-container>
    
            </ng-container>
          </span>
        </td>
      </tr>
    </ng-container>
  </ng-container>

</table>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div class="modal-header">
      <tra [slug]="caption"></tra> ({{runAsync? 'All' : 'Quick'}})
    </div>
    <div class="modal-message">
      <div>
        <tra [slug]="runAsync ? 'ie_audit_bulk_run_async' : 'ie_audit_bulk_run_sync'"></tra>
      </div>
      <div style="font-style: italic;">
        <tra slug="lbl_modal_auto_close_completion"></tra>
      </div>
    </div>

    <fieldset class="modal-actions" [disabled]="auditCtrl.runningFrameworkLevelAuditId">
      <div class="audit-options-setting">
        <label class="audit-options-label">
          <input type="checkbox" [(ngModel)]="runAsync" class="audit-options-checkbox" />
          <span><tra slug="lbl_all"></tra></span>
        </label>
      </div>

      <div class="audit-options-buttons">
        <button
          (click)="runAllAudits()"
          class="button is-small has-icon audit-options-run-btn"
          [class.is-info]="auditCtrl.runningFrameworkLevelAuditId"
        >
          <ng-container *ngIf="auditCtrl.runningFrameworkLevelAuditId; else runAuditBtn">
            <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
          </ng-container>
          <ng-template #runAuditBtn>
            <tra slug="ie_run_audit"></tra>
          </ng-template>
        </button>

        <button
          (click)="pageModal.closeModal()"
          class="button is-small is-info audit-options-close-btn"
        >
          <tra slug="btn_close"></tra>
        </button>
      </div>
    </fieldset>
  </div>
</div>