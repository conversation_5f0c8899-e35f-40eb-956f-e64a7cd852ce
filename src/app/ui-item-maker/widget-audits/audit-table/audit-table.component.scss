@import '../../../../styles/partials/_modal.scss';
.text-area-middle * {
    vertical-align: middle;
}

.flex-gap {
    display: inline-flex;
    gap: 1em;
}

.hasBorder {
    border-style: solid;
    border-width: 0.1em;
}

.ml {
    margin-left: 1em;
}

.mb {
    margin-bottom: 0.5em;
}

.audit-log-list {
    border: none;
    margin: 0.4em 0;
    tr{
        display: flex; 
        border: none;
        padding: 0.2em;
        th, td {
            border: none;
            padding: 0;
        }
        th {
            width: 6em;
        }
        td {
            overflow: wrap;
            font-weight: 400;
        }
    }
}
.is-disabled{
    th{
        opacity: 0.2;
    }
}
.audit-table-run-audit-buttons{
    display: flex;
    flex-direction: column;
    button {
        margin-top: 0.5em;
        &:first-child {
            margin-top: 0;
        }}

}

th{
    border-width: 1px;
}

.custom-modal {
    z-index: 100;
}

.modal-contents {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%; /* make sure modal takes full height */
  padding: 1.5em;
  text-align: center; /* center the modal message text */
  height: fit-content;
  width: 25em;
}

.modal-header {
    font-size: larger;
    font-weight: bolder;
    margin-bottom: 1em;
}

.modal-message {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5em;
  line-height: 1.5;
  max-width: 100%; 
  height: fit-content;
  gap: 1.5em;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1em;
  border-top: 1px solid #ddd;
  gap: 1em;
}

.audit-options-setting {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: 0.5em;
}

.audit-options-buttons {
  display: flex;
  gap: 0.5em;
  justify-content: flex-end;
}

.audit-options-label {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.audit-options-checkbox {
  transform: scale(1.1);
}
