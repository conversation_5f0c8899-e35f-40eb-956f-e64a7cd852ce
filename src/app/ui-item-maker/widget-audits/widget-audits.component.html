<ng-container *ngFor="let audit of auditArr; let idx = index">
    <ng-container *ngIf="audit.audits?.length">
        <audit-table
            [auditCtrl]="auditCtrl"
            [assetLibraryCtrl]="assetLibraryCtrl"
            [auditType]="audit.type"
            [audits]="audit.audits"
            [caption]="audit.caption"
            [twiddleState]="audit.formControl"
            [auditCacheService]="auditCacheService"
        ></audit-table>
        <hr>
    </ng-container>
</ng-container>
<div style="margin-top: 2em;" *ngIf="auditCtrl.activeAuditPanelIds">
    <ng-container [ngSwitch]="auditCtrl.auditItemListingMode">
        <widget-mscat-panels
        *ngSwitchCase="AuditTarget.MSCAT_PANELS"
        [previewCtrl]="previewCtrl"
        [panelCtrl]="panelCtrl"
        [printViewCtrl]="printViewCtrl"
        [frameworkCtrl]="frameworkCtrl"
        [showTableOnly]="true"
        [activeIds]="auditCtrl.activeAuditPanelIds"
        ></widget-mscat-panels>
        <widget-assembled-forms
        *ngSwitchCase="AuditTarget.TLOFT_PANELS"
        [previewCtrl]="previewCtrl"
        [panelCtrl]="panelCtrl"
        [printViewCtrl]="printViewCtrl"
        [frameworkCtrl]="frameworkCtrl"
        [showTableOnly]="true"
        [activeIds]="auditCtrl.activeAuditPanelIds"
        ></widget-assembled-forms>
        <widget-linear-form-construction
        *ngSwitchCase="AuditTarget.LINEAR_SECTIONS"
        [previewCtrl]="previewCtrl"
        [printViewCtrl]="printViewCtrl"
        [frameworkCtrl]="frameworkCtrl"
        [saveLoadCtrl]="frameworkCtrl.saveLoadCtrl"
        [itemBankCtrl]="frameworkCtrl.itemBankCtrl"
        [activeIds]="auditCtrl.activeAuditPanelIds"
        [itemEditCtrl]="itemEditCtrl"
        [itemFilterCtrl]="frameworkCtrl.itemFilterCtrl"
        [assetLibraryCtrl]="assetLibraryCtrl"
        [showTableOnly]="true"
        ></widget-linear-form-construction>
    </ng-container>
</div>
