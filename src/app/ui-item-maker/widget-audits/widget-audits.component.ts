import {Component, Input, OnDestroy, OnInit} from '@angular/core';

// app services
import {ItemSetPreviewCtrl} from '../item-set-editor/controllers/preview';
import {AssetLibraryCtrl} from '../item-set-editor/controllers/asset-library';
import {ItemSetFrameworkCtrl} from '../item-set-editor/controllers/framework';
import {ItemBankAuditor} from '../item-set-editor/controllers/audits';
import {ItemEditCtrl} from '../item-set-editor/controllers/item-edit';
import {PanelCtrl} from '../item-set-editor/controllers/mscat';
import {ItemSetPrintViewCtrl} from '../item-set-editor/controllers/print-view';
import {AuditTarget, AuditType, IAuditConfig} from './data/audits';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ItemMakerService } from '../item-maker.service';
import { AuditCacheService } from '../services/audit-cache.service';


// File brought over fully from ABED
@Component({
  selector: 'widget-audits',
  templateUrl: './widget-audits.component.html',
  styleUrls: ['./widget-audits.component.scss']
})
export class WidgetAuditsComponent implements OnInit, OnDestroy {


  @Input() auditCtrl:ItemBankAuditor
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() itemEditCtrl: ItemEditCtrl
  @Input() previewCtrl: ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() initOpen: boolean = true;
  @Input() excludedTypes: AuditType[];

  AuditTarget = AuditTarget;
  auditArr: {caption:string, type:AuditType, audits:IAuditConfig[], formControl: FormControl}[] = [];

  subscription = new Subscription();
  audits: IAuditConfig[];

  constructor(
    private itemMaker: ItemMakerService,
    public auditCacheService: AuditCacheService,
  ) { }

  ngOnInit(): void {
    this.initAuditArray();
    this.initAudits();
    this.auditCtrl.initAuditAutoFixes()
    this.subscription.add(this.itemMaker.assesmentTypePubSub.subscribe(async () =>{
      this.auditCtrl.resetAudits();
      this.initAudits(false);
    }));
    // this.subscription.add(this.auditCtrl.auditsMetadataUpdated.subscribe(()=>{
    //     this.setAudits();
    // }));
  }
  
  ngOnDestroy(): void {
    // this.auditCtrl.clearAuditItemPreview();
    this.auditCtrl.clearSubscriptions();
    this.subscription.unsubscribe();
  }

  async initAudits(initAuditIterator:boolean = true){
    if(initAuditIterator){
      await this.auditCtrl.initAudits();
    } else {
      this.auditCtrl.getAuditLogs();
    }
    this.audits = await this.auditCtrl.getFrameworkLevelAudits();
    this.setAudits()
  }
  initAuditArray(): void {
    const auditTypes = [
      {
        caption: "ie_audit_data_validation",
        type: "DATA" as AuditType
      },
      {
        caption: "ie_audit_content_validation",
        type: "CONTENT" as AuditType
      },
      {
        caption: "ie_audit_adif_validation",
        type: "ADIF" as AuditType
      },
      {
        caption: "ie_audit_content_publish",
        type: "PUBLISH" as AuditType
      },
      {
        caption: "ie_audit_psych_only_validation",
        type: "PSYCH" as AuditType
      }
    ];

    this.auditArr = auditTypes.map(auditType => ({
      caption: auditType.caption,
      type: auditType.type,
      audits: [],
      formControl: new FormControl(this.auditCacheService.getExpandedState(auditType.type))
    }));
  }

  async setAudits(){
    if(this.excludedTypes){
      this.auditArr = this.auditArr.filter(audit => !this.excludedTypes.includes(audit.type));
    }
    for (const audit of this.auditArr) {
      audit.audits = this.audits.filter(({auditType}) => auditType === audit.type);
      // Update cache with audit count
      this.auditCacheService.updateAuditCount(audit.type, audit.audits.length);
      
      // Subscribe to form control changes to save expanded state
      this.subscription.add(
        audit.formControl.valueChanges.subscribe(expanded => {
          this.auditCacheService.setExpandedState(audit.type, expanded);
        })
      );
    }
  }
}
