import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { insertAtCursor, deleteSurrounding, EInsertType, codemirrorInsertAtCursor } from '../../ui-testrunner/element-render-input/util/insert-at-cursor'
import { IContentElementTextParagraph } from 'src/app/ui-testrunner/element-render-text/model';
import { SpecialKeyboardService } from '../special-keyboard.service';
import * as Diff from 'diff';
import { escapeRegEx } from 'src/app/core/styleprofile.service';


export interface SpecialCharacter {
    type: EInsertType,
    beginning?: string,
    end?: string,
    replaceA?: string,
    replaceB?: string,
    caption?: string,
    icon?: string,
    innerHtml?: string,
    isDisabled?: boolean;
}

//This is incase any other element other than passages use codemirror you just add the element type and it should work
const elementUsesCodeMirror = ["passage"]

@Component({
    selector: 'special-character-keyboard',
    templateUrl: './special-character-keyboard.component.html',
    styleUrls: ['./special-character-keyboard.component.scss']
  })
export class SpecialCharacterKeyboardComponent implements OnInit {

    @Input() includeMathChars = false
    @Input() includeTextChars = true
    @ViewChildren('specialkeyboard') htmlComp

    showKeyBoard = false
    currentElement = undefined
    prevTarget = undefined
    currentTarget = undefined
    formControl = new FormControl()
    codeMirror;
    interval;
    showButton
    unfocusCounter=0
    constructor(private specialKeyboard: SpecialKeyboardService) {
        
    }

    ngOnInit(): void {
        if (this.includeMathChars) {
            this.buttons.push({type: EInsertType.BEG_END, beginning: '\\triangle', end: '', icon: '', caption: '△'}),
            this.buttons.push({type: EInsertType.BEG_END, beginning: '\\angle', end: '', icon: '', caption: '∠'})
        }
        if (this.includeTextChars) {
            this.buttons.push({type: EInsertType.BEG_END, beginning: '&lsquo;', end: '&rsquo;', icon: '', innerHtml:"&lsquo;&rsquo;"});
            this.buttons.push({type: EInsertType.BEG_END, beginning: '&ldquo;', end: '&rdquo;', icon: '', innerHtml:"&ldquo;&rdquo;"});
            this.buttons.push({type: EInsertType.BEG_END, beginning: '`', end: '`', icon: '', innerHtml:"x"});
        }
        this.specialKeyboard.currentElement.subscribe((event)=>{
            this.currentElement = event.element;
            this.codeMirror = event?.codeMirror;
            this.formControl = event.formControl;
            if (!event.htmlElement) {
                this.prevTarget;
            }
            this.currentTarget = event.htmlElement
            if (this.currentElement || this.currentTarget) {
                this.showButton = true;
                this.clearCurrTimeout();
            }
        })

        this.interval = setInterval(()=>{
            const currElement = document.activeElement
            let found = false;
            if (currElement==this.currentTarget) {
                found = true
            } else if (this.htmlComp) {
                this.htmlComp._results.forEach((child)=>{
                    if (child.nativeElement == currElement) {
                        found = true
                    }
                })
            }
            if (!found) {
                this.unfocusCounter++;
                if (this.unfocusCounter==2) {
                    this.showButton = false
                }
            } else {
                this.unfocusCounter = 0
            }
            this.showButton = found
        },50)
    }

    ngOnDestroy() {
        clearInterval(this.interval)
    }

    buttons:SpecialCharacter[] = [
        {type: EInsertType.BEG_END, beginning: '<b>', end: '</b>', icon: 'fas fa-bold'},
        {type: EInsertType.BEG_END, beginning: '<i>', end: '</i>', icon: 'fa fa-italic'},
        {type: EInsertType.BEG_END, beginning: '<u>', end: '</u>', icon: 'fas fa-underline'},
        {type: EInsertType.BEG_END, beginning: '<strike>', end: '</strike>', icon: 'fas fa-strikethrough'},
        {type: EInsertType.BEG_END, beginning: '<sup>', end: '</sup>', icon: 'fa fa-superscript'},
        {type: EInsertType.BEG_END, beginning: '<sub>', end: '</sub>', icon: 'fa fa-subscript'},
        {type: EInsertType.BEG_END, beginning: '', end: '&mdash', icon: '', caption: '', innerHtml: '&mdash;'},
        {type: EInsertType.BEG_END, beginning: '', end: '&ndash', icon: '', caption: '', innerHtml: '&ndash;'},
        {type: EInsertType.BEG_END, beginning: '', end: '&deg', icon: '', innerHtml: '&deg'},
        {type: EInsertType.BEG_END, beginning: '', end: '&colon;', icon: '', caption: '', innerHtml: '&colon;'},
        {type: EInsertType.BEG_END, beginning: '', end: '&deg;', icon: '', innerHtml: '&deg'},
        {type: EInsertType.BEG_END, beginning: '« ', end: ' »', innerHtml: '&laquo;&raquo;'},
    ]

    toggleKeyBoard() {
        this.showKeyBoard = !this.showKeyBoard
        setTimeout(()=>{
            if (this.currentTarget) {
                this.currentTarget.focus()
            }
        }, 20)
        
        this.clearCurrTimeout()
    }

    clearCurrTimeout() {
        setTimeout(()=>clearTimeout(this.specialKeyboard.currentTimer), this.specialKeyboard.focusBufferTime/2)
    }

    getButtonColor(button:SpecialCharacter) {
        switch(button.type) {
            case EInsertType?.REPLACE:
                const {value} = insertAtCursor(this.currentTarget, undefined, button);
                if(value !== this.currentTarget.value) {
                    button.isDisabled = false;
                    const diffs = Diff.diffWords(this.currentTarget.value, value);
                    for(const d of diffs) {
                        const regExpA = new RegExp(`(${escapeRegEx(button.replaceA)})+`);
                        const regExpB = new RegExp(`(${escapeRegEx(button.replaceB)})+`);
                        if((d.added && d.value.match(regExpA)) || (d.removed && d.value.match(regExpB))) {
                            return '#cccccc'
                        }
                    }
                } else {
                    button.isDisabled = true;
                }
            case EInsertType?.BEG_END:
                const afterDel = deleteSurrounding(this.currentTarget, button.beginning, button.end)
                if (afterDel && afterDel.val != this.currentTarget.value) {
                    return '#cccccc'
                }
                break;
            default:
                break;
            }
        return '#ffffff'
    }

    addCode(button:SpecialCharacter) {
        if(this.currentTarget) {
            switch(button.type) {
                case EInsertType.REPLACE:
                    const {value, newPos} = insertAtCursor(this.currentTarget, undefined, button);
                    this.currentTarget.setSelectionRange(newPos, newPos);
                    if(this.formControl) {
                        this.formControl.setValue(value);
                    } else if(this.currentElement) {
                        this.currentElement.caption = value;
                    }
                    break;
                case EInsertType.BEG_END:
                    if(elementUsesCodeMirror.includes(this.currentElement.elementType)){
                        codemirrorInsertAtCursor(this.codeMirror, button['beginning'], button['end'])
                    } else {
                        const save = this.currentTarget.value;
                        const valueAfterDeletion = deleteSurrounding(this.currentTarget, button['beginning'], button['end'])
                        let result;
                        let newCursorLocation;
                        if (valueAfterDeletion && valueAfterDeletion.val != save) {
                            result = valueAfterDeletion.val
                            newCursorLocation = valueAfterDeletion.newCursorLoc
                        } else {
                            const {value, newPos} = insertAtCursor(this.currentTarget, undefined, button);
                            result = value;
                            this.currentTarget.setSelectionRange(newPos, newPos);
                        }
                        this.currentTarget.focus()
                        if (this.formControl) {
                            this.formControl.setValue(result)
                        } else if (this.currentElement) {
                            this.currentElement.caption = result
                        }
                        if (newCursorLocation) {
                            this.currentTarget.setSelectionRange(newCursorLocation, newCursorLocation)
                        }
                    }
                    break;
                    default:
                        break;
                }
            }
            this.clearCurrTimeout()
    }

}
