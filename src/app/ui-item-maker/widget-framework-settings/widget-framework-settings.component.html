<div>
  <h3>Item Pool</h3>
  <div>
    <input type="text" class="input" [(ngModel)]="frameworkCtrl.asmtFmrk.itemPool">
  </div>
</div>

<div>
  <h3>Framework Notes</h3>
  <p>You can use this section to leave notes for other authors regarding this framework.</p>
  <div style="margin-bottom:1em;">
    <textarea 
      class="textarea is-small" 
      [(ngModel)]="frameworkCtrl.asmtFmrk.notes"
    ></textarea>
  </div>
  <hr>
</div>

<h3>Parameters</h3>
<!-- <div class="buttons"> -->
  <!-- <button  [disabled]="isReadOnly()" class="button is-info"  [class.is-info]="frameworkCtrl.isAFParamDetailExpanded" (click)="frameworkCtrl.isAFParamDetailExpanded = !frameworkCtrl.isAFParamDetailExpanded">View Parameters/Dimensions</button> -->
  <!-- <button style="margin: 0px;" [disabled]="isReadOnly()" class="button" [class.is-info]="frameworkCtrl.isAFParamDetailOrdering" (click)="frameworkCtrl.isAFParamDetailOrdering = !frameworkCtrl.isAFParamDetailOrdering">Change Order</button> -->
  <!-- <button style="margin: 0px;" class="button" [class.is-info]="frameworkCtrl.isFrameworkParamImportExport" (click)="frameworkCtrl.openFrameworkParamExport()">Import/Export</button> -->
<!-- </div> -->

<!-- <div *ngIf="frameworkCtrl.isAFParamDetailExpanded"> -->
<div>
  <div *ngIf="frameworkCtrl.isFrameworkParamImportExport">
    <p>You can copy the settings from the text area below into the import/export box of another item bank to transfer them over.</p>
    <textarea rows=6 [formControl]="frameworkCtrl.frameworkParamImportExport" class="textarea is-small"></textarea>
    <!-- <button (click)="frameworkCtrl.importFrameworkParams(false)">Import</button>
    <button (click)="frameworkCtrl.importFrameworkParams(true)">Insert New Fields Only</button> -->
    <hr/>
  </div>
  <div style="display: flex;flex-direction: column; gap: 1em;margin-bottom: 1em;">
    <div>
      Assessment Language
    </div>
    <div style="width: 5em;" class="select">
      <select style="width: 100%;" [(ngModel)]="frameworkCtrl.asmtFmrk.assessmentLanguage" (ngModelChange)="onLanguageChange()">
        <option *ngFor="let lang of getLanguages()" [value]="lang">{{lang}}</option>
      </select>
    </div>
    <div>
      Assessment Type Slug
    </div>
    <div class="select" style="width: 12em;">
      <select style="width: 100%;" [(ngModel)]="frameworkCtrl.asmtFmrk.assessmentTypeSlug">
        <option *ngFor="let grade of getGrades()" [value]="grade">{{grade}}</option>
      </select>
    </div>
    <div >
      Assessment Type
    </div>
    <div class="buttons">
      <button *ngFor="let assessment of getAssessmentParameterList()"
        class="button is-small"
        [class.is-info]="isAssessmentParameterToggled(assessment.assessment_slug)"
        (click)="selectAssessmentParameter(assessment)"
        style="margin: 0px;"
      >
        {{assessment.assessment_slug}}
      </button>
    </div>
  </div>
  <div *ngFor="let dim of arrangedDimensions; let dimLevel = index; trackBy: trackByIndex" style="margin-bottom:3em;">
    <h3>
      <span *ngIf="dimLevel == 0"> Standard Dimensions </span>
      <span *ngIf="dimLevel == 1"> Primary Parameters </span>
      <span *ngIf="dimLevel == 2"> Other Parameters </span>
      <!-- <button class="button is-small" style="margin-left:0.5em" (click)="toggleAdvanced">Toggle Advanced</button> -->
    </h3>
    <ol *ngIf="frameworkCtrl.isAFParamDetailOrdering" cdkDropList [cdkDropListDisabled]="isReadOnly()" (cdkDropListDropped)="util.drop(dim, $event)">
      <li *ngFor="let param of dim; let i_param = index;" cdkDrag>
        <span class="tag is-large">{{param.code}} {{param.name}}</span>
      </li>
    </ol>
    <table *ngIf="!frameworkCtrl.isAFParamDetailOrdering && dim && dim.length" class="table" style=" width: auto; ">
      <tr>
        <th style="width:4em">Code</th>
        <th style="width:12em">Parameter</th>
        <th style="width:12em">Parameter Category</th>
        <th style="width:8em">Data Type</th>
        <th style="width: 12em">Configurations</th>
        <th *ngIf="dimLevel != 0" style="width:4em">Edit</th>
        <th *ngIf="dimLevel != 0" style="width:4em">Remove?</th>

      </tr>
      <tr  *ngFor="let param of dim; let i_param = index;"> 
        <td [ngStyle]="{'background-color':param.color}">{{param.code}}</td>
        <td>{{param.name}} <span *ngIf="param.isHidden">(hidden)</span> </td>
        <td>{{param.category}}</td>
        <td>{{param.type}}</td>
        <td>
          <div *ngIf="param && param.config && param.config.tags">
            <a *ngIf="!isConfigExpanded(param.code)" (click)="expandConfig(param.code, true)">Show</a>
            <a *ngIf="isConfigExpanded(param.code)" (click)="expandConfig(param.code, false)">Hide</a>
            <div class="flex-column" style="gap: 0.4em;" *ngIf="isConfigExpanded(param.code)">
              <div *ngFor="let tag of param.config.tags" class="tag-input-row">
                <div class="flex-row" style="gap: 0.25em">
                  <a class="tag-info is-bold" [class.is-error]="!tag.code">{{tag.code || 'Code'}}</a>
                  <a class="tag-info" [class.is-error]="!tag.name">{{tag.name || 'Name/Desc.'}}</a>
                </div>
              </div>
            </div>
          </div>
        </td>
        <td *ngIf="dimLevel != 0">
          <button [disabled]="frameworkCtrl.isPsychoParam(param) && !canEditPsychoParam()" *ngIf="!frameworkCtrl.isParamExpanded(param)" (click)="frameworkCtrl.expandParam(param, true)">Edit</button>
          <framework-dimension-editor *ngIf="frameworkCtrl.isParamExpanded(param)" [param]="param" (remove)="frameworkCtrl.expandParam(param, false)"></framework-dimension-editor>
        </td>
        <td *ngIf="dimLevel != 0"> <button (click)="util.removeArrEl(dim, param)"><tra slug="ie_delete_param"></tra></button> </td>
      </tr>
    </table>
    <div *ngIf="dimLevel != 0">
      <button [disabled]="isReadOnly()" (click)="frameworkCtrl.defineNewParameter(dim)" class="button is-small has-icon">
        <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
        <span>New {{frameworkCtrl.getParamCategNaming(dimLevel)}}</span>
      </button>
    </div>
  </div>
</div>
  
<div *ngIf="frameworkCtrl.isTestformLOFT()">
  <hr/>
  <h3>Section Definitions</h3>
  <div>
    <table class="table is-bordered" style="width: auto;margin-bottom:2em;">
      <tr>
        <th><tra slug="ie_section_number"></tra></th>
        <th><tra slug="ie_description"></tra></th>
        <th><tra slug="ie_preamble"></tra></th>
        <th>Map Meta</th>
        <th>Order By Param</th>
        <th><tra slug="ie_shuffle_order_q"></tra></th>
        <th><tra slug="ie_enable_calculator_q"></tra></th>
        <th>Disable Notepad</th>
        <th><tra slug="ie_enable_formula_sheet_q"></tra></th>
        <th>Skippable Questions?</th>
        <th><tra slug="ie_remove_section"></tra></th>
        <th></th>
      </tr>
      <tr *ngFor="let partition of frameworkCtrl.asmtFmrk.partitions; let index = index">
        <td>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'id')">
            {{partition.id}}
          </a>
        </td>
        <td>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'description')">
            <tra [slug]="partition.description || '--'"></tra>
          </a>
        </td>
        <td>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'preambleQuestionLabel')">
            {{partition.preambleQuestionLabel || '--'}}
          </a>
        </td>
        <td>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'mapMetaItemId')">
            {{partition.mapMetaItemId || '--'}}
          </a>
        </td>
        <td>
          <a [class.no-pointer-events]="isReadOnly()" (click)="util.replaceProp(partition, 'orderByParam')">
            {{partition.orderByParam || '--'}}
          </a>
        </td>
        <td>
          <check-toggle [disabled]="isReadOnly()" [isChecked]="partition.isShuffled" (toggle)="partition.isShuffled = !partition.isShuffled"></check-toggle>
        </td>
        <td>
          <check-toggle [disabled]="isReadOnly()" [isChecked]="partition.isCalculatorEnabled" (toggle)="partition.isCalculatorEnabled = !partition.isCalculatorEnabled"></check-toggle>
        </td>
        <td>
          <check-toggle [disabled]="isReadOnly()" [isChecked]="partition.isNotepadDisabled" (toggle)="partition.isNotepadDisabled = !partition.isNotepadDisabled"></check-toggle>
        </td>
        <td>
          <check-toggle [disabled]="isReadOnly()" [isChecked]="partition.isFormulaSheetEnabled" (toggle)="partition.isFormulaSheetEnabled = !partition.isFormulaSheetEnabled"></check-toggle>
        </td>
        <td>
          <check-toggle [disabled]="isReadOnly()" [isChecked]="partition.areQuestionsSkippable" (toggle)="partition.areQuestionsSkippable = !partition.areQuestionsSkippable"></check-toggle>
        </td>
        <td>
          <button [disabled]="isReadOnly()" class="button is-small is-danger" (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.partitions, partition)">Remove</button>
        </td>
        <td>
          <button class="button is-small" (click)="this.openSectionEditModal.emit(partition)"><i class="fas fa-ellipsis-h"></i></button>
        </td>
      </tr>
    </table>
    <button [disabled]="isReadOnly()" (click)="frameworkCtrl.createNewSection()" class="button has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span><tra slug="ie_new_section"></tra></span>
    </button>
  </div>
</div>
  
<hr/>
<button  [disabled]="isReadOnly()" class="button"  [class.is-info]="frameworkCtrl.isScoringScalesExpanded" (click)="frameworkCtrl.isScoringScalesExpanded = !frameworkCtrl.isScoringScalesExpanded">Show possible scoring scales</button>
<ng-container *ngIf="frameworkCtrl.isScoringScalesExpanded">
  <h3><tra slug="List of all possible human scale for this assessment"></tra></h3>
  <p>Please add assessment tag in Framework Tags. ex. OSSLT, PJ</p>
  <table>
    <tr>
      <th>Id</th>
      <th>Score Profile Id</th>
      <th>Name</th>
      <th>Discription En</th>
      <th>Discription Fr</th>
      <th>Type Slug</th>
    </tr>
    <tr *ngFor="let scale of itemBankCtrl.getAllPossibleScoringScalesForAssessment()">
      <td>{{ scale.id }}</td>
      <td>{{ scale.score_profile_id }}</td>
      <td>{{ scale.short_name }}</td>
      <td>{{ scale.description_en }}</td>
      <td>{{ scale.description_fr }}</td>
      <td>{{ scale.type_slug }}</td>
    </tr>
  </table>

  <h3> Define pairable scales</h3>
  <div>
    <table class="table" style=" width: auto; ">
      <tr>
        <th style="width:12em">scale 1</th>
        <th style="width:12em">scale 2</th>
        <th style="width:4em">Remove?</th>
      </tr>
      <tr  *ngFor="let scales of frameworkCtrl.getPairableHumanScoringScales(); let scalePairIdx = index;"> 
        <td>
          <button mat-button  class="button" [matMenuTriggerFor]="scale1Manu">{{ scales[0].name || 'Select Scale' }}</button>
          <mat-menu #scale1Manu="matMenu">
              <ng-container *ngFor="let sc of itemBankCtrl.getAllPossibleScoringScalesForAssessment()">
                  <button mat-menu-item (click)="frameworkCtrl.updatePairableHumanScoringScales(scalePairIdx, 0, sc)"> {{ itemBankCtrl.getScoringScaleById(sc.id).short_name }}</button>
              </ng-container>
          </mat-menu>
        </td>
        <td>
          <button mat-button  class="button" [matMenuTriggerFor]="scale2Manu">{{ scales[1].name || 'Select Scale' }}</button>
          <mat-menu #scale2Manu="matMenu">
              <ng-container *ngFor="let sc of itemBankCtrl.getAllPossibleScoringScalesForAssessment()">
                  <button mat-menu-item (click)="frameworkCtrl.updatePairableHumanScoringScales(scalePairIdx, 1, sc)"> {{ itemBankCtrl.getScoringScaleById(sc.id).short_name }}</button>
              </ng-container>
          </mat-menu>
        </td>
        <td> <button (click)="util.removeArrEl(frameworkCtrl.getPairableHumanScoringScales(), scales)"><tra slug="ie_delete_param"></tra></button> </td>
      </tr>
    </table>
    <div>
      <button [disabled]="isReadOnly()" (click)="frameworkCtrl.addPairableHumanScoringScales()" class="button is-small has-icon">
        <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
        <span>New Pair</span>
      </button>
    </div>
  </div>
</ng-container>
<hr/>
  
<h3><tra slug="ie_test_form_construction_mode"></tra></h3>
<p><tra slug="ie_test_form_construction_mode_description"></tra></p>
<fieldset [disabled]="isReadOnly()">
  <div [class.is-disabled]="isReadOnly()" class="select" style="margin-top:0.5em">
    <select [formControl]="frameworkCtrl.testFormConstructionMethod">
      <option [value]="TestFormConstructionMethod.LINEAR"><tra slug="ie_test_form_construction_linear"></tra></option>
      <option [value]="TestFormConstructionMethod.TLOFT"><tra slug="ie_test_form_construction_tloft"></tra></option>
      <option [value]="TestFormConstructionMethod.MSCAT"><tra slug="ie_test_form_construction_mscat"></tra></option>
    </select>
  </div>
  <div *ngFor="let option of assessmentOptions" style="margin-top:0.5em;">
    <label class="radio">
      <input type="radio" name="assessmentType"
            [value]="option.key"
            [(ngModel)]="selectedAssessmentTypeOption"
            (change)="onAssessmentTypeChange()">
      <span style="margin-left:1em;">
        <tra [slug]="option.slug"></tra>
      </span>
    </label>
  </div>

  <div style="margin-top:0.5em; margin-left: -1em;" *ngIf="frameworkCtrl.testFormConstructionMethod.value === TestFormConstructionMethod.TLOFT && !frameworkCtrl.asmtFmrk.assembledPanels?.length">
    <span style="margin-left:1em;">Number of Iterations </span>
    <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'numOfPublishingIterations')">{{frameworkCtrl.asmtFmrk.numOfPublishingIterations || 1}}</a>
  </div>
</fieldset>
  
<hr/>
<div>
  <div style="margin-bottom:1em;">
    <strong></strong>Help Page Item ID: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'helpPageId')">{{frameworkCtrl.asmtFmrk.helpPageId || '--'}}</a> 
  </div>
  <div>Reference Documents <button (click)="frameworkCtrl.addRefDoc()">New</button></div>
  <table *ngIf="frameworkCtrl.asmtFmrk.referenceDocumentPages" style="width:auto">
    <tr>
      <td><tra slug="ie_item_id"></tra></td>
      <td>Caption</td>
      <td></td>
    </tr>
    <tr *ngFor="let doc of frameworkCtrl.asmtFmrk.referenceDocumentPages">
      <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(doc, 'itemId')">{{doc.itemId || '--'}}</a> </td>
      <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(doc, 'caption')">{{doc.caption || '--'}}</a> </td>
      <td> <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.referenceDocumentPages, doc)"><tra slug="ie_remove_item_bank"></tra></button> </td>
    </tr>
  </table>
</div>
  
<div class="panel_definition" *ngIf="frameworkCtrl.isTestformMsCat()">
  <hr/>
  <div>
    <button class="button" (click)="panelCtrl.importPanelAssemblyConfig(frameworkCtrl.asmtFmrk)">Import Panel Assembly</button>
  </div>
  <div *ngIf="frameworkCtrl.asmtFmrk.panelAssembly">
    <h2><strong>Panel Assembly</strong></h2>
    <p>You can configure the mscat properpties that are shared across all panels here.</p>
    <h3>Modules</h3>
    <div>
      <mat-slide-toggle [(ngModel)]="isMscatPanelContentDetails"></mat-slide-toggle>
      <span [ngSwitch]="!!isMscatPanelContentDetails" style="margin-left: 0.5em;">
        <span *ngSwitchCase="false">Hiding panel content details</span>
        <span *ngSwitchCase="true">Viewing panel content details</span>
      </span>
    </div>
    <table style="width:auto; margin-top:1em;" class="table is-striped is-hoverable">
      <tr>
        <th><tra slug="ie_stage"></tra></th>
        <th>Module</th>
        <th *ngIf="isMscatPanelContentDetails">Avg. Difficulty</th>
        <th *ngIf="isMscatPanelContentDetails">Num. Items</th>
        <th *ngIf="isMscatPanelContentDetails">Pre-ambles</th>
        <th *ngIf="isMscatPanelContentDetails">Post-ambles</th>
        <th>Routing Rules</th>
        <th>FT</th>
      </tr>
      <tr *ngFor="let module of frameworkCtrl.asmtFmrk.panelAssembly.allModules">
        <td>{{module.stageNumber}}</td>
        <td>{{module.id}}</td>
        <td *ngIf="isMscatPanelContentDetails"> ({{panelCtrl.renderDifficultyLevel(frameworkCtrl.asmtFmrk, module.difficultyLevel)}}) <input class="short-num" type="number" [(ngModel)]="module.difficultyLevel"> </td>
        <td *ngIf="isMscatPanelContentDetails"><input class="short-num" type="number" [(ngModel)]="module.item_count"> </td>
        <td *ngIf="isMscatPanelContentDetails">
          <div *ngFor="let itemLabel of module.preambleLabelList"><a>{{itemLabel}}</a></div>
        </td>
        <td *ngIf="isMscatPanelContentDetails">
          <div *ngFor="let itemLabel of module.postambleLabelList"><a>{{itemLabel}}</a></div>
        </td>
        <td>
          <div *ngFor="let constraint of rr(module)">
            <span *ngIf="constraint.maxPropC || constraint.maxPropC === 0">
              If Less Than <input class="short-num" type="number" [(ngModel)]="constraint.maxPropC">,
            </span>
            <span *ngIf="constraint.minPropC || constraint.minPropC === 0">
              If Greater-Than-or-Equal to <input class="short-num" type="number" [(ngModel)]="constraint.minPropC">,
            </span>
            <span>go to module <input type="text" class="short-num" [(ngModel)]="constraint.module"></span>
          </div>
        </td>
        <td>
          <input type="checkbox" [(ngModel)]="module.has_ft">
          <div *ngIf="module.has_ft">
            Num Items: <input class="short-num" type="number" [(ngModel)]="module.num_ft"> <br/>
            Quadrant ID: <input class="short-num" type="number" [(ngModel)]="module.ft_quadrant_id">
          </div>
        </td>
      </tr>
    </table>
    <div>
      Starting Module: <input type="text" class="short-num" [(ngModel)]="frameworkCtrl.asmtFmrk.panelAssembly.startingModule">
    </div>
    <div>
      Number of FT variants to publish: <input type="text" class="short-num" [(ngModel)]="frameworkCtrl.asmtFmrk.panelAssembly.numFtVariants">
    </div>
    <div>
      <mat-slide-toggle [(ngModel)]="frameworkCtrl.asmtFmrk.panelAssembly.allowNumCorrect"></mat-slide-toggle>
      <span [ngSwitch]="!!frameworkCtrl.asmtFmrk.panelAssembly.allowNumCorrect" style="margin-left: 0.5em;">
        <span *ngSwitchCase="true">Using number correct routing</span>
        <span *ngSwitchCase="false">Using IRT-based routing (requires API call)</span>
      </span>
    </div>
    <div>
      <mat-slide-toggle [(ngModel)]="frameworkCtrl.asmtFmrk.panelAssembly.allowShuffle"></mat-slide-toggle>
      <span [ngSwitch]="!!frameworkCtrl.asmtFmrk.panelAssembly.allowShuffle" style="margin-left: 0.5em;">
        <span *ngSwitchCase="true">Allowing Shuffle</span>
        <span *ngSwitchCase="false">Not Allowing Shuffle</span>
      </span>
    </div>
    <div>
      <mat-slide-toggle 
        [(ngModel)]="frameworkCtrl.asmtFmrk.panelAssembly.isEasyStartDisabled" 
        (change)="panelCtrl.onToggleEasyStartSettings(frameworkCtrl.asmtFmrk)"
      ></mat-slide-toggle>
      <span  [ngSwitch]="!!frameworkCtrl.asmtFmrk.panelAssembly.isEasyStartDisabled" style="margin-left: 0.5em;">
        <span *ngSwitchCase="false">Allowing Easy Start</span>
        <span *ngSwitchCase="true">Not Allowing Easy Start</span>
      </span>
    </div>
    <div>
      <div *ngIf="frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings && !!frameworkCtrl.asmtFmrk.panelAssembly.isEasyStartDisabled" style="display:inline-block; padding:0.5em; margin:0.5em; border: 1px solid #ccc;">
        <div>
          <strong><tra slug="ie_num_items"></tra>:</strong>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings, 'numberOfItems')">
            {{frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings.numberOfItems || 'undefined'}}
          </a>
        </div>
        <div>
          <strong>Sort items by:</strong>
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings, 'prop')">
            {{frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings.prop || 'undefined'}}
          </a>
        </div>
        <div>
          <strong>Invert order?: </strong>
          <table-row-selector [entry]="frameworkCtrl.asmtFmrk.panelAssembly.easyStartSettings" prop="isInverse"></table-row-selector>
        </div>
      </div>
    </div>
  </div>
  <div>
  <tra slug="ie_generated_panels_footnote"></tra>
  </div>
</div>

<div class="quadrant_definitions" *ngIf="frameworkCtrl.isTestformLOFT() || frameworkCtrl.isTestformMsCat()">
  <hr/>
  <h3><tra slug="ie_testlet_quadrant_definitions"></tra></h3>
  <div>
    <fieldset [disabled]="isReadOnly()">
      <label class="checkbox">
        <input type="checkbox" [formControl]="quadrantCtrl.showQuadrantConfigDetail">
        <span style="margin-left:1em;"><tra slug="ie_show_quad_config_details"></tra></span>
      </label>
    </fieldset>
  </div>
  <div>
    <table class="table is-condensed is-hoverable" style="width:auto;">
      <tr>
        <th><tra slug="ie_id"></tra></th>
        <th><tra slug="ie_section"></tra></th>
        <th><tra slug="ie_description"></tra></th>
        <th><tra slug="ie_num_items"></tra></th>
        <th  *ngIf="quadrantCtrl.showQuadrantConfigDetail.value"><tra slug="ie_intersection"></tra></th>
        <th  *ngIf="quadrantCtrl.showQuadrantConfigDetail.value"><tra slug="ie_constraints"></tra></th>
        <ng-container *ngIf="frameworkCtrl.isTestformLOFT() && quadrantCtrl.showQuadrantConfigDetail.value">
          <th><tra  slug="ie_quadrant_form_creation_settings"></tra></th>
        </ng-container>
      </tr>
      <tr *ngFor="let quadrant of frameworkCtrl.asmtFmrk.quadrants">
        <td><b>{{quadrant.id}}</b></td>
        <td> 
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant, 'section')">{{quadrant.section}}</a> 
        </td>
        <td> 
          <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant, 'description')">{{quadrant.description}}</a> 
          <button [disabled]="isReadOnly()" (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.quadrants, quadrant)"><tra slug="ie_remove_quadrant"></tra></button>
          <div>
            <span *ngFor="let constraint of quadrant.constraints">
              <span> {{constraint.param}}</span>
              <span> = </span>
              <span> {{constraint.val}} </span>
              <span>; </span>
            </span>
          </div>
        </td>
        <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant.config, 'numItems')">{{quadrant.config.numItems}}</a> </td>
        <td *ngIf="quadrantCtrl.showQuadrantConfigDetail.value">
          <table>
            <tr *ngFor="let constraint of quadrant.constraints">
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint, 'param')">{{constraint.param}}</a> </td>
              <td> = </td>
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint, 'val')">{{constraint.val}}</a> </td>
              <td>
                <a [class.is-disabled]="isReadOnly()" class="close"  (click)="util.removeArrEl(quadrant.constraints, constraint)">
                  <i class="fa fa-times" aria-hidden="true"></i>
                </a>
              </td>
            </tr>
          </table>
          <button [disabled]="isReadOnly()" (click)="quadrantCtrl.createQuadrantConstraint(quadrant.constraints)"><tra slug="ie_new_constraint"></tra></button>
        </td>
        <td *ngIf="quadrantCtrl.showQuadrantConfigDetail.value">
          <table>
            <tr>
              <td><tra slug="ie_discard_threshold"></tra></td>
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant.config, 'discardThreshold')">{{quadrant.config.discardThreshold || "(missing)"}}</a> </td>
            </tr>
            <tr>
              <td><tra slug="ie_tries_before_fail_whole"></tra></td>
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant.config, 'triesBeforeFailWhole')">{{quadrant.config.triesBeforeFailWhole || "(missing)"}}</a> </td>
            </tr>
            <tr>
              <td><tra slug="ie_tries_before_fail_next_testlet"></tra></td>
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(quadrant.config, 'triesBeforeFail')">{{quadrant.config.triesBeforeFail || "(missing)"}}</a> </td>
            </tr>
          </table>
          <table>
            <tr *ngFor="let constraint of quadrant.config.constraints">
              <td> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint.config, 'param')">{{constraint.config.param}}</a> </td>
              <td> <a [class.is-disabled]="isReadOnly()" style="color:#000" (click)="util.replaceProp(constraint, 'func')">{{constraint.func}}</a> </td>
              <td style="white-space: nowrap;"> 
                <button [disabled]="isReadOnly()" (click)="constraint.config.isMax = false; constraint.config.isMin = false; constraint.config.isEqual = true " [class.is-dark]="constraint.config.isEqual" class="button is-small">=</button> 
                <button [disabled]="isReadOnly()" (click)="constraint.config.isMax = true;  constraint.config.isMin = false; constraint.config.isEqual = false " [class.is-dark]="constraint.config.isMax" class="button is-small">MAX</button> 
                <button [disabled]="isReadOnly()" (click)="constraint.config.isMax = false; constraint.config.isMin = true;  constraint.config.isEqual = false " [class.is-dark]="constraint.config.isMin" class="button is-small">MIN</button>
                <button [disabled]="isReadOnly()" *ngIf="constraint.func === TestletConstraintFunction().VARIETY && frameworkCtrl.isTestformLOFT()" (click)="constraint.config.isEmptyWildcard = !constraint.config.isEmptyWildcard" [class.is-dark]="constraint.config.isEmptyWildcard" class="button is-small">isEmptyWildcard</button>
              </td>
              <td style="white-space: nowrap;">  
                <div [ngSwitch]="constraint.func">
                  <div *ngSwitchCase="'MATCH'">
                    <div><tra slug="ie_value"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint.config, 'val')">{{constraint.config.val || '--'}}</a></div>
                    <div><tra slug="ie_count"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint.config, 'count')">{{constraint.config.count || '--'}}</a></div>
                  </div>
                  <div *ngSwitchCase="'VARIETY'">
                    <tra slug="ie_count"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint.config, 'count')">{{constraint.config.count || '--'}}</a>
                  </div>
                  <div *ngSwitchCase="'AVG'">
                    <tra slug="ie_value"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(constraint.config, 'val')">{{constraint.config.val || '--'}}</a>
                  </div>
                </div>
              </td>
              <td>
                <label style="display:flex; align-items: center;">
                  <input type="checkbox" styl="margin-right: 4px" [(ngModel)]="constraint.config.isHidden" [disabled]="isReadOnly()" />
                  <span><tra slug="disabled" style="white-space: nowrap;"></tra>?</span>
                </label>
                <label style="display:flex; align-items: center;">
                  <input type="checkbox" [(ngModel)]="constraint.config.excludeEmptyParams" (change)="updateConstraintExludeBlanks(constraint.config.excludeEmptyParams, constraint.config.param, quadrant.config.constraints)" [disabled]="isReadOnly()" />
                  <span> <tra slug="ie_do_not_allow_blanks" style="white-space: nowrap;"></tra></span>
                </label>
              </td>
              <td>            
                
                <a class="close" [class.is-disabled]="isReadOnly()" (click)="util.removeArrEl(quadrant.config.constraints, constraint)">
                  <i class="fa fa-times" aria-hidden="true"></i>
                </a>
              </td>
            </tr>
          </table>
          <button [disabled]="isReadOnly()" (click)="testletCtrl.createTestletConstraint(quadrant.config.constraints)"><tra slug="ie_testlet_constraint"></tra></button>
        </td>
        <ng-container *ngIf="frameworkCtrl.isTestformLOFT() && quadrantCtrl.showQuadrantConfigDetail.value">
          <td>
            <div class="quadrant-publish-settings">
              <label>
                <input type="checkbox" [(ngModel)]="quadrant.is_post_assembly_insertion">
                Is Post-assembly Insertion?
              </label>
              <label>
                <input type="checkbox" [(ngModel)]="quadrant.include_in_form_mapping">
                <tra slug="ie_quadrant_included_in_form_mapping"></tra>
              </label>
            </div>
          </td>
        </ng-container>
      </tr>
    </table>
  </div>
  <button [disabled]="isReadOnly()" (click)="quadrantCtrl.createQuadrantSection()" class="button has-icon">
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span><tra slug="ie_new_testlet_quadrant"></tra></span>
  </button>
  <div>
    <mat-slide-toggle [(ngModel)]="frameworkCtrl.asmtFmrk.isTestletItemsReplaceDisabled">
      isTestletItemsReplaceDisabled
    </mat-slide-toggle>
  </div>
  <div *ngIf="frameworkCtrl.isTestformLOFT()">
    <mat-slide-toggle [(ngModel)]="frameworkCtrl.asmtFmrk.isPostAssemblyTestletsAppliedToAllPanels">
      isPostAssemblyTestletsAppliedToAllPanels
    </mat-slide-toggle>
  </div>
  <hr/>
</div>

<hr/>
<div>
  <button (click)="frameworkCtrl.asmtFmrk.isTimerDisabled = !frameworkCtrl.asmtFmrk.isTimerDisabled" [ngSwitch]="!!frameworkCtrl.asmtFmrk.isTimerDisabled" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.isTimerDisabled">
    <span *ngSwitchCase="true">Countdown Timer is Not Visible</span>
    <span *ngSwitchCase="false">Countdown Timer is Visible to Test Takers</span>
  </button>
</div>

<div>
  <button (click)="frameworkCtrl.asmtFmrk.isSpentTimerDisabled = !frameworkCtrl.asmtFmrk.isSpentTimerDisabled" [ngSwitch]="!!frameworkCtrl.asmtFmrk.isSpentTimerDisabled" class="button" [class.is-info]="!frameworkCtrl.asmtFmrk.isSpentTimerDisabled">
    <span *ngSwitchCase="true">Time Spent Timer is Not Visible</span>
    <span *ngSwitchCase="false">Time Spent Timer is Visible to Test Takers</span>
  </button>
  <div *ngIf="!frameworkCtrl.asmtFmrk.isSpentTimerDisabled" style="margin: 1em;">
    <label>
      Recommended Completion Time:
      <textarea 
      [(ngModel)]="frameworkCtrl.asmtFmrk.recommendedTime" 
      cdkTextareaAutosize 
      [cdkTextareaAutosize]="true" 
      [cdkAutosizeMinRows]="2"
      style="width:50%"
      ></textarea>
      <!-- <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.recommendedTime"> -->
    </label>
    <br/>
    <label style="margin-top:1em;">
      Additional Time Allowed:
      <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.extraTime"> 
    </label>
  </div>
</div>

<div>
  <button (click)="frameworkCtrl.asmtFmrk.isResultsDetailDisabled = !frameworkCtrl.asmtFmrk.isResultsDetailDisabled" [ngSwitch]="!!frameworkCtrl.asmtFmrk.isResultsDetailDisabled" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.isResultsDetailDisabled">
    <span *ngSwitchCase="true">Don't show detailed results screen (just a congrats)</span>
    <span *ngSwitchCase="false">Show results screen, like normal</span>
  </button>
</div>

<div>
  <button (click)="frameworkCtrl.asmtFmrk.isOrale = !frameworkCtrl.asmtFmrk.isOrale" [ngSwitch]="!!frameworkCtrl.asmtFmrk.isOrale" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.isOrale">
    <span *ngSwitchCase="true">Orale Assessment mode Enabled</span>
    <span *ngSwitchCase="false">Orale Assessment mode Disabled</span>
  </button>
</div>

<div>
  <button (click)="frameworkCtrl.asmtFmrk.isUnreadyExcluded = !frameworkCtrl.asmtFmrk.isUnreadyExcluded" [ngSwitch]="!!frameworkCtrl.asmtFmrk.isUnreadyExcluded" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.isUnreadyExcluded">
    <span *ngSwitchCase="true">Replace Items Not Marked as Ready with Placeholder</span>
    <span *ngSwitchCase="false">Include All Items</span>
  </button>
</div>

<div style="margin-top:2em;">
  <strong><tra slug="ie_tags"></tra></strong>
  <div *ngFor="let tag of frameworkCtrl.asmtFmrk.tags">
    <input type="text" [(ngModel)]="tag.slug">
    <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.tags, tag)"> &times; </button>
  </div>
  <button (click)="frameworkCtrl.newTag()">New tag</button>
</div>

<div style="margin-top:2em;">
  <strong><tra slug="Similarity Slugs"></tra></strong>
  <div *ngFor="let slug of frameworkCtrl.asmtFmrk.similaritySlugs">
    <input type="text" [(ngModel)]="slug.slug">
    <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.similaritySlugs, slug);"> &times; </button>
  </div>
  <button (click)="frameworkCtrl.newSimilaritySlug();">New slug</button>
</div>

<div style="margin-top:2em;">
  <strong>Preserved Parameters</strong>
  <div *ngFor="let param of frameworkCtrl.asmtFmrk.preservedMetaParams; let index = index;">
    {{param}}
    <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.preservedMetaParams, param)"> &times;  </button>
  </div>
  <button (click)="frameworkCtrl.newPreservedParam()">Insert Parameter to List</button>
</div>

<hr/>

<h3>Sidebar</h3>
<p>Question Naming</p>
<fieldset [disabled]="isReadOnly()">
  <div [class.is-disabled]="isReadOnly()" class="select" style="margin-top:0.5em; margin-bottom:1.5em;">
    <select [formControl]="frameworkCtrl.questionWordingFc">
      <option *ngFor="let questionWordingOpt of QUESTION_WORDING_OPTS" [value]="questionWordingOpt"><tra [slug]="questionWordingOpt"></tra></option>
    </select>
  </div>
</fieldset>

<div style="margin-bottom:1.5em">
  <button (click)="frameworkCtrl.asmtFmrk.useQuestionLabel = !frameworkCtrl.asmtFmrk.useQuestionLabel" [ngSwitch]="!!frameworkCtrl.asmtFmrk.useQuestionLabel" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.useQuestionLabel">
    <span *ngSwitchCase="true">Item Label is Used As Question Title</span>
    <span *ngSwitchCase="false">Questions Are Denoted By Their Position</span>
  </button>
</div>

<div style="margin-bottom: 1.5em">
  <button (click)="frameworkCtrl.asmtFmrk.useSectionCaptions = !frameworkCtrl.asmtFmrk.useSectionCaptions" [ngSwitch]="!!frameworkCtrl.asmtFmrk.useSectionCaptions" class="button" [class.is-info]="frameworkCtrl.asmtFmrk.useSectionCaptions">
    <span *ngSwitchCase="true">Use Section Captions</span>
    <span *ngSwitchCase="false">Show Section Numbers (in "Section 1 of 3" format)</span>
  </button>
</div>

<p style="margin-bottom:0.5em;">Sidebar Thumbnail</p>
<div style="margin-bottom:0.5em;">
  <label>
    EN
    <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.sidebarThumbnailEn">
  </label>
</div>
<div style="margin-bottom: 0.5em;">
  <label>
    FR
    <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.sidebarThumbnailFr">
  </label>
</div>
<div>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.isThumbnailFullWidth">
    <span style="margin-left:1em;">Full Width</span>
  </label>  
</div>
<hr/>
<progress-bar-editor
[frameworkCtrl]="frameworkCtrl"
[isReadOnly]="isReadOnly()"
>
</progress-bar-editor>
<!-- BCED uses flush navigation which always uses the next/prev buttons. This setting would not work for them. -->
<ng-container *ngIf="!isBCED()">
  <hr/>
  <h3>Navigation</h3>
  <div class="select" style="margin-top:0.5em; margin-bottom:1.5em;">
    <select [(ngModel)]="frameworkCtrl.asmtFmrk.nextButtonOpt">
      <option *ngFor="let nextButtonOpt of NEXT_BTN_OPTS" [value]="nextButtonOpt.id"><tra [slug]="nextButtonOpt.caption"></tra></option>
    </select>
  </div>
  <hr/>
  <h3>Question Header</h3>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.showQHeader">
    <span style="margin-left:1em;">Show Question Header</span>
  </label>  
</ng-container>
<hr/>

<hr/>

<h3>Estimated Number of Test Takers</h3>

<a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'estimatedTestTakers')">
  {{frameworkCtrl.asmtFmrk.estimatedTestTakers || 'undefined'}}
</a>

<hr/>
<h3>Toolbar Options </h3>
<div *ngIf="!isAlwaysTTS()">
  <fieldset [disabled]="isReadOnly()">
      <label class="checkbox">
        <input 
                type="checkbox" 
                [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.tts"
                (ngModelChange)="onTtsChange($event)"
        >
        <!-- (change)="frameworkCtrl.asmtFmrk.toolbarOptions.tts_v2 = frameworkCtrl.asmtFmrk.toolbarOptions.tts" -->
        <span style="margin-left: 1em;">Text-to-speech V1</span>
      </label>
      <label class="checkbox" style="margin-left: 1em;">
        <input 
                type="checkbox" 
                [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.tts_v2"
        >
        <!-- (ngModelChange)="onTtsV2Change($event)" -->

        <span>Use TTS V2</span>
      </label>
  </fieldset>
</div>
<div *ngIf="!isAlwaysTTS()">
  <fieldset [disabled]="isReadOnly()">
      <label class="checkbox">
        <input 
                type="checkbox" 
                [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.stt"
        >
        <span style="margin-left: 1em;">Speech-to-text</span>
      </label>
  </fieldset>
</div>
<div *ngIf="!isAlwaysTTS()">
  <fieldset [disabled]="isReadOnly()">
    <div *ngIf="frameworkCtrl.asmtFmrk.toolbarOptions.tts || frameworkCtrl.asmtFmrk.toolbarOptions.tts_v2">
      <tra slug="Hover-to-read TTS granularity"></tra>
      <div style="margin-left: 1em;">
        <label class="radio">
          <input type="radio" 
                 name="ttsHoverGranularity"
                 [value]="TTSHoverGranularity.READ_ALL"
                 [ngModel]="frameworkCtrl.asmtFmrk.toolbarOptions.ttsHoverGranularity"
                 (ngModelChange)="frameworkCtrl.asmtFmrk.toolbarOptions.ttsHoverGranularity = $event"
                 >
          <tra slug="Read all (default)"></tra>
        </label>
      </div>
      <div style="margin-left: 1em;">
        <label class="radio">
          <input type="radio" 
                 name="ttsHoverGranularity"
                 [value]="TTSHoverGranularity.SENTENCE_BY_SENTENCE"
                 [ngModel]="frameworkCtrl.asmtFmrk.toolbarOptions.ttsHoverGranularity"
                 (ngModelChange)="frameworkCtrl.asmtFmrk.toolbarOptions.ttsHoverGranularity = $event"
                 >
          <tra slug="Read sentence by sentence"></tra>
        </label>
      </div>
    </div>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.zoomIn">
      <span style="margin-left:1em;"><tra slug="btn_zoom_in"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.zoomOut">
      <span style="margin-left:1em;"><tra slug="btn_zoom_out"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.lineReader">
      <span style="margin-left:1em;"><tra [slug]="getLineReaderCaption()"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.hiContrast">
      <span style="margin-left:1em;"><tra slug="btn_hi_contrast"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.toggleEditor">
      <span style="margin-left:1em;"><tra slug="btn_toggle_editor"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.highlighter">
      <span style="margin-left:1em;"><tra slug="el_draw_highlighter"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.eraser">
      <span style="margin-left:1em;"><tra slug="btn_eraser"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.notepad">
      <span style="margin-left:1em;"><tra [slug]="getNotepadSlug()"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.toolbarOptions.infoButton">
    <span style="margin-left:1em;"><tra slug="Info Button"></tra></span>
  </label>
</div>

<div>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.showDocumentsSplitScreen">
    <span style="margin-left: 1em;"><tra slug="Open Documents in Split-Screen"></tra></span>
  </label>
</div>

<hr/>
<div style="display: flex; flex-direction: row; align-items: center">
  <h3 style="margin-bottom: 0; margin-right: 0.5em">Accessibility Settings</h3> 
  <img src="https://eqao.vretta.com/authoring/user_uploads/6276/authoring/v4Asset_81/1642783446947/v4Asset_81.svg" style="height: 2em">
</div>
<table style="width:auto">
  <tr>
    <th>Setting Name</th>
    <th>Configurable?</th>
    <th>Default</th>
  </tr>
  <tr *ngFor="let settingProp of Object.keys(frameworkCtrl.accessibilitySettings)">
    <td><tra [slug]="frameworkCtrl.accessibilitySettings[settingProp].caption"></tra></td>
    <td>  
      <div class="checkbox">
        <input type="checkbox" [ngModel]="frameworkCtrl.getAccSettingConfigurable(settingProp)" (ngModelChange)="frameworkCtrl.asmtFmrk.accessibilitySettings[settingProp].isConfigurable = $event">
      </div>
    </td>
    <td><mat-slide-toggle [ngModel]="frameworkCtrl.getAccSettingDefault(settingProp)" (ngModelChange)="frameworkCtrl.asmtFmrk.accessibilitySettings[settingProp].defaultVal = $event"></mat-slide-toggle></td>
  </tr>
</table>
<hr/>
<h3>Report Options</h3>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.reportOptions.hideScore">
      <span style="margin-left:1em;"><tra slug="tr_results_hide_score"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.reportOptions.showCheckmark">
      <span style="margin-left:1em;"><tra slug="tr_results_show_indicators"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.reportOptions.hideStudentPen">
      <span style="margin-left:1em;"><tra slug="tr_results_hide_student_pen"></tra></span>
    </label>
  </fieldset>
</div>

<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.reportOptions.hideLegend">
      <span style="margin-left:1em;"><tra slug="tr_results_hide_legend"></tra></span>
    </label>
  </fieldset>
</div>
<div>
  <fieldset [disabled]="isReadOnly()">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.isCustomResultPageNote">
      <span style="margin-left:1em;"><tra slug="tr_results_show_note"></tra></span>
    </label>
  </fieldset>
</div>
z
<div *ngIf="frameworkCtrl.asmtFmrk.isCustomResultPageNote">
  <strong>Note (EN):</strong>
  <div><textarea [(ngModel)]="frameworkCtrl.asmtFmrk.customResultPageNoteEN"></textarea></div>
  <strong>Note (FR):</strong>
  <div><textarea [(ngModel)]="frameworkCtrl.asmtFmrk.customResultPageNoteFR"></textarea></div>
</div>


<hr/>
<h3>General Language/Info used in the Assessment </h3>

<div>
  <strong>Assessment Name:</strong>
  <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'assessmentName')">
    {{frameworkCtrl.asmtFmrk.assessmentName || '(No Name)'}}
  </a>
</div>
<div>
  <strong>Rubric Download Link:</strong>
  <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'rubricDownloadLink')">
    {{frameworkCtrl.asmtFmrk.rubricDownloadLink || '(No Link)'}}
  </a>
</div>
<div>
  <strong>Missing Path Selection Warning (Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.msgPathWarnOverride" (click)="frameworkCtrl.asmtFmrk.msgPathWarnOverride='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.msgPathWarnOverride" [(ngModel)]="frameworkCtrl.asmtFmrk.msgPathWarnOverride"></textarea></div>
  <div style="padding-left:2em;">
    <div>
      Confirmation Message
      <button *ngIf="!frameworkCtrl.asmtFmrk.msgPathCnfmOverride" (click)="frameworkCtrl.asmtFmrk.msgPathCnfmOverride='new message'">Override</button>
      <div><textarea *ngIf="frameworkCtrl.asmtFmrk.msgPathCnfmOverride" [(ngModel)]="frameworkCtrl.asmtFmrk.msgPathCnfmOverride"></textarea></div>
    </div>
    <div>
      <label>
        <tra slug="ie_cancel"></tra>
        <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.msgPathCnfmCancelOverride"> 
      </label>
    </div>
    <div>
      <label>
        Proceed
        <input type="text" [(ngModel)]="frameworkCtrl.asmtFmrk.msgPathCnfmProceedOverride"> 
      </label>
    </div>
  </div>
</div>
<div>
  <strong>Final Submission Warning (Override):</strong> 
  <button *ngIf="!frameworkCtrl.asmtFmrk.msgFinalSubmission" (click)="frameworkCtrl.asmtFmrk.msgFinalSubmission='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.msgFinalSubmission" [(ngModel)]="frameworkCtrl.asmtFmrk.msgFinalSubmission"></textarea></div>
</div>
<div>
  <strong>Custom Text For Results Page (Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.msgResultsPage" (click)="frameworkCtrl.asmtFmrk.msgResultsPage='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.msgResultsPage" [(ngModel)]="frameworkCtrl.asmtFmrk.msgResultsPage"></textarea></div>
</div>
<div>
  <strong>Custom NavBar Results Page Title (EN) (Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleEN" (click)="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleEN='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleEN" [(ngModel)]="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleEN"></textarea></div>
  <strong>Custom NavBar Results Page Title (FR) (Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleFR" (click)="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleFR='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleFR" [(ngModel)]="frameworkCtrl.asmtFmrk.customNavbarResultIntroPageTitleFR"></textarea></div>
  <strong>Custom Results Page Title(Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.customResultPageTitle" (click)="frameworkCtrl.asmtFmrk.customResultPageTitle='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.customResultPageTitle" [(ngModel)]="frameworkCtrl.asmtFmrk.customResultPageTitle"></textarea></div>
  <strong>Custom Results Page (Override):</strong>
  <button *ngIf="!frameworkCtrl.asmtFmrk.customResultPage" (click)="frameworkCtrl.asmtFmrk.customResultPage='new message'">Override</button>
  <div><textarea *ngIf="frameworkCtrl.asmtFmrk.customResultPage" [(ngModel)]="frameworkCtrl.asmtFmrk.customResultPage"></textarea></div>
</div>
<hr/>
<div>
  <h3>Cut Score Info</h3>
  <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(frameworkCtrl.asmtFmrk, 'cutScore')">
    {{frameworkCtrl.asmtFmrk.cutScore || '(No Cut Score)'}}
  </a>
</div>
<br><br>
<div>
  <h3>Assessment Uploads</h3>
  <capture-upload-files [element]="frameworkCtrl.asmtFmrk" prop="uploads" [displayImageControls]="false"></capture-upload-files>
  <a (click)="insertUpload()">
    <i class="fa fa-upload" aria-hidden="true"></i>
    <tra slug="ie_upload_files"></tra>
  </a>
</div>

<hr/>

<div *ngIf="frameworkCtrl.isEditRawFramework">
  <textarea class="textarea" [formControl]="frameworkCtrl.rawFrameworkJson"></textarea>
  <div>
    <button class="button is-small" (click)="frameworkCtrl.saveRawFramework()">Save</button>
    <button class="button is-small" (click)="frameworkCtrl.cancelEditRawFramework()">Cancel</button>
  </div>
</div>

  
<div> 
  <button [disabled]="isReadOnly()" class="button is-small has-icon" style="z-index: 10;" [class.is-success]="saveLoadCtrl.isSaveChangedRequired" (click)="saveLoadCtrl.saveChanges()">
    <span class="icon"><i class="fa fa-floppy" aria-hidden="true"></i></span>
    <span><tra slug="ie_save_changes"></tra></span>
  </button> 
  <button [disabled]="isReadOnly()" class="button is-small" (click)="frameworkCtrl.editRawFramework()">
    <span class="icon"><i class="fa fa-pencil" aria-hidden="true"></i></span>
    <span>Edit Raw Framework Settings</span>
  </button> 
</div>

