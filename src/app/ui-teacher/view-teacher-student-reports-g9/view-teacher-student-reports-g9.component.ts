import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { mtz } from '../../core/util/moment';
import { AuthService, getFrontendDomain } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { LangService } from '../../core/lang.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';

import * as _ from 'lodash';
import { Subscription } from 'rxjs';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

interface IStudentsReportPayload {
  testForm: string,
  studentReports: any[]
}

enum ReportStatus {
  GENERATIONREADY = 'generationReady',
  GENERATING = 'generating',
  VALIDATING = 'validating',
  HAVESINGLEREPORT = 'haveSingleReport',
  HAS_REPORTED_ISSUE = 'hasReportedIssue',
  PENDED = 'pended',
  WITHHELD = 'withheld'
}
@Component({
  selector: 'view-teacher-student-reports-g9',
  templateUrl: './view-teacher-student-reports-g9.component.html',
  styleUrls: ['./view-teacher-student-reports-g9.component.scss']
})
export class ViewTeacherStudentReportsG9Component implements OnInit, OnDestroy {

  constructor(
    private route: ActivatedRoute,
    private routes: RoutesService,
    private sidepanel: SidepanelService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private classroomService: ClassroomsService,
    private g9demoService: G9DemoDataService,
    public lang: LangService,
    private auth: AuthService,
    private router: Router,
    private whitelabel: WhitelabelService
  ) { }
  
  breadcrumb;
  classroomId:string;
  isLoaded:boolean;
  testForm: string;
  studentsReportList: any[];
  ReportStatus = ReportStatus;
  isSecreteUser;
  currentQueryParams;

  students = [];
  haveClassReport:boolean = false
  filterToClass:number|string;
  schlClassGroupId:string;
  private classroomServiceSub: Subscription = null;

  ngOnInit(): void {
    this.sidepanel.activate();
    this.sidepanel.unexpand();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
    });
    this.route.params.subscribe(params => {
      this.classroomId = params['classroomId'];
      this.schlClassGroupId = params['schlClassGroupId'];
      this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
        if (data){
          this.initRoute();
        }
      })
    });
  }
  
  ngOnDestroy(): void {
    if(this.classroomServiceSub) {
      this.classroomServiceSub.unsubscribe();
    }
  }
  async initRoute(){
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator/classrooms`
    if(this.g9demoService.getIsFromSchoolAdmin()) {
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT( this.lang.tra('sa_dashboard_school_admin'), `/${this.lang.c()}/school-admin/dashboard`, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(this.lang.tra('lbl_reports'), `/${this.lang.c()}/school-admin/reports`, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(classroomName, this.router.url),
      ];
    } else {
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT( this.lang.tra('lbl_classes') , basePath),
        this.breadcrumbsService._CURRENT( classroomName, basePath+'/'+this.classroomId),
        this.breadcrumbsService._CURRENT( this.lang.tra('student_report_p1') , this.router.url),
      ];
    }
    this.loadStudentReportsList();
    this.route.queryParams.subscribe(queryParams => {
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
    })
  }

  getLogo() {
    if (this.lang.c() === 'fr') return this.whitelabel.getSiteText('logo_bw_fr');
    else return this.whitelabel.getSiteText('logo_bw_en');
  }

  downloadReport(){
    this.auth.apiCreate(
      this.routes.EDUCATOR_REPORT_RESULTS,
      {
        query: {
          classroomId: this.classroomId,
          outputMode: 'flat',
        }
      }
    )
  }

  isreportSelectionMade:boolean;
  generateNewReports(){
    const selectedStudentUids = [];
    this.students.forEach(student => {
      if (student._isReadyForReport){
        selectedStudentUids.push(student.uid);
      }
    })
    if (selectedStudentUids.length == 0){
      this.loginGuard.quickPopup(this.lang.tra('msg_ready_for_report_instr'));
    }
    else{
      if (this.isLoaded){
        this.loadStudentReportsList(selectedStudentUids);
      }
    }
  }

  isStuReportGenAvail(s){
    return !s.reportInfo && this.isReportGenAvailable && s.studentInfo.sessionProgress && s.studentInfo.sessionProgress.length && s.reportStatus
  }

  loadStudentReportsList(selectedStudentUids?:number[]) {
    this.isLoaded = false;
    this.errorMsg = null;
    this.auth.apiCreate(
      this.routes.EDUCATOR_REPORT_RESULTS,
      {
        classroomId: this.classroomId,
        schl_class_group_id: this.schlClassGroupId,
        selectedStudentUids,
      }
    )
    .then((res: {studentReportsData: IStudentsReportPayload[], classReportRecord: any}) => {
      this.isLoaded = true;
      const studentReportsData = res.studentReportsData
      const classReportRecord = res.classReportRecord
      this.students = _.orderBy(studentReportsData, ['studentInfo.lastName', 'studentInfo.firstName']);
      this.isReportGenAvailable = false;
      this.students.forEach(s => {
        this.isReportGenAvailable = s.isReportGenAvail
        if (s.reportInfo){
          if (!s.reportInfo.reportGeneratedOn){
            s.reportInfo.reportGeneratedOn = (new Date()).toISOString()
          }
        }
      })
      this.haveClassReport = classReportRecord.length > 0
    })
    .catch((e) => {
      if (e.message == 'G9_REPORTS_DISABLED'){
        this.errorMsg = this.lang.tra('report_error_message5');
      }
      else{
        this.errorMsg = e.message;
      }
      alert(this.errorMsg)
    })
  }

  isReportGenAvailable:boolean = true;
  errorMsg:string;

  renderDateString(d: string) {
    return mtz(d).format('MMM D, YYYY');
  }

  renderDateTimeString(d: string) {
    return mtz(d).format(this.lang.tra('datefmt_timestamp'))
  }

  numsSum(nums:number[]){
    let total = 0;
    nums.forEach(num =>{
      if (num){
        total += (+num);
      }
    })
    return total;
  }

  renderQuickDate(date: string) {
    return date ? mtz(date).format('MMM D') : '';
  }

  isMatchFilter(s:any){
    return !this.filterToClass || ((<any>+this.filterToClass) == +s.class_id);
  }

  maskOen(str:string){
    str = ''+str;
    return str.substr(0, 2) + '******' + str.substr(7, 2)
  }

  print(){
    window.print()
  }

  renderRouting(s:{routing_path: any}, stage:number){
    switch (s.routing_path['stage_'+stage]){
      case 'low-med': return 'Low/medium';
      case 'med-high': return 'Medium/high';
    }
    return '--'
  }

  renderSectionSlug (sectionLabel: string) {
    if (sectionLabel === 'A') return this.lang.tra('g9_report_session_A');
    if (sectionLabel === 'B') return this.lang.tra('g9_report_session_B');
  }

  haveReportGenReady(){
    if(this.students.find(student => student.reportStatus === ReportStatus.GENERATIONREADY)){
      return true
    }
    return false
  }

  generateG9Report(){
    this.classroomService
      .generateClassG9Reports(this.schlClassGroupId, false)
      .then(res =>{
        if(res.message === 'REPORT_GENERATING'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generating'),
            confirm: () => {}
          });
          this.students.forEach(student =>{
            if(student.reportStatus === ReportStatus.GENERATIONREADY){
              student.reportStatus = ReportStatus.GENERATING
            }
          })
        }
        if(res.message === 'REPORT_REGENERATING'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_regenerating'),
            confirm: () => {}
          });
        }
        if(res.message === 'REPORT_GENERATED'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generated'),
            confirm: () => {}
          });
        }
      })
      .catch(err => {
        if (err.message === 'REPORT_GENERATING') {
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generating'),
            confirm: () => {}
          });
        }
        if(err.message === 'REPORT_GENERATED'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generated'),
            confirm: () => {}
          });
        }
        if (err.message === 'SCHL_ADMIN_SIGN_OFF_STUDENT_DATA_REQUIRED') {
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('lbl_student_data_confirm_required'),
            confirm: () => {}
          });
        }
        if (err.message === 'REPORT_VALIDATING') {
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('lbl_report_reviewing'),
            confirm: () => {}
          });
        }
        if (err.message === 'NO_DATA_FOR_REPORT') {
          //do nothing
        }
      })
  }

  async simulateG9Pipline(){
    const queryparams = {
      schl_class_group_id: this.schlClassGroupId,
      clientDomain: getFrontendDomain()
    }
    return this.auth
      .apiUpdate(this.routes.EDUCATOR_G9_REPORTS_PDF, this.classroomId, {},{
        query: queryparams
      })
  }

  // async validateG9Report(){
  //   const queryparams = {
  //     schl_class_group_id: this.schlClassGroupId,
  //   }
  //   return this.auth
  //     .apiPatch(this.routes.EDUCATOR_G9_REPORTS_PDF, this.classroomId, {},{
  //       query: queryparams
  //     })
  // }

  async downloadG9Report(){
    let noAvailableReport = false
    const res = await this.classroomService
       .loadClassG9Reports(this.schlClassGroupId, false)
       .catch(err => {
          noAvailableReport = true;
          if (err.message === 'REPORT_GENERATING') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('sa_report_generating'),
              confirm: () => {}
            });
          }
          if (err.message === 'REPORT_VALIDATING') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('lbl_report_reviewing'),
              confirm: () => {}
            });
          }
          if (err.message === 'SCHL_ADMIN_SIGN_OFF_STUDENT_DATA_REQUIRED') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('lbl_student_data_confirm_required'),
              confirm: () => {}
            });
          }
          if (err.message === 'NO_BULK_REPORT_FOUND') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('lbl_no_bulk_reports_avaliable'),
              confirm: () => {}
            });
          }
          if (err.message === 'NO_DATA_FOR_REPORT') {
            //do nothing
          }
          if (err.message === 'REPORT_IS_PENDING') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('sa_report_state_0'),
              confirm: () => {}
            });
          }
       })

    if(!noAvailableReport){
      const message = res.message
      if(message === 'REPORT_GENERATING'){ // shouldn't come into this line, but put here just incase
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('sa_report_generating'),
          confirm: () => {}
        });
      } 
      if(message === 'REPORT_REGENERATING'){
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('sa_report_regenerating'),
          confirm: () => {}
        });
      } 
      const fileURL = res.reportURL
      if(message === 'REPORT_GENERATED' && fileURL && !noAvailableReport){
        let a = document.createElement('a');
        a.href = fileURL;
        a.download = 'StudentResponseSheets.pdf';
        a.dispatchEvent(new MouseEvent('click'));
      }   
    }   
  }
  /**
   * Generate individual g9 report s3 download link
   * @param student 
   */
  async downloadIndividualReport(student){
    const res = await this.classroomService
      .downloadClassG9IndividualReports(this.schlClassGroupId, student.test_attempt_id, student.uid)
      .catch(err => {
        this.singleReportDownloadPopup(err.message)
        return
      })

    const message = res.message  
    const fileURL = res.reportURL
    if(message === 'REPORT_GENERATED' && fileURL){
      let a = document.createElement('a');
      a.href = fileURL;
      a.download = 'StudentResponseSheets.pdf';
      a.dispatchEvent(new MouseEvent('click'));
    }else{
      this.singleReportDownloadPopup(message)
    }
  }
  /**
   * Generate class g9 reports for individual and whole class
   * This function is used by secret user for testing purpose
   */
  generateG9PDF(){
    const data = {}
    const params = {
      query: {
        clientDomain: getFrontendDomain(),
        class_group_id: this.schlClassGroupId
      }
    }
    this.auth.apiCreate(this.routes.EDUCATOR_G9_REPORTS_PDF, data, params)
  }

  singleReportDownloadPopup(message:string){
    let traSlug: string
    switch(message){
      case "REPORT_GENERATING":
        traSlug = "lbl_report_generating"
        break
      case "REPORT_REGENERATING":
        traSlug = "sa_report_regenerating"
        break
      case "REPORT_VALIDATING":
      case "REPORT_IS_PENDING":
        traSlug = "lbl_report_reviewing"
        break
      case "NO_DATA_FOR_REPORT":
        traSlug = "lbl_no_reports_avaliable"
        break
      default:
        traSlug = "lbl_no_reports_avaliable"
    }
    this.loginGuard.quickPopup(traSlug)
  }
}
