<div>
   <header 
    [breadcrumbPath]="breadcrumb" 
    [hasSidebar]="true"
   ></header>
  <div *ngIf="!isLoaded" style="padding:4em;">
    <div *ngIf="errorMsg">
      <div class="notification is-warning">
        {{errorMsg}}
      </div>
    </div>
    <div *ngIf="!errorMsg">
      Loading...
    </div>
  </div>
  <ng-container *ngIf="isLoaded">
    <div class="page-body off-white " style="display:flex; flex-direction: column; justify-content: space-between; min-height:100vh;">
      <div>
        <!-- <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true"></header> -->
        <div class="printed-page is-large dont-print" style="margin-top: 3em;">
          <tra-md slug="txt_msg_isr_warning"></tra-md>
          <tra-md slug="txt_rep_gen_instr"></tra-md>
          <tra-md slug="note_rep_gen_instr"></tra-md>
          <div  class="notification">
            <a target="_blank" [href]="lang.tra('link_g9_isr_report_ref')">
              <tra slug="lbl_g9_isr_report_ref"></tra>
            </a>
          </div>
          <hr/>
          <table>
            <tr>
              <th><tra slug="sa_students_col_oen"></tra></th>
              <th><tra slug="inst_name_lbl"></tra></th>
              <!-- <th><tra slug="lbl_report_avail_q"></tra></th> -->
              <th><tra slug="lbl_session_prog"></tra></th>
              <th><tra slug="lbl_gen_new_q"></tra></th>
            </tr>
            <tr *ngFor="let s of students">
                <td>{{maskOen(s.studentInfo.oen)}}</td>
                <td>
                  {{s.studentInfo.firstName}} {{s.studentInfo.lastName}}
                  <span *ngFor="s.studentInfo.isDuplicate" style="color:red;">(Duplicate Registration)</span>
                </td>
                <!-- <td>
                  <span *ngIf="s.reportInfo" class="tag is-success"><tra slug="lbl_avail_see_below"></tra></span>
                  <span *ngIf="!s.reportInfo">
                    <span *ngIf="!isStuReportGenAvail(s)">
                      <span *ngIf="isReportGenAvailable" class="tag"><tra slug="lbl_not_yet_started"></tra></span>
                      <span *ngIf="!isReportGenAvailable" class="tag"><tra slug="lbl_no"></tra></span>
                    </span>
                    <span *ngIf="isStuReportGenAvail(s)">
                      <span class="tag"><tra slug="lbl_report_can_be_generated"></tra></span>
                    </span>
                  </span>
                </td> -->
                <td>
                  <div *ngIf="s.studentInfo.sessionProgress">
                    <div 
                      *ngFor="let progress of s.studentInfo.sessionProgress"
                      class="session-indicator" 
                      [class.is-session-submitted]="progress.is_submitted==1"
                    >
                      <tra [slug]="progress.slug"></tra>
                      <div style="font-size:0.8em">
                        <tra slug="lbl_last_touch"></tra>
                        : {{renderQuickDate(progress.last_touch_on)}}
                      </div>
                    </div>
                  </div>
                </td>
                <td [ngSwitch]="s.reportStatus">
                  <div *ngSwitchCase="ReportStatus.GENERATIONREADY">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_ready_for_report"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.GENERATING">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_report_generating"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.VALIDATING">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_report_reviewing"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.HAS_REPORTED_ISSUE">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_report_unresolved_issue"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.HAVESINGLEREPORT">
                    <label>
                      <span class = 'report-status'>
                        <button (click)="downloadIndividualReport(s)" class="button is-small is-success">
                          <tra slug="btn_download_individual_report"></tra>
                        </button>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.WITHHELD">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_g9_report_withheld"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchCase="ReportStatus.PENDED">
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_g9_report_pended"></tra>
                      </span>
                    </label>
                  </div>
                  <div *ngSwitchDefault>
                    <label>
                      <span class = 'report-status'>
                        <tra slug="lbl_student_not_submitted_or_insufficient_data"></tra>
                      </span>
                    </label>
                  </div>
                  <!-- <div *ngIf="s.reportInfo" style="text-align:center;">
                    <i class="fas fa-check" style="color:#48c774; font-size:1.3rem; margin-top:0.4rem;"></i>
                  </div> -->
                  <div *ngIf="s.studentInfo.reportRequestedOn">
                    <div style="font-size:0.8em">
                       <tra slug="lbl_report_requested_on"></tra>: {{renderDateTimeString(s.studentInfo.reportRequestedOn)}}
                    </div>
                  </div>
                </td>
            </tr>
          </table>
          <div style="display: inline; float: right;">
            <div style="margin-top:1em; text-align: right; display: inline-block;">
              <button [disabled]="!haveClassReport" (click)="downloadG9Report()" class="button is-info has-text-center">
                <tra slug="btn_download_g9_report"></tra>
              </button>
            </div>
            <div style="margin-top:1em; text-align: right; display: inline-block;">
              <button [disabled]="!haveReportGenReady()" (click)="generateG9Report()" class="button is-success has-text-center">
                <tra slug="btn_gen_new_reports"></tra>
              </button>
            </div>
          </div> 
          
        </div>
        <div class="dont-print" style="text-align:center;">
          <hr/>
          <button *ngIf="isSecreteUser==='0sidfj'" (click)="simulateG9Pipline()" class="button is-info is-large has-text-center">
            Simulate G9 Pipeline
          </button>
        </div>
      </div>
      <div class="dont-print" style="text-align:center;">
        <hr/>
        <button *ngIf="isSecreteUser==='0sidfj'" (click)="generateG9PDF()" class="button is-info is-large has-text-center">
          Generate G9 PDF
        </button>
      </div>
      <!-- <div class="dont-print" style="text-align:center;">
        <hr/>
        <button (click)="print()" class="button is-info is-large">
          <tra slug="btn_print_reports"></tra>
        </button>
      </div>
      <div *ngFor="let s of students">
        <div *ngIf="s.reportInfo">
          <div class="printed-page"  >
              <div style="text-align:center; font-weight: 700; font-size:1.5em;">
                  <tra slug="g9_report_header"></tra>
              </div>
              <div class="personal-id-header">
                  <img class="eqao-logo" src={{getLogo()}}>
                  <table class="personal-data-table">
                      <tr>
                          <td class="personal-title"><tra slug="g9_report_OEN"></tra></td>
                          <td>{{maskOen(s.studentInfo.oen)}}</td>
                          <td></td>
                          <td class="personal-title"><tra slug="g9_report_name"></tra></td>
                          <td>{{s.studentInfo.firstName}} {{s.studentInfo.lastName}}</td>
                      </tr>
                      <tr>
                          <td class="personal-title"><tra slug="g9_report_school"></tra></td>
                          <td>{{s.schoolAndDistInfo.schoolName}} ({{s.schoolAndDistInfo.schoolMident}})</td>
                          <td></td>
                          <td class="personal-title"><tra slug="g9_report_test_written"></tra></td>
                          <td>{{renderDateString(s.reportInfo.writtenOn)}}</td>
                      </tr>
                      <tr>
                          <td class="personal-title"><tra slug="g9_report_school_board"></tra></td>
                          <td>{{s.schoolAndDistInfo.distName}}</td>
                          <td></td>
                          <td class="personal-title"><tra slug="g9_report_generated_on"></tra></td>
                          <td>{{renderDateString(s.reportInfo.reportGeneratedOn)}}</td>
                      </tr>
                  </table>
              </div>

              <div>
                  <tra-md slug="g9_report_description"></tra-md>
              </div>

              <div style="font-weight:700;">
                  <tra slug="g9_report_your_results_header"></tra>
              </div>
                  
              <table class="results-table">
                  <tr>
                      <td *ngFor="let sectionPerf of s.reportInfo.sectionsPerf" class="session-header">{{renderSectionSlug(sectionPerf.label)}}</td>
                  </tr>
                  <tr class="score-data">
                      <td *ngFor="let sectionPerf of s.reportInfo.sectionsPerf">
                          <div><tra slug="g9_report_score"></tra> <strong>{{sectionPerf.score}}/{{sectionPerf.weight}}</strong></div>
                      </td>
                  </tr>
                  <tr>
                      <td [attr.colspan]="s.reportInfo.sectionsPerf.length" class="stage-router-summary">
                          <tra-md slug="g9_report_final_score" [props]="{SCORE: s.reportInfo.overallRawScore, WEIGHT: s.reportInfo.overallRawWeight}"></tra-md>
                      </td>
                  </tr>
              </table>
              <div style="font-size:0.8em; line-height: 1.5em;">
                  <tra-md slug="g9_report_foot_note"></tra-md>
              </div>
              <div>
                  <tra-md slug="g9_report_share"></tra-md>
              </div>
              <div style="line-height: 1em;">
                  <p>
                      <em>
                          <tra-md slug="g9_report_confidential"></tra-md>
                      </em>
                  </p>
              </div>
          </div>
          <div class="dont-print" style=" text-align:center; ">
            &nbsp; &nbsp;
          </div>
        </div>
      </div> -->
    </div>
  </ng-container>
</div>
