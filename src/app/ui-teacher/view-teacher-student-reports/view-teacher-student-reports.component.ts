import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { LangService } from 'src/app/core/lang.service';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { G9DemoDataService } from 'src/app/ui-schooladmin/g9-demo-data.service';
import { G9_STUDENT_DATA_20211012 } from './data';

interface IStudentsReportPayload {
  testForm: string,
  studentReports: any[]
}

@Component({
  selector: 'view-teacher-student-reports',
  templateUrl: './view-teacher-student-reports.component.html',
  styleUrls: ['./view-teacher-student-reports.component.scss']
})
export class ViewTeacherStudentReports implements OnInit, OnDestroy {

  constructor(
    private route: ActivatedRoute,
    private routes: RoutesService,
    private sidepanel: SidepanelService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private classroomService: ClassroomsService,
    private g9demoService: G9DemoDataService,
    private lang: LangService,
    private auth: AuthService,
    private router: Router,
  ) { }
  
  breadcrumb;
  classroomId:string;
  isLoaded:boolean;
  testForm: string;
  studentsReportList: any[];
  

  students = G9_STUDENT_DATA_20211012;
  filterToClass:number|string;
  schlClassGroupId:string;
  private classroomServiceSub: Subscription = null;

  ngOnInit(): void {
    this.sidepanel.activate();
    this.sidepanel.unexpand();
    this.loginGuard.activate();
    this.route.params.subscribe(params => {
      this.classroomId = params['classroomId'];
      this.schlClassGroupId = params['schlClassGroupId'];
      this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
        if (data){
          this.initRoute();
        }
      })
    });
  }

  ngOnDestroy(): void {
    if(this.classroomServiceSub) {
      this.classroomServiceSub.unsubscribe();
    }
  }

  async initRoute(){
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator/classrooms`
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_classes') , basePath),
      this.breadcrumbsService._CURRENT( classroomName, basePath+'/'+this.classroomId),
      this.breadcrumbsService._CURRENT( this.lang.tra('student_report_p1') , this.router.url),
    ];
    this.loadStudentReportsList();
  }

  downloadReport(){
    this.auth.apiCreate(
      this.routes.EDUCATOR_REPORT_RESULTS,
      {
        query: {
          classroomId: this.classroomId,
          outputMode: 'flat',
        }
      }
    )
  }

  loadStudentReportsList() {
    this.errorMsg = null;
    this.auth.apiCreate(
      this.routes.EDUCATOR_REPORT_RESULTS,
    {
        classroomId: this.classroomId,
        schl_class_group_id: this.schlClassGroupId,
      }
    )
    .then((res: {studentReportsData: IStudentsReportPayload[], classReportRecord: any}) => { // this component is not being used. Update for the purpose of preventing potential future regression
      this.isLoaded = true;
      const studentReportsRes = res.studentReportsData[0];
      this.testForm = studentReportsRes.testForm;
      this.studentsReportList = studentReportsRes.studentReports;
    })
    .catch((e) => {
      if (e.message == 'G9_REPORTS_DISABLED'){
        this.errorMsg = this.lang.tra('report_error_message5');
      }
      else{
        this.errorMsg = e.message;
      }
      alert(this.errorMsg)
    })
  }

  errorMsg:string;

  renderDateString(d: Date) {
    const dateArr = d.toDateString().split(' ');
    return `${dateArr[1]} ${dateArr[2]}, ${dateArr[3]}`
  }

  numsSum(nums:number[]){
    let total = 0;
    nums.forEach(num =>{
      if (num){
        total += (+num);
      }
    })
    return total;
  }

  isMatchFilter(s:any){
    return !this.filterToClass || ((<any>+this.filterToClass) == +s.class_id);
  }

  maskOen(str:string){
    str = ''+str;
    return str.substr(0, 2) + '******' + str.substr(7, 2)
  }

  
  renderRouting(s:{routing_path: any}, stage:number){
    switch (s.routing_path['stage_'+stage]){
      case 'low-med': return 'Low/medium';
      case 'med-high': return 'Medium/high';
    }
    return '--'
  }

}
