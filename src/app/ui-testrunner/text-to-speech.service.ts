import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { IAudioLoadInfo, IAudioSrcInfo } from './render-audio/render-audio.component';
import { convertSecondsToMs, convertMsToSeconds } from '../core/util/moment';
import { speechMarkChild, SpeechMarksMetaProps, ETtsHighlightAction, ISpeechMarks, SpeechMarksMetaPropsExtended, ESpeechMarkTypes } from './utils/text-to-speech/types';
import { processDOMforTts, getCurrentSelectedNode, getNodeRefIdFromEl, getAuthoringElementOfCurrentNode, getNodeReferenceId, traverseDOM } from './utils/text-to-speech/dom-processing/main';
import { highlightSentence, highlightWord, cleanupHighlighting, highlightImg, highlightElement, highlightNodeTypes } from './utils/text-to-speech/dom-processing/highlighting';
import { CT_ALT_TEXT, FILLER_NODE, KEY_ID_NODE, DRAG_NODE, INSTRUCTION_TEXT } from './utils/text-to-speech/constants';
import { StyleprofileService } from '../core/styleprofile.service';
import { IQuestionConfig } from './models';
import { LangService } from '../core/lang.service';
import { isTextNode } from './utils/text-to-speech/dom-processing/html-util';
import { AuthService } from '../api/auth.service';
import { removeDataFromLocalStorage, retriveDataFromLocalStorage, storeDataInLocalStorage } from './services/util';
import { SpeechEngineService } from './utils/text-to-speech/on-the-fly/speech-engine.service';
import { SttEngineService } from './utils/speech-to-text/stt-engine.service';

const LOC_STORAGE_TTS_KEY = "tts-playbackspeed";
const TTS_DEFAULT_PLAYBACk_SPEED = 0.75;

export enum ETextToSpeechVersions {
  TTSv1 = 'TextToSpeech-v1',
  TTSv2 = 'TextToSpeech-v2'
}
@Injectable({
  providedIn: 'root'
})
export class TextToSpeechService {

  public isActive:boolean = false;
  public textToSpeechStateChangeSub = new BehaviorSubject<boolean>(this.isActive);
  private _isHiContrast: boolean = false;
  public isHighContrastSub = new BehaviorSubject<boolean>(this._isHiContrast);
  public onPlay: Subject<IAudioSrcInfo> = new Subject();
  public onAudioLoadEvent: Subject<IAudioLoadInfo> = new Subject();
  public isOnSameQuestion: boolean = false;
  public isOnSameQuestionSub = new BehaviorSubject<boolean>(this.isOnSameQuestion);

  private _speechMarksMeta = {
    isProcessedDOM: false,
    isSpeechMarksProcessed: false,
  }

  private _playbackSpeedToSnapTime = {
    0.75:100,    // Base speed, standard snapTime
    1.0: 500,    // Slightly faster, increase snapTime to enlarge the window
    1.25: 2000,   // Faster, increase snapTime to enlarge the window
    1.5: 2000,
    1.75: 2000,
    2.0: 2000
  };

  private _defaultPlaybackSpeed: number = TTS_DEFAULT_PLAYBACk_SPEED;

  public get defaultPlaybackSpeed() {
    return this._defaultPlaybackSpeed
  }

  public set defaultPlaybackSpeed(value) {
    this.storeUserPlaybackSpeed(value);
  }


  public get isHiContrast() : boolean {
    return this._isHiContrast;
  }

  constructor(
    private auth: AuthService,
    private styleProfile: StyleprofileService,
    private lang: LangService,
    private speechEngine: SpeechEngineService,
    private sttEngine: SttEngineService
  ) {
    this.initTtsDefaultPlaybackSpeed();
  }

  toggle(emit: boolean = true){
    this.isActive = !this.isActive;
    if(this.isActive) {
      this.initChunkForTTS();
      this.focusFirstInput();
    }
    if(!this.isActive) {
      this.speechEngine.stop();
      this.sttEngine.stop();
    }
    // if (emit) this.textToSpeechStateChangeSub.next(this.isActive);
  }

  focusFirstInput() {
    const inputs = Array.from(
      document.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>(
        'input:not([disabled]):not([tabindex="-1"]), textarea:not([disabled]):not([tabindex="-1"])'
      )
    );

    if (inputs.length) {
      setTimeout(() => inputs[0].focus({ preventScroll: true }), 0);
    } 
  }

  hi_contrast_toggle(){
    this._isHiContrast = !this._isHiContrast
    this.isHighContrastSub.next(this._isHiContrast)
  }

  hi_contrast_off() {
    this._isHiContrast = false;
    this.isHighContrastSub.next(this._isHiContrast)
  }

  private __qConfigCache: IQuestionConfig;

  setTextToSpeechConfig(config: { questionConfig: IQuestionConfig }) {
    const { questionConfig } = config;
    this.__qConfigCache = questionConfig;
  }

  setIsOnSameQuest(isSame: boolean) {
    this.isOnSameQuestion = isSame;
    this.isOnSameQuestionSub.next(this.isOnSameQuestion);
  }

  initProcessDOMForTts() {
    const questionConfig = this.__qConfigCache;
    // TODO: make sure speechMarksList and speechMarksMap is empty
    this.speechMarksMap = new Map();
    this.speechMarksList = [];

    questionConfig.voiceover.voiceoverList.forEach(({ audioUrl, speechMarks, speechMarksUrl }, audioIndex) => {
      // this._speechMarksUnprocessed = speechMarks;
      const processedSpeechMarks = this.processSpeechMarks(speechMarks);
      this.speechMarksList.push(processedSpeechMarks);
      this.createSpeechMarksMap(processedSpeechMarks, audioIndex);

      processDOMforTts({ speechMarks, styleProfile: this.styleProfile.getStyleProfile(), qConfig: questionConfig, lang: this.lang.c() });
    })
    // console.log(this.speechMarksList, this.speechMarksMap)
    this._speechMarksMeta.isSpeechMarksProcessed = true
    this._speechMarksMeta.isProcessedDOM = true
  }

  initChunkForTTS() {
    const questionContainer = document.querySelector('.question-container') as HTMLElement;
    this.speechEngine.prepareContainer(questionContainer, true);
  }

  onToggle() {
    if (!this.isActive && !this.isOnSameQuestion) {
      return this.destroy();
    }
    if (!this.isActive) {
      this.cleanUpTextToSpeech();
    }
    if (this.isOnSameQuestion && this._speechMarksMeta.isProcessedDOM && this._speechMarksMeta.isSpeechMarksProcessed) return;
    // this.initProcessDOMForTts();

    // if(!this.isActive) {
    //   return this.destroy();
    // }

    // if(this._speechMarksMeta.isProcessedDOM && this._speechMarksMeta.isSpeechMarksProcessed) return;

    // // Active
    // const questionConfig = this.__qConfigCache;
    // // TODO: make sure speechMarksList and speechMarksMap is empty
    // this.speechMarksMap = new Map();
    // this.speechMarksList = [];

    // questionConfig.voiceover.voiceoverList.forEach(({audioUrl, speechMarks, speechMarksUrl}, audioIndex) => {
    //   // this._speechMarksUnprocessed = speechMarks;
    //   const processedSpeechMarks = this.processSpeechMarks(speechMarks);
    //   this.speechMarksList.push(processedSpeechMarks);
    //   this.createSpeechMarksMap(processedSpeechMarks, audioIndex);

    //   processDOMforTts({speechMarks, styleProfile: this.styleProfile.getStyleProfile(), qConfig: questionConfig, lang: this.lang.c()});
    // })
    // // console.log(this.speechMarksList, this.speechMarksMap)
    // this._speechMarksMeta.isSpeechMarksProcessed = true
    // this._speechMarksMeta.isProcessedDOM = true

    console.log('Dom Processed')


    // proccess speech marks
    // if(!isSpeechMarksProcessed){
    //   this._speechMarksUnprocessed = speechMarks;
    //   this.speechMarks = this.processSpeechMarks(speechMarks);
    //   this.speechMarksMap = this.createSpeechMarksMap(this.speechMarks);
    //   this._speechMarksMeta.isSpeechMarksProcessed = true  
    // }    

    // pre-process dom
    // if(!isProcessedDOM){
    //   if(!this._speechMarksUnprocessed) return alert('No speechMarks available, Please generate speech marks using the item voice-over in the question')
    //   this._speechMarksMeta.isProcessedDOM = true
    //   processDOMforTts({speechMarks: this._speechMarksUnprocessed, styleProfile: this.styleProfile.getStyleProfile(), qConfig: questionConfig, lang: this.lang.c()});
    // }
  }

  processSpeechMarks(speechMarks: speechMarkChild[]): SpeechMarksMetaPropsExtended[] {
    const flattenedSpeechMarks = [];

    speechMarks.map(({ meta, nodeId, children: sentences }) => {
      sentences.map(({ meta: sentenceMeta, nodeId: sentenceNodeId, children: words }) => {
        flattenedSpeechMarks.push({ ...sentenceMeta, nodeId: sentenceNodeId })

        words.map(({ meta: wordMeta, nodeId: wordNodeId }) => {
          flattenedSpeechMarks.push({ ...wordMeta, nodeId: wordNodeId })
        })
      })
    })
    // console.log(flattenedSpeechMarks)
    return flattenedSpeechMarks
  }

  createSpeechMarksMap(speechMarks: SpeechMarksMetaPropsExtended[], audioIndex: number) {
    speechMarks.forEach(sm => this.speechMarksMap.set(sm.nodeId, { ref: sm, audioIndex }))
    // this.speechMarksMap
    // const speechMarksMap: Map<string, SpeechMarksMetaPropsExtended> = mapBy(speechMarks, 'nodeId');
    // return speechMarksMap
  }

  //  Text to Speech

  private _selectedNodeRefId: string
  private _selectedNodeSpeechMarksIndex: number
  private _selectedAudioSpeechMarksListIndex: number;
  private _speechMarksUnprocessed: ISpeechMarks;
  speechMarksList: SpeechMarksMetaPropsExtended[][];
  // speechMarks: SpeechMarksMetaPropsExtended[]
  speechMarksMap: Map<string, { ref: SpeechMarksMetaPropsExtended, audioIndex: number }>

  private _ttsAudioPlayer: HTMLAudioElement;
  public get ttsAudioPlayer(): HTMLAudioElement {
    return this._ttsAudioPlayer;
  }
  public set ttsAudioPlayer(value: HTMLAudioElement) {
    this._ttsAudioPlayer = value;
  }

  ttsAudioState = {
    isPlaying: false,
    isPaused: true,
    currentTime: 0
  }

  cleanUpTextToSpeech() {
    // remove highlighting
    if (this._selectedNodeRefId) cleanupHighlighting(this._selectedNodeRefId);
    this._selectedNodeRefId = undefined;
    this._selectedNodeSpeechMarksIndex = undefined
  }

  resetSpeechMarksMeta() {
    Object.keys(this._speechMarksMeta)?.forEach(prop => {
      this._speechMarksMeta[prop] = false;
    })
    this.speechMarksList = undefined
    this.speechMarksMap = undefined;
    this._speechMarksUnprocessed = undefined
  }

  destroy() {
    console.log('destroy tts')
    this.cleanUpTextToSpeech();

    // Object.keys(this._speechMarksMeta)?.forEach(prop => {
    //   this._speechMarksMeta[prop] = false;
    // })

    // this.speechMarksList = undefined
    // this.speechMarksMap = undefined;
    // this._speechMarksUnprocessed = undefined
    this.resetSpeechMarksMeta();




    if (this._ttsAudioPlayer) {
      try {
        if (!this._ttsAudioPlayer.paused) this.pauseTextToSpeech();
      } finally {
        this._ttsAudioPlayer.removeEventListener('timeupdate', this.onTtsAudioTimeUpdate);
        // this._ttsAudioPlayer = undefined
      }
    }

    // if(this.isActive) this.toggle(false);
  }

  public playTextToSpeech() {
    const selectedNode = getCurrentSelectedNode();
    if (this.ttsAudioState.isPaused && this.isReadyToPlay(selectedNode)) {
      // Set the current time for the audio player
      this.ttsAudioPlayer.currentTime = this.ttsAudioState.currentTime;
      // Play the audio from the current state
      return this.playTextToSpeechAudio();
    }
    // this.initalizeTextToSpeechReading();
  }

  private isReadyToPlay(selectedNode) {
    return this._selectedNodeRefId && this._selectedNodeSpeechMarksIndex && (!selectedNode || this._selectedAudioSpeechMarksListIndex === 0);
  }

  private playTextToSpeechAudio() {
    // this.ttsAudioState.isPlaying = true
    // this.ttsAudioState.isPaused = false;
    this._ttsAudioPlayer.play();
  }

  public pauseTextToSpeech() {
    this.ttsAudioPlayer.pause();
    this.ttsAudioState.isPlaying = false;
    this.ttsAudioState.isPaused = true
  }

  public stopTextToSpeech() {
    this.pauseTextToSpeech();
    this.cleanUpTextToSpeech();
  }


  public initalizeTextToSpeechReading() {

    // Check Requirements
    const selectedNode = getCurrentSelectedNode();
    let selectedNodeId: string;


    // if DOM is processed or not
    if (selectedNode && !this._speechMarksMeta.isProcessedDOM && !this._speechMarksMeta.isSpeechMarksProcessed) {
      const selectionRange = selectedNode.getRangeAt(0);
      const { startOffset, endOffset } = selectionRange;
      const authoringNode = getAuthoringElementOfCurrentNode(<HTMLElement>selectedNode.anchorNode);
      const authoringNodeId = getNodeReferenceId(authoringNode).toString();
      this.onToggle(); // process DOM
      const textEl = document.getElementById(authoringNodeId);
      const childNodes = traverseDOM(textEl, isTextNode);

      let exploredTextIdx = 0;
      const textNodes = Array.from(childNodes);

      for (const textNode of textNodes) {
        if (textNode.textContent.length + exploredTextIdx >= startOffset) {
          selectedNodeId = getNodeRefIdFromEl(textNode);
          break;
        } else {
          exploredTextIdx += textNode.textContent.length;
        }
      }

      selectedNode.removeAllRanges();
      const newRange = document.createRange();
      newRange.selectNode(document.getElementById(selectedNodeId));
      selectedNode.addRange(newRange);
    }

    if (selectedNode) {

      if (!selectedNodeId) {
        const sourceEl = (<HTMLElement>selectedNode.anchorNode)
        if (!sourceEl) return alert('Please Try again');
        this._selectedNodeRefId = getNodeRefIdFromEl(sourceEl)
      } else {
        this._selectedNodeRefId = selectedNodeId;
      }

      if (!this._selectedNodeRefId) return alert('Node not found, Please contact support');
      // check if Id matches to 'number.number.number' format
      const nodeRefIdRegex = /^\d+\.\d+\.\d+$/;
      const isSelectedNodeRefIdMatch = nodeRefIdRegex.test(this._selectedNodeRefId)
      if (!isSelectedNodeRefIdMatch) return alert("Node Id format doesn't match with speech marks nodeId")

      this._selectedAudioSpeechMarksListIndex = this.speechMarksMap.get(this._selectedNodeRefId).audioIndex;

      // find the index of the selectedNodeRefId in the speech Marks array
      this._selectedNodeSpeechMarksIndex = this.speechMarksList[this._selectedAudioSpeechMarksListIndex].findIndex(sm => sm.nodeId === this._selectedNodeRefId);

      if (!this._selectedNodeSpeechMarksIndex) return alert('Could not find int the speech Marks'); // It should always be there - //TODO: make an audit to check this before hand    
      // remove initial selection after highlight
      selectedNode.removeAllRanges();
    }

    else {
      if (!this._speechMarksMeta.isProcessedDOM && !this._speechMarksMeta.isSpeechMarksProcessed) this.onToggle();
      this._selectedAudioSpeechMarksListIndex = 0
      this._selectedNodeSpeechMarksIndex = 0;
      this._selectedNodeRefId = this.speechMarksList[this._selectedAudioSpeechMarksListIndex][this._selectedNodeSpeechMarksIndex + 1].nodeId;
    }

    // console.log(this._selectedNodeRefId, this._selectedNodeSpeechMarksIndex, this._selectedAudioSpeechMarksListIndex)

    // highlight
    highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.SENTENCE)
    highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.WORD)
    // highlightSentence(this._selectedNodeRefId, ETtsHighlightAction.ADD);
    // highlightWord(this._selectedNodeRefId, ETtsHighlightAction.ADD);

    this.initAudioPlayer();
  }

  initAudioPlayer() {
    if (!this.ttsAudioPlayer) return console.log('Audio player is undefined');
    this.loadAudioSrc();
    const startTimeMs = this.getAudioPlayTime();
    // set the initial audio time before playing based on selection
    const startTimeSec = convertMsToSeconds(startTimeMs)
    this._ttsAudioPlayer.currentTime = startTimeSec
    this.ttsAudioState.currentTime = startTimeSec

    // Initialize audio player events
    this.initAudioPlayerEvents();

    // start the audio player
    // this.playOnload();
    this.playTextToSpeechAudio();

  }

  private loadAudioSrc() {
    const audioSrc = this.__qConfigCache.voiceover.voiceoverList[this._selectedAudioSpeechMarksListIndex].audioUrl;
    this.ttsAudioPlayer.src = audioSrc;
    this.ttsAudioPlayer.load();
  }

  private playOnload() {
    this.ttsAudioPlayer.addEventListener('canplay', () => {
      if (this._shouldPlayOnLoad) {
        this._shouldPlayOnLoad = false;
        this.playTextToSpeechAudio();
      }
      this.ttsAudioPlayer.removeEventListener('canplay', () => { });
    });
  }

  private initAudioPlayerEvents() {
    this.ttsAudioPlayer.addEventListener('timeupdate', (event) => this.onTtsAudioTimeUpdate(event));
    this.ttsAudioPlayer.addEventListener('playing', () => {
      this.ttsAudioState.isPlaying = true
      this.ttsAudioState.isPaused = false;
    })
    this.ttsAudioPlayer.addEventListener('ended', () => setTimeout(() => this.onAudioEnd(), 500));
  }

  private getAudioPlayTime() {
    return this.speechMarksMap.get(this._selectedNodeRefId).ref.time
  }

  private _shouldPlayOnLoad = false
  private onAudioEnd() {
    if (!this.ttsAudioState.isPlaying) return //console.log('audio played'
    this.stopTextToSpeech();
    const nextAudioSpeechMarksListIndex = this._selectedAudioSpeechMarksListIndex + 1;
    if (nextAudioSpeechMarksListIndex >= this.speechMarksList.length) return;
    console.log('audio endned for', this._selectedAudioSpeechMarksListIndex, nextAudioSpeechMarksListIndex)
    // load the next Audio sequence
    this._selectedAudioSpeechMarksListIndex = nextAudioSpeechMarksListIndex;
    this._selectedNodeSpeechMarksIndex = 0;
    this._selectedNodeRefId = this.speechMarksList[this._selectedAudioSpeechMarksListIndex][this._selectedNodeSpeechMarksIndex].nodeId
    highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.SENTENCE)
    this.loadAudioSrc();
    // this.playTextToSpeechAudio()
    this._shouldPlayOnLoad = true;
    this.playOnload();
  }

  private onTtsAudioTimeUpdate = (event: Event) => {
    const audioElement = <HTMLAudioElement>event.target
    if(audioElement.paused) return;

    this.ttsAudioState.currentTime = audioElement.currentTime

    if(this._selectedNodeSpeechMarksIndex != null){
      const currentAudioPlaybackMilliseconds = convertSecondsToMs(audioElement.currentTime)
      const speechMarks = this.speechMarksList[this._selectedAudioSpeechMarksListIndex];
      const nextSpeechMarkIndex = this._selectedNodeSpeechMarksIndex + 1 

      if(nextSpeechMarkIndex < speechMarks.length) {

        const nextSpeechMarkMeta = speechMarks[nextSpeechMarkIndex];

        const { time, type, nodeId, customType} = nextSpeechMarkMeta;
        // console.log(currentAudioPlaybackMilliseconds, time)
        let snapTime = 100;
        if(this._playbackSpeedToSnapTime[this._defaultPlaybackSpeed]) {
          snapTime = this._playbackSpeedToSnapTime[this._defaultPlaybackSpeed];
        }
        if(Math.abs(time - currentAudioPlaybackMilliseconds) <= snapTime || currentAudioPlaybackMilliseconds >= time) {  //currentAudioPlaybackMilliseconds <= time
          // move to next speech marks
          const prevNodeRefId = this._selectedNodeRefId
          this._selectedNodeSpeechMarksIndex += 1;
          this._selectedNodeRefId = nodeId;

          // #TODO: refactor into switch case rather then nested if else?
          if(customType) {

            if(customType === FILLER_NODE) {
              cleanupHighlighting(prevNodeRefId)
            }

            if(customType.includes(KEY_ID_NODE)) {
              cleanupHighlighting(prevNodeRefId)
              let nodeConfig = customType.includes(DRAG_NODE) ?  highlightNodeTypes.DRAG_EL : highlightNodeTypes.TARGET_EL;
              highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, nodeConfig);
            }

            if(customType === CT_ALT_TEXT) {
              // highlight Image
              cleanupHighlighting(prevNodeRefId)
              highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.IMG)
            }

            if(customType === INSTRUCTION_TEXT) {
              cleanupHighlighting(prevNodeRefId);
              highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.SENTENCE)
            }

          } else {

            if(type === ESpeechMarkTypes.SENTENCE){
              cleanupHighlighting(prevNodeRefId)
              highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.SENTENCE)
            }

            if(type === ESpeechMarkTypes.WORD) {
              this.handleWordHighlighting(type, prevNodeRefId)
            }
          }


        }
      } else {
        // cleanup highlighting
        cleanupHighlighting(this._selectedNodeRefId)
      }

    } else {
      // cleanup highlighting
      cleanupHighlighting(this._selectedNodeRefId)
    }

  }

  handleWordHighlighting(type: ESpeechMarkTypes, prevNodeRefId: string) {
    if (type !== ESpeechMarkTypes.WORD) return;
    const highlightAction = () => {
      let removePreviousHighlight = true;
      const previousNodeType = this.getNodeType(prevNodeRefId);
      const [pNodeId, pSenId, pWordId] = prevNodeRefId.split('.');
      const [cNodeId, cSenId, cWordId] = this._selectedNodeRefId.split('.');
      const isSameNode = pNodeId === cNodeId;
      const isSameSentence = pSenId === cSenId;
  
      if (isSameNode && isSameSentence && pWordId === undefined) {
        // previous highlighting is for sentence we don't want to remove it here
        removePreviousHighlight = false;
      }
  
      if (removePreviousHighlight) {
        highlightElement(prevNodeRefId, ETtsHighlightAction.REMOVE, this.getHighlightNodeConfig(previousNodeType));
      }
      highlightElement(this._selectedNodeRefId, ETtsHighlightAction.ADD, highlightNodeTypes.WORD);
    };
  
    if (this.shouldDelayHighlighting()) {
      setTimeout(highlightAction, this.getHighlightDelay());
    } else {
      highlightAction();
    }

  }

  private shouldDelayHighlighting(): boolean {
    return this._defaultPlaybackSpeed <= 0.75;
  }

  private getHighlightDelay(): number {
    return this._defaultPlaybackSpeed <= 0.75 ? 200 : 0;
  }


  getNodeType(nodeId: string) {
    const meta = this.speechMarksMap.get(nodeId).ref;
    if (!meta) return undefined;
    if (meta.customType) return meta.customType;
    return meta.type;
  }

  getHighlightNodeConfig(nodeType: string) {
    switch (nodeType) {
      case INSTRUCTION_TEXT:
      case ESpeechMarkTypes.SENTENCE: return highlightNodeTypes.SENTENCE;
      case ESpeechMarkTypes.WORD: return highlightNodeTypes.WORD;
      case CT_ALT_TEXT: return highlightNodeTypes.IMG;
      default:
        break;
    }
  }

  initTtsDefaultPlaybackSpeed = () => {

    // init based on style profile
    const profile = this.styleProfile?.getStyleProfile()?.[this.lang.c()];
    if (profile) {
      const defaultPlaybackSpeed = profile.configuration?.accessibility?.textToSpeech?.defaultPlaybackSpeed;
      if (+defaultPlaybackSpeed) {
        this._defaultPlaybackSpeed = defaultPlaybackSpeed;  // override the default const from style profile
      }
    }

    // based on if user selected playback speed previously
    this._defaultPlaybackSpeed = this.getUserPlaybackSpeed();
  }

  // playback speed in local storage

  storeUserPlaybackSpeed = (playbackSpeed: number) => {

    if (this._defaultPlaybackSpeed === +playbackSpeed) return;
    this._defaultPlaybackSpeed = playbackSpeed;

    const uid = this.auth.getUid();
    if (uid === -1)
      return; // should retrive from in-memory variable

    const data = { uid, playbackSpeed };
    storeDataInLocalStorage(LOC_STORAGE_TTS_KEY, JSON.stringify(data));
  }

  getUserPlaybackSpeed = () => {
    const uid = this.auth.getUid();
    const localPlaybackSpeedStr = retriveDataFromLocalStorage(LOC_STORAGE_TTS_KEY);

    if (uid === -1 || localPlaybackSpeedStr == null)
      return this._defaultPlaybackSpeed;

    try {
      const localPlaybackSpeed = JSON.parse(localPlaybackSpeedStr);
      if (localPlaybackSpeed?.uid !== uid) {
        removeDataFromLocalStorage(LOC_STORAGE_TTS_KEY);
        return this._defaultPlaybackSpeed;
      }

      return +localPlaybackSpeed.playbackSpeed;

    } catch (error) {
      console.error(LOC_STORAGE_TTS_KEY, "failed to retrive Playback speed for user");
      return this._defaultPlaybackSpeed;
    }
  }


}
