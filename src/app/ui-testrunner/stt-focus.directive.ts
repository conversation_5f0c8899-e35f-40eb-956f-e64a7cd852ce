import { Directive, ElementRef, HostListener } from '@angular/core';
import { SttContextService } from './utils/speech-to-text/stt-context.service';

@Directive({
  selector: '[sttFocus]'
})
export class SttFocusDirective {

  constructor(
    private el: ElementRef<HTMLElement>,
    private ctx: SttContextService
  ) { }

  @HostListener('focus')
  @HostListener('input')
  onFocus() {
    this.ctx.setFocused(this.el.nativeElement);
  }

  @HostListener('blur')
  onBlur() {
    this.ctx.setFocused(null);
  }

}