import { Injectable } from '@angular/core';

export class VoiceSound { 

  constructor( private el:HTMLMediaElement, private audioBuffer:AudioBufferService, private isQuestionAudio:boolean){
    el.addEventListener('play', this.addToBuffer);
    if (!isQuestionAudio){
      el.addEventListener('pause', this.removeFromBuffer);
    }
    el.addEventListener('ended', this.removeFromBuffer);
  }
  destroy(){
    if (this.el){
      this.el.pause()
    }
    this.el.removeEventListener('play', this.addToBuffer);
    this.el.removeEventListener('pause', this.removeFromBuffer);
    this.el.removeEventListener('ended', this.removeFromBuffer);
    this.removeFromBuffer()
  }
  getElement = () =>  this.el;
  addToBuffer = () => { this.audioBuffer.add(this); }
  removeFromBuffer = () => { console.log('pause'), this.audioBuffer.remove(this); }
  play = async() => { 
    if (this.audioBuffer.activeSound){
      await this.audioBuffer.activeSound.stop();
    }

    // Wait for the audio to be loaded
    if (this.el.readyState < 2) {
      await new Promise((resolve) => {
        this.el.addEventListener('canplay', resolve, { once: true });
      });
    }

    try {
      await this.el.play();
    }
    catch (e) {
      console.warn('Error during play:', e);
      // If the error is about interrupted loading, try playing again after a short delay
      if (e.name === 'AbortError') {
        setTimeout(() => {
          this.el.play().catch(err => console.warn('Retry play failed:', err));
        }, 100);
      }
    }
  }
  stop = () => {
    try {
      if (this.audioBuffer.activeSound === this) {
        this.el.pause();
        this.el.currentTime = 0; // start it from beginning next time
      }
    }
    catch(e){
      console.warn('Cannot stop audio', e);
    }
  }
}

@Injectable({
  providedIn: 'root'
})
export class AudioBufferService {

  constructor() { }
  activeSound:VoiceSound;

  add(sound:VoiceSound){
    if (this.activeSound && (sound !== this.activeSound)){
      this.activeSound.stop();  
    }
    this.activeSound = sound;
  }

  remove(sound:VoiceSound){
    if (sound === this.activeSound){
      this.activeSound.stop();
      this.activeSound = null;
    }
  }

}
