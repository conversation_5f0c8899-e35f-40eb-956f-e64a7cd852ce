
<div 
    class="element-render-moveable-dnd"
    [id]="'element-render-moveable-dnd-'+ element.entryId"
    [class.has-frame]="element.hasFrame"
    [class.is-dragging]="isDragging"
    [style.width.em]="element.width"
    [style.height.em]="element.height"
>
    <render-image 
        class="render-image"
        *ngIf="element.backgroundImg" 
        [element]="element.backgroundImg"
        [ngStyle]="getImageStyle()"
    ></render-image>
    <div class="background-el" [class.override-no-ptr-events]="allowBgImagePtrEvents()">
        <div *ngFor="let image of this.element.moveableImages">
            <div *ngIf="image" (click)="checkWholeHighlightIfType(image)">
              <element-render class="moveable-obj" [style.left.em]="image.x" [style.top.em]="image.y" [contentElement]="image" [ngStyle]="getImageStyle()" [questionPubSub]="questionPubSub"></element-render>
            </div>
        </div>
    </div>
    <div  cdkDropListGroup>
        <div *ngFor="let target of this.targets;let cind=index" tabindex="0" (keyup.enter)="onEnter($event,target,cind)"
            cdkDropList
            class="target-el"
            [class] = "getTargetElKeyIdClassName(target)"
            [cdkDropListData]="target.contents"
            cdkDropListSortingDisabled
            (cdkDropListDropped)="drop($event); isDragging=false"
            (cdkDropListEntered)="isDragging=true"
            (mousedown)="onClickToDrag($event, target, cind)"
            [ngStyle]="target.targetContext ? getTargetStyle(target) : {}"
            [class.is-selected-edit]="editSelection.isSelectedEdit(target.targetContext, questionPubSub)"
            [style.border]="getBorderEditSelect(target)"
        >
            <div class="hover-indicator"></div>
            <div cdkDrag class="drag-el" [class.is-deactivateDropShadow]="element.isHideDraggableDropShadow" [cdkDragDisabled]="isLocked" 
                (cdkDragStarted) = "dragStarted($event)"                
                (cdkDragMoved) = "dragging($event)"
                [class] = "getGroupingElKeyIdClassName(target)"
                [class.is-transparent]="element.isDragsTransparent" 
                [style.width.em]="element.isDraggableNotResizable ? '' : target.targetContext.width" 
                [style.height.em]="element.isDraggableNotResizable ? '' : target.targetContext.height"
                [style.padding.em]="target.targetContext.padding ? target.targetContext.padding : target.contents[0].ref.padding"
                [style.background-color]="getDraggableColour(target.contents[0].ref)"
                *ngIf="target.contents && target.contents[0] && target.contents[0].ref && target.contents[0].ref.element"
                (mousedown)="clickDrag(target.contents[0].ref); onClickToDrag($event, target, cind)"
                (click)="checkWholeHighlightIfType(target.contents[0].ref.element)"
            >
                <element-render 
                    [class.is-selected]="isSelected(target)"
                    [class.is-para-full-width]="element.isParaFullWidth"
                    [contentElement]="target.contents[0].ref.element"
                    [questionPubSub]="questionPubSub"
                ></element-render>
                <div class="img-drag-shield"></div>
                <render-audio 
                   *ngIf="target.contents[0].ref.voiceover && target.contents[0].ref.voiceover.url"
                   [defaultPlaybackSpeed]="getPlaybackspeed()"
                   [url]="target.contents[0].ref.voiceover.url" 
                  [trigger]="getClickTrigger(target.contents[0].ref)" 
                  [isTriggerDisabled]="!isVoiceOverEnabled()"
                ></render-audio>
            </div>
        </div>
    </div>
</div>
