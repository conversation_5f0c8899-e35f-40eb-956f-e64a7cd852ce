import { IContentElement } from "../models";
import { IElementTypeEditInfo } from './../models/index'

export interface IContentElementMath extends IContentElement {
    latex: string;
    paragraphStyle: string;
    elementMode?: IEquationBlockMode;
    rawLatex?: string;
    altText?: string;
  }

  export const mathEditInfo: IElementTypeEditInfo = {
    editExcludeFields: [],
    editTextFields: ['latex', 'caption', 'text', 'value', 'content', 'label', 'positionLabel'],
    editKeyFieldsToShow: ['latex', 'paragraphStyle'],
    nonHiddenChangeLogFields: ['latex', 'paragraphStyle']
  }

  export enum IEquationBlockMode{
    MATH = 'Math',
    CHEMISTRY = 'Chemistry'
  }