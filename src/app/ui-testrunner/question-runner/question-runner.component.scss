.runner-container {
    
    color: #000;
    overflow: visible;
    display:flex;
    flex-direction: column;
    // justify-content: center;
    // align-items: center;
    // padding:0.5em;

    .scroll-container {
        // margin: auto;
        // min-height: 200px;
        // width: 100%;
        // overflow-y: auto;
        // overflow-x: hidden;
    }

    .question-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        &.is-print-mode {
            display:block;
        }
    }

    .question-header {
        position: absolute;
        top:0px;
        left:0px;
        right: 0px;
        background-color:#f1f1f1;
        padding: 0.2em;
        font-size: 80%;
    }
    .content-element {
        &.element-width-constrainer {
            max-width: 38em;
        }
    }
}

.footer-buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.question-panels {
    display:flex;
    flex-direction:row;
    max-width: 100%;
    &.is-print-mode {
        display:block;
    }
    .question-panel {
        max-width: 100%;
        &.is-print-mode {
            display:block;
        }
    }
}




.zoom-settings {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
}


/* make keyframes that tell the start state and the end state of our object */
@keyframes fadeIn { 
    from { 
        opacity:0; 
        transform: translateY(50px) ;
    } 
    to { 
        opacity:1; 
        transform: translateY(0);
    } 
}
.fade-in {
    opacity:0;  /* make things invisible upon start */
    animation:fadeIn cubic-bezier(0.68, -0.55, 0.265, 1.55) 1;
    animation-fill-mode:forwards;
    animation-duration:500ms;
}

.scale-to-fit {
    transform: scale(0.7);
}
.is-element-hidden {
    display: none;
}

.tts-audio-player-toolbar{
    position: fixed;
    top: 0px;
    left: 40%;
    z-index: 1000;
    display: flex;
    justify-content: center;
}

.tts-audio-player {
    &.is-player-hidden {
        display: none;
    }
}