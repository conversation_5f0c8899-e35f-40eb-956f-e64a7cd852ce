import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild, AfterViewInit, HostListener } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SafeResourceUrl } from '@angular/platform-browser';
import { ItemComponentEditService } from '../../ui-item-maker/item-component-edit.service';
import { EditViewMode } from '../../ui-item-maker/item-set-editor/models/types';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { AuthScopeSetting, AuthScopeSettingsService } from '../../ui-item-maker/auth-scope-settings.service';
import { elementIconById } from '../../ui-item-maker/item-set-editor/models';
import { DrawingLogService } from '../drawing-log.service';
import { DisplayMode } from '../element-render-audio/model';
import { IContentElementCanvas } from '../element-render-canvas/model';
import { IContentElementFrame } from '../element-render-frame/model';
import { ElementType } from "../models";
import { IContentElement, IQuestionConfig } from '../models/index';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { TextToSpeechService, ETextToSpeechVersions } from '../text-to-speech.service';
import { QuestionRunnerLayoutService } from '../question-runner-layout.service';
import { IAudioSrcInfo } from '../render-audio/render-audio.component';
import { Subscription } from 'rxjs';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { RenderAudioComponent } from '../render-audio/render-audio.component';
import { ItemSetFrameworkCtrl } from 'src/app/ui-item-maker/item-set-editor/controllers/framework';
import { SpeechEngineService, TTSMode } from '../utils/text-to-speech/on-the-fly/speech-engine.service';
import { ItemBankCtrl } from 'src/app/ui-item-maker/item-set-editor/controllers/item-bank';
import { IAssessmentFrameworkDetail } from '../../ui-item-maker/item-set-editor/models/assessment-framework';

interface IAudioPlayInfo extends IAudioSrcInfo{
  isPlaying: boolean,
}

enum EttsToolBarAction {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  READ_FROM_SELECTION = 'read-from-selection'
}

@Component({
  selector: 'question-runner',
  templateUrl: './question-runner.component.html',
  styleUrls: ['./question-runner.component.scss']
})
export class QuestionRunnerComponent implements OnInit, OnDestroy, OnChanges, AfterViewInit {

  @Input() currentQuestion:IQuestionConfig;
  @Input() currentQuestionMeta: {[key:string]: any};
  @Input() currentQuestionIndex:number = 0;
  @Input() totalQuestions:number = 4;
  @Input() questionState:any;
  @Input() isSubmitted:boolean;
  @Input() containingWidth:number = 40;
  @Input() containingWidthSpill:number;
  @Input() allowSubtitles: boolean = false;
  @Input() allowTranscripts: boolean = false;
  @Input() allowAudioPlaybackSpeed: boolean = false;
  @Input() allowVideoPlaybackSpeed: boolean = false;
  @Input() isPrintMode: boolean = false;
  @Input() selectedEditEntry: any = null; 
  @Input() isSkipAnimatedEntry:boolean; 
  @Input() frameWorkTags:{slug:string}[];
  @Input() opinionEssayEditable:boolean; 
  @Input() textInputChange:(args:any) => void;
  @Input() fromIssueReviewer: boolean;
  @Input() cancelOverrideResponse: boolean;
  @Input() frameworkCtrl: ItemSetFrameworkCtrl;
  @Input() asmtFmrk: IAssessmentFrameworkDetail;
  @Input() fromTestRunner: boolean = false;
  @Output() textToSpeechStatus = new EventEmitter<IAudioPlayInfo>();

  questionTextSize = new FormControl(1);
  questionAudioUrl:SafeResourceUrl;
  questionPubSub:QuestionPubSub;
  DisplayMode = DisplayMode; 
  styleProfileChangeSub: Subscription;
  textToSpeechStateSub: Subscription;
  baseFont: string = '';
  ttsToolBarAction = EttsToolBarAction;
  textToSpeechVersions = ETextToSpeechVersions;
  @ViewChild('TTSAudioPlayer') TTSAudioPlayer: RenderAudioComponent;

  speechMode: TTSMode = TTSMode.CLICK;

  constructor(
    private textToSpeech:TextToSpeechService,
    private authScope: AuthScopeSettingsService,
    private whitelabel: WhitelabelService,
    private bufferedLog: DrawingLogService,
    private itemComponentEdit: ItemComponentEditService,
    private questionRunnerLayout: QuestionRunnerLayoutService,
    private styleProfile: StyleprofileService,
    public speechEngine: SpeechEngineService,
    private itemBankCtrl: ItemBankCtrl
  ) {
    this.speechMode = this.speechEngine.getMode();
  }

  ngOnInit() {
    this.reset();
    this.questionRunnerLayout.reset()
    this.initSubscriptions();
    this.textToSpeech.setIsOnSameQuest(true);
    this.speechEngine.updateSettings({
      ttsHoverGranularity: this.asmtFmrk?.toolbarOptions?.ttsHoverGranularity
    });
  }
  
  private reset(){
    this.questionPubSub = new QuestionPubSub(this.isPrintMode, this.selectedEditEntry);
    if (this.questionState && this.currentQuestion){
      if (!this.questionState.__meta){
        this.questionState.__meta = {};
      }
      this.questionState.__meta.item_id = this.currentQuestion.id 
      this.questionState.__meta.entryOrder = this.currentQuestion.entryOrder
    }
    this.speechEngine.stop();
    this.textToSpeech.setTextToSpeechConfig({questionConfig: this.currentQuestion});
    // if(this.textToSpeech.isActive) {
    //   this.textToSpeech.destroy();
    // }
  }

  ngOnChanges(changes:SimpleChanges){
    if (changes.currentQuestion){
      this.bufferedLog.bufferedLog('QUESTION_RESET_PRE', {id: (this.currentQuestion || {}).id, questionState: this.questionState });
      this.reset();
      this.itemComponentEdit.reset();
      this.bufferedLog.bufferedLog('QUESTION_RESET_POST', {id:(this.currentQuestion || {}).id, questionState: this.questionState });
    }
    if(changes.isPrintMode || changes.selectedEditEntry) {
      this.reset();
    }
  }

  getZIndex(contentElement:IContentElement) {
    return this.questionRunnerLayout.id2ZIndex.get(contentElement.entryId)
  }

  isFadeInEntrance(){
    return this.authScope.getSetting(AuthScopeSetting.Q_FADE_IN_ENTRANCE);
  }

  isAudioPlayerShowing(){
    return this.textToSpeech.isActive && !this.fromTestRunner;
  }

  getQuestionAudioUrl(){
    return this.currentQuestion?.voiceover?.url;
  }

  isInputLocked(){
    return this.isSubmitted;
  }

  ghostAdded = false;
  getPixelsPerEM() {
    const el = document.getElementById("ghost");
    if (!el) return 0;
    if (!this.ghostAdded) {
      document.body.appendChild(el);
      this.ghostAdded = true;
    }
    const width = el.offsetWidth;
    const len = width/10;
    //document.body.removeChild(el);
    return len;
  }

  showElement(contentElement:IContentElement) {
    if (!this.isPrintMode || contentElement.elementType == ElementType.RESULTS_PRINT) return true;
  }

  getScaleToFitStyle(element) {
    return{};
    /*const widthConstrainedElement = this.checkForElementWidthConstraint(element);
    if (!widthConstrainedElement && window.innerWidth<1000) {
      return true;
    }*/
    let style:any = {};
    const scalingBuffer = 0.95;
    if (element.elementType == ElementType.CANVAS) {
      const pixelsPerEm = this.getPixelsPerEM();
      if (pixelsPerEm==0) return style;
      let width = ((window.innerWidth-180)/pixelsPerEm)
      const box = document.getElementById("readingPassageSplit")
      const questionRunnerInReadingPassage = document.getElementById("questionRunnerInReadingPassage")
      if (questionRunnerInReadingPassage) {
        width = box.offsetWidth/pixelsPerEm;
      }
      if (element.width>Math.floor(width)) {
        style["font-size"] = Math.floor(width)/element.width*scalingBuffer+"em";
      }
      //console.log(style);
      //console.log(window.innerWidth)
    }
    style["line-height"]="1em"
    return style;
  }

  getIconByElementTypeId(elementTypeId:string){
    return elementIconById.get(elementTypeId);
  }

  checkForElementWidthConstraint(elementTypeId:ElementType){
    // deprecated
    if (!this.whitelabel.getSiteFlag('TEST_RUNNER_WIDTH_CONSTRAINT')) return false;

    switch (elementTypeId){ 
      case ElementType.SELECT_TABLE:
      case ElementType.ORDER:
      case ElementType.CANVAS:
      case ElementType.TABLE:
        return false;
      default: 
        return true;
    }
  }

  isHighContrastException(element:IContentElement) {

    const exceptionTypes: any[] = [ElementType.CANVAS, ElementType.GROUPING, ElementType.MCQ, ElementType.SOLUTION];
    if (exceptionTypes.includes(element?.elementType)) {
      return true;
    }
    return false;
  }


  computeAnimDelayAndDuration(index:number){
    let animDelayIncr = 250;
    let animDuration = 600;
    let animDelay = 0;
    for (let i=1; i<index; i++){
      animDelay += animDelayIncr;
      animDelayIncr *= 0.8;
      animDuration *= 0.8;
    }
    return {
      animDelay,
      animDuration,
    }
  }

  private computeFontScaleByBaseWidth(baseWidth:number){
    let fontScale = 1
    if (baseWidth && this.containingWidth < baseWidth){
      fontScale = this.containingWidth / baseWidth;
    }
    return fontScale;
  }

  getFontScale(element:IContentElement){
    let fontScale = 1;
    if (this.containingWidth){
      if (element.elementType === ElementType.CANVAS){
        const elementCanvas = <IContentElementCanvas> element;
        fontScale = this.computeFontScaleByBaseWidth(elementCanvas.width);
      }
      if (element.elementType === ElementType.FRAME){
        const elementFrame = <IContentElementFrame> element;
        fontScale = this.computeFontScaleByBaseWidth(elementFrame.width);
      }
    }
    return fontScale;
  }

  getElementStyle(element:IContentElement, index:number){
    const fontScale = this.getFontScale(element);
    const {animDelay, animDuration,} = this.computeAnimDelayAndDuration(index);
    const style = {
      'font-size': fontScale+'em',
    }
    if (this.isSkipAnimatedEntry){
      style['animation-delay'] = '0ms';
      style['animation-duration'] = '1ms';
    }
    else {
      style['animation-delay'] = animDelay+'ms';
      style['animation-duration'] = animDuration+'ms';
    }
    if (this.getZIndex(element)!=undefined) {
      style['position']='relative'
      style['z-index']=this.getZIndex(element)
    }
    return style
  }

  isElementVisible(contentElement) {
    return true;
    //return contentElement.elementType === ElementType.RESULTS_PRINT || !this.isPrintMode;
  }

  emitPlay($event: IAudioPlayInfo) {
    this.textToSpeechStatus.emit({
      ... $event,
      isPlaying: true
    }); 
  }

  emitEndPlay($event: IAudioPlayInfo) {
    this.textToSpeechStatus.emit({
      ... $event,
      isPlaying: false
    }); 
  }

  ngAfterViewInit(): void {
    // if(this.textToSpeech.isActive) {
    //   setTimeout(() => {
    //     // this.textToSpeech.initProcessDOMForTts();
    //   }, 0);
    // }
  }

  ngOnDestroy() {
    if(this.styleProfileChangeSub) {
      this.styleProfileChangeSub.unsubscribe();
    }
    if(this.textToSpeechStateSub) {
      this.textToSpeechStateSub.unsubscribe();
    }
    // this.textToSpeech.destroy();
    // this.textToSpeech.setIsOnSameQuest(false);
  }

  ngAfterViewChecked() {
    this.initTtsAudioPlayer();
  }

  private initSubscriptions() {
    this.styleProfileChangeSub = this.styleProfile.getStyleProfileChanges().subscribe(hasStyleProfile => {
      if(hasStyleProfile) {
        this.baseFont = this.styleProfile.getBaseFont();
      }
    });

    // this.textToSpeechStateSub = this.textToSpeech.textToSpeechStateChangeSub.subscribe(() => 
    //   this.textToSpeech.onToggle()
    // );
  }

  getTextToSpeechVersion = () => {
    if(this.frameworkCtrl && this.frameworkCtrl.asmtFmrk.toolbarOptions.tts_v2) {
      return ETextToSpeechVersions.TTSv2;
    } else if(this.frameworkCtrl && this.frameworkCtrl.asmtFmrk.toolbarOptions.tts) {
      return ETextToSpeechVersions.TTSv1;
    } else {
      return ETextToSpeechVersions.TTSv2;
    }
  }

  isTextToSpeechV1 = () => this.getTextToSpeechVersion() === ETextToSpeechVersions.TTSv1;
  isTextToSpeechV2 = () => this.getTextToSpeechVersion() === ETextToSpeechVersions.TTSv2;

  private initTtsAudioPlayer = () => {
    if(this.TTSAudioPlayer) {
      if(!this.textToSpeech.ttsAudioPlayer) {
        this.textToSpeech.ttsAudioPlayer = this.getTtsAudioElement();
      }
    }
  }

  getTtsAudioElement() : HTMLAudioElement | undefined {
    let el;
    if (this.TTSAudioPlayer && this.TTSAudioPlayer.audioPlayer) {
      el = this.TTSAudioPlayer.audioPlayer.nativeElement;
    }
    console.log(el)    
    return el;
  }

  onSpeechModeUpdate(mode: TTSMode): void {
    this.speechMode = mode;
  }

  getTextToSpeechDefaultPlaybackRate() {
    if(this.textToSpeech.defaultPlaybackSpeed) return this.textToSpeech.defaultPlaybackSpeed;
    return 1;
  }

  onPlaybackRateChange(rate: number) {
    this.textToSpeech.defaultPlaybackSpeed = rate;
  }
}
