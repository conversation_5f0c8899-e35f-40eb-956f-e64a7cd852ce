
<!--<div 
    *ngIf="hasControls && isOneButton() && isCustomMode()" 
    class="one-button" 
    (click)="onTriggerPlay(true);stopProp($event);" 
    [class.is-default]="!isPlaying"
    [class.is-playing]="isPlaying"
>
    <img class="on-default" src="https://eqao.vretta.com/authoring/user_uploads/6276/authoring/audio_button-default/1612797879947/audio_button-default.svg" >
    <img class="on-hover" src="https://eqao.vretta.com/authoring/user_uploads/6276/authoring/audio_button-hover/1612798136994/audio_button-hover.svg" >
    <img class="on-playing" src="https://eqao.vretta.com/authoring/user_uploads/6276/authoring/audio_button-playing/1612798150063/audio_button-playing.svg">
</div>-->
<audio
    #audioPlayer
    (click)="stopProp($event)"
    [src]="sanitizeUrl(url)"
    controlsList="nodownload"
    [controls]="controlsVisible()"
    [class.has-controls]="controlsVisible()"
    (play)="emitPlay()"
    (ended)="triggerDonePlaying()"
    (canplay)="emitCanStartPlaying()"
    (loadedmetadata)="loadedMetaData()"
    (timeupdate)="onTimeUpdate($event)"
    (ratechange)="onPlaybackRateChange($event)"
    [id]="audioID ? audioID : 'audio'"
    (loadstart)="loadStart($event)"
    (loadeddata)="firstFrameLoaded($event)"
    (stalled)="dataStalled($event)"
></audio>

<!-- <plyr plyrTitle=""
      *ngIf="hasControls"
      [plyrType]="audio"
      [plyrPlaysInline]="false"
      [plyrOptions]="plyrOptions"
      [plyrSources]="getAudioSources()"
      plyrCrossOrigin="anonymous"
></plyr> -->
