import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ElementType, IQuestionConfig } from './models';
import { processText, StyleprofileService, StylingProcess } from '../core/styleprofile.service';
import { LangService } from '../core/lang.service';
import { IContentElementMcqOption, McqDisplay } from './element-render-mcq/model';
import { ScriptGenService } from '../ui-item-maker/script-gen.service';
import { IContentElementDynamicImage, ImageStates } from './element-render-image/model';
import { IContentElementTableCell } from './element-render-table/model';
import { IContentElementSolution } from './element-render-solution/model';
import { IContentElementText } from './element-render-text/model';

export interface InteractionVoiceover {
  script: string;
  audioUrl: string;
  synced: boolean;
  elementType: ElementType;
  parentElement?: any;
  entryId: number;
  scriptSnapshot?: string;
}

@Injectable({
  providedIn: 'root'
})
export class VoiceoverStateService {
  private voiceoversSubject = new BehaviorSubject<InteractionVoiceover[]>([]);
  voiceovers$ = this.voiceoversSubject.asObservable();
  constructor(
    private profile: StyleprofileService,
    private lang: LangService
  ) { }

    // Get current voiceovers
    getCurrentVoiceovers(): InteractionVoiceover[] {
      return this.voiceoversSubject.getValue();
    }
  
    // Update all voiceovers
    updateVoiceovers(voiceovers: InteractionVoiceover[]): void {
      this.voiceoversSubject.next(voiceovers);
    }
  
    // Extract voiceovers from question content
    public extractVoiceoversFromQuestion(question: any): InteractionVoiceover[] {
      const voiceovers: InteractionVoiceover[] = [];
      
      const recursiveSearch = (item: any, parent?: any, grandparent?: any) => {
        if (Array.isArray(item)) {
          item.forEach(element => recursiveSearch(element, parent, grandparent));
        } else if (item && typeof item === 'object') {
          if(item.elementType === 'template' && item.content) {
            recursiveSearch(item.content, item, parent);
          }
          if(item.elementType === 'frame' && item.content) {
            recursiveSearch(item.content, item, parent);
          }
          if (item.voiceover && typeof item.voiceover === 'object') {
            const voiceover = item.voiceover;
            // voiceover.scriptSnapshot = this.getSnapshotContentForElement(item);
            
            // TODO: we may want to keep track of the parent element in the voiceover object itself
            // so that we can display the parent element in the voiceover table
            if (voiceover.script && voiceover.url) {
              let synced = false;
              if(voiceover.scriptSnapshot && voiceover.scriptSnapshot !== '' && voiceover.script && typeof voiceover.scriptSnapshot === 'string') {
                // Remove ellipsis and trim the script
                let content = this.getSnapshotContentForElement(item).replace(/\s*\.{3,}\s*\n*/g, '').trim();
                content = processText(content, <StylingProcess[]>this.profile.getStyleProfile()[this.lang.getCurrentLanguage()].voiceScript.plainText);
                let cleanedSnapshot = voiceover.scriptSnapshot.replace(/\s*\.{3,}\s*\n*/g, '').trim();
                cleanedSnapshot = processText(cleanedSnapshot, <StylingProcess[]>this.profile.getStyleProfile()[this.lang.getCurrentLanguage()].voiceScript.plainText);
                synced = content === cleanedSnapshot;
              }
              if (parent) {
                parent.voiceoverParent = true;
                parent.voiceoverEntryId = voiceover.entryId;
              }
              voiceovers.push({
                script: voiceover.script,
                audioUrl: voiceover.url,
                synced: synced,
                elementType: item.elementType || item.element?.elementType || item.textMode || grandparent?.constructionType,
                parentElement: parent,
                entryId: item.voiceover.entryId,
                // scriptSnapshot: voiceover.scriptSnapshot
              });
            }
          }
          for (const key in item) {
            if (item.hasOwnProperty(key)) {
              recursiveSearch(item[key], item, parent);
            }
          }
        }
      };
  
      recursiveSearch(question);
      return voiceovers;
    }
  
    // Generate and update voiceovers from question
    generateNestedVoiceovers(question: IQuestionConfig): void {
      const allVoiceovers = question.content.reduce((acc, content) => {
        const contentVoiceovers = this.extractVoiceoversFromQuestion(content);
        return [...acc, ...contentVoiceovers];
      }, [] as InteractionVoiceover[]);
  
      if (allVoiceovers.length > 0) {
        const uniqueVoiceovers = [...new Map(
          allVoiceovers.map(voiceover => [`${voiceover.script}_${voiceover.entryId}`, voiceover])
        ).values()];
        this.updateVoiceovers(uniqueVoiceovers);
      }
    }
  
    // Update single voiceover
    updateSingleVoiceover(entryId: number, updates: Partial<InteractionVoiceover>): void {
      const currentVoiceovers = this.getCurrentVoiceovers();
      const index = currentVoiceovers.findIndex(v => v.entryId === entryId);
      
      if (index !== -1) {
        const updatedVoiceovers = [...currentVoiceovers];
        updatedVoiceovers[index] = {
          ...updatedVoiceovers[index],
          ...updates
        };
        this.updateVoiceovers(updatedVoiceovers);
      }
    }

    public updateVoiceoverInTemplateItem(question: any, updatedVoiceover: InteractionVoiceover): void {
      const recursiveUpdate = (item: any) => {
        if (Array.isArray(item)) {
          item.forEach(element => recursiveUpdate(element));
        } else if (item && typeof item === 'object') {
          // Handle frame elements by recursively searching their content
          if (item.elementType === 'frame' && item.content) {
            recursiveUpdate(item.content);
          }
          if(item.elementType === 'template' && item.content) {
            recursiveUpdate(item.content);
          }
          if(item.content) {
            recursiveUpdate(item.content);
          }
          // Check if the current object has a voiceover to update
          if (item.voiceover && typeof item.voiceover === 'object') {
            const voiceover = item.voiceover;
            if (voiceover.entryId === updatedVoiceover.entryId) {
              let synced = false;
              if(voiceover.scriptSnapshot && voiceover.script) {
                // Remove ellipsis and trim the script
                let cleanedScript = voiceover.script.replace(/\s*\.{3,}\s*\n*/g, '').trim();
                let cleanedSnapshot = voiceover.scriptSnapshot.replace(/\s*\.{3,}\s*\n*/g, '').trim();
                synced = cleanedScript === cleanedSnapshot;
              }
              voiceover.script = updatedVoiceover.script;
              voiceover.url = updatedVoiceover.audioUrl;
              // voiceover.scriptSnapshot = updatedVoiceover.script;
              voiceover.synced = synced;
            }
          }
    
          // Recursively search through all properties
          Object.entries(item).forEach(([key, value]) => {
            //skip recursion when the property key is "langLink" to avoid updating the fr as well
            if (key !== 'langLink') {
              recursiveUpdate(value);
            }
          });
        }
      };
    
      recursiveUpdate(question);
    }
    
  
    // Set sync status
    setSyncStatus(entryId: number, synced: boolean): void {
      this.updateSingleVoiceover(entryId, { synced });
    }

  /**
   * Gets the human-readable "content" we want to store in scriptSnapshot
   * for any element type. We do a switch/if that covers each type.
   */
  public getSnapshotContentForElement(element: any): string {
    if (!element) {
      return '';
    }

    switch (element.elementType) {

      // If you have a text element with a `caption` or `paragraphList`:
      case ElementType.TEXT: {
        const textEl = element as IContentElementText;
        if(element.optionType === ElementType.MCQ_OPTION){
          return element.content;
        }
        else if(element.optionType === ElementType.SELECTABLE_TEXT){
          return element.content;
        }
        else{
          return textEl.caption ?? ''; 
        }
      }
      case ElementType.MCQ_OPTION: {
        const mcqOption = element as IContentElementMcqOption;
        return mcqOption.content ?? '';
      }
      case ElementType.IMAGE:
      case ElementType.DYNAMIC_IMAGE: {
        const imgEl = element as IContentElementDynamicImage;
        if (imgEl.altText) {
          return imgEl.altText;
        }
        
        else if (imgEl.images) {
          const img = imgEl.images[ImageStates.DEFAULT];
          if (img?.image?.altText) {
            return img.image.altText;
          }
        }
        
        if (imgEl.subtexts) {
          let result = '';
          imgEl.subtexts.forEach((text) => {
            result += " ... " + text.text + " ... ";
          });
          return result;
        }
        return '';
      }
      case ElementType.TABLE_TEXT: {
        const tableCell = element as IContentElementTableCell;
        return tableCell.val ?? '';
      }
      case ElementType.SOLUTION: {
        const solutionEl = element as IContentElementSolution;
        let content = '';
        solutionEl.content.forEach(node => {
          content += this.getSnapshotContentForElement(node);
        });
        return content;
      }
      case ElementType.MATH: {
        return element.content;
      }
      // Fallback
      default: {
        // If it has a value attribute, that means it's a dnd element
        if(element.value) return element.value;
        // If it has an element, that means it's a insertion element
        if(element.element) return this.getSnapshotContentForElement(element.element);
        return '';
      }
    }
  }

    updateSyncedStatus(element: any) {
      if (element.voiceover && element.voiceover.entryId && element.voiceover.scriptSnapshot) {

        // Check if scriptSnapshot is a string
        if (typeof element.voiceover.scriptSnapshot !== 'string') {
          return;
        }

        const currentScript = this.getSnapshotContentForElement(element);
        let cleanedScript = currentScript.replace(/\s*\.{3,}\s*\n*/g, '').trim();
        cleanedScript = processText(cleanedScript, <StylingProcess[]>this.profile.getStyleProfile()[this.lang.getCurrentLanguage()].voiceScript.plainText);
        let cleanedScriptSnapshot   = element.voiceover.scriptSnapshot?.replace(/\s*\.{3,}\s*\n*/g, '').trim() ?? '';
        cleanedScriptSnapshot = processText(cleanedScriptSnapshot, <StylingProcess[]>this.profile.getStyleProfile()[this.lang.getCurrentLanguage()].voiceScript.plainText);
        // Compare
        element.voiceover.synced = (cleanedScript === cleanedScriptSnapshot);
        this.setSyncStatus(element.voiceover.entryId, element.voiceover.synced);
        
      }
    }

    updateAllSyncedStatuses(question: IQuestionConfig) {
      // Bind 'this' to the recursive function or use arrow function
      const recursiveCheck = (item: any) => {
        if (Array.isArray(item)) {
          item.forEach(child => recursiveCheck(child));
        } else if (item && typeof item === 'object') {
          // Check if the current object has a voiceover to update
          if (item.voiceover && typeof item.voiceover === 'object') {
            this.updateSyncedStatus(item);
          }

          // Keep recursing
          Object.values(item).forEach(val => recursiveCheck(val));
        }
      };

      recursiveCheck(question);
    }
  
    // Update voiceover content
    updateVoiceoverContent(entryId: number, script: string, audioUrl?: string): void {
      // If audioUrl is provided, store the script as the snapshot
      const updates: Partial<InteractionVoiceover> = { script};
      if (audioUrl) {
        updates.audioUrl = audioUrl;
      }
      this.updateSingleVoiceover(entryId, updates);
    }

    // Find voiceover by entryId
    getVoiceoverByEntryId(entryId: number): InteractionVoiceover | undefined {
      const currentVoiceovers = this.getCurrentVoiceovers();
      return currentVoiceovers.find(v => v.entryId === entryId);
    }

     // Clear all voiceovers
    clearInteractions(): void {
      this.voiceoversSubject.next([]);
    }
}
