import { Component, OnInit, Input, Renderer2, <PERSON>Child, ElementRef } from '@angular/core';
import { generateDefaultElementImage, generateOldDefaultImage } from '../../ui-item-maker/item-set-editor/models/index'
import { DomSanitizer } from "@angular/platform-browser";
import { state } from '@angular/animations';
import { Subject } from 'rxjs';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { TextToSpeechService } from '../text-to-speech.service';
import { QuestionState } from '../models';
import { IContentElementDynamicImage, ImageStates, IContentElementImageSubText } from './model';
import { EditSelectionService } from '../edit-selection.service';
import { ElementType } from 'src/app/ui-testrunner/models';
import { SpeechEngineService } from '../utils/text-to-speech/on-the-fly/speech-engine.service';


@Component({
  selector: 'element-render-image',
  templateUrl: './element-render-image.component.html',
  styleUrls: ['./element-render-image.component.scss']
})
export class ElementRenderImageComponent implements OnInit {

  @Input() element:IContentElementDynamicImage;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState; // not used
  @Input() isSelected?: boolean;
  @Input() isHovered?: boolean;
  @Input() questionPubSub?: QuestionPubSub;

  clickTrigger = new Subject<boolean>();
  
  constructor(
    
    private sanitizer: DomSanitizer,
    public textToSpeech: TextToSpeechService,
    public editSelection: EditSelectionService,
    private speechEngine: SpeechEngineService
  ) { }

  ngOnInit() {

  }

  speakAltTextAndTriggerAudio() {
    if (this.speechEngine && this.textToSpeech.isActive) {
      const altText = this.getImage()?.image?.altText;
      if (altText) {
        this.speechEngine.speakIsolatedText(altText);
      }
    }
    this.clickTrigger.next(true);
  }

  imgExists(img) {
    if (img && img.image && img.image.url && img.image.url != '') return true;
    return false;
  }

  getImage() {
    if (!this.element){
      return;
    }
    const imgs = this.element.images;
    if (!imgs)  {
      if (this.element.url) {
        return generateOldDefaultImage(this.element, ImageStates.DEFAULT);
      }
      return;
    }
    if (this.isSelected == true) {
      if (imgs[ImageStates.SELECTED] && this.imgExists(imgs[ImageStates.SELECTED])) return imgs[ImageStates.SELECTED];
    } else if (this.isHovered == true) {
      if (imgs[ImageStates.HOVERING] && this.imgExists(imgs[ImageStates.HOVERING])) return imgs[ImageStates.HOVERING];
    }
    if (imgs[ImageStates.DEFAULT]) return imgs[ImageStates.DEFAULT];
    else if (this.element.url) return generateOldDefaultImage(this.element, ImageStates.DEFAULT);
    return imgs[ImageStates.DEFAULT];
  }

  getFilter() {
    if (this.element.frameState) return this.sanitizer.bypassSecurityTrustStyle("drop-shadow( "+this.element.frameState.dropShadowX+"px "+this.element.frameState.dropShadowY+"px "+this.element.frameState.blurRadius+"px "+this.element.frameState.shadowColor+" )");
    else return '';
  }

  getSubTextColor(sub:IContentElementImageSubText){
    if (!sub.invisible){
      if (sub.colourScheme){
        return sub.colourScheme.textColor
      }
      return '#000000'
    }
  }
  getTransform(sub:IContentElementImageSubText) {
    if (sub.rotation){
      return `rotate(${sub.rotation}deg)`;
    }
    return '';
  }

  getPadding() {
    if (this.element.frameState) return this.element.frameState.padding;
    else return '';
  }

  getSubtextStyle() {
    const style = {}
    if (!this.element.isNoInvertOnHiContrast && this.textToSpeech.isHiContrast) {
      style["filter"] = "invert(1)"
    }
    return style
  }

  isVoiceoverEnabled() {
    return this.textToSpeech.isActive && !this.speechEngine?.isActive;
  }

  /*getOldUrl() {
    const image = { 
      elementType: ElementType.IMAGE,
      url: this.element.url, 
      fileType: this.element.fileType,
      altText: this.element.altText,
      scale: this.element.scale,
      outline: this.element.outline
    }
    return { condition: ImageStates.DEFAULT, image };
  }*/

  /**
   * Special case to not render the voiceover:
   * - When the image element is also the order option - the same voiceover is already rendered in the enclosing option in element-render-order 
   */
  excludeVoiceover():boolean {
    return this.element['optionType'] == ElementType.ORDER_OPTION
  }
}
