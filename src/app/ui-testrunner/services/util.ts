export const indexOf = (arr:any[], t:any) => {
    let i = -1;
    arr.forEach((_t, _i) => {
      if (_t === t){
        i = _i;
      }
    });
    return i;
  }
  

export const mapToJson = (map) =>  {
    return JSON.stringify([...map]);
}

export const storeDataInLocalStorage = (key: string, value: string) => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.error('failed to save in local storage', key, value);
  }
}

export const retriveDataFromLocalStorage = (key: string): string | null => {
  return localStorage.getItem(key);
}

export const removeDataFromLocalStorage = (key: string) => {
  localStorage.removeItem(key);
} 