import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RenderTtsAudioPlayerComponent } from './render-tts-audio-player.component';

describe('RenderTtsAudioPlayerComponent', () => {
  let component: RenderTtsAudioPlayerComponent;
  let fixture: ComponentFixture<RenderTtsAudioPlayerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RenderTtsAudioPlayerComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RenderTtsAudioPlayerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
