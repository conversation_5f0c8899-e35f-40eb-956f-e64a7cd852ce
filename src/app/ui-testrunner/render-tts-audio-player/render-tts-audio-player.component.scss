.tts-container {
  display: flex;
  align-items: center;
  background-color: #d6dbe5;
  border-radius: 0.3em;
  padding: 0.3em;
}

.tts-audio-player-toolbar-background {
  background-color: #fafcff;
  mix-blend-mode: normal;
  text-align: center;
  display: flex;
  justify-content: center;
  min-height: 2em;
  border-radius: 0.3em;
  width: auto;
  white-space: nowrap;
  flex: 1;
  margin-right:0.5em;
}

.tts-audio-player-toolbar-buttons {
  position: relative;
  padding: 0.2em; 
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: nowrap;
}

.tts-audio-player-toolbar__button {
  cursor: pointer;
  border: none;
  margin-right: 0.5em;
  color: rgb(23, 37, 70);
  background: transparent;
  padding: 0;
  
  &.is-disabled {
    color: rgba(67, 78, 101, 0.5);
    pointer-events: none;
    cursor: not-allowed;
  }

  .tts-icon {
    padding: 0.5em;
    &:hover {
      border-radius: 0.3em;
      box-shadow: inset 0px 0px 7px 0px rgba(67, 78, 101, 0.9);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.rate-display {
  margin: 0 8px;
  min-width: 40px;
  text-align: center;
}

.tts-settings-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #ddd;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1em;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1001;
  min-width: 200px;
}

.tts-settings-section {
  margin-bottom: 1em;
  
  &:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    margin-bottom: 0.5em;
    color: #666;
    font-size: 0.9em;
  }

  .rate-controls {
    display: flex;
    align-items: center;
    gap: 0.5em;
    justify-content: center;

    button {
      padding: 0.25em 0.5em;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      cursor: pointer;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    span {
      min-width: 3em;
      text-align: center;
    }
  }

  select {
    width: 100%;
    padding: 0.5em;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  input[type="color"] {
    width: 100%;
    height: 2em;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
  justify-content: center;
}

.color-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  padding: 0;
  transition: transform 0.2s, border-color 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: #333;
  box-shadow: 0 0 5px rgba(0,0,0,0.3);
}

/* Toggle Switch Styles */
.tts-hover-toggle {
  display: flex;
  align-items: center;
  margin-right: 0.5em;
  white-space: nowrap;
  flex-shrink: 0;
}

.hover-label {
  font-size: 0.7em;
  margin-right: 0.3em;
  color: rgb(23, 37, 70);
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 17px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .3s;
}

input:checked + .slider {
  background-color: rgb(23, 37, 70);
}

input:focus + .slider {
  box-shadow: 0 0 1px rgb(23, 37, 70);
}

input:checked + .slider:before {
  transform: translateX(13px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 17px;
}

.slider.round:before {
  border-radius: 50%;
}

.stt-button {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 0.3em;
  background-color: rgba(250,252,255,255);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(23,37,70,255);
}

.stt-recording-dot {
  position: absolute;
  bottom: 1px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: red;
  animation: blink 1s infinite;
}


.stt-tooltip {
  position: relative;
  background: #007dcc;
  color: #fff;
  border-radius: 4px;
  padding: 12px 16px;
  max-width: 260px;
  box-shadow: 0 4px 12px rgba(0,0,0,.2);
}

.stt-tooltip h4 { margin: 0 0 4px; font-size: 18px; font-weight: 600; }

.stt-tooltip .arrow {
  position: absolute;
  top: -8px;
  left: calc(50% - 8px);
  width: 0; height: 0;
  border-left : 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #007dcc;
}

@keyframes blink {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}
