<div class="tts-container" cdkDrag>
  <button class="tts-audio-player-toolbar__button" cdkDragHandle>
    <i class="fas fa-arrows-alt fa-lg tts-icon"></i>
  </button>
  <div class="tts-toolbar" *ngIf="isTTSV2Enabled()">
    <div class="tts-audio-player-toolbar-background">
      <div class="tts-audio-player-toolbar-buttons">
        <span>Text-to-Speech</span>
        <button class="tts-audio-player-toolbar__button" (click)="onTtsToolbarAction(ttsToolBarAction.PLAY)" (mousedown)="keepInputFocus($event)">
          <i class="fas fa-play fa-lg tts-icon"></i>
        </button>
        <button class="tts-audio-player-toolbar__button" (click)="onTtsToolbarAction(ttsToolBarAction.PAUSE)">
          <i class="fas fa-pause fa-lg tts-icon"></i>
        </button>
        <button class="tts-audio-player-toolbar__button" (click)="onTtsToolbarAction(ttsToolBarAction.STOP)">
          <i class="fas fa-stop fa-lg tts-icon"></i>
        </button>

        <div class="tts-hover-toggle">
          <span class="hover-label">Hover</span>
          <label class="switch" title="Toggle hover mode">
            <input type="checkbox" [checked]="speechMode === 'hover'" (change)="onToggleHoverMode()">
            <span class="slider round"></span>
          </label>
        </div>

        <!-- Settings Button -->
        <button class="tts-audio-player-toolbar__button" (click)="toggleSettings()">
          <i class="fas fa-cog fa-lg tts-icon"></i>
        </button>

        <!-- Settings Dropdown -->
        <div class="tts-settings-dropdown" *ngIf="showSettings">
          <div class="tts-settings-section">
            <label>Speech Rate</label>
            <div class="rate-controls">
              <button (click)="decreaseRate()" [disabled]="speechRate <= 0.5">
                <i class="fas fa-minus"></i>
              </button>
              <span>{{speechRate}}x</span>
              <button (click)="increaseRate()" [disabled]="speechRate >= 2">
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </div>

          <div class="tts-settings-section">
            <label>Highlight Color</label>
            <div class="color-options">
              <!-- <button class="color-option" [style.background-color]="'#ECECEC'"
                [class.selected]="highlightColor === '#ECECEC'" (click)="onHighlightColorChange('#ECECEC')"></button> -->
              <button class="color-option" [style.background-color]="'#FFFF00'"
                [class.selected]="highlightColor === '#FFFF00'" (click)="onHighlightColorChange('#FFFF00')"></button>
              <!-- <button class="color-option" [style.background-color]="'#FFA134'"
                [class.selected]="highlightColor === '#FFA134'" (click)="onHighlightColorChange('#FFA134')"></button> -->
              <button class="color-option" [style.background-color]="'#FFAFE2'"
                [class.selected]="highlightColor === '#FFAFE2'" (click)="onHighlightColorChange('#FFAFE2')"></button>
              <!-- <button class="color-option" [style.background-color]="'#6081FF'"
                [class.selected]="highlightColor === '#6081FF'" (click)="onHighlightColorChange('#6081FF')"></button> -->
              <!-- <button class="color-option" [style.background-color]="'#9EF94E'"
                [class.selected]="highlightColor === '#9EF94E'" (click)="onHighlightColorChange('#9EF94E')"></button> -->
            </div>
          </div>

          <div class="tts-settings-section">
            <label>Word Highlight Color</label>
            <div class="color-options">
              <!-- <button class="color-option" [style.background-color]="'#000000'"
                [class.selected]="wordHighlightColor === '#000000'"
                (click)="onWordHighlightColorChange('#000000')"></button> -->
              <button class="color-option" [style.background-color]="'#0000FF'"
                [class.selected]="wordHighlightColor === '#0000FF'"
                (click)="onWordHighlightColorChange('#0000FF')"></button>
              <!-- <button class="color-option" [style.background-color]="'#854700'"
                [class.selected]="wordHighlightColor === '#854700'"
                (click)="onWordHighlightColorChange('#854700')"></button> -->
              <!-- <button class="color-option" [style.background-color]="'#8B008B'"
                [class.selected]="wordHighlightColor === '#8B008B'"
                (click)="onWordHighlightColorChange('#8B008B')"></button> -->
              <!-- <button class="color-option" [style.background-color]="'#006400'"
                [class.selected]="wordHighlightColor === '#006400'"
                (click)="onWordHighlightColorChange('#006400')"></button> -->
              <!-- <button class="color-option" [style.background-color]="'#800000'"
                [class.selected]="wordHighlightColor === '#800000'"
                (click)="onWordHighlightColorChange('#800000')"></button> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="stt-container" *ngIf="isSTTEnabled()">
    <button cdkOverlayOrigin #origin="cdkOverlayOrigin" class="stt-button" (click)="onMicClick()"
      (mousedown)="keepInputFocus($event)">
      <i class="fas fa-microphone fa-lg"></i>
      <span class="stt-recording-dot" *ngIf="isRecordingSpeech"></span>
    </button>
    <ng-template cdkConnectedOverlay #tooltipOverlay="cdkConnectedOverlay" [cdkConnectedOverlayOrigin]="origin"
      [cdkConnectedOverlayOpen]="showTip" [cdkConnectedOverlayPositions]="positions" (backdropClick)="closeTip()"
      [cdkConnectedOverlayHasBackdrop]="false">
      <div class="stt-tooltip">
        <div class="arrow"></div>
        <h4>{{ tipTitle }}</h4>
        <p>{{ tipBody }}</p>
      </div>
    </ng-template>
  </div>
</div>