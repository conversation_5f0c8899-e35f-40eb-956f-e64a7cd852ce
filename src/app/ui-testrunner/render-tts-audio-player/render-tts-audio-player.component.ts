import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, AfterViewInit } from '@angular/core';
import { SpeechEngineService, TTSMode, TTSStatus } from '../utils/text-to-speech/on-the-fly/speech-engine.service';
import { DisplayMode } from '../element-render-audio/model';
import { SttContextService } from '../utils/speech-to-text/stt-context.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConnectedPosition, CdkConnectedOverlay } from '@angular/cdk/overlay';
import { CdkDrag } from '@angular/cdk/drag-drop';
import { SttEngineService, SttState } from '../utils/speech-to-text/stt-engine.service';
import { ItemSetFrameworkCtrl } from '../../ui-item-maker/item-set-editor/controllers/framework';
import { IAssessmentFrameworkDetail } from '../../ui-item-maker/item-set-editor/models/assessment-framework';


enum EttsToolBarAction {
  PLAY = 'play',
  PAUSE = 'pause',
  STOP = 'stop',
  READ_FROM_SELECTION = 'read-from-selection'
}

@Component({
  selector: 'render-tts-audio-player',
  templateUrl: './render-tts-audio-player.component.html',
  styleUrls: ['./render-tts-audio-player.component.scss']
})
export class RenderTtsAudioPlayerComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() currentQuestionId: number;
  @Input() speechMode: TTSMode = TTSMode.CLICK;
  @Input() audioUrl: string;
  @Input() defaultPlaybackSpeed: number = 1;
  @Input() displayMode = DisplayMode.NORMAL;
  @Input() hasControls: boolean = true;
  @Input() isQuestionAudio: boolean = true;
  @Input() frameworkCtrl: ItemSetFrameworkCtrl;
  @Input() asmtFmrk: IAssessmentFrameworkDetail;
  @Output() speechModeChange = new EventEmitter<TTSMode>();
  @Output() playbackRateChange = new EventEmitter<number>();
  @Output() speechToTextChange = new EventEmitter<boolean>();

  ttsToolBarAction = EttsToolBarAction;
  speechRate: number = 1.0;
  private readonly RATE_STEP = 0.25;
  showSettings = false;
  highlightColor: string = '#FFFF00';
  wordHighlightColor: string = '#0000FF';
  isRecordingSpeech: boolean = false;
  showTip = false;

  hasActiveTextTarget = false;
  private destroy$ = new Subject<void>();

  private ttsPrepared = false;
  private currentTTSQuestionId: number | null = null;
  tipTitle = '';
  tipBody = '';
  private tipTimer: any;

  // For V1 compatibility
  DisplayMode = DisplayMode;

  positions: ConnectedPosition[] = [{
    originX : 'center',  originY : 'bottom',
    overlayX: 'center',  overlayY: 'top',
    offsetY : 8
  }];

  /** All .question-container nodes found the *first* time Play is pressed */
  private containerQueue: HTMLElement[] = [];
  /** Index of the container currently being / about-to-be read */
  private currentContainerIdx = -1;
  /** Subscription for SpeechEngine status changes */
  private ttsStatusSub: Subscription;

  @ViewChild(CdkDrag) dragInstance: CdkDrag;
  @ViewChild('tooltipOverlay') overlayInstance: CdkConnectedOverlay;

  private dragSubscription: Subscription;

  private sttBaseText = ''; // To store confirmed text
  private sttInterimText = ''; // To store current interim text

  constructor(
    private sttEngine: SttEngineService,
    public speechEngine: SpeechEngineService,
    private sttCtx: SttContextService
  ) { }

  ngOnInit(): void {
    this.speechRate = this.speechEngine.getRate();
    this.highlightColor = this.speechEngine.getHighlightColor();
    this.wordHighlightColor = this.speechEngine.getWordHighlightColor();
    this.sttCtx.focusedInput$
    .pipe(takeUntil(this.destroy$))
    .subscribe(focused => {
      this.hasActiveTextTarget = !!focused;
  
      // If the current tip is "Select …" and the input is focused → Close the tip
      if (this.showTip && this.tipTitle.startsWith('Select') && focused) {
        this.showTip = false;
      }
    });

    // and add it to the text box at the caret position
    // this.sttEngine.getTranscript()
    // .pipe(takeUntil(this.destroy$))
    // .subscribe(text => this.sttCtx.addTextAtCaret(text));

    // 1. Subscribe to Interim results
    this.sttEngine.getInterim()
      .pipe(takeUntil(this.destroy$))
      .subscribe(interimText => {
        this.sttInterimText = interimText;
        // Use the new method to update the full content of the input box
        this.sttCtx.setFullText(this.sttBaseText + this.sttInterimText);
      });

    // 2. Subscribe to Final results
    this.sttEngine.getTranscript()
      .pipe(takeUntil(this.destroy$))
      .subscribe(finalText => {
        // Append final result to base text
        this.sttBaseText += finalText.trim() + ' ';
        // Clear interim text
        this.sttInterimText = '';
        // Use the new method to update the full content of the input box
        this.sttCtx.setFullText(this.sttBaseText);
      });

    // According to the state$ update the UI (recording point flashing, button disable, etc.)
    this.sttEngine.getState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => this.isRecordingSpeech = (state === SttState.LISTENING));

    /* keep an ear on the SpeechEngine so we know when a container finishes */
    this.ttsStatusSub = this.speechEngine
      .getStatusObservable()
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => this.handleTtsStatusChange(status));
  }

  ngAfterViewInit(): void {
    // Ensure dragInstance is available before subscribing
    if (this.dragInstance) {
      this.dragSubscription = this.dragInstance.moved.subscribe(() => {
        // Check if overlay is defined and has an overlayRef
        if (this.overlayInstance && this.overlayInstance.overlayRef) {
          // Check if the overlay is currently attached/visible before updating
          if (this.overlayInstance.overlayRef.hasAttached()) {
             this.overlayInstance.overlayRef.updatePosition();
          }
        }
      });
    } else {
      console.warn('CdkDrag instance not found.'); // Optional: Add a warning if dragInstance isn't found
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    // Unsubscribe from the drag moved event to prevent memory leaks
    if (this.dragSubscription) {
      this.dragSubscription.unsubscribe();
    }
    clearTimeout(this.tipTimer);
    if (this.ttsStatusSub) { this.ttsStatusSub.unsubscribe(); }
    this.speechEngine.stop();
  }

  ngOnChanges(): void {
    if (this.currentQuestionId !== this.currentTTSQuestionId) {
      this.resetTTS();
      this.currentTTSQuestionId = this.currentQuestionId;
    }
  }

  onTtsToolbarAction(action: EttsToolBarAction) {
    switch(action) {
      case EttsToolBarAction.PLAY:  return this.startTTS();
      case EttsToolBarAction.PAUSE: return this.pauseTTS();
      case EttsToolBarAction.STOP:  return this.stopTTS();
    }
  }

  startTTS() {
    /* Lazily fill the queue the first time Play is pressed */
    if (this.containerQueue.length === 0) {
      this.containerQueue = Array.from(
        document.querySelectorAll<HTMLElement>('.question-container'));
      this.currentContainerIdx = 0;
    }

    // If there is a text selection, we should play from there.
    // This may involve switching the current container.
    const activeElement = document.activeElement;
    const selection = window.getSelection();

    // Check if the active element is a textarea and has a selection.
    if (activeElement && activeElement.tagName.toLowerCase() === 'textarea') {
      const textarea = activeElement as HTMLTextAreaElement;
      // Check if there is a selection in the textarea.
      if (textarea.selectionStart !== textarea.selectionEnd) {
        const containerIndex = this.containerQueue.findIndex(container => container.contains(textarea));
        if (containerIndex !== -1 && this.currentContainerIdx !== containerIndex) {
          // A different container is selected via textarea selection, so we must switch to it.
          this.currentContainerIdx = containerIndex;
          this.ttsPrepared = false; // Force re-preparation for the new container
        }
      }
    } else if (selection && !selection.isCollapsed) { // If there is a text selection, we should play from there.
        const range = selection.getRangeAt(0);
        const selectedNode = range.startContainer;

        if (selectedNode) {
            const containerIndex = this.containerQueue.findIndex(container => container.contains(selectedNode));
            if (containerIndex !== -1 && this.currentContainerIdx !== containerIndex) {
                // A different container is selected via text selection, so we must switch to it.
                this.currentContainerIdx = containerIndex;
                this.ttsPrepared = false; // Force re-preparation for the new container
            }
        }
    }

    if (this.containerQueue.length === 0) { return; }

    /* If we haven't prepared the current container yet, do so now */
    if (!this.ttsPrepared) {
      this.prepareAndPlayCurrentContainer();
    } else {
      /* Already prepared = we were paused → simply resume */
      this.speechEngine.play();
    }
  }

  pauseTTS() {
    this.speechEngine.pause();
  }

  stopTTS() {
    this.speechEngine.stop();
    this.resetSequence();
  }

  resetTTS() {
    this.speechEngine.stop();
    this.resetSequence();
  }

  decreaseRate(): void {
    if (this.speechRate > 0.5) {
      this.speechRate = Math.max(0.5, this.speechRate - this.RATE_STEP);
      this.speechEngine.setRate(this.speechRate);
    }
  }

  increaseRate(): void {
    if (this.speechRate < 2) {
      this.speechRate = Math.min(2, this.speechRate + this.RATE_STEP);
      this.speechEngine.setRate(this.speechRate);
    }
  }

  toggleSettings(): void {
    this.showSettings = !this.showSettings;
  }

  onHighlightColorChange(color: string): void {
    this.highlightColor = color;
    this.speechEngine.setHighlightColor(color);
  }

  onWordHighlightColorChange(color: string): void {
    this.wordHighlightColor = color;
    this.speechEngine.setWordHighlightColor(color);
  }

  onToggleHoverMode(): void {
    this.speechMode = this.speechMode === TTSMode.HOVER ? TTSMode.CLICK : TTSMode.HOVER;
    this.speechEngine.setMode(this.speechMode);
    this.speechModeChange.emit(this.speechMode);
  }

  // For V1 audio player compatibility
  onPlaybackRateChangeV1(rate: number): void {
    this.playbackRateChange.emit(rate);
  }

  onMicClick(): void {
    /* ─── Scenario 3 • Recording and no text box selected ─── */
    if(this.isRecordingSpeech && !this.hasActiveTextTarget) {
      this.stopRecording();
      this.showTip = false;
      return;
    }

    /* ─── Scenario 2 • No text box selected ─── */
    if (!this.hasActiveTextTarget) {
      this.showTooltip('Select a text box','Click inside a text field to start recording…');
      this.startRecording();
      this.focusFirstInput();
      return;                       // Don't start recording
    }

    /* ─── Scenario 1 • Select a text box → Speak and record ─── */
    if (!this.isRecordingSpeech) {
      this.showTooltip('Speak', 'Speak to turn your words into text…');
      this.startRecording();
    } else {
      // already recording → stop
      this.stopRecording();
      this.showTip = false;
    }
  }

  focusFirstInput() {
    // If TTS is active, use its current container. Otherwise, find the first one.
    const container = (this.currentContainerIdx > -1 && this.containerQueue[this.currentContainerIdx])
      ? this.containerQueue[this.currentContainerIdx]
      : document.querySelector('.question-container');

    // If a container is found, search within it. Otherwise, search the whole document as a fallback.
    const scope = container || document;
    const inputs = Array.from(
      scope.querySelectorAll<HTMLTextAreaElement>(
        'textarea[sttFocus]:not([disabled]):not([tabindex="-1"])'
      )
    );

    if (inputs.length) {
      setTimeout(() => inputs[0].focus({ preventScroll: true }), 0);
    }
  }

  private startRecording(): void {
    // Before starting, get the current value of the input box as base text
    const focusedEl = this.sttCtx.getFocusedElement();
    if (focusedEl) {
      this.sttBaseText = focusedEl.value;
      // If it doesn't end with a space, add one to separate new and old text
      if (this.sttBaseText.length > 0 && !this.sttBaseText.endsWith(' ')) {
        this.sttBaseText += ' ';
      }
    } else {
      this.sttBaseText = '';
    }
    this.sttInterimText = ''; // Reset interim text

    this.isRecordingSpeech = true;
    this.speechToTextChange.emit(true);
    this.sttEngine.start();
  }

  private stopRecording(): void {
    // When the user stops manually, sttEngine will still try to return a final result.
    // Our subscription logic will handle it automatically.
    // We just need to stop the engine and update the UI state here.
    this.isRecordingSpeech = false;
    this.speechToTextChange.emit(false);
    this.sttEngine.stop();
  }

  keepInputFocus(ev: MouseEvent) {
    ev.preventDefault();        // Prevent the button from getting focus → Textbox won't blur
  }

  private showTooltip(title: string, body: string, ms = 3000) {
    clearTimeout(this.tipTimer);     // Clear the old timer when clicked multiple times
    this.tipTitle = title;
    this.tipBody  = body;
    this.showTip  = true;
    this.tipTimer = setTimeout(() => this.showTip = false, ms);
  }

  closeTip() { this.showTip = false; }

  isTTSV2Enabled(): boolean {
    if(this.frameworkCtrl) {
      return (this.frameworkCtrl.asmtFmrk?.toolbarOptions?.tts_v2) ?? false;
    }
    // Fallback to the assessment framework if this is from the test runner
    // This is to ensure that the TTS v2 is enabled even if the framework is not set
    return (this.asmtFmrk?.toolbarOptions?.tts_v2) ?? false;
  }

  isSTTEnabled(): boolean {
    if(this.frameworkCtrl) {
      return this.frameworkCtrl.asmtFmrk?.toolbarOptions?.stt ?? false;
    }
    // Fallback to the assessment framework if this is from the test runner
    return this.asmtFmrk?.toolbarOptions?.stt ?? false;
  }
  /** Called whenever SpeechEngine status changes */
  private handleTtsStatusChange(status: TTSStatus): void {
    /* We only care when it *finishes* the container we launched */
    if (status !== TTSStatus.IDLE || !this.ttsPrepared) { return; }

    /* Mark current container as done */
    this.ttsPrepared = false;

    if (this.currentContainerIdx < this.containerQueue.length - 1) {
      /* More containers remain → advance pointer and wait for next Play click */
      this.currentContainerIdx++;
    } else {
      /* Whole queue finished → tidy up */
      this.resetSequence();
    }
  }

  /** Prepares the container at currentContainerIdx and then calls play() */
  private prepareAndPlayCurrentContainer(): void {
    if(this.speechMode === TTSMode.CLICK) {
      const container = this.containerQueue[this.currentContainerIdx];
      this.speechEngine
        .prepareContainers([container], true)
        .then(() => {
          this.ttsPrepared = true;
          this.speechEngine.play();
        })
        .catch(err => console.error('Error preparing TTS:', err));
    }
    else if(this.speechMode === TTSMode.HOVER) {
      this.speechEngine
        .prepareContainers(this.containerQueue, true)
        .then(() => {
          this.ttsPrepared = true;
          this.speechEngine.play();
        })
        .catch(err => console.error('Error preparing TTS:', err));
    }
  }

  /** Clears queue & flags (called on Stop, question change, etc.) */
  private resetSequence(): void {
    this.containerQueue = [];
    this.currentContainerIdx = -1;
    this.ttsPrepared = false;
  }
}
