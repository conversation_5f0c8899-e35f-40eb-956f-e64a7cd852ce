import { Injectable, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subject, fromEvent, merge, Subscription } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import { LangService } from '../../../core/lang.service';

// Define the expected shape of the SpeechRecognitionError event
interface SpeechRecognitionError extends Event {
  readonly error: string;
  readonly message: string;
}

export enum SttState { IDLE, LISTENING, ERROR }
@Injectable({
  providedIn: 'root'
})
export class SttEngineService implements OnDestroy {
  private state$ = new BehaviorSubject<SttState>(SttState.IDLE);
  private transcript$ = new Subject<string>();       // final
  private interim$ = new Subject<string>();       // interim
  private error$ = new Subject<{ error?: string, message?: string }>();
  private recognition: SpeechRecognition | null = null;
  private recognitionEventsSubscription: Subscription | null = null;
  private currentLangCode: string;
  private destroy$ = new Subject<void>(); // To manage subscriptions

  constructor(
    private zone: NgZone,
    private lang: LangService,
  ) {
    this.currentLangCode = this.lang.getCurrentLanguage(); // Initialize with current language
    this.initializeRecognition();

    // Listen for language changes from LangService
    this.lang.languageInitialized.pipe(takeUntil(this.destroy$)).subscribe(() => {
      const newLangCode = this.lang.getCurrentLanguage();
      if (newLangCode !== this.currentLangCode) {
        this.setLanguage(newLangCode);
      }
    });
  }

  private initializeRecognition(): void {
    //Tear down anything left from a previous session
    this.cleanupRecognition();
  
    //Detect constructor
    const SpeechRecognitionCtor =
      (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
  
    if (!SpeechRecognitionCtor) {
      this.state$.next(SttState.ERROR);
      this.error$.next({
        error: 'unsupported',
        message: 'Speech Recognition API not supported in this browser',
      });
      return;
    }
  
    //Build the recogniser
    this.recognition = new SpeechRecognitionCtor();
    this.recognition.lang = this.currentLangCode === 'en' ? 'en-US' : 'fr-FR'; // or fr-CA
    this.recognition.interimResults = true;
    this.recognition.continuous = true;
    this.recognition.maxAlternatives = 1;
  
    //RESULTS
    const result$ = fromEvent<SpeechRecognitionEvent>(
      this.recognition as any,
      'result'
    );
  
    const resultSub = result$.subscribe((e: SpeechRecognitionEvent) => {
      this.zone.run(() => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = e.resultIndex; i < e.results.length; ++i) {
          const result = e.results[i];
          const transcript = result[0].transcript;

          if (result.isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript.trim().length > 0) {
          this.transcript$.next(finalTranscript.trim());
        }
        
        // Always emit the full interim transcript so subscribers can display it.
        this.interim$.next(interimTranscript.trim());
      });
    });
  
    //ERRORS & NOMATCH
    const errorSub = fromEvent<SpeechRecognitionError>(
      this.recognition as any,
      'error'
    ).subscribe((err) => {
      this.zone.run(() => {
        this.state$.next(SttState.ERROR);
        this.error$.next({ error: err.error, message: err.message });
      });
    });
  
    const nomatchSub = fromEvent<Event>(
      this.recognition as any,
      'nomatch'
    ).subscribe(() => {
      this.zone.run(() => {
        this.state$.next(SttState.ERROR);
        this.error$.next({
          error: 'no-match',
          message: 'No speech match found',
        });
      });
    });
  
    //Keep a single Subscription handle so we can clean up easily
    this.recognitionEventsSubscription = new Subscription();
    this.recognitionEventsSubscription.add(resultSub);
    this.recognitionEventsSubscription.add(errorSub);
    this.recognitionEventsSubscription.add(nomatchSub);
  }

  private cleanupRecognition(): void {
    if (this.recognition) {
      // Ensure stop/abort methods exist before calling
      if (typeof this.recognition.stop === 'function') {
        this.recognition.stop();
      }
       if (typeof this.recognition.abort === 'function') {
        this.recognition.abort(); // Stop any ongoing recognition
      }
    }
    if (this.recognitionEventsSubscription) {
      this.recognitionEventsSubscription.unsubscribe();
      this.recognitionEventsSubscription = null;
    }
    this.recognition = null;
  }

  ngOnDestroy(): void { // Add OnDestroy lifecycle hook
    this.cleanupRecognition();
    this.destroy$.next();
    this.destroy$.complete();
  }

  setLanguage(langCode: string): void {
    // Update the language code regardless of the recognition state
    this.currentLangCode = langCode;

    if (!this.recognition) {
      // If recognition wasn't initialized, try initializing now with the new language
      console.log(`STT Engine: Initializing with language ${this.currentLangCode}`);
      this.initializeRecognition();
      return;
    }

    // If language is already set, no need to re-initialize
    const targetLang = langCode === 'en' ? 'en-US' : 'fr-FR';
     if (this.recognition.lang === targetLang) {
        return;
     }

    const wasListening = this.state$.value === SttState.LISTENING;
    if (wasListening) {
      this.stop(); // Stop current recognition cleanly
    }

    console.log(`STT Engine: Setting language to ${this.currentLangCode}`);

    // Re-initialize with the new language
    this.initializeRecognition();

    // if (wasListening) {
    //   this.start(); // Consider adding a small delay if needed
    // }
  }


  getState(): Observable<SttState>        { return this.state$.asObservable(); }
  getTranscript(): Observable<string>     { return this.transcript$.asObservable(); }
  getInterim(): Observable<string>        { return this.interim$.asObservable(); }
  getError(): Observable<{ error?: string, message?: string }> { return this.error$.asObservable(); }


  start() {
    if (!this.recognition) {
       console.error("STT Recognition not initialized or supported.");
       this.state$.next(SttState.ERROR);
       this.error$.next({ error: 'not-initialized', message: 'Speech Recognition not initialized or supported' });
       return;
    }
    // Ensure we are not already listening
    if (this.state$.value === SttState.LISTENING) {
        console.warn("STT Engine: Already listening.");
        return;
    }
    try {
        // Double check recognition object exists before calling start
        if(this.recognition) {
            this.recognition.start();
            this.state$.next(SttState.LISTENING);
            console.log("STT Engine: Started listening.");
        } else {
             throw new Error("Recognition object became null unexpectedly.");
        }
    } catch (e: any) {
        console.error("Error starting speech recognition:", e);
        this.state$.next(SttState.ERROR);
        // Try to extract meaningful error/message from the exception
        const error = e.name || 'start-error';
        const message = e.message || 'Failed to start recognition';
        this.error$.next({ error: error, message: message });
        // Attempt to clean up if start failed badly
        this.cleanupRecognition();
    }
  }

  stop() {
    if (!this.recognition || this.state$.value !== SttState.LISTENING) {
       // console.log("STT Engine: Stop called but not listening or recognition unavailable.");
      return;
    }
     try {
        // Check for stop method before calling
        if(this.recognition && typeof this.recognition.stop === 'function') {
            this.recognition.stop();
            this.state$.next(SttState.IDLE);
            console.log("STT Engine: Stopped listening.");
        } else {
            console.warn("STT Engine: Stop method not available on recognition object.");
            this.state$.next(SttState.IDLE); // Set state to IDLE even if stop fails
        }
     } catch (e: any) {
        console.error("Error stopping speech recognition:", e);
        // Even if stop fails, we likely want to consider it stopped from the service's perspective
        this.state$.next(SttState.IDLE);
        this.error$.next({ error: e.name || 'stop-error', message: e.message || 'Failed to stop recognition' });
     }
  }
}

