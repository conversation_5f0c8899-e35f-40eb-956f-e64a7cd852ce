import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class SttContextService {
  private _focusedInput = new BehaviorSubject<HTMLInputElement | HTMLTextAreaElement | null>(null);
  focusedInput$ = this._focusedInput.asObservable();  
  constructor() { }
  setFocused(el: HTMLElement | null) { 
    const isInput = (el instanceof HTMLInputElement) || (el instanceof HTMLTextAreaElement);
    this._focusedInput.next(isInput ? el as any : null);
  }

  getFocusedElement(): HTMLInputElement | HTMLTextAreaElement | null {
    return this._focusedInput.value;
  }

  addTextAtCaret(text: string) {
    const el = this._focusedInput.value;
    if (!el) { return; }
    const start = el.selectionStart ?? el.value.length;
    const end   = el.selectionEnd   ?? el.value.length;
    const val   = el.value;
    el.value = val.slice(0, start) + text + val.slice(end);
    // move caret to the end of the text
    const caretPos = start + text.length;
    el.setSelectionRange(caretPos, caretPos);
    el.dispatchEvent(new Event('input', { bubbles: true }));
  }

  /**
   * Replaces the entire content of the focused input and moves the caret to the end.
   */
  setFullText(text: string) {
    const el = this._focusedInput.value;
    if (!el) { return; }

    el.value = text;
    el.setSelectionRange(text.length, text.length);
    el.dispatchEvent(new Event('input', { bubbles: true }));
  }
}
