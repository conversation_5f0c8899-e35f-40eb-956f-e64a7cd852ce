import { Injectable, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { ChunkifyService, Chunk, TextNodeWithParent } from './chunkify.service';
import { LangService } from 'src/app/core/lang.service';
import { TTSHoverGranularity } from 'src/app/ui-item-maker/item-set-editor/models/assessment-framework';
import { TTSPlatformSettingsService } from './tts-platform-settings.service';

// --- Interfaces and Enums ---

export interface TTSVoice {
  voice: SpeechSynthesisVoice;
  name: string;
  lang: string;
  default: boolean;
}

export interface TTSSettings {
  rate: number;
  pitch: number;
  volume: number;
  preferredVoices: { [lang: string]: string };
  mode: TTSMode;
  highlightColor: string;
  wordHighlightColor: string;
  ttsHoverGranularity?: TTSHoverGranularity;
}

export enum TTSStatus {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  LOADING = 'loading'
}

export interface ReadingState {
  status: TTSStatus;
  currentChunkIndex: number;
  currentNodeIndex: number;
  currentWordStartIndex: number; // Start index of the word within the current node's original text
  highlightedNodeElement?: HTMLElement; // The element containing the highlighted node/sentence (span wrapper or math/img)
  highlightedWordElement?: HTMLElement; // The span wrapping the highlighted word
}

export enum TTSMode {
  HOVER = 'hover',
  CLICK = 'click'
}

// --- Service Implementation ---

@Injectable({
  providedIn: 'root'
})
export class SpeechEngineService implements OnDestroy {
  // ---- Constants ----
  private readonly MAX_UTTERANCE_CHARS = 1800; // Safe character limit for Safari synthesis

  // Available voices
  private availableVoices: TTSVoice[] = [];
  private voicesLoaded = false;
  private voicesLoaded$ = new BehaviorSubject<boolean>(false);

  // User settings
  private settings: TTSSettings = {
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    preferredVoices: {
      'en': 'Samantha',
      'fr': 'Amélie'
    },
    mode: TTSMode.CLICK, // default to click mode
    highlightColor: '#FFFF00', // Default highlight color
    wordHighlightColor: '#0000FF', // Default word highlight color
    ttsHoverGranularity: TTSHoverGranularity.READ_ALL
  };

  // Current state
  private chunks: Chunk[] = [];
  // private selectedContainer?: HTMLElement;
  private selectedContainers: HTMLElement[] = [];
  private status: TTSStatus = TTSStatus.IDLE;
  private currentChunkIndex = -1;
  private currentNodeIndex = -1;
  private currentNodeCharOffset = 0; // Offset within the current node to start reading from
  private currentWordStartIndex = -1; // Start index of the currently highlighted word within the node's original text

  // Observables for UI components
  private statusChange$ = new BehaviorSubject<TTSStatus>(TTSStatus.IDLE);
  private readingStateChange$ = new Subject<ReadingState>();
  private error$ = new Subject<string>();

  // Highlight elements - References to the *wrapper* spans or elements we modify
  private currentSentenceHighlightSpan?: HTMLElement; // Span wrapping the current node/sentence or the math/img element itself
  private currentWordHighlightSpan?: HTMLElement;   // Span wrapping the current word

  // CSS classes and data attribute for highlighting
  private sentenceHighlightClass = 'tts-highlight-sentence';
  private wordHighlightClass = 'tts-highlight-word';
  private originalTextDataAttribute = 'data-tts-original-text'; // For storing original text

  // Reference to the currently speaking utterance to prevent garbage collection
  private currentUtterance: SpeechSynthesisUtterance | null = null;

  // Store the range that initiated playback via selection
  private playbackInitiatedByRange: Range | null = null;

  // Style element for dynamic highlight colors
  private highlightStyleElement: HTMLStyleElement | null = null;

  // Flag for internal pause state (e.g., due to visibility change)
  private wasPausedByVisibility = false;

  // Hover Mode State
  private hoverTimeoutId: any = null; // Stores the setTimeout ID for hover delay
  private readonly hoverDelay = 2000; // 2.0 seconds delay
  private isHoverListenerAttached = false; // Flag to track listener status
  public isActive = false; // Flag to track if TTS is active
  private _realContainers: HTMLElement[] = [];

  // Bound event handlers for listener removal
  private boundHandleMouseOver: (event: MouseEvent) => void;
  private boundHandleMouseOut: (event: MouseEvent) => void;

  // Textarea selection state tracking
  private textareaSelectionState?: {
    element: HTMLTextAreaElement;
    hasValue: boolean;
  };

  constructor(
    private chunkifyService: ChunkifyService,
    private ngZone: NgZone,
    private lang: LangService,
    private ttsPlatformSettingsService: TTSPlatformSettingsService
  ) {
    this.isActive = true;
    // Bind handlers
    this.boundHandleMouseOver = this.handleMouseOver.bind(this);
    this.boundHandleMouseOut = this.handleMouseOut.bind(this);

    this.settings.rate = this.ttsPlatformSettingsService.getDefaultRate(this.lang.c() || 'en');

    this.initVoices();
    this.initSpeechSynthesis();
    this.createHighlightStyleElement(); // Create style element on init
    this.updateHighlightStyles(); // Apply initial styles
  }

  ngOnDestroy(): void {
    this.isActive = false;
    this.removeHoverListeners(); // Ensure listeners are removed
    if (this.highlightStyleElement && this.highlightStyleElement.parentNode) {
      this.highlightStyleElement.parentNode.removeChild(this.highlightStyleElement);
    }
    speechSynthesis.cancel(); // Stop any speech on destroy
    this.currentUtterance = null; // Clear reference on destroy
  }

  /**
   * Create a <style> element to hold dynamic highlight rules.
   */
  private createHighlightStyleElement(): void {
    if (this.highlightStyleElement) return; // Already created
    this.highlightStyleElement = document.createElement('style');
    document.head.appendChild(this.highlightStyleElement);
  }

  /**
   * Update the CSS rules for highlighting based on current settings.
   */
  private updateHighlightStyles(): void {
    if (!this.highlightStyleElement) return;

    const css = `
      .${this.sentenceHighlightClass} {
        background-color: ${this.settings.highlightColor} !important;
        color: black !important;
        padding: 0.1em 0;
        border-radius: 3px;
        box-decoration-break: clone;
        -webkit-box-decoration-break: clone;
        line-height: 1.2;
      }
      .${this.wordHighlightClass} {
        color: #FFFFFF; 
        background: ${this.settings.wordHighlightColor} !important; 
        padding: 2px; 
        margin: -2px; 
        border-radius: 4px; 
        text-shadow: 0 3px 8px #2A2A2A; 
        position: relative; 
        text-indent: 0px;
      }
      select.${this.sentenceHighlightClass} {
        outline: 2px solid ${this.settings.highlightColor} !important; 
        outline-offset: 2px;
        background-color: transparent !important;
      }        
      /* Special handling for math/image elements */
      [role="math"].${this.sentenceHighlightClass},
      img.${this.sentenceHighlightClass},
      textarea.${this.sentenceHighlightClass} {
        outline: 2px solid ${this.settings.highlightColor} !important;
        outline-offset: 2px;
        background-color: transparent !important; /* Don't apply background to the element itself */
      }
    `;
    this.highlightStyleElement.textContent = css;
  }

  /**
   * Initialize voices and handle voice list changes
   */
  private initVoices(): void {
    const load = () => {
      if (typeof speechSynthesis === 'undefined') {
        this.error$.next('Speech synthesis not supported in this browser');
        this.voicesLoaded$.next(false); // Indicate loading failed or not supported
        this.voicesLoaded$.complete();
        return;
      }
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        this.availableVoices = voices.map(voice => ({
          voice: voice,
          name: voice.name,
          lang: voice.lang.toLowerCase(),
          default: voice.default
        }));
        this.voicesLoaded = true;
        this.voicesLoaded$.next(true);
        this.voicesLoaded$.complete(); // Complete after loading
        console.log(`TTS Voices Loaded: ${this.availableVoices.length}`);
      } else {
        // Retry mechanism in case voices load asynchronously after the event fires
        console.log("No voices found yet, retrying...");
        setTimeout(load, 250); // Retry after a short delay
      }
    };

    if (typeof speechSynthesis !== 'undefined') {
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        // Voices already available
        this.ngZone.run(() => load());
      } else {
        // Voices not yet available, wait for the event
        speechSynthesis.onvoiceschanged = () => {
          // Ensure the handler runs only once for initial load
          speechSynthesis.onvoiceschanged = null;
          console.log("onvoiceschanged event fired.");
          this.ngZone.run(() => load());
        };
        // Trigger loading attempt in case event doesn't fire reliably
        setTimeout(() => {
          if (!this.voicesLoaded) {
            console.log("Timeout waiting for voices, attempting load anyway.");
            this.ngZone.run(() => load());
          }
        }, 1000); // Wait 1 second max
      }
    } else {
      // SpeechSynthesis API not available
      this.ngZone.run(() => load());
    }
  }


  /**
   * Initialize speech synthesis event handlers
   */
  private initSpeechSynthesis(): void {
    if (typeof speechSynthesis !== 'undefined') {
      console.log("[TTS Debug] Speech synthesis initialized", {
        browser: navigator.userAgent,
        isSafari: /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
      });

      // Handle visibility change
      document.addEventListener('visibilitychange', () => {
        console.log("[TTS Debug] Visibility changed", {
          hidden: document.hidden,
          status: this.status
        });

        if (this.status === TTSStatus.PLAYING) {
          if (document.hidden) {
            this.pause(true); // Internal pause only
          } else {
            //  if (this.status === TTSStatus.PAUSED && this.wasPausedByVisibility) {
            //     this.resume();
            //  }
          }
        }
      });
    }
  }

  public prepareContainers(
    containers: HTMLElement[],
    resetState = true
  ): Promise<void> {
    // clear old state
    if (this.selectedContainers.length > 0) {
      this.removeHoverListeners();
      this.selectedContainers.forEach(c => this.clearAllHighlightsFromContainer(c));
    }

    this.chunks = [];
    containers.forEach(c => {
      this.clearAllHighlightsFromContainer(c);
      this.chunks.push(...this.chunkifyService.extractChunks(c));
    });

    // 3. record container reference (the first one for hover event binding)
    this.selectedContainers = containers;
    this._realContainers = containers;

    // 4. if current mode is HOVER, add hover listeners again
    if (this.settings.mode === TTSMode.HOVER) {
      this.addHoverListeners();
    }
    return Promise.resolve();
  }

  /**
   * Prepare the container for text-to-speech
   */
  public prepareContainer(container: HTMLElement, resetState: boolean = true): Promise<void> {
    return this.prepareContainers([container], resetState);
  }

  public resetState(): void {
    // Clear hover timeout on reset
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }

    this.currentChunkIndex = -1;
    this.currentNodeIndex = -1;
    this.currentNodeCharOffset = 0;
    this.currentWordStartIndex = -1;
    this.playbackInitiatedByRange = null;
    this.status = TTSStatus.IDLE;
    this.statusChange$.next(TTSStatus.IDLE);
    this.clearHighlights();
    this.emitReadingState();
  }

  /**
   * Play text-to-speech. Starts from selection if available,
   * or current position, or beginning.
   */
  public play(): void {
    console.log("[TTS Debug] play() called", {
      status: this.status,
      chunks: this.chunks.length,
      containers: this.selectedContainers.length
    });

    // Clear hover timeout if play is manually triggered
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }

    if (this.chunks.length === 0 || this.selectedContainers.length === 0) {
      this.error$.next('No content prepared for reading');
      this.setStatus(TTSStatus.IDLE);
      return;
    }

    if (this.status === TTSStatus.PAUSED) {
      this.resume();
      return;
    }

    // --- Determine Starting Position ---
    let startFromSelection = false;
    const selection = window.getSelection();
    const active = document.activeElement;
    if(active && active.tagName.toLowerCase() === 'textarea') {
      const ta = active as HTMLTextAreaElement;
      const ci = this.chunks.findIndex(c => c.root === ta);
      if (ci >= 0) {
          const { start, end } = this.getTextareaSelection(ta);
          if (start !== end) { // Only treat as starting point if it's a selection
            this.currentChunkIndex      = ci;
            this.currentNodeIndex       = 0;
            this.currentNodeCharOffset  = start;
            this.currentWordStartIndex  = -1;
            startFromSelection = true;
            this.playbackInitiatedByRange = null;
          }
      }
    }

    if(!startFromSelection) {
      if (selection && selection.rangeCount > 0 && !selection.isCollapsed && !this.isRangeEqual(selection.getRangeAt(0), this.playbackInitiatedByRange)) {
        const position = this.getPositionFromRange(selection.getRangeAt(0));
        if (position && this.isPositionValid(position)) {
          this.currentChunkIndex = position.chunkIndex;
          this.currentNodeIndex = position.nodeIndex;
          this.currentNodeCharOffset = position.nodeOffset;
          this.currentWordStartIndex = -1;
          startFromSelection = true;
          this.playbackInitiatedByRange = selection.getRangeAt(0);
          console.log(`Starting from selection: Chunk ${this.currentChunkIndex}, Node ${this.currentNodeIndex}, Offset ${this.currentNodeCharOffset}`);
        } else {
          this.playbackInitiatedByRange = null;
        }
      } else {
        this.playbackInitiatedByRange = null;
      }
    }

    if (this.settings.mode === TTSMode.HOVER) {
      // In hover mode, don't automatically start from selection unless explicitly called by readFromSelection
      this.playbackInitiatedByRange = null;
    }

    if (!startFromSelection && !this.isPositionValid({ chunkIndex: this.currentChunkIndex, nodeIndex: this.currentNodeIndex })) {
      this.currentChunkIndex = 0;
      this.currentNodeIndex = 0;
      this.currentNodeCharOffset = 0;
      this.currentWordStartIndex = -1;
      console.log('Starting from beginning');
    } else if (!startFromSelection) {
      console.log(`Resuming from: Chunk ${this.currentChunkIndex}, Node ${this.currentNodeIndex}, Offset ${this.currentNodeCharOffset}`);
    }
    // --- End Determine Starting Position ---

    speechSynthesis.cancel();
    this.clearHighlights();

    this.setStatus(TTSStatus.PLAYING);
    this.speakCurrentNode();
  }

  /**
   * Pause the current reading
   */
  public pause(internalOnly = false): void {
    if (this.status === TTSStatus.PLAYING) {
      this.wasPausedByVisibility = internalOnly;
      if (!internalOnly) {
        speechSynthesis.pause();
      }
      this.setStatus(TTSStatus.PAUSED);
      console.log('Paused', internalOnly ? '(Internal)' : '');
    }
  }

  /**
   * Resume from paused state
   */
  public resume(): void {
    if (this.status === TTSStatus.PAUSED) {
      if (this.wasPausedByVisibility) {
        this.wasPausedByVisibility = false;
        this.setStatus(TTSStatus.PLAYING);
        console.log('Resuming from internal pause - restarting utterance');
        // Restart speech from the exact point it was paused internally
        this.speakCurrentNode();
      } else {
        speechSynthesis.resume();
        this.setStatus(TTSStatus.PLAYING);
        console.log('Resumed');
      }
    }
  }

  /**
   * Stop the current reading and reset position
   */
  public stop(): void {
    // Clear hover timeout on stop
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }

    speechSynthesis.cancel();
    this.currentUtterance = null; // Clear reference on stop
    this.clearHighlights();
    this.resetState(); // Reset position to the beginning
    console.log('Stopped and reset');
  }

  /**
   * Stop the current reading but keep the current position
   */
  public halt(): void {
    // Clear hover timeout on halt
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }

    speechSynthesis.cancel();
    this.currentUtterance = null; // Clear reference on halt
    this.clearHighlights(); // Clear highlights but keep position state
    this.setStatus(TTSStatus.IDLE);
    console.log('Halted at current position');
  }

  /**
   * Finds the currently live text node within a parent element that corresponds
   * to the originally chunked text node.
   * @param parentElement The expected parent element.
   * @param originalNodeRef The potentially stale reference to the original text node.
   * @param expectedText The original text content we expect to find.
   * @returns The live Node object or null if not found.
   */
  private findLiveTextNode(parentElement: Element, originalNodeRef: Node, expectedText: string): Node | null {
    if (!parentElement || !expectedText) {
      return null;
    }

    // 1. Quick check: Is the original reference still valid? (Less likely after manipulation)
    if (originalNodeRef && originalNodeRef.parentNode === parentElement && document.body.contains(originalNodeRef)) {
      console.log("findLiveTextNode: Using original reference.");
      return originalNodeRef;
    }

    // 2. Search child nodes: Find a text node whose content matches the start.
    const trimmedExpectedText = expectedText.trim();
    if (!trimmedExpectedText) return null; // Don't search for empty strings

    console.log(`findLiveTextNode: Searching in parent for text starting with: "${trimmedExpectedText.substring(0, 20)}..."`, parentElement);

    for (const child of Array.from(parentElement.childNodes)) {
      if (child.nodeType === Node.TEXT_NODE) {
        const childText = child.textContent || '';
        const trimmedChildText = childText.trim();

        // Match based on trimmed content starting the same way.
        // Use a reasonable length for comparison to avoid issues with minor whitespace diffs.
        if (trimmedChildText.startsWith(trimmedExpectedText.substring(0, Math.min(trimmedExpectedText.length, 20))) && trimmedChildText.length >= trimmedExpectedText.length * 0.8) { // Heuristic match
          console.log("findLiveTextNode: Found matching live text node:", child);
          return child;
        }
      }
    }

    console.warn("findLiveTextNode: Could not find live text node matching:", expectedText.substring(0, 20));
    return null; // Not found
  }

  /**
   * Highlight the element corresponding to the current node.
   * @param originalText The original text content of the node (used for text nodes).
   */
  private highlightCurrentNode(originalText: string): void {
    // Clear previous highlights *before* finding/applying new ones.
    // This ensures we are searching in a relatively clean DOM state.
    this.clearHighlights();

    if (!this.isPositionValid({ chunkIndex: this.currentChunkIndex, nodeIndex: this.currentNodeIndex })) {
      console.warn("highlightCurrentNode: Invalid position, cannot highlight.");
      return;
    }

    try {
      const chunk = this.chunks[this.currentChunkIndex];
      const nodeWithParent = chunk.nodes[this.currentNodeIndex];
      const parentElement = nodeWithParent.parent; // The original parent element reference
      const originalNodeRef = nodeWithParent.node; // The potentially stale node reference

      let targetElementForScroll: HTMLElement | null = null; // Initialize as null

      if (!parentElement || !document.body.contains(parentElement)) {
        console.error("highlightCurrentNode: Parent element is detached from DOM!", parentElement);
        return; // Cannot highlight if parent isn't there
      }

      if (parentElement.getAttribute('role') === 'math' || parentElement.tagName.toLowerCase() === 'img') {
        parentElement.classList.add(this.sentenceHighlightClass);
        parentElement.setAttribute(this.originalTextDataAttribute, originalText);
        this.currentSentenceHighlightSpan = parentElement; // It's the element itself
        targetElementForScroll = parentElement;
      } else if (parentElement.tagName.toLowerCase() === 'textarea') {
        // Handle textarea with Selection API if it has value
        const textarea = parentElement as HTMLTextAreaElement;
        // Store state
        this.textareaSelectionState = {
          element: textarea,
          hasValue: !!textarea.value
        };

        // If textarea has value, use Selection API
        if (textarea.value) {
          textarea.focus();
          textarea.setSelectionRange(0, textarea.value.length);
        } else {
          // If no value (reading placeholder), just apply CSS class
          parentElement.classList.add(this.sentenceHighlightClass);
          parentElement.setAttribute(this.originalTextDataAttribute, originalText);
        }
        this.currentSentenceHighlightSpan = parentElement;
        targetElementForScroll = parentElement;
      }
      else if (parentElement.tagName.toLowerCase() === 'select') {
        // --- Logic for Select Elements ---
        console.log("Highlighting: Applying class directly to SELECT element.");
        parentElement.classList.add(this.sentenceHighlightClass);
        // Store original text if needed for consistency, though it's not used for unwrapping here
        parentElement.setAttribute(this.originalTextDataAttribute, originalText);
        this.currentSentenceHighlightSpan = parentElement;
        targetElementForScroll = parentElement;
      } else if (originalNodeRef && originalNodeRef.nodeType === Node.TEXT_NODE && originalText.trim()) {
        // --- Logic for Text Nodes ---
        const liveNodeToHighlight = this.findLiveTextNode(parentElement, originalNodeRef, originalText);

        if (liveNodeToHighlight) {
          const range = document.createRange();
          try {
            range.selectNode(liveNodeToHighlight); // Use the LIVE node

            const highlightSpan = document.createElement('span');
            highlightSpan.className = this.sentenceHighlightClass;
            highlightSpan.setAttribute(this.originalTextDataAttribute, originalText); // Store original text

            // Wrap the live text node. This detaches liveNodeToHighlight and puts it in the span.
            range.surroundContents(highlightSpan);

            this.currentSentenceHighlightSpan = highlightSpan;
            if(this.settings.mode === TTSMode.HOVER) {
              targetElementForScroll = highlightSpan; // Scroll to the new span
            }
          } catch (selectError) {
            console.error('Error executing selectNode/surroundContents on the found node:', selectError, liveNodeToHighlight);
            // Before applying class to parent, check if it's ALREADY a highlight span
            if (parentElement.classList.contains(this.sentenceHighlightClass) && parentElement.tagName.toLowerCase() === 'span') {
              console.error("Highlight fallback failed: Parent is already a leftover highlight span. Aborting highlight.");
              targetElementForScroll = parentElement; // Still scroll to it
            } else {
              // Original fallback: Highlight the parent element
              parentElement.classList.add(this.sentenceHighlightClass);
              parentElement.setAttribute(this.originalTextDataAttribute, originalText);
              this.currentSentenceHighlightSpan = parentElement;
              targetElementForScroll = parentElement;
            }
          }
        } else {
          console.error(`Highlighting: Could not find the live text node for chunk ${this.currentChunkIndex}, node ${this.currentNodeIndex}. Highlighting parent as fallback.`, parentElement);

          // MODIFIED FALLBACK
          // Before applying class to parent, check if it's ALREADY a highlight span
          if (parentElement.classList.contains(this.sentenceHighlightClass) && parentElement.tagName.toLowerCase() === 'span') {
            console.error("Highlight fallback failed: Parent is already a leftover highlight span. Aborting highlight.");
            targetElementForScroll = parentElement; // Still scroll to it
          } else {
            // Original fallback: Highlight the parent element
            parentElement.classList.add(this.sentenceHighlightClass);
            parentElement.setAttribute(this.originalTextDataAttribute, originalText);
            this.currentSentenceHighlightSpan = parentElement;
            targetElementForScroll = parentElement;
          }
        }
      } else {
        // Not a highlightable text node (might be empty) or not math/img
        console.log("Highlighting: Node not highlightable or empty.", `Chunk: ${this.currentChunkIndex}, Node: ${this.currentNodeIndex}`);
        return;
      }

      // Scroll only if a target was determined and is an HTMLElement
      if (targetElementForScroll && targetElementForScroll instanceof HTMLElement && targetElementForScroll.scrollIntoView) {
        targetElementForScroll.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
      } else {
        console.warn("Highlighting: No valid element to scroll into view.");
      }
      this.emitReadingState(); // Emit state after highlighting

    } catch (error) {
      console.error('Error during highlightCurrentNode:', error, `Chunk: ${this.currentChunkIndex}, Node: ${this.currentNodeIndex}`);
      this.clearHighlights(); // Attempt cleanup on error
    }
  }

  /**
   * Highlight the current word being read within the sentence highlight.
   * Uses the reconstruction method.
   * @param fullOriginalNodeText The complete original text content of the node being spoken.
   * @param charIndex The starting character index of the word within fullOriginalNodeText.
   * @param charLength The length of the word/boundary segment.
   */
  private highlightCurrentWord(fullOriginalNodeText: string, charIndex: number, charLength: number | undefined): void {
    if (!this.currentSentenceHighlightSpan) {
      console.warn("Cannot highlight word: Sentence highlight container is missing.");
      return;
    }
    if (this.currentSentenceHighlightSpan.getAttribute('role') === 'math' || this.currentSentenceHighlightSpan.tagName.toLowerCase() === 'img' || this.currentSentenceHighlightSpan.tagName.toLowerCase() === 'textarea') {
      // Word highlight doesn't apply visually to math/img in this way
      if (this.currentSentenceHighlightSpan.tagName.toLowerCase() === 'textarea' && this.textareaSelectionState?.hasValue) {
        // Handle textarea word highlighting with Selection API if it has value
        const textarea = this.currentSentenceHighlightSpan as HTMLTextAreaElement;
        
        // Calculate word boundaries
        let startIndex = charIndex;
        let endIndex = charIndex + (charLength || 0);
        
        // Fallback/Refinement for boundaries
        if (!charLength || endIndex <= startIndex || !/\S/.test(fullOriginalNodeText.substring(startIndex, endIndex))) {
          startIndex = charIndex;
          while (startIndex > 0 && !/\s/.test(fullOriginalNodeText[startIndex - 1])) {
            startIndex--;
          }
          endIndex = charIndex;
          while (endIndex < fullOriginalNodeText.length && !/\s/.test(fullOriginalNodeText[endIndex])) {
            endIndex++;
          }
        }
        
        // Apply selection if valid
        if (startIndex >= 0 && endIndex <= textarea.value.length && startIndex < endIndex) {
          textarea.focus();
          textarea.setSelectionRange(startIndex, endIndex);
        }
      }
      return;
    }

    if (this.currentSentenceHighlightSpan.tagName.toLowerCase() === 'select') {
      // Word highlighting is not applicable/safe for SELECT elements
      return;
    }

    // Clear only the previous word highlight *state* (we will replace content)
    this.currentWordHighlightSpan = undefined; // Reset reference before creating new one

    try {
      // Calculate word boundaries using the original full text
      let startIndex = charIndex;
      let endIndex = charIndex + (charLength || 0);

      // Fallback/Refinement for boundaries
      if (!charLength || endIndex <= startIndex || !/\S/.test(fullOriginalNodeText.substring(startIndex, endIndex))) {
        startIndex = charIndex;
        while (startIndex > 0 && !/\s/.test(fullOriginalNodeText[startIndex - 1])) {
          startIndex--;
        }
        endIndex = charIndex;
        while (endIndex < fullOriginalNodeText.length && !/\s/.test(fullOriginalNodeText[endIndex])) {
          endIndex++;
        }
      }

      // Ensure indices are valid and point to a non-whitespace word
      if (startIndex < 0 || endIndex > fullOriginalNodeText.length || startIndex >= endIndex || !/\S/.test(fullOriginalNodeText.substring(startIndex, endIndex))) {
        this.clearWordHighlightOnly(); // Restore original text if boundaries invalid
        return;
      }

      // --- Reconstruct content within the sentence span ---
      const sentenceSpan = this.currentSentenceHighlightSpan;
      sentenceSpan.textContent = ''; // Clear existing children

      const beforeText = fullOriginalNodeText.substring(0, startIndex);
      const wordText = fullOriginalNodeText.substring(startIndex, endIndex);
      const afterText = fullOriginalNodeText.substring(endIndex);

      if (beforeText) {
        sentenceSpan.appendChild(document.createTextNode(beforeText));
      }

      const wordSpan = document.createElement('span');
      wordSpan.className = this.wordHighlightClass;
      wordSpan.textContent = wordText;
      sentenceSpan.appendChild(wordSpan);
      this.currentWordHighlightSpan = wordSpan; // Store reference

      if (afterText) {
        sentenceSpan.appendChild(document.createTextNode(afterText));
      }
      // --- End Reconstruction ---

    } catch (error) {
      console.error('Error highlighting word:', error, `CharIndex: ${charIndex}, Length: ${charLength}`);
      this.clearWordHighlightOnly(); // Attempt to restore original text on error
    }
  }

  /**
   * Clears both sentence and word highlights, restoring the original DOM structure.
   */
  public clearHighlights(): void {
    // Clear state references first
    const sentenceSpan = this.currentSentenceHighlightSpan;
    const wordSpan = this.currentWordHighlightSpan;
    this.currentSentenceHighlightSpan = undefined;
    this.currentWordHighlightSpan = undefined;

    // Now perform the clearing operations using the captured references
    if (wordSpan && sentenceSpan) {
      this.clearWordHighlightOnly(sentenceSpan);
    }
    if (sentenceSpan) {
      this.clearSentenceHighlightOnlyRef(sentenceSpan);
    }

    // Reset selection range tracking
    this.playbackInitiatedByRange = null;
  }

  /**
   * Clears sentence highlight using the provided reference
   */
  private clearSentenceHighlightOnlyRef(element: HTMLElement): void {
    if (!element) return;

    const parent = element.parentNode; // Get parent

    // Remove stored text attribute regardless of element type
    element.removeAttribute(this.originalTextDataAttribute);

    if (element.classList.contains(this.sentenceHighlightClass)) {
      // Handle Math/Image (just remove class)
      if (element.getAttribute('role') === 'math' || element.tagName.toLowerCase() === 'img' || element.tagName.toLowerCase() === 'textarea') {
        if (element.tagName.toLowerCase() === 'textarea' && this.textareaSelectionState) {
          // Clear textarea selection if it had value
          const textarea = element as HTMLTextAreaElement;
          if (this.textareaSelectionState.hasValue) {
            textarea.blur();
          }
          this.textareaSelectionState = undefined;
        }
        element.classList.remove(this.sentenceHighlightClass);
      }
      // Handle wrapper spans (attempt robust removal)
      else if (parent && element.tagName.toLowerCase() === 'span') {
        try {
          // Get the original text content that *should* be restored
          const originalText = element.getAttribute(this.originalTextDataAttribute) || element.textContent || ''; // Use stored text if available, fallback to current textContent

          // Create a new text node with the original content
          const textNode = document.createTextNode(originalText);

          // Replace the span with the text node
          parent.replaceChild(textNode, element);

          // Normalize the parent to merge adjacent text nodes
          parent.normalize();
          console.log("Successfully unwrapped and normalized:", textNode);

        } catch (unwrapError) {
          console.error("Error unwrapping sentence highlight span, attempting brute force removal:", unwrapError, element);
          // Fallback: If replacement fails, just try removing the element
          if (element.parentNode === parent) {
            try {
              parent.removeChild(element);
            } catch (removeError) {
              console.error("Brute force removal failed:", removeError);
            }
          }
          // Attempt normalization even if removal failed, might help clean up
          if (parent) {
            parent.normalize();
          }
        }
      }
      // Handle fallback case (class added directly to a non-span/math/img parent)
      else {
        element.classList.remove(this.sentenceHighlightClass);
      }
    }
    // Defensive check: Ensure element is removed if it's a span and still somehow exists
    if (element.tagName?.toLowerCase() === 'span' && element.parentNode === parent && element.classList.contains(this.sentenceHighlightClass)) {
      console.warn("Span highlight might not have been removed correctly, attempting final removal.");
      try {
        parent?.removeChild(element);
        parent?.normalize();
      } catch (finalRemoveError) {
        console.error("Final removal attempt failed:", finalRemoveError);
      }
    }
  }

  /**
   * Clears only the word highlight by restoring the full original text
   * within the sentence highlight container.
   */
  private clearWordHighlightOnly(sentenceSpanRef?: HTMLElement): void {
    const sentenceSpan = sentenceSpanRef || this.currentSentenceHighlightSpan;

    if (sentenceSpan) {
      // Check it's not a math/img element
      if (sentenceSpan.getAttribute('role') === 'math' || sentenceSpan.tagName.toLowerCase() === 'img') {
        return;
      }

      if (sentenceSpan.tagName.toLowerCase() === 'textarea') {
        if (this.textareaSelectionState?.hasValue) {
          const textarea = sentenceSpan as HTMLTextAreaElement;
          textarea.setSelectionRange(textarea.value.length, textarea.value.length);
          textarea.blur();
        }
        return;
      }

      if (sentenceSpan.tagName.toLowerCase() === 'select') {
        console.log("clearWordHighlightOnly: Skipping text restore for SELECT.");
        return;
      }

      const originalFullText = sentenceSpan.getAttribute(this.originalTextDataAttribute);
      if (originalFullText !== null) {
        // Check if word was highlighted
        const wordHighlightChild = sentenceSpan.querySelector(`.${this.wordHighlightClass}`);
        if (wordHighlightChild || sentenceSpan.textContent !== originalFullText) {
          sentenceSpan.textContent = ''; // Clear current content
          sentenceSpan.appendChild(document.createTextNode(originalFullText));
        }
      } else {
        console.warn("Original text attribute missing during word clear on:", sentenceSpan);
        // Attempt basic flatten as fallback
        sentenceSpan.textContent = sentenceSpan.textContent;
      }
    }
  }

  /**
   * Utility to clear any highlight classes/spans within a given container.
   */
  private clearAllHighlightsFromContainer(container: HTMLElement): void {
    // Remove sentence highlight classes from math/img
    container.querySelectorAll(`[role="math"].${this.sentenceHighlightClass}, img.${this.sentenceHighlightClass}, textarea.${this.sentenceHighlightClass}`)
      .forEach(el => {
        if (el.tagName.toLowerCase() === 'textarea' && this.textareaSelectionState?.element === el) {
          // Clear textarea selection if it had value
          const textarea = el as HTMLTextAreaElement;
          if (this.textareaSelectionState.hasValue) {
            textarea.blur();
          }
          this.textareaSelectionState = undefined;
        }
        el.classList.remove(this.sentenceHighlightClass);
        el.removeAttribute(this.originalTextDataAttribute);
      });

    // Find and unwrap sentence/word wrapper spans
    container.querySelectorAll(`span.${this.sentenceHighlightClass}, span.${this.wordHighlightClass}`)
      .forEach(span => {
        const parent = span.parentNode;
        if (parent) {
          // Retrieve text content *before* removing attributes/unwrapping
          const textContent = span.getAttribute(this.originalTextDataAttribute) || span.textContent || '';
          span.removeAttribute(this.originalTextDataAttribute); // Remove data attribute

          // Replace span with a single text node of its original content
          parent.replaceChild(document.createTextNode(textContent), span);
          parent.normalize(); // Merge text nodes after unwrapping
        }
      });
    // Reset internal references if they happen to be within this container
    this.currentSentenceHighlightSpan = undefined;
    this.currentWordHighlightSpan = undefined;
  }

  /**
   * Emit current reading state for UI components
   */
  private emitReadingState(): void {
    const state: ReadingState = {
      status: this.status,
      currentChunkIndex: this.currentChunkIndex >= 0 ? this.currentChunkIndex : -1,
      currentNodeIndex: this.currentNodeIndex >= 0 ? this.currentNodeIndex : -1,
      currentWordStartIndex: this.currentWordStartIndex,
      highlightedNodeElement: this.currentSentenceHighlightSpan,
      highlightedWordElement: this.currentWordHighlightSpan
    };
    this.readingStateChange$.next(state);
  }

  /**
   * Update TTS settings
   */
  public updateSettings(settings: Partial<TTSSettings>): void {
    const oldMode = this.settings.mode;
    const requiresStyleUpdate =
      this.settings.highlightColor !== settings.highlightColor &&
      settings.highlightColor !== undefined
      || this.settings.wordHighlightColor !== settings.wordHighlightColor &&
      settings.wordHighlightColor !== undefined;

    this.settings = { ...this.settings, ...settings };

    if (requiresStyleUpdate) {
      this.updateHighlightStyles();
    }

    // Manage hover listeners when mode changes
    if (settings.mode !== undefined && settings.mode !== oldMode && this.selectedContainers.length > 0) {
      this.stop();
      if (settings.mode === TTSMode.HOVER) {
        this.addHoverListeners();
      } else {
        this.removeHoverListeners();
      }
    }
  }

  /**
   * Set preferred voice for a language
   */
  public setPreferredVoice(lang: string, voiceName: string): void {
    this.settings.preferredVoices[lang.toLowerCase()] = voiceName;
  }

  /**
   * Get the appropriate voice for a language, considering preferences.
   */
  private getVoiceForLanguage(lang: string): TTSVoice | undefined {
    if (!this.voicesLoaded || this.availableVoices.length === 0) {
      console.warn("Attempted to get voice before voices loaded.");
      return undefined;
    }
    const normalizedLang = lang.toLowerCase();
    const langCode = normalizedLang.split('-')[0];

    // 1. Try exact preferred match for the full language tag
    const preferredName = this.settings.preferredVoices[normalizedLang];
    if (preferredName) {
      const preferredVoice = this.availableVoices.find(v => v.name === preferredName && v.lang.toLowerCase().startsWith(normalizedLang));
      if (preferredVoice) return preferredVoice;
    }

    // 2. Try preferred match for the language code only
    const preferredNameLangCode = this.settings.preferredVoices[langCode];
    if (preferredNameLangCode && (!preferredName || this.settings.preferredVoices[langCode] !== preferredName)) { // Avoid redundant check if full lang pref failed
      const preferredVoice = this.availableVoices.find(v => v.name === preferredNameLangCode && v.lang.toLowerCase().startsWith(langCode));
      if (preferredVoice) return preferredVoice;
    }

    // 3. Try exact match for the full language tag (default or non-preferred)
    let voice = this.availableVoices.find(v => v.lang.toLowerCase() === normalizedLang);
    if (voice) return voice;

    // 4. Try match for the language code only (default or non-preferred)
    voice = this.availableVoices.find(v => v.lang.toLowerCase().startsWith(langCode));
    if (voice) return voice;

    // 5. Fallback: Find *any* voice for the base language code, prioritizing default
    const langCodeVoices = this.availableVoices.filter(v => v.lang.toLowerCase().startsWith(langCode));
    if (langCodeVoices.length > 0) {
      return langCodeVoices.find(v => v.default) || langCodeVoices[0];
    }

    // 6. Absolute Fallback: Browser default or first available voice
    console.warn(`No specific voice found for ${lang}. Falling back.`);
    return this.availableVoices.find(v => v.default) || this.availableVoices[0];
  }

  // --- Getters for Observables and State ---

  public getAvailableVoices(): TTSVoice[] {
    return this.availableVoices;
  }

  public getVoicesForLanguage(lang: string): TTSVoice[] {
    if (!this.voicesLoaded) return [];
    const normalizedLang = lang.toLowerCase();
    const langCode = normalizedLang.split('-')[0];
    return this.availableVoices.filter(v =>
      v.lang.toLowerCase() === normalizedLang ||
      v.lang.toLowerCase().startsWith(langCode)
    );
  }

  public getSettings(): TTSSettings {
    return { ...this.settings };
  }

  public getStatusObservable(): Observable<TTSStatus> {
    return this.statusChange$.asObservable();
  }

  public getReadingStateObservable(): Observable<ReadingState> {
    return this.readingStateChange$.asObservable();
  }

  public getErrorObservable(): Observable<string> {
    return this.error$.asObservable();
  }

  /** Calculates progress based on chunks */
  public getProgress(): number {
    if (this.chunks.length === 0 || this.currentChunkIndex < 0) return 0;
    const progress = ((this.currentChunkIndex + 1) / this.chunks.length) * 100;
    return Math.min(100, Math.max(0, Math.floor(progress)));
  }

  // --- Settings Setters ---
  public setRate(rate: number): void {
    if (rate >= 0.5 && rate <= 4) {
      this.updateSettings({ rate });
    }
  }
  public getRate(): number { return this.settings.rate; }

  public setPitch(pitch: number): void {
    if (pitch >= 0 && pitch <= 2) {
      this.updateSettings({ pitch });
    }
  }
  public getPitch(): number { return this.settings.pitch; }

  public setVolume(volume: number): void {
    if (volume >= 0 && volume <= 1) {
      this.updateSettings({ volume });
    }
  }
  public getVolume(): number { return this.settings.volume; }

  public setMode(mode: TTSMode): void { this.updateSettings({ mode }); }
  public getMode(): TTSMode { return this.settings.mode; }

  public setHighlightColor(color: string): void {
    if (/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
      this.updateSettings({ highlightColor: color });
    } else {
      console.warn("Invalid highlight color format:", color);
    }
  }
  public getHighlightColor(): string { return this.settings.highlightColor; }

  public setWordHighlightColor(color: string): void {
    if (/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
      this.updateSettings({ wordHighlightColor: color });
    } else {
      console.warn("Invalid word highlight color format:", color);
    }
  }

  public getWordHighlightColor(): string {
    return this.settings.wordHighlightColor;
  }

  // --- Selection Handling ---

  /**
   * Gets the precise chunk, node, and character offset from a Range object.
   */
  public getPositionFromRange(range: Range): { chunkIndex: number, nodeIndex: number, nodeOffset: number } | null {
    if (!range || !range.startContainer) {
      return null;
    }

    // Handle cases where selection might start in an element containing the text node
    let startNode: Node | null = range.startContainer;
    let startOffset = range.startOffset;

    // If selection starts in an element, try to find the text node at that offset
    if (startNode.nodeType === Node.ELEMENT_NODE) {
      if (startNode.childNodes.length > startOffset && startNode.childNodes[startOffset].nodeType === Node.TEXT_NODE) {
        startNode = startNode.childNodes[startOffset];
        startOffset = 0; // Offset is 0 within the found text node
      } else {
        // More complex cases (e.g., selection across multiple nodes) are harder
        // For simplicity, we primarily support selection starting *within* a single text node
        console.warn("Selection starts within an element, not directly in a text node. Trying parent.");
        // Attempt to use the parent if it's our highlight span
        if (startNode.parentElement && startNode.parentElement.classList.contains(this.sentenceHighlightClass)) {
          startNode = startNode.parentElement.firstChild; // Assume first child is the text node
          startOffset = range.startOffset; // Use original offset relative to the span's start? Risky.
          if (!startNode || startNode.nodeType !== Node.TEXT_NODE) return null; // Give up if not text
        } else {
          return null; // Cannot reliably determine position
        }
      }
    }

    if (startNode.nodeType !== Node.TEXT_NODE) {
      return null; // Only handle selections starting within text nodes
    }

    for (let chunkIndex = 0; chunkIndex < this.chunks.length; chunkIndex++) {
      const chunk = this.chunks[chunkIndex];
      for (let nodeIndex = 0; nodeIndex < chunk.nodes.length; nodeIndex++) {
        const nodeWithParent = chunk.nodes[nodeIndex];
        // Check if the selection's start node is the node we are checking
        if (nodeWithParent.node === startNode) {
          return { chunkIndex, nodeIndex, nodeOffset: startOffset };
        }
      }
    }

    return null; // Start node not found in any chunk's text nodes
  }

  /** Checks if a given position is valid within the current chunks */
  private isPositionValid(position: { chunkIndex: number, nodeIndex: number }): boolean {
    return position.chunkIndex >= 0 &&
      position.chunkIndex < this.chunks.length &&
      position.nodeIndex >= 0 &&
      position.nodeIndex < this.chunks[position.chunkIndex].nodes.length;
  }

  /** Helper to compare two Range objects */
  private isRangeEqual(range1: Range | null, range2: Range | null): boolean {
    if (!range1 || !range2) {
      return range1 === range2;
    }
    try {
      return (
        range1.startContainer === range2.startContainer &&
        range1.startOffset === range2.startOffset &&
        range1.endContainer === range2.endContainer &&
        range1.endOffset === range2.endOffset
      );
    } catch (e) {
      // Firefox can throw errors accessing properties if the range is invalid
      console.warn("Error comparing ranges:", e);
      return false;
    }
  }

  /** Helper to set status and notify observers */
  private setStatus(newStatus: TTSStatus): void {
    if (this.status !== newStatus) {
      this.status = newStatus;
      this.statusChange$.next(this.status);
      this.emitReadingState(); // Emit state change when status changes
    }
  }

  // Hover Mode Logic

  /**
   * Adds hover event listeners to the selected container.
   */
  public addHoverListeners(): void {
    if (this.selectedContainers.length === 0 || this.isHoverListenerAttached) return;
    this.ngZone.runOutsideAngular(() => {
      this.selectedContainers.forEach(c => {
        c.addEventListener('mouseover', this.boundHandleMouseOver);
        c.addEventListener('mouseout', this.boundHandleMouseOut);
      });
    });
    this.isHoverListenerAttached = true;
    console.log("Hover listeners added.");
  }

  /**
   * Removes hover event listeners from the selected container.
   */
  public removeHoverListeners(): void {
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }
    if (this.selectedContainers.length > 0 && this.isHoverListenerAttached) {
      this.ngZone.runOutsideAngular(() => {
        // this.selectedContainer?.removeEventListener('mouseover', this.boundHandleMouseOver);
        // this.selectedContainer?.removeEventListener('mouseout', this.boundHandleMouseOut);

        this.selectedContainers.forEach(c => {
          c.removeEventListener('mouseover', this.boundHandleMouseOver);
          c.removeEventListener('mouseout', this.boundHandleMouseOut);
        });
      });
      this.isHoverListenerAttached = false;
      console.log("Hover listeners removed.");
    }
  }

  /**
   * Handles the mouseover event to start the hover timer.
   * Runs outside Angular zone to avoid unnecessary change detection.
   */
  private handleMouseOver(event: MouseEvent): void {
    if (this.settings.mode !== TTSMode.HOVER) {
      // Don't trigger hover logic if not in hover mode or if already speaking/paused
      return;
    }

    const targetElement = event.target as HTMLElement;

    // Clear any existing timer if mouse moves to a new element quickly
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }

    // Start a new timer
    this.hoverTimeoutId = setTimeout(() => {
      // This callback runs after the delay
      this.ngZone.run(() => { // Run inside zone to trigger potential UI updates via play()
        this.triggerHoverRead(targetElement);
      });
    }, this.hoverDelay);
  }

  /**
   * Handles the mouseout event to clear the hover timer.
   * Runs outside Angular zone.
   */
  private handleMouseOut(event: MouseEvent): void {
    if (this.hoverTimeoutId) {
      clearTimeout(this.hoverTimeoutId);
      this.hoverTimeoutId = null;
    }
  }

  /**
   * Finds the chunk index associated with the hovered element and starts playback.
   */
  private triggerHoverRead(targetElement: HTMLElement | null): void {
    this.hoverTimeoutId = null;

    if (!targetElement || this.settings.mode !== TTSMode.HOVER) {
      return;
    }

    const chunkIndex = this.getChunkIndexFromElement(targetElement);

    if (chunkIndex !== null) {
      console.log(`Hover triggered read for Chunk ${chunkIndex}. Current status: ${this.status}`);

      // 1. Force stop any ongoing speech synthesis immediately
      speechSynthesis.cancel();
      // 2. Clear reference to the cancelled utterance IMMEDIATELY
      this.currentUtterance = null;

      // 3. Clear any existing highlights (using the now more robust method)
      this.clearHighlights(); // This now also clears state refs

      // 4. Set position to the start of the hovered chunk
      this.currentChunkIndex = chunkIndex;
      this.currentNodeIndex = 0;
      this.currentNodeCharOffset = 0;
      this.currentWordStartIndex = -1;
      this.playbackInitiatedByRange = null;

      // 5. Start playback
      this.play();
    }
  }

  /**
   * Finds the chunk index containing the given element or its corresponding text node.
   */
  private getChunkIndexFromElement(element: Node | null): number | null {
    if (!this._realContainers?.some(c => c.contains(element))) return null;


    for (let ci = 0; ci < this.chunks.length; ci++) {
      const chunk = this.chunks[ci];
      for (let ni = 0; ni < chunk.nodes.length; ni++) {
        const nodeWithParent = chunk.nodes[ni];

        // Check direct match (text node or parent element like math/img)
        if (nodeWithParent.node === element || nodeWithParent.parent === element) {
          return ci;
        }
        // Check if the hovered element is *inside* the parent element associated with the node
        // This handles cases where the hover target is a child/grandchild (e.g., inside a complex math element or a highlight span)
        if (nodeWithParent.parent.contains(element)) {
          return ci;
        }
      }
    }
    return null; // Not found in any chunk
  }

  /**
   * Speak the node specified by currentChunkIndex and currentNodeIndex.
   */
  private speakCurrentNode(): void {
    // Clear reference to the previous utterance before creating a new one
    this.currentUtterance = null;

    console.log("[TTS Debug] speakCurrentNode called",
      {
        chunkIndex: this.currentChunkIndex, nodeIndex: this.currentNodeIndex,
        browser: navigator.userAgent, status: this.status
      });

    if (!this.isPositionValid({ chunkIndex: this.currentChunkIndex, nodeIndex: this.currentNodeIndex })) {
      console.log("speakCurrentNode: Invalid position, stopping.", this.currentChunkIndex, this.currentNodeIndex);
      this.stop();
      return;
    }

    const chunk = this.chunks[this.currentChunkIndex];
    const nodeWithParent = chunk.nodes[this.currentNodeIndex];
    const element = nodeWithParent.parent;
    const textNode = nodeWithParent.node; // Potentially stale reference, used mainly to determine *type*

    // *** Check if parent element is still in DOM ***
    if (!element || !document.body.contains(element)) {
      console.error("speakCurrentNode: Parent element is detached. Halting.", element);
      this.error$.next("Content structure changed. Please stop and restart TTS.");
      this.halt();
      return;
    }

    let textToSpeak = '';
    let originalNodeTextForHighlight = ''; // Store the text used for highlighting later
    let isSpecialElement = false;

    if (element.getAttribute('role') === 'math') {
      textToSpeak = element.getAttribute('aria-label') || element.textContent || 'mathematical expression';
      originalNodeTextForHighlight = textToSpeak; // Use the spoken text for math highlight attribute
      isSpecialElement = true;
    } else if (element.tagName.toLowerCase() === 'img') {
      textToSpeak = element.getAttribute('alt') || 'image';
      originalNodeTextForHighlight = textToSpeak; // Use alt text for img highlight attribute
      isSpecialElement = true;
    } else if (element.tagName.toLowerCase() === 'select') {
      const selectElement = element as HTMLSelectElement;
      if (selectElement.selectedIndex >= 0 && selectElement.options.length > selectElement.selectedIndex) {
        textToSpeak = selectElement.options[selectElement.selectedIndex].text;
      }
      originalNodeTextForHighlight = textToSpeak;
      isSpecialElement = true;
    } else if (textNode && textNode.nodeType === Node.TEXT_NODE) {
      // *** Get CURRENT text content from the parent, not the stale node ***
      originalNodeTextForHighlight = textNode.textContent || ''; // Starting with what we have
      textToSpeak = originalNodeTextForHighlight;
      if (!textToSpeak) {
        // Maybe the live node has the text? (Less reliable)
        const liveNode = this.findLiveTextNode(element, textNode, textNode.textContent || '');
        textToSpeak = liveNode?.textContent || '';
        originalNodeTextForHighlight = textToSpeak; // Update if found this way
        console.warn("speakCurrentNode: Original text from node was empty, using live node content.");
      }
    } else {
      console.warn("speakCurrentNode: Node is not Math, Img, or Text. Skipping.", this.currentChunkIndex, this.currentNodeIndex);
      this.advanceToNextNodeAndSpeak();
      return;
    }

    // Apply the starting character offset if needed
    let startOffsetForUtterance = 0;
    if (this.currentNodeCharOffset > 0 && this.currentNodeCharOffset < textToSpeak.length) {
      startOffsetForUtterance = this.currentNodeCharOffset;
      textToSpeak = textToSpeak.substring(startOffsetForUtterance);
    } else {
      this.currentNodeCharOffset = 0; // Reset offset if it was invalid or 0
    }

    textToSpeak = textToSpeak.trim();
    if (!textToSpeak) {
      console.log("speakCurrentNode: Node text is empty after offset/trim, skipping.", this.currentChunkIndex, this.currentNodeIndex);
      this.advanceToNextNodeAndSpeak();
      return;
    }

    console.log("[TTS Debug] Preparing utterance", {
      text: textToSpeak,
      textLength: textToSpeak.length,
      language: chunk.language
    });

    // *** Clear previous highlights BEFORE creating and speaking the utterance ***
    this.clearHighlights();

    // ---- Safari text length limitation ----
    const utterancesToSpeak = textToSpeak.length > this.MAX_UTTERANCE_CHARS ?
      this.splitLongText(textToSpeak) : [textToSpeak];

    this.queueUtterances(
      utterancesToSpeak,
      chunk.language,
      originalNodeTextForHighlight,
      startOffsetForUtterance
    );
  }

  /**
   * Split long text into smaller parts for Safari compatibility
   */
  private splitLongText(text: string): string[] {
    const parts: string[] = [];
    let buffer = '';

    for (const char of text) {
      buffer += char;
      // Split on sentence breaks or spaces if we're over the limit
      if (buffer.length >= this.MAX_UTTERANCE_CHARS && /[\s\.!?]/.test(char)) {
        parts.push(buffer.trim());
        buffer = '';
      }
    }

    // Add remaining text if any
    if (buffer.trim()) {
      parts.push(buffer.trim());
    }

    return parts;
  }

  /**
   * Queue multiple utterances to be spoken in sequence
   */
  private queueUtterances(
    textParts: string[],
    language: string,
    originalText: string,
    startOffset: number
  ): void {
    if (textParts.length === 0) {
        // If there are no text parts, try to advance.
        // This handles cases like an empty node.
        this.advanceToNextNodeAndSpeak();
        return;
    }

    const speakPart = (index: number) => {
      // Check if status changed away from playing before this part could start
      if (this.status !== TTSStatus.PLAYING) {
        console.log(`[TTS Debug] Aborting speakPart ${index + 1} due to status change: ${this.status}`);
        return;
      }

      if (index >= textParts.length) {
        // All parts of the current node's text have been processed by speakPart calls.
        // Let advanceToNextNodeAndSpeak decide what to do next (move to next node or halt in hover).
        if (this.status === TTSStatus.PLAYING) { // Ensure we are still in a playing state
            this.advanceToNextNodeAndSpeak();
        }
        return;
      }

      const utterance = new SpeechSynthesisUtterance(textParts[index]);
      // Store reference BEFORE assigning handlers and speaking
      const currentRef = utterance; // Capture ref for handler checks
      this.currentUtterance = utterance;

      utterance.lang = language;
      utterance.rate = this.settings.rate;
      utterance.pitch = this.settings.pitch;
      utterance.volume = this.settings.volume;

      const voice = this.getVoiceForLanguage(language);
      if (voice) {
        utterance.voice = voice.voice;
      }

      const handleUtteranceEnd = () => {
        this.ngZone.run(() => {
          // STALE CHECK
          if (currentRef !== this.currentUtterance) {
            console.log("[TTS Debug] Stale onend/onerror ignored for utterance:", currentRef.text.substring(0, 20));
            return;
          }

          this.currentUtterance = null; // Clear ref *after* stale check

          // Check status before proceeding
          if (this.status !== TTSStatus.PLAYING) {
            console.log(`[TTS Debug] Not advancing/speaking next part due to status change: ${this.status}`);
            return;
          }

          if (index < textParts.length - 1) {
            speakPart(index + 1);
          } else {
            this.currentNodeCharOffset = 0;
            this.currentWordStartIndex = -1;
            this.clearWordHighlightOnly();
            this.advanceToNextNodeAndSpeak();
          }
        });
      };

      utterance.onstart = () => {
        // STALE CHECK
        if (currentRef !== this.currentUtterance) {
          console.log("[TTS Debug] Stale onstart ignored");
          return;
        }

        this.ngZone.run(() => {
          console.log(`[TTS Debug] onstart event fired for part ${index + 1}/${textParts.length}`);
          if (index === 0) {
            this.highlightCurrentNode(originalText);
          }
          this.emitReadingState();
        });
      };

      utterance.onboundary = (event: SpeechSynthesisEvent) => {
        // STALE CHECK
        if (currentRef !== this.currentUtterance) {
          console.log("[TTS Debug] Stale onboundary ignored");
          return;
        }

        this.ngZone.run(() => {
          if (event.name === 'word') {
            // Check if sentence highlight still exists before trying to highlight word
            if (!this.currentSentenceHighlightSpan) {
              console.warn("Cannot highlight word: Sentence highlight container missing (likely due to interruption).");
              return; // Skip word highlight if sentence highlight is gone
            }

            let globalCharIndex = startOffset + event.charIndex;
            if (index > 0) {
              for (let i = 0; i < index; i++) {
                globalCharIndex += textParts[i].length;
              }
            }

            this.currentWordStartIndex = globalCharIndex;
            this.highlightCurrentWord(originalText, globalCharIndex, event.charLength);
            this.emitReadingState();
          }
        });
      };

      utterance.onend = handleUtteranceEnd; // Already includes stale check

      utterance.onerror = (event: SpeechSynthesisErrorEvent) => {
        this.ngZone.run(() => {
          if (event.error === 'canceled' || event.error === 'interrupted') {
            console.log(`[TTS Debug] utterance ${index + 1}/${textParts.length} canceled/interrupted, treating as complete`);
            handleUtteranceEnd(); // Call the common handler which includes stale check
          } else {
            // STALE CHECK for real errors
            if (currentRef !== this.currentUtterance) {
              console.log("[TTS Debug] Stale onerror (real error) ignored");
              return;
            }

            console.error('[TTS Debug] Speech synthesis error:', event.error);
            this.error$.next(`Speech synthesis error: ${event.error}`);
            this.currentUtterance = null; // Clear ref on real error too
            this.halt(); // Halt on real errors
          }
        });
      };

      // Speak the utterance (currentUtterance is already set)
      speechSynthesis.speak(utterance);
    };

    speakPart(0);
  }

  /** Helper to advance state to the next node or chunk and trigger speech */
  private advanceToNextNodeAndSpeak(): void {
    console.log("[TTS Debug] advanceToNextNodeAndSpeak called", {
      currentChunk: this.currentChunkIndex,
      currentNode: this.currentNodeIndex,
      totalChunks: this.chunks.length,
      status: this.status
    });

    if (!this.isPositionValid({ chunkIndex: this.currentChunkIndex, nodeIndex: this.currentNodeIndex })) {
      console.log("[TTS Debug] Invalid position in advanceToNextNodeAndSpeak, stopping");
      this.stop();
      return;
    }

    const currentChunk = this.chunks[this.currentChunkIndex];
    if (this.currentNodeIndex < currentChunk.nodes.length - 1) {
      // More nodes in the current chunk
      this.currentNodeIndex++;
      console.log("[TTS Debug] Advancing to next node", {
        chunk: this.currentChunkIndex,
        node: this.currentNodeIndex
      });
      setTimeout(() => {
        console.log("[TTS Debug] setTimeout callback for next node", {
          status: this.status,
          chunk: this.currentChunkIndex,
          node: this.currentNodeIndex
        });
        if (this.status === TTSStatus.PLAYING) {
          this.speakCurrentNode();
        }
      }, 50); // Short pause between nodes
    } else {
      if (this.settings.mode === TTSMode.HOVER && this.settings.ttsHoverGranularity === TTSHoverGranularity.SENTENCE_BY_SENTENCE) {
        console.log("[TTS Debug] Hover mode: End of current chunk, halting playback.");
        this.halt(); // Halt after completing all nodes in the current chunk in hover mode
        return;     // Explicitly return to prevent further advancement logic
      }
      // End of the current chunk, move to the next chunk
      if (this.currentChunkIndex < this.chunks.length - 1) {
        this.currentChunkIndex++;
        this.currentNodeIndex = 0;
        console.log("[TTS Debug] Advancing to next chunk", {
          chunk: this.currentChunkIndex,
          node: this.currentNodeIndex
        });
        setTimeout(() => {
          console.log("[TTS Debug] setTimeout callback for next chunk", {
            status: this.status,
            chunk: this.currentChunkIndex,
            node: this.currentNodeIndex
          });
          if (this.status === TTSStatus.PLAYING) {
            this.speakCurrentNode();
          }
        }, 200); // Longer pause between chunks
      } else {
        // End of all content
        console.log("[TTS Debug] End of content reached, stopping");
        this.stop();
      }
    }
  }

  /**
   * Speak a specific piece of text directly, canceling any current speech.
   * Useful for short announcements or reading specific elements on demand.
   * Does not use the chunking/highlighting system.
   *
   * @param text The text to speak.
   */
  public speakIsolatedText(text: string): void {
    if (!text || typeof speechSynthesis === 'undefined') {
      return;
    }
    // Clear references from previous main flow speech
    this.currentUtterance = null;
    this.clearHighlights(); // Clear highlights from main flow if any
    const utterance = new SpeechSynthesisUtterance(text.trim());
    // Determine language and voice
    const targetLang = this.lang.c() || 'en';
    utterance.lang = targetLang;
    const voice = this.getVoiceForLanguage(targetLang);
    if (voice) {
      utterance.voice = voice.voice;
    }
    // Apply settings
    utterance.rate = this.settings.rate;
    utterance.pitch = this.settings.pitch;
    utterance.volume = this.settings.volume;


    utterance.onstart = () => {
      this.ngZone.run(() => {
        // Optional: Notify external listeners if needed for isolated speech start
        console.log("[TTS Isolated] Start:", text);
      });
    };

    utterance.onend = () => {
      this.ngZone.run(() => {
        // Clean up reference for this specific utterance
        if (this.currentUtterance === utterance) {
          this.currentUtterance = null;
        }
        // Optional: Notify external listeners if needed for isolated speech end
        console.log("[TTS Isolated] End:", text);
        // Do NOT reset main flow state here (like currentChunkIndex)
      });
    };

    utterance.onerror = (event: SpeechSynthesisErrorEvent) => {
      this.ngZone.run(() => {
        console.error('[TTS Isolated] Speech synthesis error:', event.error, text);
        this.error$.next(`Speech synthesis error: ${event.error}`);
        // Clean up reference for this specific utterance
        if (this.currentUtterance === utterance) {
          this.currentUtterance = null;
        }
        // Do NOT reset main flow state here
      });
    };

    // Store reference and speak
    this.currentUtterance = utterance; // Store ref before speaking
    speechSynthesis.speak(utterance);
  }

  private getTextareaSelection(ta: HTMLTextAreaElement): { start: number, end: number } {
    const start = ta.selectionStart ?? 0;
    const end   = ta.selectionEnd   ?? start;
    return { start, end };
  }
}