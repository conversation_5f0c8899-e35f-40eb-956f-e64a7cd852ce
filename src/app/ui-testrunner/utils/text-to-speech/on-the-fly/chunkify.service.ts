import { Injectable } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';

/**
 * Interface for configuration parameters for the ChunkifyService
 */
export interface ChunkConfig {
  // Selectors for elements that should be treated as individual chunks
  selectors: string[];
  // Selectors for elements that should be excluded from chunks
  excludeSelectors?: string[];
  // Classes that should be excluded from chunks
  excludeClasses?: string[];
}

/**
 * Interface representing a text node with its parent information
 */
export interface TextNodeWithParent {
  // The text node
  node: Text;
  // The parent element of this text node
  parent: HTMLElement;
}

/**
 * Interface representing a chunk of content extracted from the DOM
 */
export interface Chunk {
  // Text nodes contained in this chunk with their parent information
  nodes: TextNodeWithParent[];
  // Language of the chunk (determined from element.lang or parent elements)
  language: string;
  // The root HTML element for this chunk
  root: HTMLElement;
}

@Injectable({
  providedIn: 'root'
})
export class ChunkifyService {
  /**
   * Default configuration for chunk extraction
   */
  private defaultConfig: ChunkConfig = {
    selectors: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li', 'blockquote', 'th', 'td', 'caption', 'dt', 'dd', 'figcaption','div'],
    excludeSelectors: ['sup', 'sub', 'option'],
    excludeClasses: ['tts-audio-player-toolbar-background']
  };

  constructor(
    private lang: LangService
  ) { }

  /**
   * Extract chunks from a container element based on provided configuration
   * 
   * @param container - The container element to extract chunks from
   * @param config - Optional configuration to override the default config
   * @returns Array of chunks, each containing text nodes, language, and root element
   */
  public extractChunks(container: HTMLElement, config?: Partial<ChunkConfig>): Chunk[] {
    const mergedConfig: ChunkConfig = {
      ...this.defaultConfig,
      ...config
    };

    // Convert selector strings to matching functions
    const rootIncludeSelector = mergedConfig.selectors.join(',');
    const rootIncludeFilter = (element: HTMLElement) => element.matches(rootIncludeSelector);
    
    const leafExcludeSelector = mergedConfig.excludeSelectors?.join(',') || '';
    const excludeClasses = mergedConfig.excludeClasses || [];

    // const leafExcludeFilter = leafExcludeSelector 
    //   ? (element: HTMLElement) => element.matches(leafExcludeSelector)
    //   : () => false;
    const leafExcludeFilter = (element: HTMLElement) => {
      // 
      if (leafExcludeSelector && element.matches(leafExcludeSelector)) {
        return true;
      }
      // 
      if (excludeClasses.some(cls => element.classList.contains(cls))) {
        return true;
      }
      if (element.parentElement?.tagName.toLowerCase() === 'select') {
        return true;
      }
      return false;
    };

    // Array to store extracted chunks
    const chunks: Chunk[] = [];
    
    // Root element stack for tracking current root elements
    const rootElementStack: HTMLElement[] = [];

    /**
     * Add a text node to a chunk
     */
    const addChunkNode = (textNode: Text, parent: HTMLElement, language: string) => {
      // Skip nodes that don't contain words or mathematical symbols
      // \w matches any word character
      // [\+\-\*\/\=\(\)\[\]\{\}\<\>\|\~\^\%] matches common mathematical operators and symbols
      // \d matches any digit
      // \s matches whitespace (to avoid treating whitespace-only nodes as valid)
      if (!(/[\w\d\+\-\*\/\=\(\)\[\]\{\}\<\>\|\~\^\%]/.test(textNode.nodeValue || '') && !/^\s*$/.test(textNode.nodeValue || ''))) {
        return;
      }

      const root = rootElementStack[rootElementStack.length - 1];
      if (!root) {
        return;
      }

      // Get the current last chunk
      let currentChunk = chunks[chunks.length - 1];
      
      // Create new chunk if none exists, or if language/root differs
      if (!currentChunk || currentChunk.language !== language || root !== currentChunk.root) {
        currentChunk = {
          language,
          nodes: [],
          root
        };
        chunks.push(currentChunk);
      }

      // Add text node with its parent to current chunk
      currentChunk.nodes.push({
        node: textNode,
        parent: parent
      });
    };

    /**
     * Process element and its children
     */
    const processElement = (element: HTMLElement) => {
      // Handle math elements
      const isMathElement = element.getAttribute('role') === 'math';
      if (isMathElement) {
        // Get the text content of the math element
        const mathText = element.getAttribute('aria-label') || element.textContent;
        //Create a synthetic text node, using mathText to represent
        const syntheticTextNode = document.createTextNode(mathText);
        addChunkNode(syntheticTextNode, element, this.determineLanguage(element));
        // Just return here, don't process children for math elements
        return;
      }
      
      // Handle image elements
      const isImageElement = element.tagName.toLowerCase() === 'img';
      if (isImageElement) {
        const altText = element.getAttribute('alt');
        // Only process images with non-empty alt text
        if (altText && altText.trim()) {
          const syntheticTextNode = document.createTextNode(altText);
          addChunkNode(syntheticTextNode, element, this.determineLanguage(element));
        }
        // Don't process children for image elements
        return;
      }
      const isSelectElement = element.tagName.toLowerCase() === 'select';
      if (isSelectElement) {
        const selectElement = element as HTMLSelectElement;
        // Check if an option is actually selected
        if (selectElement.selectedIndex >= 0 && selectElement.options.length > selectElement.selectedIndex) {
          const selectedOption = selectElement.options[selectElement.selectedIndex];
          const selectedText = selectedOption.text; // Get the visible text of the option

          // Only process if the selected option has meaningful text
          if (selectedText && selectedText.trim()) {
            // Create a synthetic text node for the selected option's text
            const syntheticTextNode = document.createTextNode(selectedText);
            // Add this node to the chunk, using the <select> element as the parent context
            addChunkNode(syntheticTextNode, selectElement, this.determineLanguage(selectElement));
          }
        }
        //Crucially, stop processing children
        return;
      }      

      const isTextareaElement = element.tagName.toLowerCase() === 'textarea';
      if (isTextareaElement) {
        const textareaElement = element as HTMLTextAreaElement;
        // Check if textarea has value first, otherwise use placeholder
        const textToRead = textareaElement.value || textareaElement.placeholder;
        if (textToRead && textToRead.trim()) {
          rootElementStack.push(textareaElement);
          const syntheticTextNode = document.createTextNode(textToRead);
          addChunkNode(syntheticTextNode, textareaElement, this.determineLanguage(textareaElement));
          rootElementStack.pop();
        }
        // Stop processing children of textarea
        return;
      }
      
      
      // Determine element's language
      const elementLanguage = this.determineLanguage(element);
      
      // Check if current element is a root chunk element
      const isRootChunkElement = rootIncludeFilter(element);
      
      if (isRootChunkElement) {
        rootElementStack.push(element);
      }

      // Process all child nodes
      for (const childNode of Array.from(element.childNodes)) {
        if (childNode.nodeType === Node.ELEMENT_NODE) {
          // Skip excluded elements
          if (!leafExcludeFilter(childNode as HTMLElement)) {
            processElement(childNode as HTMLElement);
          }
        } else if (childNode.nodeType === Node.TEXT_NODE) {
          // Only process text nodes within root elements
          if (rootElementStack.length > 0) {
            // Pass the element as the parent of this text node
            addChunkNode(childNode as Text, element, elementLanguage);
          }
        }
      }

      // Remove from stack if it's a root chunk element
      if (isRootChunkElement) {
        rootElementStack.pop();
      }
    };

    // Start processing from the container element
    processElement(container);

    return chunks;
  }

  /**
   * Determine the language of an element
   * 
   * @param element - The element to determine the language for
   * @returns The determined language code
   */
  private determineLanguage(element: HTMLElement): string {
    const lang = this.lang.getCurrentLanguage();
    if(lang) {
      return lang === 'en' ? 'en-US' : 'fr-FR';
    }
    // Check if the element has a lang attribute
    if (element.lang) {
      return element.lang.toLowerCase();
    }
    
    // Traverse up the DOM tree to find a parent with a lang attribute
    let parent = element.parentElement;
    while (parent) {
      if (parent.lang) {
        return parent.lang.toLowerCase();
      }
      parent = parent.parentElement;
    }
    
    // Default to the document's language or 'en' if not specified
    return (document.documentElement.lang || 'en').toLowerCase();
  }
}
