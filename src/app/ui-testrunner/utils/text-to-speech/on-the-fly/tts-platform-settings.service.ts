import { Injectable } from '@angular/core';

type OS = 'Mac' | 'Windows' | 'Other';
type Browser = 'Chrome' | 'Safari' | 'Firefox' | 'Edge' | 'Other';
type Language = 'English' | 'French';

interface PlatformSettings {
  OS: OS;
  Browser: Browser;
  Language: Language;
  RecommendedDefaultSpeed: number;
}

interface PlatformInfo {
  os: OS;
  browser: Browser;
}

@Injectable({
  providedIn: 'root'
})
export class TTSPlatformSettingsService {

  private platformSettings: PlatformSettings[] = [
    { OS: 'Mac', Browser: 'Chrome', Language: 'English', RecommendedDefaultSpeed: 1 },
    { OS: 'Mac', Browser: 'Safari', Language: 'English', RecommendedDefaultSpeed: 1 },
    { OS: 'Mac', Browser: 'Firefox', Language: 'English', RecommendedDefaultSpeed: 0.75 },
    { OS: 'Mac', Browser: 'Edge', Language: 'English', RecommendedDefaultSpeed: 1 },
    { OS: 'Mac', Browser: 'Chrome', Language: 'French', RecommendedDefaultSpeed: 1 },
    { OS: 'Mac', Browser: 'Safari', Language: 'French', RecommendedDefaultSpeed: 1 },
    { OS: 'Mac', Browser: 'Firefox', Language: 'French', RecommendedDefaultSpeed: 0.75 },
    { OS: 'Mac', Browser: 'Edge', Language: 'French', RecommendedDefaultSpeed: 1 },
    { OS: 'Windows', Browser: 'Chrome', Language: 'English', RecommendedDefaultSpeed: 1.25 },
    { OS: 'Windows', Browser: 'Firefox', Language: 'English', RecommendedDefaultSpeed: 1.25 },
    { OS: 'Windows', Browser: 'Edge', Language: 'English', RecommendedDefaultSpeed: 1.25 },
    { OS: 'Windows', Browser: 'Chrome', Language: 'French', RecommendedDefaultSpeed: 1 },
    { OS: 'Windows', Browser: 'Edge', Language: 'French', RecommendedDefaultSpeed: 1 },
    { OS: 'Windows', Browser: 'Firefox', Language: 'French', RecommendedDefaultSpeed: 1 }
  ];

  private platformInfo: PlatformInfo;

  constructor() {
    this.platformInfo = this.getPlatformInfo();
  }

  private getPlatformInfo(): PlatformInfo {
    const ua = navigator.userAgent.toLowerCase();
    const os: OS = this.detectOS(ua);
    const browser: Browser = this.detectBrowser(ua);
    return { os, browser };
  }

  private detectOS(ua: string): OS {
    if (ua.includes('win')) return 'Windows';
    if (ua.includes('mac')) return 'Mac';
    return 'Other';
  }

  private detectBrowser(ua: string): Browser {
    if (ua.includes('edg/')) return 'Edge';
    if (ua.includes('firefox')) return 'Firefox';
    if (ua.includes('chrome')) return 'Chrome';
    if (ua.includes('safari')) return 'Safari';
    return 'Other';
  }

  public getDefaultRate(languageCode: string): number {
    const language: Language = languageCode.startsWith('en') ? 'English' : 'French';

    const setting = this.platformSettings.find(s =>
      s.OS === this.platformInfo.os &&
      s.Browser === this.platformInfo.browser &&
      s.Language === language
    );

    return setting ? setting.RecommendedDefaultSpeed : 1.0;
  }
} 