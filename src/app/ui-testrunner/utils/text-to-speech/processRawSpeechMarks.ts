import { speechMarkChild, NodeId, SpeechMarksMetaProps, SpeechMarksMetaPropsExtended } from "./types";
import { AUTHORING_NODE_ID_START, AUTHORING_NODE_ID_END, AUTHORING_NODE_ID, CT_ALT_TEXT, CUSTOM_TAG_START, CUSTOM_TAG_END, FILLER_NODE, KEY_ID_NODE, DRAG_NODE_KEY_ID, TARGET_NODE_KEY_ID, INSTRUCTION_TEXT } from './constants';
import { ESpeechMarkTypes } from "./types";

const generateExtendedNodeIds = (values: number[]) => {
    // format : [entryId, sentenceId, wordId, ...]
    return values.join('.')
}

const processNodeRefval = (rawVal: string) => {
    const values = rawVal?.split('-');
    return {prefix: values[0], nodeId: values[1]}
}


export const processSpeechMarksFromPolly = (res: { speechMarkUrl: string, speechMarkData: string }) => {
    // const speechMarkFile = await fetch(res.speechMarkUrl);
    // console.log(speechMarkFile.text())

    const speechMarksMeta: speechMarkChild[] = [] // to keep the content in order
    const speechMarksMetaMap: Map<NodeId, { meta: SpeechMarksMetaPropsExtended, children: speechMarkChild[] }> = new Map();

    // track vars

    let currentNodeRefId: string;
    let currentIndex = 0;
    let prevSentenceId = 0
    let prevWordId = 0
    let sentenceType = undefined;
    let wordType = undefined;


    const speechMarkData = res.speechMarkData?.trim().split('\n');
    if (!speechMarkData) return speechMarksMeta;

    // helper methods

    const processAuthoringNodeId = (parsedLine) => {
        // referring to node ref id
        const { nodeId, prefix } = processNodeRefval(parsedLine.value);
        if (prefix === AUTHORING_NODE_ID_START) {
            currentNodeRefId = nodeId
            speechMarksMetaMap.set(nodeId, { meta: parsedLine, children: [] })
            speechMarksMeta.push({ nodeId, ...speechMarksMetaMap.get(nodeId) })
        }

        if (prefix === AUTHORING_NODE_ID_END) {
            currentNodeRefId = undefined
            prevSentenceId = 0
            prevWordId = 0
        }
    }

    const processKeyIdNode = (parsedLine) => {
      const keyIdTagId = parsedLine.value.split('-')[0];
      const [keyIdTag, keyIdVal] = keyIdTagId.split('_');
      let customeTag = keyIdTag;
      
      if(keyIdTag.includes(DRAG_NODE_KEY_ID)) customeTag = DRAG_NODE_KEY_ID;
      else if(keyIdTag.includes(TARGET_NODE_KEY_ID)) customeTag = TARGET_NODE_KEY_ID;
      
      processCustomTag(parsedLine, `${customeTag}_${keyIdVal}`);
    }

    const processCustomTag = (parsedLine, customTag) => {
      // referring to node ref id
      const {prefix } = processNodeRefval(parsedLine.value);
      if (parsedLine.value.includes(CUSTOM_TAG_START)) sentenceType = customTag
      if (parsedLine.value.includes(CUSTOM_TAG_END)) sentenceType = undefined;
    }


    const processParsedLine = (parsedLine) => {
        switch (parsedLine.type) {
            case ESpeechMarkTypes.SENTENCE: processSentence(parsedLine); break;
            case ESpeechMarkTypes.SSML: processSsml(parsedLine); break;
            case ESpeechMarkTypes.WORD: processWord(parsedLine); break;
        }
    }

    const processSentence = (parsedLine) => {
        // edge case : check if next is contains ssml node id
        let nextIndex = currentIndex + 1
        if (currentIndex === 0 && nextIndex < speechMarkData.length) {
            let nextLine = JSON.parse(speechMarkData[nextIndex])
            while (nextLine.type === ESpeechMarkTypes.SSML && nextIndex < speechMarkData.length) {
                processSsml(nextLine)
                currentIndex += 1 // another +1 happens in while loop not ideal but keeping it temporarily
                nextIndex = nextIndex + 1
                nextLine = JSON.parse(speechMarkData[nextIndex])
            }
        }

        // process senetence 
        prevWordId = 0
        const currentNodeRefSentenceId = generateExtendedNodeIds([+currentNodeRefId, ++prevSentenceId]); //currentNodeRefId + '.' + (++prevSentenceId)
        if(sentenceType) parsedLine.customType = sentenceType;
        speechMarksMetaMap.get(currentNodeRefId).children.push({ nodeId: currentNodeRefSentenceId, meta: parsedLine, children: [] })

    }

    const processSsml = (parsedLine) => {
        if (parsedLine.value.includes(AUTHORING_NODE_ID)) processAuthoringNodeId(parsedLine);
        if (parsedLine.value.includes(CT_ALT_TEXT)) processCustomTag(parsedLine, CT_ALT_TEXT);
        if (parsedLine.value.includes(FILLER_NODE)) processCustomTag(parsedLine, FILLER_NODE);
        if (parsedLine.value.includes(INSTRUCTION_TEXT)) processCustomTag(parsedLine, INSTRUCTION_TEXT);
        if (parsedLine.value.includes(KEY_ID_NODE)) processKeyIdNode(parsedLine);

    }

    const processWord = (parsedLine) => {
        const currentNodeRefSentenceId = generateExtendedNodeIds([+currentNodeRefId, prevSentenceId]) //currentNodeRefId + '.' + prevSentenceId;
        const wordId = generateExtendedNodeIds([+currentNodeRefId, prevSentenceId, ++prevWordId]) //currentNodeRefSentenceId + '.' + (++prevWordId)
        speechMarksMetaMap.get(currentNodeRefId).children.find(node => node.nodeId === currentNodeRefSentenceId).children.push({ nodeId: wordId, meta: parsedLine })
    }



    while (currentIndex < speechMarkData.length) {
        const line = speechMarkData[currentIndex];
        const parsedLine = JSON.parse(line);
        processParsedLine(parsedLine);
        currentIndex += 1
    }

    console.log("speechMarksMetaProccessed", speechMarksMeta)
    return speechMarksMeta
}


/*dep_processSpeechMarks(res: {speechMarkUrl: string, speechMarkData: string}){
    const speechMarkData = res.speechMarkData?.trim().split('\n');
    if(!speechMarkData) return ''

    const processedSpeechMarkData = []


    const processParsedLine = (parsedLine) => {      
      const {time, type, start, end, value} = parsedLine;
      switch(type){
        case 'sentence':
          processSentence(parsedLine); break;
        case 'ssml':
          processSsml(parsedLine); break;
        case 'word':
          processWord(parsedLine); break;
      }

    }

    const processSentence = (parsedLine) => {
      const {time, type, start, end, value} = parsedLine;

      // edge case : check if next is contains ssml node id
      const nextIndex = currentIndex + 1
      if(currentIndex === 0 &&  nextIndex < speechMarkData.length){
        const nextLine = JSON.parse(speechMarkData[nextIndex])
        if(nextLine.type === 'ssml'){
          processSsml(nextLine)
          currentIndex += 1 // another +1 happens in while loop not ideal but keeping it temporarily
        }
      }

      // process senetence 
      prevWordId = 0
      const sentenceId = currentNodeRefId + '.' + (++prevSentenceId)
      addProcessedSpeechMarks(parsedLine, {nodeId: sentenceId})
    }

    const processNodeRefval = (rawVal: string) => {
      const values = rawVal?.split('-');
      return {prefix: values[0], nodeId: values[1]}
    }

    const addProcessedSpeechMarks = (parsedLine, config:{nodeId: string}) => {
      const { nodeId } = config;
      processedSpeechMarkData.push({
        ...parsedLine,
        nodeId
      })
    }

    const processSsml = (parsedLine) => {
      const {value} = parsedLine;
      
      if(parsedLine.value.includes('START') || parsedLine.value.includes('END')){
        // referring to node ref id
        const {nodeId, prefix} = processNodeRefval(value);
        if(prefix === 'START'){
          currentNodeRefId = nodeId
        } prevWordId

        if(prefix === 'END') {
          currentNodeRefId = undefined
          prevSentenceId = 0
          prevWordId = 0
        }
      }

    }

    const processWord = (parsedLine) => {
      const wordId = currentNodeRefId + '.' + prevSentenceId + '.' + (++prevWordId)
      addProcessedSpeechMarks(parsedLine, {nodeId: wordId});
    }

    let currentIndex = 0
    let currentNodeRefId:string;
    let prevSentenceId = 0
    let prevWordId = 0

    while (currentIndex < speechMarkData.length ){
      const line = speechMarkData[currentIndex];
      const parsedLine = JSON.parse(line); 
      processParsedLine(parsedLine); 
      currentIndex += 1
    }

    console.log(processedSpeechMarkData)
    return processedSpeechMarkData;
  }*/