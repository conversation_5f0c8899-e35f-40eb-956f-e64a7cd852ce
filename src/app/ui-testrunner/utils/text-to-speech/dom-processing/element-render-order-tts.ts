import { IStyleProfile } from "src/app/core/styleprofile.service";
import { IContentElement } from "src/app/ui-testrunner/models";
import { speechMarkChild } from "../types";
import { traverseDOM } from "./main";
import { orderingDragElIdPrefix, processOrderDragEl } from "./tts-dom-alteration-patch";

export const processElementRenderOrderTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
    const isDraggableEl = (node: Element) => node.classList?.contains('cdk-drag');
    const isTargetEl = (node: Element) => node.classList?.contains('target-options');
    const isOrderingEl = (node: Element) => isDraggableEl(node) || isTargetEl(node);

    const nodes = <Element[]>traverseDOM(el, isOrderingEl);
    const dragEls = <Element[]>nodes.filter(n => isDraggableEl(n));
    const TargetEls = <Element[]>nodes.filter(n => isTargetEl(n));
    
    processOrderDragEl(speechMarkRef, dragEls, orderingDragElIdPrefix);
}