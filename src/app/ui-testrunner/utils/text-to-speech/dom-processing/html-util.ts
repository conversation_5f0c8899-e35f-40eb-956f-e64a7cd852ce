// HTML utils

import { EHtmlTagNames } from "../types";

export const findElementNode = (el: Element) => {
    let elementNode: Element | null = el
    while (elementNode && !isElementNode(elementNode)) {
        elementNode = elementNode.parentElement
    }

    return elementNode
}

export const getNextElement = (node: Element, shouldBeElementNode?: boolean) => shouldBeElementNode ? node.nextElementSibling : node.nextSibling;

export const isTextNode = (node: Element | ChildNode) => node.nodeType === Node.TEXT_NODE
export const isElementNode = (node: Element) => node.nodeType === Node.ELEMENT_NODE
export const isImgNode = (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.IMAGE;
export const isButtonNode = (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.BUTTON;
export const isDropdownNode = (node: Element) => isElementNode(node) && [EHtmlTagNames.SELECT, EHtmlTagNames.OPTION].includes(EHtmlTagNames[node.tagName]);
export const isTableNode = (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.TABLE;
export const isTableRowNode =  (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.TR;
export const isTableDataNode =  (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.TD;
export const isMarkDownNode = (node: Element) => isElementNode(node) && (node as HTMLElement).tagName === EHtmlTagNames.MARKDOWN_INLINE;



export const isTextOrImgNode = (node: Element) => isTextNode(node) || isImgNode(node);
export const isMcqNode = (node: Element) => isButtonNode(node) || isDropdownNode(node) || isTableNode(node);