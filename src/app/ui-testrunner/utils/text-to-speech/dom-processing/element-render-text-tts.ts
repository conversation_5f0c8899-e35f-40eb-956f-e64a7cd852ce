import { speechMarkChild } from "../types"
import { createHtmlEl, createSpanEl } from "./gen-html-elements"
import { CT_ALT_TEXT } from "../constants"
import { traverseDOM } from "./main"
import { isElementNode, isMarkDownNode, isTextNode } from "./html-util"
import { IStyleProfile, processText } from "../../../../core/styleprofile.service"
import { IContentElement } from "../../../models"

export const processElementRenderText = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
  if(el.classList.contains('tts-text-processed')) return;
  el.classList.add('tts-text-processed');
  const textNode = traverseDOM(el, isMarkDownNode)?.[0].firstElementChild;
  const spans = processTextElementIntoSpans(textNode.childNodes, speechMarkRef.children, styleProfile, nodeConfig, lang);
  if(!spans) return;
  textNode.textContent = null;
  textNode.append(...spans);
  // const textNodes = traverseDOM(el, isTextNode);
  // textNodes.forEach(textNode => splitTextNodeIntoSpans(<Element>textNode.parentElement, nodeRefId, speechMarkRef.children))
}

export const splitTextNodeIntoSpans = (node: Element, nodeRefId: number, sentences: speechMarkChild[]) => {
    const childEl = splitTextElementsIntoSpans(node, sentences) // createSpansWithDelimiters(node.textContent, nodeRefId);
    node.textContent = null
    node.append(...childEl)
    // node.innerHTML = processedElement(childEl)   //sentences.map((sentence) => `<span>${sentence.trim()}</span>`).join('');
}

// Normal Text Processing : 

export const splitTextElementsIntoSpans = (textNode: Element, sentences: speechMarkChild[]) => {
    const sentencesSpans = []
    let textContent = textNode.textContent;
    // const { children: sentences } = speechMarkRef;

    for(const sentence of sentences){
      const { nodeId, meta: sentenceMeta, children: words } = sentence
      if(sentenceMeta.customType === CT_ALT_TEXT) continue;

      //process sentence
      const spSentenceText = sentenceMeta.value;
      // find the sentence in the text
      let sentenceStartIdx = textContent.indexOf(spSentenceText);
      if (sentenceStartIdx !== 0) {
          // splice and append , this could be spaces comma and othe type of delimiters
          const extras = textContent.slice(0, sentenceStartIdx)
          textContent = textContent.substring(sentenceStartIdx)
          const extraSpan = createSpanEl(null, extras)
          sentencesSpans.push(extraSpan);
          sentenceStartIdx = 0
      }
      const senetenceEndIdx = sentenceStartIdx + spSentenceText.length;
      let txtNodeSentence = textContent.slice(sentenceStartIdx, senetenceEndIdx);
      textContent = textContent.substring(senetenceEndIdx, textContent.length)
      const sentenceSpan = createSpanEl(nodeId, null, nodeId);
      sentencesSpans.push(sentenceSpan)

      for(const wordData of words){
        const { nodeId, meta: wordMeta } = wordData;
        // find the word in the sentence
        const spWord = wordMeta.value;
        let wordStartIdx = txtNodeSentence.indexOf(spWord);
        if (wordStartIdx !== 0) {
            // splice and append , this could be spaces comma and othe type of delimiters
            const extraWords = txtNodeSentence.slice(0, wordStartIdx)
            txtNodeSentence = txtNodeSentence.substring(wordStartIdx, txtNodeSentence.length)
            const extraSpan = createSpanEl(null, extraWords)
            sentenceSpan.appendChild(extraSpan)

            wordStartIdx = 0
        }
        const wordEndIdx = wordStartIdx + spWord.length;
        const word = txtNodeSentence.slice(wordStartIdx, wordEndIdx)
        txtNodeSentence = txtNodeSentence.substring(wordEndIdx, txtNodeSentence.length)
        const wordSpan = createSpanEl(nodeId, word, nodeId);
        sentenceSpan.appendChild(wordSpan)
      }

      if (txtNodeSentence.length) {
        const sentenceEndSpan = createSpanEl(null, txtNodeSentence);
        sentenceSpan.appendChild(sentenceEndSpan)
      }

    }

    // sentences.forEach(({ nodeId, meta: sentenceMeta, children: words }) => {
    //     const spSentenceText = sentenceMeta.value;
    //     // find the sentence in the text
    //     let sentenceStartIdx = textContent.indexOf(spSentenceText);
    //     if (sentenceStartIdx !== 0) {
    //         // splice and append , this could be spaces comma and othe type of delimiters
    //         const extras = textContent.slice(0, sentenceStartIdx)
    //         textContent = textContent.substring(sentenceStartIdx)
    //         const extraSpan = createSpanEl(null, extras)
    //         sentencesSpans.push(extraSpan);
    //         sentenceStartIdx = 0
    //     }
    //     const senetenceEndIdx = sentenceStartIdx + spSentenceText.length;
    //     let txtNodeSentence = textContent.slice(sentenceStartIdx, senetenceEndIdx);
    //     textContent = textContent.substring(senetenceEndIdx, textContent.length)
    //     const sentenceSpan = createSpanEl(nodeId);
    //     sentencesSpans.push(sentenceSpan)

    //     words.map(({ nodeId, meta: wordMeta }) => {

    //         // find the word in the sentence
    //         const spWord = wordMeta.value;
    //         let wordStartIdx = txtNodeSentence.indexOf(spWord);
    //         if (wordStartIdx !== 0) {
    //             // splice and append , this could be spaces comma and othe type of delimiters
    //             const extraWords = txtNodeSentence.slice(0, wordStartIdx)
    //             txtNodeSentence = txtNodeSentence.substring(wordStartIdx, txtNodeSentence.length)
    //             const extraSpan = createSpanEl(null, extraWords)
    //             sentenceSpan.appendChild(extraSpan)

    //             wordStartIdx = 0
    //         }
    //         const wordEndIdx = wordStartIdx + spWord.length;
    //         const word = txtNodeSentence.slice(wordStartIdx, wordEndIdx)
    //         txtNodeSentence = txtNodeSentence.substring(wordEndIdx, txtNodeSentence.length)
    //         const wordSpan = createSpanEl(nodeId, word);
    //         sentenceSpan.appendChild(wordSpan)
    //     })

    //     if (txtNodeSentence.length) {
    //         const sentenceEndSpan = createSpanEl(null, txtNodeSentence);
    //         sentenceSpan.appendChild(sentenceEndSpan)
    //     }


    // })

    return sentencesSpans
}

export const processTextElementIntoSpans = (textNodes: NodeListOf<ChildNode>, sentences: speechMarkChild[], styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
  const sentencesSpans = []
  let currentNode = textNodes.item(0);
  let currentNodetextContent = currentNode.textContent;

  function stringToUnicode(str) {
    const unicodeArray = [];
    for (let i = 0; i < str.length; i++) {
        const unicode = str.charCodeAt(i).toString(16).toUpperCase();
        unicodeArray.push(`\\u${'0000'.substring(0, 4 - unicode.length)}${unicode}`);
    }
    return unicodeArray.join(' ');
}
  
  const createInnerChildNode = () => {
    if(!currentNode || isTextNode(currentNode)) return undefined;
    const elNode = <Element>currentNode;
    // Check if the element has child elements (nested structure)
    const hasNestedElements = Array.from(elNode.children).some(child => child.children.length > 0 || !isTextNode(child.firstChild));
    if (hasNestedElements) {
      // Use removeInnermostTextNode for nested structures
      const cleanedEl = removeInnermostTextNode(elNode);
      const newEl = createHtmlEl(cleanedEl.tagName, { id: cleanedEl.id, classes: Array.from(cleanedEl.classList) });
      
      // Copy the cleaned children
      while (cleanedEl.firstChild) {
        newEl.appendChild(cleanedEl.firstChild);
      }
      return newEl;
    } else {
      // For simple elements, just create a new element without content
      return createHtmlEl(elNode.tagName, { id: elNode.id, classes: Array.from(elNode.classList) });
    }
  }

  const removeInnermostTextNode = (element: Element) => {
      const clone = element.cloneNode(true) as Element;
      
      function removeTextNodesRecursive(el: Element): boolean {
          let removedAny = false;
          for (let i = el.childNodes.length - 1; i >= 0; i--) {
              const child = el.childNodes[i];
              if (child.nodeType === Node.TEXT_NODE) {
                  if (child.textContent && child.textContent.trim() !== '') {
                      el.removeChild(child);
                      removedAny = true;
                  }
              } else if (child.nodeType === Node.ELEMENT_NODE) {
                  if (removeTextNodesRecursive(child as Element)) {
                      removedAny = true;
                  }
              }
          }
          return removedAny;
      }

      removeTextNodesRecursive(clone);
      return clone;
  } 
  
  const _processText = (text: string) => {
    let processes = styleProfile[lang].renderStyling.plainText.transforms.filter(({slug}) => slug !== 'PROCESS_NON_BREAKING_HYPHEN_PAIR');
    text = processText(text, processes);
    // text = text.replace(/\n/g, "<br>");
    // text = text.replace(/ /g, "&nbsp;");
    text = text.replace(/&nbsp;/g, " ");
    text = text.replace(/&amp;/g, "&");
    return text;
  }
  
  const _processPhoneticTags = (text: string) => {
    if(!styleProfile[lang].renderStyling.phonetic_spelling) return text;
    let processes = styleProfile[lang].renderStyling.phonetic_spelling.transforms;
    text = processText(text, processes);
    return text;
  }

  const sliceText = (position: {start: number, end: number}, spanConfig?: {id: string, className: string}) => {
    const {start, end} = position;
    const {id, className } = spanConfig || {}
    const text = _processText(currentNodetextContent.slice(start, end));
    currentNodetextContent = currentNodetextContent.substring(end);
    return createSpanEl(id, text, className);
  }

  
  let innerParentStyleEl = createInnerChildNode();
  
  const appendToParentNode = (parent: Element, child: Element) => {
    parent.append(child);
  }
  
  const appendToInnerParentStyleNode = (parent: Element, child: Element) => {
    const hasBTag = parent.querySelector('b');
    const hasEmTag = parent.querySelector('em');
    //handle the case where there are b and em tags in the parent
    if(hasBTag && hasEmTag) {
      const appendToInnermostElement = (element: Element): void => {
        const lastChild = element.lastElementChild;
        if (lastChild) {
          appendToInnermostElement(lastChild);
        } else {
          if(child.textContent && !child.textContent.includes(' ')) {
            element.appendChild(child);
          }
        }
      };
      appendToInnermostElement(parent); 
    } else {
      parent.append(child);
    }
  }

  const nextSibling = () => {
    currentNode = currentNode?.nextSibling;
    if(!currentNode) return false;
    currentNodetextContent = currentNode.textContent;
    innerParentStyleEl = createInnerChildNode();
    return true;
  }

  for( const [sId, sentenceObj] of sentences.entries() ){
    const { meta: sMeta, nodeId: sNodeId, children: words } = sentenceObj;

    if(sMeta.customType === CT_ALT_TEXT) continue;

    const sentenceText = _processPhoneticTags(sMeta.value);

    // create a new SPAN for the sentence
    const sentenceSpanEl = createSpanEl(sNodeId, undefined, sNodeId);

    let wIdx = 0;

    let _loopCount = 0;
    
    // #TODO: Keeping it until we find a better solution
    const MAX_LOOP_LIMIT = 20000 // to avoid infinite loop in cases where the produced word by script doesn't match what's in the DOM

    while( wIdx < words.length && _loopCount < MAX_LOOP_LIMIT) {
      _loopCount += 1
      const wordObj = words[wIdx];
      
      const { meta: wMeta, nodeId: wNodeId } = wordObj;
      
      let word = _processText(wMeta.value);
      // console.log(word)

      // check currentNodetextContent
      if(!currentNodetextContent) {
        if(innerParentStyleEl) {
          appendToParentNode(sentenceSpanEl, innerParentStyleEl);
        } 
        nextSibling();
      }

      // Check if the content contains the word joiner character (Unicode 8288)
      if (currentNodetextContent.includes('\u2060')) {
        // Remove word joiner characters (Unicode 8288)
        currentNodetextContent = currentNodetextContent.replace(/[\u2060]/g, '');
      }

      if(word.includes('\u2060')) {
        word = word.replace(/[\u2060]/g, '');
      }

      // find index of the word
      let wordStartIndex = currentNodetextContent.indexOf(word);
      if(wordStartIndex === -1){
        const extraSpan = sliceText({start: 0, end: currentNodetextContent.length});
        if(innerParentStyleEl) {
          appendToInnerParentStyleNode(innerParentStyleEl, extraSpan);
          appendToParentNode(sentenceSpanEl, innerParentStyleEl);
        } else {
          appendToParentNode(sentenceSpanEl, extraSpan);
        }
        nextSibling();
      }

      else if(wordStartIndex !== 0) {
        // splice and append , this could be spaces comma and othe type of delimiters
        const extraSpan = sliceText({start: 0, end: wordStartIndex});
        // Identify the previous sentence span
        const previousSentenceSpan = sentencesSpans[sentencesSpans.length - 1];
        // Check if the extra span consists of a period or question mark followed by new lines
        // If true, append it to the previous sentence span
        if (extraSpan.textContent.trim().match(/^[.?]+$/) && previousSentenceSpan) {
          appendToParentNode(previousSentenceSpan, extraSpan);
        } else {
          // Otherwise, append it to the current sentence span
          if(innerParentStyleEl) {
            appendToInnerParentStyleNode(innerParentStyleEl, extraSpan);
          } else {
            appendToParentNode(sentenceSpanEl, extraSpan);
          }
        }
      } 
      
      else {
        // word is at the starting index sub and append.
        // wordStartIndex should be 0 here
        if(wordStartIndex !== 0) console.log('Word start index is not at 0');
        const wordEndIndex = wordStartIndex + word.length;
        const slicedWordSpan = sliceText({start: wordStartIndex, end: wordEndIndex}, {id: wNodeId, className: wNodeId});
        if(innerParentStyleEl) {
          appendToInnerParentStyleNode(innerParentStyleEl, slicedWordSpan);
        } else {
          appendToParentNode(sentenceSpanEl, slicedWordSpan);
        }
        wIdx += 1;
      }
      
    }

    if(_loopCount >= MAX_LOOP_LIMIT) {
      console.log('TTS: text element processing failed', words[wIdx]);
      alert('Please Contact Support!');
      return undefined; 
    }

    // check if last sentence
    if(sId === sentences.length - 1) {
      if(currentNodetextContent){
        const extraSpan = sliceText({start: 0, end: currentNodetextContent.length});
        appendToParentNode(innerParentStyleEl ? innerParentStyleEl : sentenceSpanEl, extraSpan);
      }
    } else {
      // append sentence ends extras
      const lastWord = sentenceObj.children[sentenceObj.children.length - 1].meta.value;
      const lastWordIndex = sentenceText.lastIndexOf(lastWord);
      const sentenceExtras = sentenceText.substring(lastWordIndex + lastWord.length, sentenceText.length);
      const sentenceExtrasExceptions = new Set(['.','?']);
      if(sentenceExtras && !sentenceExtrasExceptions.has(sentenceExtras.trim())) {
        const sentenceExtraSpan = createSpanEl(undefined, _processText(sentenceExtras), undefined);
        appendToParentNode(innerParentStyleEl ? innerParentStyleEl : sentenceSpanEl, sentenceExtraSpan);
      }
    }


    // sentence end
     // check currentNodetextContent
    if(!currentNodetextContent) {
      if(innerParentStyleEl) {
        appendToParentNode(sentenceSpanEl, innerParentStyleEl);
      } 
      nextSibling();
    }

    sentencesSpans.push(sentenceSpanEl);

  }

  // check if something is remaining in the childNodes of actual elements:
  while(currentNode){
    sentencesSpans.push(currentNode);
    nextSibling();
  }

  return sentencesSpans;
}