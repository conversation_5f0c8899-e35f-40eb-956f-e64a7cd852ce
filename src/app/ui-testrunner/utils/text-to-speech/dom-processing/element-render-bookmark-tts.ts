import { speechMarkChild } from "../types";
import { createSpanEl } from "./gen-html-elements";
import { IStyleProfile, processText } from "../../../../core/styleprofile.service";
import { IContentElement } from "../../../models";
export const processElementRenderBookmarkTts = (
    el: Element,
    nodeRefId: number,
    speechMarkRef: speechMarkChild,
    styleProfile: IStyleProfile,
    nodeConfig: IContentElement,
    lang: string
  ): void => {
    // Prevent duplicate processing
    if (el.classList.contains('tts-bookmark-processed')) return;
    el.classList.add('tts-bookmark-processed');
  
    // Try to find the <p> element inside <tra-md> which holds the caption text.
    // If not found, fall back to the <tra-md> element.
    let textNode: HTMLElement =
      el.querySelector('tra-md p') || (el.querySelector('tra-md') as HTMLElement);
    if (!textNode || textNode.textContent.trim() === "") {
      console.warn("Bookmark TTS: Could not find a valid text node.");
      return;
    }
  
    if (!textNode.parentElement) {
      console.warn("Bookmark TTS: The found text node does not have a parent element.");
      return;
    }
  
    // In our speech mark structure, the first sentence (if exists) holds the desired id (e.g. "5.1")
    if (speechMarkRef.children && speechMarkRef.children.length > 0) {
      const sentenceMark = speechMarkRef.children[0];
      textNode.id = sentenceMark.nodeId;
      // Optionally, add a class as well
      textNode.classList.add(sentenceMark.nodeId);
    }
  
    // Split the text node (and its children) based on the speech mark information.
    const spans = splitBookmarkTextNodeIntoSpans(
      textNode,
      nodeRefId,
      speechMarkRef.children,
      styleProfile,
      nodeConfig,
      lang
    );
    if (!spans || spans.length === 0) return;
  
    // Clear the original content and insert the processed span elements.
    textNode.textContent = "";
    spans.forEach(span => textNode.appendChild(span));
  };
  
  /**
   * Splits the bookmark_link text node into multiple span elements using the speech mark data,
   * while preserving any formatting such as <em> tags.
   *
   * This function:
   *   1. Extracts an ordered list of words (with their nodeIds) from the speech mark.
   *   2. Recursively processes the child nodes of the target text element.
   *      - For text nodes, it finds the next speech word and wraps extra text and the word in spans.
   *      - For element nodes (e.g. <em>), it clones the element and processes its children recursively.
   *
   * @param textNode The original text node (or element containing the text and formatting).
   * @param nodeRefId The reference ID of the current element.
   * @param sentences An array of speech mark objects representing sentences.
   * @param styleProfile The style configuration object.
   * @param nodeConfig The current element configuration.
   * @param lang The current language.
   * @returns An array of span elements after splitting.
   */
  export const splitBookmarkTextNodeIntoSpans = (
    textNode: HTMLElement,
    nodeRefId: number,
    sentences: speechMarkChild[],
    styleProfile: IStyleProfile,
    nodeConfig: IContentElement,
    lang: string
  ): HTMLElement[] => {
    // First, extract an ordered list of words (with their nodeIds) from the speech mark.
    const speechWords: { word: string, nodeId: string }[] = [];
    sentences.forEach(sentence => {
      if (sentence.children && sentence.children.length > 0) {
        sentence.children.forEach(wordMark => {
          speechWords.push({ word: wordMark.meta.value, nodeId: wordMark.nodeId });
        });
      }
    });
  
    // Create a pointer to track the current speech word.
    let currentWordIndex = 0;
  
    /**
     * Recursively process a node (which may be a text node or an element node)
     * and return an array of nodes.
     */
    function processNode(node: Node): Node[] {
        const result: Node[] = [];
        node.childNodes.forEach(child => {
          if (child.nodeType === Node.TEXT_NODE) {
            // Process a text node.
            let text = child.textContent || "";
            // Optionally process the text with style transforms.
            text = processText(text, styleProfile[lang].renderStyling.plainText.transforms);
            let nodesForText: Node[] = [];
            // While there are remaining speech words, try to locate them in this text.
            while (currentWordIndex < speechWords.length) {
              const { word, nodeId } = speechWords[currentWordIndex];
              const foundIndex = text.indexOf(word);
              if (foundIndex === -1) break; // Current word not found in this text node.
              // If there's extra text before the word, wrap it in a span (preserve all characters, including spaces).
              if (foundIndex > 0) {
                const extraText = text.substring(0, foundIndex);
                if (extraText.length > 0) {
                  const extraSpan = createSpanEl(null, extraText, null);
                  nodesForText.push(extraSpan);
                }
              }
              // Wrap the found word in a span. Use its nodeId for identification.
              const wordSpan = createSpanEl(nodeId, word, nodeId);
              nodesForText.push(wordSpan);
              // Remove the processed portion of the text.
              text = text.substring(foundIndex + word.length);
              currentWordIndex++;
            }
            // If any text remains, wrap it in a final span (even if it is only whitespace).
            if (text.length > 0) {
              const finalSpan = createSpanEl(null, text, null);
              nodesForText.push(finalSpan);
            }
            result.push(...nodesForText);
          } else if (child.nodeType === Node.ELEMENT_NODE) {
            // For element nodes (e.g. <em>), clone the element (without children)
            // and process its children recursively.
            const elem = child as HTMLElement;
            const cloned = elem.cloneNode(false) as HTMLElement;
            const processedChildren = processNode(child);
            processedChildren.forEach(n => cloned.appendChild(n));
            result.push(cloned);
          }
          // Other node types (e.g. comments) are skipped.
        });
        return result;
      }
      
  
    // Process the textNode recursively.
    const processedNodes = processNode(textNode);
  
    // Ensure all returned nodes are HTMLElements (wrap non-element nodes in a span).
    const finalSpans: HTMLElement[] = [];
    processedNodes.forEach(n => {
      if (n instanceof HTMLElement) {
        finalSpans.push(n);
      } else {
        const span = document.createElement("span");
        span.appendChild(n);
        finalSpans.push(span);
      }
    });
    return finalSpans;
  };