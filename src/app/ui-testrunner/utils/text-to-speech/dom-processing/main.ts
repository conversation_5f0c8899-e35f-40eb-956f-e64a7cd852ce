import { speechMarkChild, ISpeechMarks, EHtmlTagNames, IAuthoringElementsTtsConfig } from "../types";
import { splitTextNodeIntoSpans, processElementRenderText } from "./element-render-text-tts";
import { findElementNode, getNextElement, isTextNode, isTextOrImgNode, isImgNode, isButtonNode, isMcqNode } from "./html-util";
import { processElementRenderImg, processElementRenderImgsubText } from "./element-render-image-tts";
import { processElementRenderMcqTts } from "./element-render-mcq-tts";
import { processElementRenderDndTts } from "./element-render-movable-dnd-tts";
import { processElementRenderGroupingTts } from "./element-render-grouping-tts";
import { processElementRenderTableTts } from "./element-render-table-tts";
import { IStyleProfile } from "../../../../core/styleprofile.service";
import { IQuestionConfig, IContentElement } from "../../../models";
import { getQuestionContent, getQuestionDeepElements } from "../../../../ui-item-maker/widget-audits/util/content-analysis";
import { processElementRenderInsertionTts } from "./element-render-insertion-tts";
import { processElementRenderPassageTts } from "./element-render-passage-tts";
import { processElementRenderSelectTextTts } from "./element-render-select-text-tts";
import { processElementRenderOrderTts } from "./element-render-order-tts";
import { processElementRenderBookmarkTts } from "./element-render-bookmark-tts";


// Node Selection and Reference
export const getCurrentSelectedNode = (): Selection | undefined => {
    let selection;
    if (window.getSelection) {
        selection = window.getSelection();
    }
    /*else if (document.selection) {
       selection = document.selection.createRange();
    }*/

    if (selection.toString()) return selection;
    return undefined
}

export const getNodeReferenceId = (authoringEl: Element) => {
    // assuming the node Id is applied to the first child of the authoring element
    const htmlNodeEntryId = (<HTMLElement>authoringEl)?.children?.[0]?.id;
    if (!htmlNodeEntryId && isNaN(+htmlNodeEntryId)) return undefined;
    return +htmlNodeEntryId
}

export const getNodeRefIdFromEl = (el: Element) => {
    if (!el) return undefined;
    let elementNode = findElementNode(el)
    while (!elementNode.id) {
        elementNode = <Element>getNextElement(elementNode, true)
    }

    return elementNode.id
}




// Process Authoring Elements

export const getAuthoringElementOfCurrentNode = (sourceEl: Element) => {
    let currEl = sourceEl

    while (!isAuthoringElement(currEl)) {
        currEl = currEl.parentElement
    }

    return currEl
}

export const authoringElementsTtsConfig: IAuthoringElementsTtsConfig = {
    'element-render-text':          { process: processElementRenderText },
    'element-render-image':         { process: processElementRenderImg },
    'element-render-mcq':           { process: processElementRenderMcqTts },
    'element-render-moveable-dnd':  { process: processElementRenderDndTts },
    'element-render-grouping':      { process: processElementRenderGroupingTts },
    'element-render-table':         { process: processElementRenderTableTts },
    'element-render-insertion':     { process: processElementRenderInsertionTts },
    'element-render-passage':       { process: processElementRenderPassageTts },
    'element-render-select-text':   { process: processElementRenderSelectTextTts },
    'element-render-order':         { process: processElementRenderOrderTts},
    'element-render-bookmark-link': { process: processElementRenderBookmarkTts}
}

export const isAuthoringElement = (el: Element) => {
    return !!authoringElementsTtsConfig[el.localName]
}

export const processAuthoringElementsForTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
    const localName = el.localName;
    return authoringElementsTtsConfig[localName].process(el, nodeRefId, speechMarkRef, styleProfile, nodeConfig, lang)
}


// Traverse Dom from the given node

export const processDOMforTts = (config: { speechMarks: ISpeechMarks, styleProfile: IStyleProfile, qConfig: IQuestionConfig, lang: string }) => {
    // const qRunnerEl = document.querySelector(".runner-container");
    // const elements = qRunnerEl.querySelectorAll('*');
    // const elementsArray = Array.from(elements);
    // for (const el of elementsArray) {
    //     // console.log('pre-processing dom', el)
    //     if(isAuthoringElement(el)){
    //         const nodeRefId = getNodeReferenceId(el);
    //         const speechMarkRef = config.speechMarks.filter(o => o.nodeId === nodeRefId?.toString())?.[0]
    //         processAuthoringElementsForTts(el, nodeRefId, speechMarkRef)
    //     } 
    // }

    const {speechMarks, styleProfile, qConfig, lang} = config;

    let elConfig: IContentElement;            
    const qContents = getQuestionDeepElements(qConfig, <any>lang);

    speechMarks.forEach(speechMarkRef => {
        const element = document.getElementById(speechMarkRef.nodeId);
        if(element) {
            const parentAuthoringEl = element.parentElement
            
            const elementConfigs = qContents.filter(block => block.entryId.toString() == speechMarkRef.nodeId );
            elConfig = elementConfigs.length ? elementConfigs[0] : undefined

            processAuthoringElementsForTts(parentAuthoringEl, +speechMarkRef.nodeId, speechMarkRef, styleProfile, elConfig, lang) 
        }
    })

    // console.log("elements", qRunnerEl, elementsArray) 
}

export const traverseDOM = (node: Element, callBackFn: Function, resultNodes: Element[] = [], visitedNodes: Set<Element> = new Set()) => {
    if (visitedNodes.has(node)) {
        return resultNodes; // Skip the node if it has already been visited
    }

    if (callBackFn(node)) {
        resultNodes.push(node)
    }

    visitedNodes.add(node);

    const children = Array.from(node.childNodes);
    for (const child of children) {
        traverseDOM(<Element>child, callBackFn, resultNodes, visitedNodes);
    }

    return resultNodes
}








// Extras for future ref if processing real time:

/*

export const processedElement = (childEls) => {
    return  `<span id="TTS_PROCESSED">${childEls}</span>`
}

const getSelectedWordAndSentence = (selection: Selection) => {
    let word = '';
    let sentence = '';
    let index = -1;
  
    if (selection) {
        word = selection.toString().trim();
  
        if (word !== '') {
            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;
  
            if (container.nodeType === Node.TEXT_NODE) {
                const content = container.textContent;
                const sentenceDelimiter = '.';
                const sentenceDelimitersRegex = /[.!?]/g; // Maybe use Regex - TODO: Find what Amazon polly uses
                const startIndex = content.lastIndexOf(sentenceDelimiter, range.startOffset) + 1;
                const endIndex = content.indexOf(sentenceDelimiter, range.endOffset);
  
                // handle sentences edge cases
                // single sentence ends with delimiter
                // single sentence ends without delimiter
                // multi sentences first sentence
                // multi sentences ends without delimiter
  
                // Handle single / first sentence scenario
                if (startIndex === 0 && endIndex === -1) {  
                  sentence = content.trim();
                  index = sentence.split(' ').length - 1;
                }
                else if (startIndex !== -1 && endIndex !== -1) {
                  if(endIndex === -1) {
                    // sentence ends without delimiter
                  } else {
                    sentence = content.substring(startIndex + 1, endIndex + 1).trim();
                  }
                }
  
                if (sentence.length) {
                  index = sentence.split(' ').length - 1;
                }
            }
        }
    }
  
    return { index, word, sentence };
  } 
*/