import { speechMarkChild, EHtmlTagNames } from "../types";
import { traverseDOM } from "./main";
import { IStyleProfile } from "../../../../core/styleprofile.service";
import { IContentElement } from "../../../models";
import { insertionDragElIdPrefix } from "./tts-dom-alteration-patch";
import { KEY_ID_NODE, FILLER_NODE, INSTRUCTION_TEXT } from "../constants";
import { isTextNode } from "./html-util";
import { splitTextNodeIntoSpans } from "./element-render-text-tts";
import { IContentElementInsertion } from "../../../element-render-insertion/model";

export const processElementRenderInsertionTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
    const isDraggableEl = (node: Element) => node.classList?.contains('drag-el'); // node.classList ? Array.from(node.classList)?.some(className => className.includes(insertionDragElIdPrefix)) : false; 
    const isBlockEl = (node: Element) => node.classList?.contains('block-el'); 
    const isDndEl = (node: Element) => isDraggableEl(node) || isBlockEl(node);

    const nodes = <Element[]>traverseDOM(el, isDndEl);
    const dragEls = <Element[]>nodes.filter(n => isDraggableEl(n));
    const blockEls = <Element[]>nodes.filter(n => isBlockEl(n));

    

    const processDragEl = () => {
        const dragSpeechMarks = speechMarkRef.children.filter(sentence => sentence.meta.customType && sentence.meta.customType.includes(KEY_ID_NODE) ? true : false )
        dragEls.forEach(el => {
            let dragElkeyIdClass;
            Array.from(el.classList).forEach(className => {
                if(className.includes(insertionDragElIdPrefix)) dragElkeyIdClass = className
            });

            if(!dragElkeyIdClass) console.log("keyId is not defined, TTS will fail for the drag el", el);
            else {
                // find the nodeId in speech marks from keyId
                let added: boolean
                dragSpeechMarks.forEach(sp => {
                    if(sp.meta.customType){
                        const keyId = sp.meta.customType?.split('_')[1];
                        if(insertionDragElIdPrefix + keyId == dragElkeyIdClass){
                            el.classList.add(sp.nodeId)
                            added = true
                        } 
                    }
                })

                if(!added) console.log('nodeId is not added, TTS will fail', el)
            }
        })
    }

    const processBlockEl = () => {

        const blockSpeechMarks = speechMarkRef.children.filter(sentence => !sentence.meta.customType )
        console.log(blockSpeechMarks);
        let blockSpeechMarksIndex = 0
        blockEls.forEach((block: Element, blockIdx: number) => {
            // determine textBlock or targetBlock
            const isTextBlock = block.children.length && block.children[0].tagName === 'SPAN';
            if(isTextBlock) {
                const textNodes = traverseDOM(block, isTextNode);
                if(textNodes.length){
                    splitTextNodeIntoSpans(<Element>textNodes[0].parentElement, nodeRefId, [blockSpeechMarks[blockSpeechMarksIndex]]);
                    blockSpeechMarksIndex++;
                }
            }
            else {
                // draggable nodes get processed in processDragEl
            }
        })
    }

    const elConfig = <IContentElementInsertion>nodeConfig;
    let isTargetsAbove = elConfig.isTargetsAbove;
    let showInstrText = elConfig.isShowInstructions;

    // process instruction text
    if(showInstrText) {
        const instrSentence = speechMarkRef.children.filter(sentence => sentence.meta.customType === INSTRUCTION_TEXT)?.[0];
        //insertion-instr-tts
        const instrEl = el.getElementsByClassName('insertion-instr-tts');
        if(instrEl?.length){
            const instrTextNodes = traverseDOM(instrEl[0], isTextNode);
            splitTextNodeIntoSpans(<Element>instrTextNodes[0].parentElement, nodeRefId, [instrSentence]);
        }
    }


    if(isTargetsAbove){
        processDragEl();
        processBlockEl();
    } else {
        processBlockEl();
        processDragEl();
    }

}