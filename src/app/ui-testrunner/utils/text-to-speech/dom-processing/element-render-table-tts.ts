import { speechMarkChild, EHtmlTagNames } from "../types";
import { traverseDOM } from "./main";
import { isTableRowNode, isTableDataNode } from "./html-util";
import { IStyleProfile } from "../../../../core/styleprofile.service";
import { FILLER_NODE } from "../constants";
import { IQuestionConfig, IContentElement } from "../../../models";
import { IContentElementTable } from "../../../element-render-table/model";

export const processElementRenderTableTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {

    const tableConfig = <IContentElementTable>nodeConfig;
    const firstColumnIsHeader = tableConfig.isHeaderCol;

    const rowNodes = <Element[]>traverseDOM(el, isTableRowNode);

    const nodesGrid: Element[][] = [];

    rowNodes.forEach(row => {
        const cellNodes = <Element[]>traverseDOM(row, isTableDataNode);
        nodesGrid.push(cellNodes);
    });

    if(!lang) lang = 'en';
    const tableProfile = styleProfile[lang].voiceScript.table;
    const { children: sentence} = speechMarkRef;
    
    let sentenceIdx = 0

    const getCellSentence = () => {
        const getSentenceMeta = () => sentence[sentenceIdx].meta;

        while(sentenceIdx < sentence.length && getSentenceMeta().customType === FILLER_NODE){
            sentenceIdx++ ;
        };

        if (sentenceIdx < sentence.length) return sentence[sentenceIdx];
        return undefined;
    }

    if(firstColumnIsHeader && tableProfile.columnHeaderReadRowsFirst && nodesGrid.length > 0) {
        for(let c = 0; c < nodesGrid[0].length; c++) {
          for(let r = 0; r < nodesGrid.length; r++) {
            // assign id to nodes
            const sentenceMeta = getCellSentence();
            if(sentenceMeta){
                nodesGrid[r][c].classList.add(sentenceMeta.nodeId)
            }
            sentenceIdx++;
          }
        }
      } else {
        nodesGrid.forEach( (row, i_row) => {
          let cellScript = [];
          row.forEach((cell, i_col) => {
            // assign id to nodes
            const sentenceMeta = getCellSentence();
            if(sentenceMeta){
                cell.classList.add(sentenceMeta.nodeId)
            }
            sentenceIdx++
          });
        });
      }
}