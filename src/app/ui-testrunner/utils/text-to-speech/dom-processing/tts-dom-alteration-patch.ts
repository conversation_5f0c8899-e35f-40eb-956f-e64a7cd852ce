import { CdkDragStart, CdkDragDrop, CdkDropList } from "@angular/cdk/drag-drop";
import { TextToSpeechService } from "../../../text-to-speech.service";
import { traverseDOM } from "./main";
import { IElementPos } from "../../../element-render-dnd/model";
import { ElementRef } from "@angular/core";
import { speechMarkChild } from "../types";
import { KEY_ID_NODE, TARGET_NODE_KEY_ID, DRAG_NODE_KEY_ID, ORDER_NODE_KEY_ID } from "../constants";

export const groupingDragElIdPrefix = 'grouping-drag-keyid-';
export const groupingTargetElIdPrefix = 'grouping-target-keyid-';  
export const insertionDragElIdPrefix = 'insertion-drag-keyid-';
export const movableDndDragElPrefix = 'dnd-drag-keyid-';
export const movableDndTargetElPrefix = 'dnd-target-keyid-';
export const orderingDragElIdPrefix = 'ordering-drag-keyid-';
export const orderingTargetElIdPrefix = 'ordering-target-keyid-';

export const patchDragDropEl = (dragPatchMeta: {prefix: string, classList: DOMTokenList}, textToSpeech : TextToSpeechService) => {
    if(!textToSpeech.isActive) return;
    
    const { classList, prefix } = dragPatchMeta;
    // const key_id = event.container.data[event.currentIndex].ref.key_id
    // if(!key_id) return console.log('TTS Drag Drop Patch failed, counld not find key_id');
    
    // const dragIdClass = `${prefix + key_id}`
    // const dragNodes =  document.getElementsByClassName(dragIdClass);  // traverseDOM(elementRef.nativeElement, (node: Element) => node.classList?.contains(dragIdClass))
    
    // retrive the TTS element class
    const prevDragElClasses = classList // event.item.element.nativeElement.classList;
    let dragKeyIdClass; 
    let ttsDragNodeId;
    if(prevDragElClasses){
        prevDragElClasses.forEach(className => {
            // drag key_id ref
            if(className.includes(prefix)) dragKeyIdClass = className;

            // speech mark ref
            const speechMarksMetaChild = textToSpeech.speechMarksMap.get(className)?.ref;
            if(speechMarksMetaChild) ttsDragNodeId = speechMarksMetaChild.nodeId
        })
    }

    if(!ttsDragNodeId)  return console.log('TTS Drag Drop Patch failed, speech marks ref not found');
    
    const dragNodes =  document.getElementsByClassName(dragKeyIdClass);
    Array.from(dragNodes).forEach(n => { 
        if(!n.classList?.contains(ttsDragNodeId)) n.classList.add(ttsDragNodeId)
    });

    console.log(dragKeyIdClass, dragNodes, ttsDragNodeId);
}




export const processDragEl = (speechMarkRef: speechMarkChild, dragEls: Element[], dragKeyIdPrefix: string) => {
    const dragSpeechMarks = speechMarkRef.children.filter(sentence => sentence.meta.customType && sentence.meta.customType.includes(DRAG_NODE_KEY_ID) ? true : false )
    dragEls.forEach(el => {
        let dragElkeyIdClass;
        Array.from(el.classList).forEach(className => {
            if(className.includes(dragKeyIdPrefix)) dragElkeyIdClass = className
        });

        if(!dragElkeyIdClass){
            console.log("keyId is not defined, TTS will fail for the drag el", el);
        }
        else {
            // find the nodeId in speech marks from keyId
            let added: boolean
            dragSpeechMarks.forEach(sp => {
                if(sp.meta.customType){
                    const keyId = sp.meta.customType?.split('_')[1];
                    if(dragKeyIdPrefix + keyId == dragElkeyIdClass){
                        el.classList.add(sp.nodeId)
                        added = true
                    } 
                }
            })

            if(!added) console.log('nodeId is not added, TTS will fail', el)
        }
    })
}

export const processTargetEl = (speechMarkRef: speechMarkChild, TargetEls: Element[], targetKeyIdPrefix: string) => {
    const TargetSpeechMarks = speechMarkRef.children.filter(sentence => sentence.meta.customType && sentence.meta.customType.includes(TARGET_NODE_KEY_ID) ? true : false )
    TargetEls.forEach(el => {
        let targetElkeyIdClass;
        Array.from(el.classList).forEach(className => {
            if(className.includes(targetKeyIdPrefix)) targetElkeyIdClass = className
        });

        if(!targetElkeyIdClass) console.log("keyId is not defined, TTS will fail for the Target el", el.classList);
        else {
            // find the nodeId in speech marks from keyId
            let added: boolean
            TargetSpeechMarks.forEach(sp => {
                if(sp.meta.customType){
                    const keyId = sp.meta.customType?.split('_')[1];
                    if(targetKeyIdPrefix + keyId == targetElkeyIdClass){
                        el.classList.add(sp.nodeId)
                        added = true
                    } 
                }
            })

            if(!added) console.log('nodeId is not added, TTS will fail', el)
        }
    })
}

export const processOrderDragEl = (speechMarkRef: speechMarkChild, dragEls: Element[], orderingDragElIdPrefix) => {
    const dragSpeechMark = speechMarkRef.children.filter(sentence => sentence.meta.customType && sentence.meta.customType.includes(ORDER_NODE_KEY_ID) ? true : false )
    dragEls.forEach((el) => {
        let dragElKeyIdClass;
        Array.from(el.classList).forEach((className) => {
            if(className.includes(orderingDragElIdPrefix)) dragElKeyIdClass = className;
        });
        if(!dragElKeyIdClass) console.log("keyId is not defined, TTS will fail for the drag el", el);
        let added: boolean;
        dragSpeechMark.forEach((sp) => {
            if(sp.meta.customType) {
                const keyId = sp.meta.customType.split('_')[1];
                if(orderingDragElIdPrefix + keyId == dragElKeyIdClass){
                    el.classList.add(sp.nodeId)
                    added = true
                } 
            }
        })
        if(!added) console.log('nodeId is not added, TTS will fail', el);
    })
}