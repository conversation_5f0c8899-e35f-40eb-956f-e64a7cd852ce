import { speechMarkChild, EHtmlTagNames } from "../types";
import { traverseDOM } from "./main";
import { isMcqNode, isTableRowNode, isTableDataNode } from "./html-util";
import { FILLER_NODE } from "../constants";
import { IStyleProfile } from "../../../../core/styleprofile.service";
import { IContentElement } from "../../../models";
import { IContentElementMcq } from "../../../element-render-mcq/model";

export const processElementRenderMcqTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string ) => {
    const mcqConfig = <IContentElementMcq>nodeConfig;
    
    const mcqNodes = <Element[]>traverseDOM(el, isMcqNode);
    const mcqSelect = mcqNodes?.filter(node => node.tagName === EHtmlTagNames.SELECT);
    const mcqTable = mcqNodes?.filter(node => node.tagName === EHtmlTagNames.TABLE);
    const mcqButtons = mcqNodes?.filter(node => node.tagName === EHtmlTagNames.BUTTON);
    if(mcqSelect.length) {
        // process dropdown
        return mcqSelect[0].classList.add(...speechMarkRef.children.map(child => child.nodeId))
    }

    if(mcqTable.length){
        let mcqTableData = traverseDOM(mcqTable[0], isTableDataNode);        
        const speechMarksColRef = speechMarkRef.children.filter(child => child.meta.customType !== FILLER_NODE);

        if(mcqConfig.isRadioRowHeaderDisabled) mcqTableData = mcqTableData.slice(1);
        return mcqTableData?.forEach((col, colIndex) => {            
            col.classList.add(speechMarksColRef[colIndex].nodeId);
        })
    }

    if(mcqButtons.length === 1 && mcqButtons[0].classList.contains('custom-dropdown-button')) {
        // custom dropdown
        return mcqButtons[0].classList.add(...speechMarkRef.children.map(child => child.nodeId))
        // mcqButtons[0].id = speechMarkRef.children[0].nodeId;
    }
 
    mcqButtons?.forEach((node, buttonIndex) => {
        // node.id = speechMarkRef.children[buttonIndex].nodeId;
        node.classList.add(speechMarkRef.children[buttonIndex].nodeId)
    })
    // console.log(mcqNodes, speechMarkRef)  
}