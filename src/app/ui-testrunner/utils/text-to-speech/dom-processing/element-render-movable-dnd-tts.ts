import { speechMarkChild, EHtmlTagNames } from "../types";
import { traverseDOM } from "./main";
import { movableDndDragElPrefix, movableDndTargetElPrefix, processDragEl, processTargetEl  } from "./tts-dom-alteration-patch";

export const processElementRenderDndTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild) => {
    
    const isFoundEl = (node: Element, prefix: string) => {
        let isMatchFound = false;
        node.classList?.forEach(_class => {
            if(_class.includes(prefix)) isMatchFound = true
        });
        return isMatchFound;
    }

    const isDraggableEl = (node: Element) => isFoundEl(node, movableDndDragElPrefix);
    const isTargetEl = (node: Element) => isFoundEl(node, movableDndTargetElPrefix); 
    const isDndEl = (node: Element) => isDraggableEl(node) || isTargetEl(node);

    const nodes = <Element[]>traverseDOM(el, isDndEl);
    const dragEls = <Element[]>nodes.filter(n => isDraggableEl(n));
    const TargetEls = <Element[]>nodes.filter(n => isTargetEl(n));

    processDragEl(speechMarkRef, dragEls, movableDndDragElPrefix);
    processTargetEl(speechMarkRef, TargetEls, movableDndTargetElPrefix);
}