import { speechMarkChild } from "../types";
import { CT_ALT_TEXT } from "../constants";
import { splitTextNodeIntoSpans } from "./element-render-text-tts";
import { traverseDOM } from "./main";
import { isTextOrImgNode, isImgNode, isTextNode } from "./html-util";

export const processElementRenderImg = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild) => {
    const imgTxtNodes = traverseDOM(el, isTextOrImgNode)
    const imgNodes = imgTxtNodes.filter(node => isImgNode(node));            
    const subtxtNodes = imgTxtNodes.filter(node => isTextNode(node));
    if(imgNodes) processElementRenderImgAltText(imgNodes, nodeRefId, speechMarkRef)
    if(subtxtNodes) processElementRenderImgsubText(subtxtNodes, nodeRefId, speechMarkRef)
} 


export const processElementRenderImgAltText = (nodes: Element[], nodeRefId: number, speechMarkRef: speechMarkChild) => {
    const { children: senetences } = speechMarkRef;
    const altTextSentence = senetences.filter(s => s.meta.customType === CT_ALT_TEXT)?.[0]
    if(!altTextSentence) return console.log('TTS: No alt-text found, Image won\'t be highlighted');
    const altTextNodeId = altTextSentence.nodeId
    // nodes[0].id = altTextNodeId // assuming there will be only one image node at a time
    nodes[0].classList.add(altTextNodeId);
}

export const processElementRenderImgsubText = (nodes: Element[], nodeRefId: number, speechMarkRef: speechMarkChild) => {
    const { children: senetences } = speechMarkRef;
    const subtextSentences = senetences.filter(s => s.meta.customType !== CT_ALT_TEXT);
    if(subtextSentences.length !== nodes.length) console.log(`TTS: for img entryId: ${nodeRefId} SubTexts and speechMarks count are not equal `)
    nodes.forEach((node, idx) => splitTextNodeIntoSpans(<Element>node.parentElement, nodeRefId, [subtextSentences[idx]]));
}