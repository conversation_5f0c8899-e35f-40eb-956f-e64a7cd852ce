import { IStyleProfile } from "../../../../core/styleprofile.service";
import { showGroupingInstr } from "../../../element-render-grouping/element-render-grouping.component";
import { IContentElementGroup } from "../../../element-render-grouping/model";
import { IContentElement } from "../../../models";
import { INSTRUCTION_TEXT } from "../constants";
import { speechMarkChild, EHtmlTagNames } from "../types";
import { splitTextNodeIntoSpans } from "./element-render-text-tts";
import { isTextNode } from "./html-util";
import { traverseDOM } from "./main";
import { groupingDragElIdPrefix, processDragEl, processTargetEl, groupingTargetElIdPrefix } from "./tts-dom-alteration-patch";

export const processElementRenderGroupingTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
    
    const elConfig = <IContentElementGroup>nodeConfig;
    
    const isDraggableEl = (node: Element) => node.classList?.contains('drag-el'); 
    const isTargetEl = (node: Element) => node.classList?.contains('target-el'); 
    const isDndEl = (node: Element) => isDraggableEl(node) || isTargetEl(node)

    const nodes = <Element[]>traverseDOM(el, isDndEl);
    const dragEls = <Element[]>nodes.filter(n => isDraggableEl(n));
    const TargetEls = <Element[]>nodes.filter(n => isTargetEl(n));

    processDragEl(speechMarkRef, dragEls, groupingDragElIdPrefix);
    processTargetEl(speechMarkRef, TargetEls, groupingTargetElIdPrefix);

    if(showGroupingInstr(elConfig)){
        // Currently there's only one Instruction text
        const instrSentence = speechMarkRef.children.filter(sentence => sentence.meta.customType === INSTRUCTION_TEXT)?.[0];
        const instrEl = el.getElementsByClassName('grouping-instr-tts');
        if(instrEl?.length){
            const instrTextNodes = traverseDOM(instrEl[0], isTextNode);
            splitTextNodeIntoSpans(<Element>instrTextNodes[0].parentElement, nodeRefId, [instrSentence]);
        }
    }
}