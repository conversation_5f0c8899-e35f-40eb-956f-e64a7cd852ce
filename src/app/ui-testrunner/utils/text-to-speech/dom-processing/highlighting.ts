// Highlighting

import { ETtsHighlightAction, ESpeechMarkTypes } from "../types";

const getNodeRefIdOfSentence = (nodeRefId: string) => {
    if(!nodeRefId) {
        console.log('NodeId not found')
        return undefined
    }
    return nodeRefId.split('.').slice(0, 2).join('.');
}

interface IHighlightNodeTypeProp {    
    name: string, 
    getNodeId: (arg: string) => string | undefined; 
    highlightClass: string,
    isCustom?: boolean;    
}
interface IHighlightNodeTypes {
    [key: string]: IHighlightNodeTypeProp
}

export const highlightNodeTypes: IHighlightNodeTypes = {
    SENTENCE: { name: 'sentence', getNodeId: getNodeRefIdOfSentence, highlightClass: 'tts-h-sentence'},
    WORD: { name: 'word', getNodeId: (nodeId:string) => nodeId, highlightClass: 'tts-h-word'},
    IMG: { name: 'img', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover' , isCustom: true},                // tts-h-img
    BUTTON: {name: 'button', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover',  isCustom: true},           // tts-h-button
    TR: {name: 'tr', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover',  isCustom: true},                   // tts-h-tr
    TD: {name: 'td', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover',  isCustom: true},                   // tts-h-tr
    TARGET_EL : {name: 'target-el', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover',  isCustom: true},    // tts-h-targel-el
    DRAG_EL : {name: 'drag-el', getNodeId: getNodeRefIdOfSentence, highlightClass: 'custom-tts-layover',  isCustom: true}         // tts-h-drag-el
}


const determineElementHighlightConfig = (nodeRefId: string, config: IHighlightNodeTypeProp): IHighlightNodeTypeProp => {
    const specialTags = ['BUTTON', 'IMG', 'TR', 'TD']
    // const node = document.getElementById(nodeRefId);
    const node = document.getElementsByClassName(nodeRefId)?.[0];
    if(node){
        if(specialTags.includes(node.tagName)) config = highlightNodeTypes[node.tagName];
        // special cases:
        if(node.classList.contains('target-el')) config = highlightNodeTypes['TARGET_EL'];  // MOVABLE DND
        if(node.classList.contains('drag-el')) config = highlightNodeTypes['DRAG_EL'];  
    }
    //  console.log(nodeRefId, node,  config.name)
    return config;
}

export const  highlightElement = (nodeRefId: string, action: ETtsHighlightAction, highlightNodeConfig: IHighlightNodeTypeProp) => {
    highlightNodeConfig = determineElementHighlightConfig(nodeRefId, highlightNodeConfig)
    // console.log(highlightNodeConfig.name, action)
    const { name, highlightClass, getNodeId, isCustom} = highlightNodeConfig;
    const nodeId = getNodeId(nodeRefId);
    if(isCustom) {
        layoverEffect(<string>nodeId, highlightClass, action)
    } else {
        highlightNode(<string>nodeId, highlightClass, action);
    }
}

const highlightNode = (nodeId: string, className: string, action: ETtsHighlightAction) => {
    let nodeList = document.getElementsByClassName(nodeId);
    for(let i = 0; i < nodeList.length; i++) {
        let node = nodeList[i];
        if(!node) continue;
        if (action === ETtsHighlightAction.ADD) node.classList.add(className);
        else if (action === ETtsHighlightAction.REMOVE) node.classList.remove(className)
    }
}

export const highlightSentence = (nodeRefId: string, action: ETtsHighlightAction) => {
    highlightElement(nodeRefId, action, highlightNodeTypes.SENTENCE)
}

export const highlightWord = (nodeRefId: string, action: ETtsHighlightAction) => {
    highlightElement(nodeRefId, action, highlightNodeTypes.WORD)
}

export const cleanupHighlighting = (nodeRefId: string) => {
    Object.keys(highlightNodeTypes).forEach(key => highlightElement(nodeRefId, ETtsHighlightAction.REMOVE, highlightNodeTypes[key]))
}

export const highlightImg = (nodeRefId: string, action: ETtsHighlightAction) => {
    highlightElement(nodeRefId, action, highlightNodeTypes.IMG)
}


const layoverEffect = (nodeId: string, highlightClass: string, action: ETtsHighlightAction) => {
    if (action === ETtsHighlightAction.ADD) applyLayoverEffect(nodeId, highlightClass);
    else if (action === ETtsHighlightAction.REMOVE) removeLayoverEffect(nodeId, highlightClass);
}

const applyLayoverEffect = (nodeId: string, highlightClass: string) => {
    const el = document.getElementsByClassName(nodeId)?.[0];
    const rect = el.getBoundingClientRect();
    const LayoverBox = document.createElement('div');
    LayoverBox.style.position = 'fixed';
    LayoverBox.style.top = rect.top + 'px';
    LayoverBox.style.left = rect.left + 'px';
    LayoverBox.style.width = (rect.right - rect.left)+'px';
    LayoverBox.style.height = (rect.bottom - rect.top)+'px';
    LayoverBox.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
    LayoverBox.classList.add(`${highlightClass}-${nodeId}`);
    document.getElementsByTagName('app-root')[0].appendChild(LayoverBox);
}

const removeLayoverEffect = (nodeId: string, highlightClass: string) => {
    const className = `${highlightClass}-${nodeId}`;
    const el = document.getElementsByClassName(className)?.[0];
    if(el) el.remove();
}