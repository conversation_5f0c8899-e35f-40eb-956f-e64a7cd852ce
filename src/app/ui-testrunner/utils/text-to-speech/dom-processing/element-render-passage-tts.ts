import { IStyleProfile } from "src/app/core/styleprofile.service";
import { IContentElement } from "src/app/ui-testrunner/models";
import { speechMarkChild } from "../types";
import { traverseDOM } from "./main";
import { splitTextElementsIntoSpans } from "./element-render-text-tts";
import { createHtmlEl, createSpanEl } from "./gen-html-elements"


export const processElementRenderPassageTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElement, lang: string) => {
    let { children: sentences } = speechMarkRef;
    let isProcessedDOM = false;
    const isBookmark = (node: Element) => node.classList?.contains('bookmark') && /^id-line_\d+$/.test(node.className.split(' ').find(c => c.startsWith('id-line_')) || '');
    let paragraphNodes = traverseDOM(el, isBookmark);
    paragraphNodes.forEach(p => {
        if(p.classList.contains('passage-tts-processed')) {
            isProcessedDOM = true;
        }
    })
    if(isProcessedDOM) {
        return;
    }
    // console.log(paragraphNodes);
    // console.log(sentences);
    // console.log(reshapePassageSentence(sentences));
    sentences = reshapePassageSentence(sentences);
    let currentIndex = 0;
    for (let i = 0; i < paragraphNodes.length; i++) {
        const paragraphNode = paragraphNodes[i];
        const textContent = paragraphNode.textContent;
        let matchedSentences = [];
        let remainingContent = textContent;
        let extraSpanLast = null;
        let extraSpanFirst = null;
        let periodSpace = false;
        if(textContent.indexOf('. ') !== -1) {
            periodSpace = true;
        }
        while (currentIndex < sentences.length && remainingContent.length > 0) {
            const sentence = sentences[currentIndex];
            let sentenceText = sentence.meta.value;
            let sentenceIndex = remainingContent.indexOf(sentenceText);
            if(sentenceIndex === -1) {
                extraSpanLast = sliceText(remainingContent,{start: 0, end: remainingContent.length});
                //this could be spaces comma and othe type of delimiters
                if (!extraSpanLast) {
                    console.error("Failed to create extraSpan for remaining content.");
                    break;
                }
            }
            if (sentenceIndex === 0 ) {
                matchedSentences.push(sentence);
                remainingContent = remainingContent.slice(sentenceText.length);
                currentIndex++;
            } else if(sentenceIndex > 0) {
                extraSpanFirst = sliceText(remainingContent,{start: 0, end: sentenceIndex});
                matchedSentences.push(sentence);
                remainingContent = remainingContent.slice(sentenceIndex + sentenceText.length);
                currentIndex++;
            }else {
                break;
            }
        }
        const spans = splitTextElementsIntoSpans(paragraphNode, matchedSentences).filter(span => span.id || span.className);
        if (matchedSentences.length > 0 && spans.length > 0 && remainingContent != null) {
            paragraphNode.innerHTML = '';
            if(extraSpanFirst && textContent.startsWith(extraSpanFirst.innerHTML)) {
                paragraphNode.appendChild(extraSpanFirst);
            }
            spans.forEach(span => {
                paragraphNode.appendChild(span);
                if(periodSpace) {
                    const span = document.createElement('span');
                    span.innerHTML = ' ';
                    paragraphNode.appendChild(span);
                }
            });
            if(extraSpanLast) {
                paragraphNode.appendChild(extraSpanLast);
            }
            paragraphNode.classList.add('passage-tts-processed');
        }
    }

}

export const reshapePassageSentence = (inputArray) => {
    const result = [];
    
    inputArray.forEach(item => {
        const reshapedItems = reshapeSentence(item);
        reshapedItems.forEach(reshapedItem => result.push(reshapedItem));
    });
    
    return result;
}

export const reshapeSentence = (input) => {
    const { nodeId, meta, children } = input;
    const sentences = meta.value.split('\n');
    if (sentences.length === 1) {
        return [input]; // No new line found, return the input as it is.
    }
    const result = [];
    let childIndex = 0;
    let startOffset = meta.start;

    for (let i = 0; i < sentences.length; i++) {
        const sentence = sentences[i];
        const endOffset = startOffset + sentence.length;
        const newMeta = {
            ...meta,
            start: startOffset,
            end: endOffset,
            value: sentence,
        };
        const newChildren = [];

        while (childIndex < children.length) {
            const child = children[childIndex];
            if (child.meta.start >= startOffset && child.meta.end <= endOffset + 2) {
                newChildren.push(child);
                childIndex++;
            }
            else {
                break;
            }
        }
        if(newChildren.length === 0) {
            //handle the case where newChildren array is empty but there are words in the speech mark
            let sentenceWords = sentence.split(' ');
            for (let j = 0; j < sentenceWords.length; j++) {
                if (childIndex < children.length && children[childIndex].meta.value.includes(sentenceWords[j])) {
                    newChildren.push({
                        ...children[childIndex],
                        meta: {
                            ...children[childIndex].meta,
                            value: sentenceWords[j],
                        },
                    });
                    childIndex++;
                }
            }
        }
        result.push({
            nodeId: nodeId,
            meta: newMeta,
            children: newChildren,
        });

        startOffset = endOffset + 1; // Update startOffset for the next sentence.
    }

    return result;
}

const sliceText = (currentNodetextContent:string,position: {start: number, end: number}, spanConfig?: {id: string, className: string}) => {
    const {start, end} = position;
    const {id, className } = spanConfig || {}
    const text = _processText(currentNodetextContent.slice(start, end));
    currentNodetextContent = currentNodetextContent.substring(end);
    return createSpanEl(id, text, className);
}

const _processText = (text: string) => {
    text = text.replace(/&nbsp;/g,' ')
    text = text.replace(/&amp;/g, "&");
    text = text.replace(/\u00A0/g, ' ');
    return text;
}