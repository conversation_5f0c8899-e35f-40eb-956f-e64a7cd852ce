import { EHtmlTagNames } from "../types";

// create new HTML elements
export const createSpanEl = (id?: string, text?: string, className?: string) => {
    const span = document.createElement('span');
    if(text) span.innerHTML = text;
    if(className) span.classList.add(className);
    if(id) span.id = id
    return span;
}

export const createAudioEl = (id?: string, src?: string, className?: string) => {
    const audioEl: HTMLAudioElement = new Audio(src); // document.createElement('audio');
    if(className) audioEl.classList.add(className);
    if(id) audioEl.id = id
    return audioEl;
}


export const createHtmlEl = (tag: EHtmlTagNames | string , options?: {id?: string, classes?: string[]}) => {
    const { id, classes } = options || {};
    const newEl = document.createElement(tag);
    if(id) newEl.id = id;
    if(classes) newEl.classList.add(...classes)
    return newEl;
}