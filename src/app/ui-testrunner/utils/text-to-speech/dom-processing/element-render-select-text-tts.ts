import { IStyleProfile } from "src/app/core/styleprofile.service";
import { IContentElement } from "src/app/ui-testrunner/models";
import { speechMarkChild } from "../types";
import { traverseDOM } from "./main";
import { isMarkDownNode, isTextNode } from "./html-util";
import { splitTextElementsIntoSpans, splitTextNodeIntoSpans, processTextElementIntoSpans } from "./element-render-text-tts";
import { IContentElementTextSelection } from "src/app/ui-testrunner/element-render-select-text/model";
import { createHtmlEl, createSpanEl } from "./gen-html-elements"

export const processElementRenderSelectTextTts = (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile: IStyleProfile, nodeConfig: IContentElementTextSelection, lang: string) => {
    let { children: sentences } = speechMarkRef;
    const isEligibleElement = (node: Element) => {
        return node.classList?.contains('block-el') && !node.classList?.contains('is-disabled');
    };
    const _processText = (text: string) => {
        text = text.replace(/&nbsp;/g,' ')
        text = text.replace(/&amp;/g, "&");
        text = text.replace(/\u00A0/g, ' ');
        return text;
    }
    
    const sliceText = (currentNodetextContent:string,position: {start: number, end: number}, spanConfig?: {id: string, className: string}) => {
        const {start, end} = position;
        const {id, className } = spanConfig || {}
        const text = _processText(currentNodetextContent.slice(start, end));
        currentNodetextContent = currentNodetextContent.substring(end);
        return createSpanEl(id, text, className);
    }
    let textNodes = traverseDOM(el, isEligibleElement);
    textNodes = textNodes.map(divNode => divNode.firstElementChild.firstElementChild)
    let currentIndex = 0;
    for(let i = 0;i < textNodes.length;i++) {
        let textSelecNode = textNodes[i];
        let textContent = textSelecNode.textContent;
        textContent = _processText(textContent);
        let matchedSentences = [];
        let remainingContent = textContent;
        let extraSpan = null;
        while(currentIndex < sentences.length && remainingContent.length > 0) {
            const sentence = sentences[currentIndex];
            let sentenceText = sentence.meta.value;
            let sentenceIndex = remainingContent.indexOf(sentenceText);
            if(sentenceIndex === -1) {
                extraSpan = sliceText(remainingContent,{start: 0, end: remainingContent.length});
                //this could be spaces comma and othe type of delimiters
                if (!extraSpan) {
                    console.error("Failed to create extraSpan for remaining content.");
                    break;
                }
            } 
            if (sentenceIndex === 0 ) {
                matchedSentences.push(sentence);
                remainingContent = remainingContent.slice(sentenceText.length);
                currentIndex++;
            } else if (sentenceIndex > 0) {
                matchedSentences.push(sentence);
                remainingContent = remainingContent.slice(sentenceIndex + sentenceText.length);
                currentIndex++;
            }
            else  {
                break;
            }
        }
        const spans = splitTextElementsIntoSpans(textSelecNode, matchedSentences).filter(span => span.id || span.className);
        if (spans.length > 0) {
            textSelecNode.innerHTML = '';
            spans.forEach(span => textSelecNode.appendChild(span));
            if(extraSpan) {
                textSelecNode.firstElementChild.appendChild(extraSpan);
            }
        }
    }
}