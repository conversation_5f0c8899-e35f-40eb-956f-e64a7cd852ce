// Constants
export const CUSTOM_TAG_START = 'Start';
export const CUSTOM_TAG_END = 'End';

export const AUTHORING_NODE_ID = 'authoringNodeId';
export const AUTHORING_NODE_ID_START = AUTHORING_NODE_ID + CUSTOM_TAG_START;
export const AUTHORING_NODE_ID_END = AUTHORING_NODE_ID + CUSTOM_TAG_END;


export const FILLER_NODE = 'fillerNode' // suggests TTS has extra script that is not present in the DOM
export const CT_ALT_TEXT = 'altText'
export const KEY_ID_NODE = 'keyId'
export const INSTRUCTION_TEXT = 'InstructionText'

export const DRAG_NODE = 'DragNode';
export const TARGET_NODE = 'TargetNode';
export const ORDER_NODE = 'OrderNode';
export const ORDER_NODE_KEY_ID = ORDER_NODE + KEY_ID_NODE;
export const DRAG_NODE_KEY_ID = DRAG_NODE + KEY_ID_NODE
export const TARGET_NODE_KEY_ID = TARGET_NODE + KEY_ID_NODE

// utils functions
export const getCustomNodeRefval = (prefix: string, nodeRef:string|number, prefixId?: string) =>  {
    let val = prefix
    if(prefixId) val = prefix + '_' + prefixId;
    val += '-' + nodeRef;
    return val
}
export const getNodeCustomRefTag = (nodeRef:string) =>  `<mark name="${nodeRef}"/>`

export const getCustomNode = (nodeRefId:string|number, value: string, nodeType: string, prefixId?: string, notWrapInSentenceTag?: boolean) => {
    const customStartTag = getNodeCustomRefTag(getCustomNodeRefval(nodeType + CUSTOM_TAG_START, nodeRefId, prefixId));
    const customEndTag = getNodeCustomRefTag(getCustomNodeRefval(nodeType + CUSTOM_TAG_END, nodeRefId, prefixId));
    let customTagVal = notWrapInSentenceTag ? value : `<s> ${value} </s>`;
    return `${customStartTag} ${customTagVal} ${customEndTag}`;
}

export const getFillerNode = (nodeRefId:string|number, value: string, notWrapInSentenceTag?: boolean) => {
    return getCustomNode(nodeRefId, value, FILLER_NODE, undefined,  notWrapInSentenceTag);
}

// type is either DRAG_NODE_KEY_ID | TARGET_NODE_KEY_ID
export const getKeyIdNode = (nodeRefId:string|number, keyIdVal: string, value: string, type: string ) => getCustomNode(nodeRefId, value, type , keyIdVal);

export const getInstrNode = (nodeRefId: string|number, value: string, notWrapInSentenceTag?: boolean) => getCustomNode(nodeRefId, value, INSTRUCTION_TEXT, undefined, notWrapInSentenceTag);
