import { IStyleProfile } from "../../../core/styleprofile.service";
import { IQuestionConfig, IContentElement } from "../../models";

export enum ESpeechMarkTypes{
    SSML = 'ssml',
    SENTENCE = 'sentence',
    WORD = 'word',
    VISEME = 'viseme'
}


export type NodeId = string

export interface SpeechMarksMeta {
  // meta: SpeechMarksMetaProps,
  subNodes?: { text: string, words: SpeechMarksMetaProps[]}[]
}

export interface SpeechMarks extends SpeechMarksMetaProps {
  // [nodeId: string] : SpeechMarksMeta
}


export interface speechMarkChild {
  nodeId : string,
  meta: SpeechMarksMetaPropsExtended,
  children?: speechMarkChild[]
}

export type ISpeechMarks = speechMarkChild[]

// export type speechMarkMetaType = 'sentence' | 'word' | 'ssml' | string; 

export type speechMarkTextType = "text" | "ssml";

export interface SpeechMarksMetaProps { 
    time: number, 
    type: ESpeechMarkTypes, 
    start: number, 
    end: number, 
    value: string, 
}

export interface SpeechMarksMetaPropsExtended extends SpeechMarksMetaProps { 
    nodeId: string ,
    customType?: string
}

export enum ETtsHighlightAction {
    'ADD' = 'add',
    'REMOVE' = 'remove'
}

export enum EHtmlTagNames {
  IMAGE = 'IMG',
  BUTTON = 'BUTTON',
  SELECT = 'SELECT',
  OPTION = 'OPTION',
  TABLE = 'TABLE',
  TR = 'TR',
  TD = 'TD',
  MARKDOWN_INLINE = 'MARKDOWN-INLINE'
}

export interface IAuthoringElementsTtsConfig {
  [key: string] : {
    process: (el: Element, nodeRefId: number, speechMarkRef: speechMarkChild, styleProfile?: IStyleProfile, nodeConfig?: IContentElement, lang?: string) => void
  }
}

export interface IScriptNodeRef {
  nodeRefId: string, 
  nodeScript: string
}
