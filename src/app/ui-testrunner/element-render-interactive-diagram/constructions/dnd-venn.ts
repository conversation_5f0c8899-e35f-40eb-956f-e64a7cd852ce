import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import { LAYOUT_HOME_POSITION, IDgIntElementDnDTarget, DND_UNUSED_ID, isUnusedId, resolveDnDTargetOptions, resolveDnDTargetLabelChange, generateDnD<PERSON>lock, TAG_DND_DRAGGABLE, IDgIntHomeConfig, IDgIntElementDnDAnswerSet, HOME_CONFIG_LAYOUT, generateDnDHomeConfigFromOrdering, regenerateIdOnDnDTargets, getAllOptionIdsFromDnDTargets, resolveDnDHomeConfig, resolveDnDAltAnswers, groupByHome, unusedId, IDgIntDnDConfig, generateHomeTargetDiagramsWithLabel, generateDefaultAnswerSet, ReusableDnDStateManager, DnDStateManager, setupInteractiveDnD, isConfigIndividualHome, appendHomeSuffix, getOptionLabel, DND_ISFILLED_MODE, generateHomeGroupBg, generateVoiceoverDataMap } from "./dnd-common";
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { getPxFontSize, formattedStringToBBCode, multilineTextWithSize, getFontFamily, flattenArray, generateImageTextDiagram, numberDuplicateString, IDgIntScoringParam } from "./common";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { DgIntCallbacks } from "../renderer";
import { ElementType, IContentElement } from "../../models";
import { generateDefaultElement } from "src/app/ui-item-maker/item-set-editor/models";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;
const CIRCLE_RESOLUTION = 50;

enum DND_VENN_TARGETS {
  LEFT = 'Left',
  RIGHT = 'Right',
  INTERSECTION = 'Intersection',
  OUTER = '_unused' // DND_UNUSED_ID
}

export interface IDgIntElementDnDVenn extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_VENN;
  label: {
    left: IContentElement;
    right: IContentElement;
  }
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  config: {
    angleFactor: number;
    isUseImage: boolean;
    isAllowMultipleAnswer: boolean;
    homeMaxWidth: number;
    diagramMinWidth: number;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    bgColors: {
      home: string,
      draggable: string,
      left: string,
      intersection: string,
      right: string,
    }
    capacity: {
      isLimitMaxCapacity: boolean;
      maxCapacity: number;
    }
    isNoDraggableBackground: boolean;
  };
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
}

export function generateDefaultDgIntElementDnDVenn(): IDgIntElementDnDVenn {
  return {
    constructionType: DgIntConstructionType.DND_VENN,
    label: {
      left: {caption: 'Even', ...generateDefaultElement(ElementType.TEXT)},
      right: {caption: 'Prime', ...generateDefaultElement(ElementType.TEXT)},
    },
    dndTargets: [
      {
        label : DND_VENN_TARGETS.LEFT as string,
        content: [{value:'four', ...generateDefaultElement(ElementType.TEXT)}, {value:'eight', ...generateDefaultElement(ElementType.TEXT)}],
        elementType: ElementType.TEXT,
      },
      {
        label : DND_VENN_TARGETS.INTERSECTION as string,
        content: [{value:'two', ...generateDefaultElement(ElementType.TEXT)}],
        elementType: ElementType.TEXT,
      },
      {
        label : DND_VENN_TARGETS.RIGHT as string,
        content: [{value:'seven', ...generateDefaultElement(ElementType.TEXT)}, {value:'three', ...generateDefaultElement(ElementType.TEXT)}],
        elementType: ElementType.TEXT,
      },
      {
        label : DND_VENN_TARGETS.OUTER as string,
        content: [{value:'nine', ...generateDefaultElement(ElementType.TEXT)}],
        elementType: ElementType.TEXT,
      },
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      angleFactor: 45,
      isUseImage: false,
      isAllowMultipleAnswer: false,
      diagramMinWidth: 100,
      homeMaxWidth: 200,
      homePosition: LAYOUT_HOME_POSITION.TOP,
      padding: [0.5,0.75],
      bgColors: {
        home: '#ffffff',
        draggable: '#ffffff',
        left: '#A92187',
        intersection: '#8189C2',
        right: '#80CAE8',
      },
      capacity: {
        isLimitMaxCapacity: true,
        maxCapacity: 3,
      },
      isNoDraggableBackground: false,
    }
  }
}

export function ensureDgIntElementStructureDnDVenn(element) : IDgIntElementDnDVenn {
  if (!element.label.left.elementType) {
    element.label.left = {...generateDefaultElement(ElementType.TEXT), caption: element.label.left};
  }
  if (!element.label.right.elementType) {
    element.label.right = {...generateDefaultElement(ElementType.TEXT), caption: element.label.right};
  }
  element.dndTargets.forEach((target, i) => {
    target.content.forEach((option, j) => {
      if (!option.elementType) {
        element.dndTargets[i].content[j] = {...generateDefaultElement(ElementType.TEXT), ...option};
      }
    });
  });
  return element;
}
export function resolveDnDVennTargets(element : IDgIntElementDnDVenn) : void {
  let targetNames = [];
  element.dndTargets.forEach(target => {
    if (!isUnusedId(target.label)) {
      targetNames.push(target.label);
    }
  });
  targetNames = numberDuplicateString(targetNames);
  
  if (element.homeConfig == undefined) 
    element.homeConfig = generateDnDHomeConfigFromOrdering(element.dndTargets, element.ordering);
  if (element.altAnswers == undefined) element.altAnswers = [];
  
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions(element.dndTargets, targetNames, true);
  const [idChangeMap, newOptionIds, _] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
}


export function renderDgIntDndVenn(
  element : IDgIntElementDnDVenn, svgElement : SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  
  // backward compatibility
  if (element.dndTargets.length > 0 && element.dndTargets[0].id == undefined){
    element = cloneDeep(element) as IDgIntElementDnDVenn;
    resolveDnDVennTargets(element);
  }
  //
  
  const dndTargets = element.dndTargets;
  let answerSets = [generateDefaultAnswerSet(dndTargets)];
  if (element.config.isAllowMultipleAnswer){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  if (element.config.isUseImage) {
    for (let target of dndTargets){
      for (let option of target.content){
        const url = option.image?.url;
        if (url) imageManager.queueSizeCalculation(url, option);
      }
    }
  }
  
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const homeBgColor = element.config.bgColors.home;
  const draggableBgColor = element.config.bgColors.draggable;
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const pxFontSize = getPxFontSize(svgElement);
  const fontFamily = getFontFamily(svgElement);
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isNoDraggableBackground = element.config.isNoDraggableBackground;
  const angleFactor = element.config.angleFactor;
  const diagramMinWidth = element.config.diagramMinWidth * 20/100;
  
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, pxFontSize, fontFamily, EM_LINE_HEIGHT);
  }
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  
  const maxCapacity = element.config.capacity.isLimitMaxCapacity ?
    element.config.capacity.maxCapacity : options.length;
  
  const optionInnerDiagrams : DgDiagram[] = options.map((s) => generateImageTextDiagram(
    dg, imageManager, s.value, s.image,
    multilineText, s.imageLabelPosition, padding
  ));
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  const optionMaxWidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  const optionMaxHeight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
  const optionDiagrams = optionInnerDiagrams.map(o => {
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    if (isNoDraggableBackground) return dg.style.applyStyleProfiles(source, styleProfiles);
    const sourceSize = dg.geometry.size(source);
    const bg = generateDnDBlock(
      dg, sourceSize[0] + 2 * padding[1], sourceSize[1] + 2 * padding[0], 
      styleProfiles, true)
      .append_tags(TAG_DND_DRAGGABLE);
    const combined =  bg.fill(draggableBgColor).combine(source);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  
  const [
    [regionLeft, regionMid, regionRight],
    [rectLeft, rectMid, rectRight]
  ] = generateVennDiagramTargetZones(
    dg, maxCapacity, angleFactor, diagramMinWidth,
    element.config, optionMaxWidth, optionMaxHeight
  );
  
  const leftLabel = multilineText(element.label.left.caption)
    .move_origin('bottom-center').position(regionLeft.get_anchor('top-center'))
    .translate(dg.V2(0, padding[0]));
  const rightLabel = multilineText(element.label.right.caption)
    .move_origin('bottom-center').position(regionRight.get_anchor('top-center'))
    .translate(dg.V2(0, padding[0]));
  const vennDiagram = dg.diagram_combine(regionLeft, regionMid, regionRight).fill('white').stroke('none')
    .combine(leftLabel, rightLabel);
  
  
  const groupedOptionDiagrams = groupByHome(optionDiagrams, element.homeConfig.element, idToIndexMap);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, element.homeConfig,
    groupedOptionDiagrams, vennDiagram, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, multilineText, imageManager);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, element.homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(element.config?.bgColors?.home));
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(element.homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  const idToDiagramMap = new Map<string, DgDiagram>();
  const idToTargetConfig = new Map<string, any>();
  {
    // setup diagram for dnd elements
    // dnd target
    const targetConfig = {
      type: "flex-row",
      horizontal_alignment: "center",
      padding: EM_DND_ELEMENT_PADDING,
    }
    idToDiagramMap.set(dndTargets[0].id, regionLeft);
    idToTargetConfig.set(dndTargets[0].id, { ...targetConfig, custom_region_box: rectLeft.bounding_box() });
    idToDiagramMap.set(dndTargets[1].id, regionMid);
    idToTargetConfig.set(dndTargets[1].id, { ...targetConfig, custom_region_box: rectMid.bounding_box() });
    idToDiagramMap.set(dndTargets[2].id, regionRight);
    idToTargetConfig.set(dndTargets[2].id, { ...targetConfig, custom_region_box: rectRight.bounding_box() });
    
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(homeBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, getOptionLabel(option));
      }
    }
  }
  
  const isUsingReusableDraggable = false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(vennDiagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: element.homeConfig,
      position: element.config.homePosition,
    },
    dndElementConfig: {
      targetMaxCapacity: maxCapacity,
      globalTargetConfig: {},
      idToTargetConfig
    },
    styleProfiles,
    svgElement,
    functions: {
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: DND_ISFILLED_MODE.FILL_ANY_TARGET
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function generateVennDiagramTargetZones(
  dg: DgLib, maxCapacity: number, outerAngleFactor: number, minDiagramWidth: number,
  config: IDgIntElementDnDVenn['config'], optionMaxWidth: number, optionMaxHeight: number
) : [[DgDiagram, DgDiagram, DgDiagram],[DgDiagram,DgDiagram,DgDiagram]] {
  const bgColors = config.bgColors;
  // w_i = R(2cos(a)-1)
  // h_i = 2Rsin(a)
  // w_o = Rcos(b)
  // h_o = 2Rsin(b)
  const outerAngle = outerAngleFactor * Math.PI / 180;
  const innerAngle = Math.acos((Math.cos(outerAngle) + 1) / 2);
  const minRegionWidth = optionMaxWidth + 2*EM_DND_ELEMENT_PADDING;
  const minRegionHeight = optionMaxHeight * maxCapacity + EM_DND_ELEMENT_PADDING * (maxCapacity + 1);
  
  // from min diagram width
  const r0 = minDiagramWidth / 3;
  // from min target width
  const r1 = minRegionWidth / Math.cos(outerAngle);
  // from min height (inner)
  const r2 = minRegionHeight / (2 * Math.sin(innerAngle));
  // from min height (outer)
  const r3 = minRegionHeight / (2 * Math.sin(outerAngle));
  
  const maxRadiusPageContain = (200 * 20 / 100) / 3;
  let radius = Math.max(r0, r1, r2, r3);
  radius = Math.min(radius, maxRadiusPageContain);
  
  const separation = radius;
  
  let c1 = dg.regular_polygon(CIRCLE_RESOLUTION, radius);
  let c2 = c1.translate(dg.V2(separation,0));
  let cr = dg.boolean.difference(c2, c1).fill(bgColors.right);
  let cm = dg.boolean.intersect(c2, c1).fill(bgColors.intersection);
  let cl = dg.boolean.difference(c1, c2).fill(bgColors.left);
  
  let vrLeft = dg.V2(radius, 0);
  let vrTopRight = dg.Vdir(outerAngle).scale(radius).add(c2.origin);
  let rectr = dg.rectangle_corner(dg.V2(vrLeft.x, -vrTopRight.y), vrTopRight)
    .strokedasharray([5])
  let rectl = rectr.hflip(separation/2);
  
  let vmTopRight =  dg.Vdir(innerAngle).scale(radius)
  let vmBotLeft = vmTopRight.scale(-1).add(c2.origin)
  let rectm = dg.rectangle_corner(vmBotLeft, vmTopRight)
    .strokedasharray([5])
  
  return [
    [cl, cm, cr],
    [rectl, rectm, rectr]
  ]
}
