import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import { LAYOUT_HOME_POSITION, IDgIntElementDnDTarget, DND_UNUSED_ID, 
  generateIdOnDnDTargets, isUnusedId, resolveDnDTargetOptions, resolveDnDTargetLabelChange, 
  IDgIntHomeConfig, IDgIntElementDnDAnswerSet, HOME_CONFIG_LAYOUT, generateDnDHomeConfigFromOrdering, 
  regenerateIdOnDnDTargets, resolveDnDHomeConfig, resolveDnDAltAnswers, generateDefaultAnswerSet, groupByHome, 
  generateHomeTargetDiagrams,
  unusedId,
  DND_OPTION_TEXT_MODE,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  generateHomeTargetDiagramsWithLabel,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  getAllOptionIdsFromDnDTargets,
  ReusableDnDStateManager,
  DnDStateManager,
  isConfigIndividualHome,
  appendHomeSuffix,
  IDgIntStyleConfig,
  DND_STYLE_MODE,
  resolveDnDStyleConfig,
  generateElementColorsObject,
  getOptionLabel,
  resolveTargetAndAnswersForCombinedOptions,
  generatePaddedDiagram,
  DND_ISFILLED_MODE,
  determineDnDIsFilledMode,
  generateHomeGroupBg,
  generateVoiceoverDataMap,
} from "./dnd-common";
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { getPxFontSize, formattedStringToBBCode, multilineTextWithSize, getFontFamily, flattenArray, generateImageTextDiagram, numberDuplicateString, IDgIntScoringParam, EX_TO_EM, getStyle } from "./common";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { tex2dgMathExpression } from "../mathjax";
import { mathExpressionDg } from "../math-expression";
import { DgIntCallbacks } from "../renderer";
import { generateDefaultElement } from "src/app/ui-item-maker/item-set-editor/models";
import { ElementType } from "../../models";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;

enum BOX_LAYOUT {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
  GRID = 'grid',
}

export interface IDgIntElementDnDGroup extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_GROUP;
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  styleConfig: IDgIntStyleConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  _isAlwaysSeparateAnswerSetup?: boolean;
  diagramPadding?: {
    isEnabled?: boolean;
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  }
  config: {
    isAllowMultipleAnswer: boolean;
    isUseImage: boolean;
    isUsingReusableDraggable: boolean;
    boxLayout: BOX_LAYOUT;
    boxLayoutParam?: number;
    boxMinHeight: number;
    boxMinWidth: number;
    homeMaxWidth: number;
    totalMaxWidth: number;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    draggableBgColor?: string;
    targetBgColor?: string;
    isNoDraggableBackground: boolean;
    optionFixedWidth?: number;
  };
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
}

export function generateDefaultDgIntElementDnDGroup(): IDgIntElementDnDGroup {
  return {
    constructionType: DgIntConstructionType.DND_GROUP,
    dndTargets: [
      {
        label : 'Odd',
        content: [{value:'one', ...generateDefaultElement(ElementType.TEXT)}, {value:'seven', ...generateDefaultElement(ElementType.TEXT)}],
        ...generateDefaultElement(ElementType.TEXT),
      },
      {
        label : 'Even',
        content: [{value:'eight', ...generateDefaultElement(ElementType.TEXT)}, {value:'twelve', ...generateDefaultElement(ElementType.TEXT)}],
        ...generateDefaultElement(ElementType.TEXT),
      },
    ],
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      isAllowMultipleAnswer: false,
      isUsingReusableDraggable: false,
      isUseImage: false,
      boxLayout: BOX_LAYOUT.HORIZONTAL,
      boxMinHeight: 40,
      boxMinWidth: 40,
      homeMaxWidth: 200,
      totalMaxWidth: 200,
      homePosition: LAYOUT_HOME_POSITION.TOP,
      padding: [0.5,0.75],
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
      isNoDraggableBackground: false,
    }
  }
}

export function ensureDgIntElementStructureDnDGroup(element : IDgIntElementDnDGroup) : IDgIntElementDnDGroup {
  element.dndTargets.forEach((target, i) => {
    if (!target.elementType) {
      element.dndTargets[i] = {...generateDefaultElement(ElementType.TEXT), ...target};
    }
    target.content.forEach((option, j) => {
      if (!option.elementType) {
        element.dndTargets[i].content[j] = {...generateDefaultElement(option.textMode == DND_OPTION_TEXT_MODE.MATH ? ElementType.MATH : ElementType.TEXT), ...option};
      }
    });
  });
  return element;
}

export function resolveDnDGroupTargets(element : IDgIntElementDnDGroup) : void {
  let targetNames = [];
  element.dndTargets.forEach(target => {
    if (!isUnusedId(target.label)) {
      targetNames.push(target.label);
    }
  });
  targetNames = numberDuplicateString(targetNames);
  
  if (element.homeConfig == undefined) 
    element.homeConfig = generateDnDHomeConfigFromOrdering(element.dndTargets, element.ordering);
  if (element.altAnswers == undefined) element.altAnswers = [];
  if (element.diagramPadding == undefined) element.diagramPadding = {left: 0, right: 0, top: 0, bottom: 0};
  
  const isCombineOptionContainer = element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable;
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions(element.dndTargets, targetNames, true);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  resolveTargetAndAnswersForCombinedOptions(isCombineOptionContainer, element.dndTargets, element.altAnswers);
  
  if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
  if (element.dndIsFilledMode == 'auto') {
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, isCombineOptionContainer);
  }
}

export function renderDgIntDndGroup(
  element : IDgIntElementDnDGroup, svgElement : SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  let dndTargets = element.dndTargets;
  if (dndTargets.length > 0 && dndTargets[0].id == undefined){
    dndTargets = cloneDeep(element.dndTargets) as typeof element.dndTargets;
    generateIdOnDnDTargets(dndTargets);
  }
  let homeConfig = element.homeConfig;
  if (homeConfig == undefined) {
    homeConfig = generateDnDHomeConfigFromOrdering(dndTargets, element.ordering);
  }
  //
  
  let answerSets = []
  if (!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup)
    answerSets.push(generateDefaultAnswerSet(element.dndTargets));
  if (element.config.isAllowMultipleAnswer || element.config.isUsingReusableDraggable || element._isAlwaysSeparateAnswerSetup){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  if (element.config.isUseImage) {
    for (let target of dndTargets){
      for (let option of target.content){
        const url = option.image?.url;
        if (url) imageManager.queueSizeCalculation(url, option);
      }
    }
  }
  
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const totalMaxWidth = (element.config.totalMaxWidth ?? 200) * 20/100;
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const boxMinHeight = element.config.boxMinHeight * 20/100;
  let boxMinWidth = element.config.boxMinWidth * 20/100;
  const WidthLowerLimit = boxMinWidth;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isNoDraggableBackground = element.config.isNoDraggableBackground;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, homeConfig)
  
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, isBBCode = false, maxWidth: number = Infinity) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, maxWidth);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  let maxOptionInnerWidth = Infinity
  if (element.config.optionFixedWidth) maxOptionInnerWidth = element.config.optionFixedWidth*20/100 - 2*padding[1];
  const multilineTextForOption = (s : string) => multilineText(s, false, maxOptionInnerWidth);
  
  // const optionInnerDiagrams : DgDiagram[] = options.map(s =>  multilineText(s.value));
  const optionInnerDiagrams: DgDiagram[] = options.map((s) => { 
    let textDiagramGenerator = multilineTextForOption;
    if (s.textMode == DND_OPTION_TEXT_MODE.MATH) textDiagramGenerator = mathExpression;
    return generateImageTextDiagram(
      dg, imageManager, s.value, s.image,
      textDiagramGenerator, s.imageLabelPosition, padding)
  });
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  const choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  const optionDiagrams = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    if (isNoDraggableBackground) return dg.style.applyStyleProfiles(source, styleProfiles);
    const sourceSize = dg.geometry.size(source);
    const optionWidth = element.config.optionFixedWidth ? element.config.optionFixedWidth * (20 / 100) : sourceSize[0] + 2 * padding[1];
    const optionHeight = sourceSize[1] + 2 * padding[0];
    if (element.config.optionFixedWidth) boxMinWidth = optionWidth > WidthLowerLimit ? optionWidth : WidthLowerLimit;
    let bg = generateDnDBlock(dg, 
      optionWidth, optionHeight, 
      styleProfiles, true).fill(draggableBgColor)
      .position(source.origin).append_tags(TAG_DND_DRAGGABLE);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors){
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = bg.combine(source);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeConfig.element.map((_, i) => unusedId(i));
  }
  const getDiagramUpdate = () => {
    const boxDiagrams = dndTargets.filter(t => !isUnusedId(t.label)).map(target => {
      const isMath = target.isLabelMath;
      const labelText = isMath ? mathExpression(target.label) : multilineText(target.label);
      const labelTextSize = dg.geometry.size(labelText);
      const boxWidth = Math.max(labelTextSize[0], choicesMaxwidth, boxMinWidth) + 2 * padding[1];
      const contentHeight = int.dragAndDropHandler?.get_container_content_size(target.id)[1] ?? 0
      const boxHeight = Math.max(boxMinHeight, contentHeight);

      const labelRect = dg.rectangle(boxWidth, labelTextSize[1] + 2 * padding[0]);
      const label = dg.diagram_combine(labelRect, labelText);
      const box = dg.rectangle(boxWidth, boxHeight);
      return dg.distribute_vertical_and_align([label, box])
    });
      
    const alignedBoxDiagram = dg.distribute_variable_row(
      boxDiagrams, totalMaxWidth, EM_DND_ELEMENT_PADDING, EM_DND_ELEMENT_PADDING,
      "top", "center"
    );
    // configure padding
    const diagram = generatePaddedDiagram(dg, alignedBoxDiagram, element.diagramPadding);
    const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
      dg, homePosition, homeConfig,
      groupedOptionDiagrams, diagram, 
      homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager, 0.5);
    const homeGroupBg = !isVisibleHome ? [] : 
      generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
    
    const idToDiagramMap = new Map<string, DgDiagram>();
    {
      // setup diagram for dnd elements
      // dnd target
      for (let i = 0; i < dndTargets.length; i++) {
        const target = dndTargets[i];
        if (target.label === DND_UNUSED_ID) continue;
        const targetBox = alignedBoxDiagram.children[i].children[1];
        let block = targetBox.fill(targetBgColor).append_tags(TAG_DND_TARGET);
        block = dg.style.applyStyleProfiles(block, styleProfiles);
        if (elementColors[target.id]) block = block.fill(elementColors[target.id]);
        if (element.styleConfig.isUseCustomBorderColors){
          block = block.stroke(element.styleConfig.commonTargetBorderColor);
          if (borderColors[target.id]) block = block.stroke(borderColors[target.id]);
        }
        idToDiagramMap.set(target.id, block);
      }
      // dnd home
      for (let i in homeDiagrams){
        const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
        const homeId = homeIdList[i];
        idToDiagramMap.set(homeId, homeDiagram);
      }
    }
    
    return {
      staticDiagram: dg.diagram_combine(diagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap
    }
  }
  
  const {staticDiagram, idToDiagramMap} = getDiagramUpdate();
  // dnd draggable
  for (let i in options){
    const optionDiagram = optionDiagrams[i];
    idToDiagramMap.set(options[i].id, optionDiagram);
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, getOptionLabel(option));
      }
    }
  }
  
  if (element.dndIsFilledMode == undefined){ // backward compatibility
    element.dndIsFilledMode = 'auto';
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable);
  }
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList, 
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram,
      idToDiagramMap,
    },
    getDiagramUpdate,
    dndElementConfig: {
      targetMaxCapacity: Infinity,
      globalTargetConfig: {
        type: "flex-row",
        horizontal_alignment: "center",
        padding: padding[0],
      }
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    config: {
      isAllowGrouping: true,
      isUsingReusableDraggable,
      dndIsFilledMode: element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode,
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}
