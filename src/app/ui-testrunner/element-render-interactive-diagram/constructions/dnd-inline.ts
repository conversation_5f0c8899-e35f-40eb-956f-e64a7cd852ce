/**
WARNING: THIS FILE IS DEPRECATED
THIS IMPLEMENTATION IS DEPRECATED, BUT STILL KEPT FOR BACKWARD COMPATIBILITY
USE `dnd-inline-2.ts` INSTEAD
*/

import { IDgIntConstructionElement, DgIntConstructionType } from "../common";
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID, generateIdOnDnDTargets, 
  LAYOUT_HOME_POSITION, unusedId, HOME_CONFIG_LAYOUT,
  isUnusedId, AnswerGroup, resolveDnDTargetOptions, resolveDnDTargetLabelChange, generateSourceZone,
  IDgIntHomeConfig, IDgIntElementDnDAnswerSet,
  regenerateIdOnDnDTargets, resolveDnDHomeConfig, resolveDnDAltAnswers,
  generateDefaultAnswerSet, generateHomeTargetDiagrams, groupByHome,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  generateHomeTargetDiagramsWithLabel,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  getAllOptionIdsFromDnDTargets,
  ReusableDnDStateManager,
  DnDStateManager,
  isConfigIndividualHome,
  appendHomeSuffix,
  DND_OPTION_TEXT_MODE,
  IDgIntStyleConfig,
  resolveDnDStyleConfig,
  DND_STYLE_MODE,
  generateElementColorsObject,
  isHomeId,
  getDnDElementBaseId,
  assembleDraggableBlock,
  DND_ISFILLED_MODE,
  generateHomeGroupBg,
  generateVoiceoverDataMap
} from "./dnd-common";
import { getPxFontSize, flattenArray, formattedStringToBBCode, getFontFamily, multilineTextWithSize, formattedStringToBBCodeWithTarget, IDgIntScoringParam, EX_TO_EM, formatBBCodeTaggedPlaceholder, CONTENT_JUSTIFY } from "./common";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { tex2dgMathExpression } from "../mathjax";
import { DgIntCallbacks } from "../renderer";
import { generateDefaultElement } from "src/app/ui-item-maker/item-set-editor/models";
import { ElementType, IContentElement } from "../../models";
import { IContentElementAudio } from "../../element-render-audio/model";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;
const EM_MAX_LINE_WIDTH = 40;

interface IDgIntElementDnDTargetWithGroup extends IDgIntElementDnDTarget {
    group: number;
}

export enum DND_INLINE_CONTENT_TYPE {
  TEXT = 'TEXT',
  TARGET = 'TARGET',
  MATH = 'MATH',
}
export interface IDgIntDnDInlineContentElement extends IContentElement {
  type: DND_INLINE_CONTENT_TYPE;
  value: string;
}

export interface IDgIntElementDnDInline extends IDgIntConstructionElement, IContentElementAudio {
  constructionType: DgIntConstructionType.DND_INLINE;
  content: string;
  contentArray?: IDgIntDnDInlineContentElement[];
  styleConfig: IDgIntStyleConfig;
  dndTargets: IDgIntElementDnDTargetWithGroup[];
  homeConfig: IDgIntHomeConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  config: {
    isUseArrayEditor?: boolean;
    isSyncHomeGroupAndAnswerGroup: boolean;
    isAllowMultipleAnswer: boolean;
    isUsingReusableDraggable: boolean;
    homePosition: LAYOUT_HOME_POSITION,
    maxHomeWidth: number,
    padding : [number, number];
    draggableBgColor: string;
    targetBgColor: string;
    isUsingAnswerGroup: boolean,
    isUniformLineSpacing: boolean,
    contentJustify?: CONTENT_JUSTIFY,
  }
  //
  ordering? : Array<number[]>; // deprecated
}

export function generateDefaultDgIntElementDnDInline(): IDgIntElementDnDInline {
  return {
    constructionType: DgIntConstructionType.DND_INLINE,
    ...generateDefaultElement(ElementType.TEXT),
    // content: 'x = [short1] + 2\ny = 3 + [short2]\n \nf(x) = [long1]\ng(x) = [long2]',
    content: 'x = [target1] + 2\ny = 3 + [target2]',
    contentArray: [
      {
        ...generateDefaultElement(ElementType.TEXT),
        type: DND_INLINE_CONTENT_TYPE.TEXT,
        value: 'x = '
      },
      {
        ...generateDefaultElement(ElementType.DND_TARGET),
        type: DND_INLINE_CONTENT_TYPE.TARGET,
        value: 'target1'
      },
      {
        ...generateDefaultElement(ElementType.TEXT),
        type: DND_INLINE_CONTENT_TYPE.TEXT,
        value: ' + 2\ny = 3 + '
      },
      {
        ...generateDefaultElement(ElementType.DND_TARGET),
        type: DND_INLINE_CONTENT_TYPE.TARGET,
        value: 'target2'
      }
    ],
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    dndTargets: [
      { 
        label : 'target1', 
        group : 1, 
        content: [{...generateDefaultElement(ElementType.TEXT), value : 'a'}],
        ...generateDefaultElement(ElementType.TEXT),
      },
      { 
        label : 'target2', 
        group : 1, 
        content: [{...generateDefaultElement(ElementType.TEXT), value : 'b'}],
        ...generateDefaultElement(ElementType.TEXT),
      },
      // { label : 'long1', group : 2, content: [{value : 'x+1'}] },
      // { label : 'long2', group : 2, content: [{value : 'x+2'}] },
      {
        label : DND_UNUSED_ID,
        group : 1,
        content: [{...generateDefaultElement(ElementType.TEXT), value: 'c'}, {...generateDefaultElement(ElementType.TEXT), value:'d'}],
        ...generateDefaultElement(ElementType.TEXT),
      },
      // {
      //   label : DND_UNUSED_ID,
      //   group : 2,
      //   content: [{value: 'x+3'}]
      // }
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      isUseArrayEditor: true,
      isSyncHomeGroupAndAnswerGroup: true,
      isAllowMultipleAnswer: false,
      isUsingReusableDraggable: false,
      padding : [0.05, 0.75],
      maxHomeWidth : 200,
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
      homePosition: LAYOUT_HOME_POSITION.BOTTOM,
      isUsingAnswerGroup: false,
      isUniformLineSpacing: false,
    }
  }
}

function contentArrayToContent(contentArray: IDgIntDnDInlineContentElement[]): string {
  let str = '';
  for (let content of contentArray){
    switch (content.type){
      case DND_INLINE_CONTENT_TYPE.TEXT: {
        str += content.value;
      } break;
      case DND_INLINE_CONTENT_TYPE.TARGET: {
        str += `[${content.value}]`;
      } break;
      case DND_INLINE_CONTENT_TYPE.MATH: {
        str += `\\{${content.value}\\}`;
      } break;
    }
  }
  return str;
}

export function ensureDgIntElementStructureDnDInline(element : IDgIntElementDnDInline) : IDgIntElementDnDInline {
  if (!element.elementType) {
    element = {...generateDefaultElement(ElementType.TEXT), ...element};
  }
  if (element.contentArray) {
    element.contentArray.forEach((content, i) => {
      if (!content.elementType) {
        element.contentArray[i] = {...generateDefaultElement(content.type), ...content};
      }
    });
  }

  element.dndTargets.forEach((target, i) => {
    target.content.forEach((option, j) => {
      if (!option.elementType) {
        element.dndTargets[i].content[j] = {...generateDefaultElement(option.textMode == DND_OPTION_TEXT_MODE.MATH ? ElementType.MATH : ElementType.TEXT), ...option};
      }
    });
    if (!target.elementType) {
      element.dndTargets[i] = {...generateDefaultElement(ElementType.TEXT), ...target};
    }
  });
  return element;
}

export function resolveDnDInlineTargets(element : IDgIntElementDnDInline) : void {
  if (element.config.isUseArrayEditor){
    if (element.contentArray == undefined) element.contentArray = [];
    element.content = contentArrayToContent(element.contentArray);
  }
  
  const regex = /\[(.*?)\]/g;
  const match = element.content.match(regex);
  let tags = match?.map(m => m.slice(1,-1)) ?? [];
  const targetNames = tags;
  
  if (element.homeConfig == undefined){
    element.homeConfig = generateDnDHomeConfigFromGroupedOrdering(element.dndTargets, element.ordering);
    element.config.isSyncHomeGroupAndAnswerGroup = true;
  }
  if (element.altAnswers == undefined) element.altAnswers = [];
  
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames) as IDgIntElementDnDTargetWithGroup[];
  element.dndTargets = resolveGroupedDnDTargetOptions(element.dndTargets, targetNames);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  if (element.config.isSyncHomeGroupAndAnswerGroup && element.homeConfig.isGroupHome) 
    element.homeConfig = syncDnDHomeConfig(element.dndTargets, element.homeConfig);
}

export function renderDgIntDndInline(
  element : IDgIntElementDnDInline, svgElement : SVGSVGElement,
  dg: DgLib, int: DgInt, _imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  let MathJax = window['MathJax'];
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  let dndTargets = element.dndTargets;
  if (dndTargets.length > 0 && dndTargets[0].id == undefined){
    dndTargets = cloneDeep(element.dndTargets) as typeof element.dndTargets;
    generateIdOnDnDTargets(dndTargets);
  }
  let homeConfig = element.homeConfig;
  if (homeConfig == undefined) {
    homeConfig = generateDnDHomeConfigFromGroupedOrdering(dndTargets, element.ordering);
  }
  //
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const dndTargetsLabelMap = new Map<string, IDgIntElementDnDTargetWithGroup>();
  for (const target of dndTargets) {
    dndTargetsLabelMap.set(target.label, target);
  }
  let idToGroupMap = new Map<string, number>();
  for (let target of dndTargets){
    const group = target.group ?? 1;
    idToGroupMap.set(target.id, group);
    for (let option in target.content){
      idToGroupMap.set(target.content[option].id, target.group ?? 1);
    }
  }
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const indexToGroupMap = new Map(options.map((option,i) => [i, idToGroupMap.get(option.id)]));
  
  let answerSets = [generateDefaultAnswerSet(dndTargets)];
  if (element.config.isAllowMultipleAnswer){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  const pxFontSize = getPxFontSize(svgElement);
  const fontFamily = getFontFamily(svgElement);
  const padding = element.config.padding ?? [0.05, 0.75];
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const homeMaxWidth = (element.config.maxHomeWidth ?? 150) * 20/100;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const isUniformLineSpacing = element.config.isUniformLineSpacing;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const contentJustify = element.config.contentJustify ?? CONTENT_JUSTIFY.CENTER;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, homeConfig)
  
  let emLineHeight = EM_LINE_HEIGHT;
  
  const multilineText = (
    s : string,  justification: CONTENT_JUSTIFY = CONTENT_JUSTIFY.CENTER,
    isBBCode = false, emLineHeight = EM_LINE_HEIGHT, 
    idToSizeMap?: Map<string, [number,number]>
  ) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(
      bbcode, dg, pxFontSize, fontFamily, emLineHeight, EM_MAX_LINE_WIDTH, justification, idToSizeMap
    );
  }
  const mathExpression = (s : string) => {
    if (MathJax){
      const mathExpressionData = tex2dgMathExpression(MathJax, s, false);
      //TODO: calculate exToEm properly
      const widthEm = mathExpressionData.widthEx * EX_TO_EM;
      const heightEm = mathExpressionData.heightEx * EX_TO_EM;
      const dataURL = mathExpressionData.dataURL;
      return dg.image(dataURL, widthEm, heightEm);
    } else {
      return multilineText(s);
    }
  }
  
  const optionInnerDiagrams : DgDiagram[] = options.map((s) => {
    switch (s.textMode){
      case DND_OPTION_TEXT_MODE.MATH: return mathExpression(s.value);
      case DND_OPTION_TEXT_MODE.TEXT: 
      default: return multilineText(s.value, contentJustify);
    }
  });
  const groupDraggableSizes = calcGroupDraggableSize(optionInnerDiagrams, indexToGroupMap, padding);
  const groupDnDBlock = generateGroupDnDBlock(dg, groupDraggableSizes, styleProfiles);
  
  const optionDiagrams = options.map((o, i) => {
    const id = o.id;
    const group = indexToGroupMap.get(i);
    const innerDiagram = optionInnerDiagrams[i];
    const dndBlock = groupDnDBlock[group];
    const text = innerDiagram.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg = dndBlock.position(text.origin).fill(draggableBgColor).append_tags(TAG_DND_DRAGGABLE);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors) {
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = assembleDraggableBlock(dg, text, bg, padding, contentJustify);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  })
  
  let contentText = element.content;
  let idToSizeMap = new Map<string, [number, number]>();
  
  // collect math `{{...}}`
  const mathRegex = /\\\{(.*?)\\\}/g;
  const mathMatch = contentText.match(mathRegex);
  const mathTex = mathMatch?.map(m => m.slice(2, -2)) ?? [];
  const mathIds = mathTex.map((_, i) => `math${i}`);
  const mathDiagrams = mathTex.map(tex => mathExpression(tex));
  let mathIdToDiagramMap = new Map<string, DgDiagram>();
  for (let i = 0; i < mathIds.length; i++) {
    contentText = contentText.replace(`\\{${mathTex[i]}\\}`, `\\{${mathIds[i]}\\}`);
    mathIdToDiagramMap.set(mathIds[i], mathDiagrams[i]);
    idToSizeMap.set(mathIds[i], dg.geometry.size(mathDiagrams[i]) as [number,number]);
  }
  
  const targetRegex = /\[(.*?)\]/g;
  const match = contentText.match(targetRegex);
  const tags = match?.map(m => m.slice(1, -1)) ?? [];
  const matchlen = match?.length ?? 0;
  // replace "[target]" with "\{target\}"
  for (let i = 0; i < matchlen; i++) {
    contentText = contentText.replace(match[i], `\\{${tags[i]}\\}`);
  }
  
  for (let i = 0; i < matchlen; i++) {
    const targetLabel = tags[i];
    const target = dndTargetsLabelMap.get(targetLabel);
    if (!target) continue;
    const group = target.group ?? 1;
    const dx = groupDraggableSizes[group][0];
    idToSizeMap.set(target.label, [dx, EM_LINE_HEIGHT + 2*padding[0]]);
  }
  
  let bbtext = formattedStringToBBCode(contentText);
  bbtext = formatBBCodeTaggedPlaceholder(bbtext, idToSizeMap);
  
  if (isUniformLineSpacing){
    const maxTaggedSize = Math.max(1,...Array.from(idToSizeMap.values()).map(([_,h]) => h));
    emLineHeight = maxTaggedSize + (EM_LINE_HEIGHT-1);
    // emLineHeight = EM_LINE_HEIGHT + 2*padding[0] + (EM_LINE_HEIGHT-1);
  }
  const mm = multilineText(bbtext, CONTENT_JUSTIFY.LEFT, true, emLineHeight, isUniformLineSpacing ? undefined : idToSizeMap);
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, homeConfig,
    groupedOptionDiagrams, mm, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, _imageManager);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  const targetPositionOrigin = mm.get_anchor('top-left');
  const taggedPositionMap = getTaggedPosition(mm, pxFontSize, fontFamily, targetPositionOrigin, emLineHeight);
  let positionedMathDiagrams: DgDiagram[] = [];
  for (let i = 0; i < mathIds.length; i++) {
    const pos = taggedPositionMap.get(mathIds[i]);
    const vPos = dg.V2(pos.x, pos.y);
    const mathDiagram = mathDiagrams[i];
    positionedMathDiagrams.push(mathDiagram.position(vPos));
  }
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    const dy = -padding[0] - (EM_LINE_HEIGHT-1)/2;
    for (let i = 0; i < dndTargets.length; i++) {
      const target = dndTargets[i];
      const group = target.group ?? 1;
      if (isUnusedId(target.label)) continue;
      const targetPos = taggedPositionMap.get(target.label);
      if (!targetPos) continue;
      const vTargetPos = dg.V2(targetPos.x, targetPos.y);
      let dndBlock = groupDnDBlock[group].fill(targetBgColor);
      if (elementColors[target.id]) dndBlock = dndBlock.fill(elementColors[target.id]);
      if (element.styleConfig.isUseCustomBorderColors) {
        dndBlock = dndBlock.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[target.id]) dndBlock = dndBlock.stroke(borderColors[target.id]);
      }
      let targetDiagram = dndBlock.position(vTargetPos)
        .append_tags(TAG_DND_TARGET);
      targetDiagram = dg.style.applyStyleProfiles(targetDiagram, styleProfiles);
      idToDiagramMap.set(target.id, targetDiagram);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const moveValidationFunction = (draggableId:string, targetId:string) : boolean =>  {
    if (isUnusedId(targetId)) return true;
    const baseDraggableId = getDnDElementBaseId(draggableId);
    const baseTargetId = getDnDElementBaseId(targetId);
    const draggableGroup = idToGroupMap.get(baseDraggableId);
    const targetGroup = idToGroupMap.get(baseTargetId);
    return draggableGroup === targetGroup;
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, option.value);
      }
    }
  }
  
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList, 
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(mm, ...homeDiagrams, labelDiagram, ...positionedMathDiagrams, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: DND_ISFILLED_MODE.FILL_ALL_TARGET
    },
    functions: {
      moveValidation: element.config.isUsingAnswerGroup ? moveValidationFunction : undefined,
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

type TextSpanData = Array<{text:string, style:any}>;
const textMeasureCanvas = document.createElement('canvas');
const textMeasureContext = textMeasureCanvas.getContext('2d');
function getTaggedPosition(
  mm: DgDiagram, pxFontSize: number, fontFamily: string, origin: {x:number, y:number}, emLineHeight: number
) : Map<string, {x:number, y:number}> {
  const textSpanData = mm.children?.[1]?.multilinedata?.content as TextSpanData ?? [];
  let taggedPositionMap = new Map<string, {x:number,y:number}>();
  let x = 0;
  let y = 0;
  let lastDy = 0;
  for (let tspanData of textSpanData) {
    let fontData = `${pxFontSize}px ${fontFamily}`
    if (tspanData?.style?.['font-weight'] == 'bold') fontData = `bold ${fontData}`;
    if (tspanData?.style?.['font-style'] == 'italic') fontData = `italic ${fontData}`;
    textMeasureContext.font = fontData;
    
    if (tspanData.text == '\n') {
      lastDy = parseFloat(tspanData.style?.dy);
      y += lastDy;
      x = 0;
    } else {
      if (tspanData?.style?.tag){
        const dx = parseFloat(tspanData.style?.dx) ?? 0;
        taggedPositionMap.set(tspanData.style.tag, {x: origin.x + x + dx/2, y: origin.y - y - emLineHeight/2});
      }
      let tspanWidth = textMeasureContext.measureText(tspanData.text).width / pxFontSize;
      if (tspanData?.style?.dx) tspanWidth += parseFloat(tspanData.style.dx);
      x += tspanWidth;
    }
  }
  return taggedPositionMap;
}

function groupByIndexToGroupMap<T>(arr: T[], indexToGroupMap: Map<number, number>) : {[group:number] : T[]} {
  let result = {};
  for (let i = 0; i < arr.length; i++){
    const group = indexToGroupMap.get(i) ?? 1;
    if (result[group] == undefined) result[group] = [];
    result[group].push(arr[i]);
  }
  return result;
}

function generateGroupDnDBlock(
  dg: DgLib, groupedDraggableSize: { [group: number]: [number, number] },
  styleProfiles: Array<DgStyleProfile>
): { [group: number]: DgDiagram } {
  let result = {};
  for (let group in groupedDraggableSize){
    const [width, height] = groupedDraggableSize[group];
    const dndBlock = generateDnDBlock(dg, width, height, styleProfiles)
    result[group] = dndBlock;
  }
  return result;
}

function calcGroupDraggableSize(
  innerDiagrams: DgDiagram[], indexToGroupMap: Map<number, number>,
  padding: [number, number]
): { [group: number]: [number, number] } {
  const groupedInnerDiagrams = groupByIndexToGroupMap(innerDiagrams, indexToGroupMap);
  let result = {};
  for (let group in groupedInnerDiagrams){
    const innerDiagrams = groupedInnerDiagrams[group];
    const optionsBB = innerDiagrams.map(o => o.bounding_box());
    const choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
    const choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
    result[group] = [choicesMaxwidth, choicesMaxheight];
  }
  return result;
}

export function groupDnDTarget(targets : IDgIntElementDnDTargetWithGroup[]) : AnswerGroup<IDgIntElementDnDTargetWithGroup[]> {
  const groupArray = [];
  for (let target of targets) {
    const group = target.group ?? 1;
    if (!groupArray[group]) groupArray[group] = [];
    groupArray[group].push(target);
  }
  return groupArray;
}

// backward compatibility
export function generateDnDHomeConfigFromGroupedOrdering(dndTargets: IDgIntElementDnDTargetWithGroup[], groupOrdering: AnswerGroup<number[]>) 
  : IDgIntHomeConfig 
{
  try {
    if (dndTargets[0].id == undefined) generateIdOnDnDTargets(dndTargets);
    let element = [];
    const groupedTargets = groupDnDTarget(dndTargets);
    for (let g in groupOrdering){
      const targets = groupedTargets[g];
      if (targets == undefined) continue;
      const ordering = groupOrdering[g];
      const options = flattenArray(targets.map(t => t.content));
      const orderingWithId = ordering.map(i => options[i].id);
      element.push(orderingWithId);
    }
    return {
      isGroupHome: true,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element,
    }
  } catch(e){
    return {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    }
  }
}

function syncDnDHomeConfig(dndTargets: IDgIntElementDnDTargetWithGroup[], homeConfig: IDgIntHomeConfig
) : IDgIntHomeConfig {
  let idToGroupMap = new Map<string, number>();
  for (let target of dndTargets){
    idToGroupMap.set(target.id, target.group ?? 1);
    for (let option in target.content){
      idToGroupMap.set(target.content[option].id, target.group ?? 1);
    }
  }
  const groupElement = {};
  for (let row of homeConfig.element){
    for (let id of row){
      const group = idToGroupMap.get(id);
      if (groupElement[group] == undefined) groupElement[group] = [];
      groupElement[group].push(id);
    }
  }
  const element = [];
  for (let group in groupElement) element.push(groupElement[group]);
  return { isGroupHome: homeConfig.isGroupHome, layout: homeConfig.layout, element: element };
}


export function resolveGroupedDnDTargetOptions(targets : IDgIntElementDnDTargetWithGroup[], 
  validTargetNames : string[]
) : IDgIntElementDnDTargetWithGroup[] {
  
  let groups = new Set<number>();
  for (let target of targets) groups.add(target.group ?? 1);
  const groupedTargets = groupDnDTarget(targets);
  const targetLabels = targets.map(target => target.label);
  
  let resolvedUnorderedTargets : IDgIntElementDnDTargetWithGroup[] = [];
  for (let g in groupedTargets){
    const gTargets = groupedTargets[g];
    // rename the unused target
    for (let target of gTargets){
      if (isUnusedId(target.label)) target.label = DND_UNUSED_ID;
    }
    
    let gValidTargetNames : string[] = [];
    if (g == "1"){
      // new unknown target will be added to the first group
      const gValidTargetNames0 = gTargets.map(target => target.label).filter(name => validTargetNames.includes(name));
      const gNewTargetNames = validTargetNames.filter(name => !targetLabels.includes(name));
      gValidTargetNames = gValidTargetNames0.concat(gNewTargetNames);
    } else {
      gValidTargetNames = gTargets.map(target => target.label).filter(name => validTargetNames.includes(name));
    }
    
    const gResolvedTargets = resolveDnDTargetOptions(gTargets, gValidTargetNames);
    const gResolvedTargetsWithGroup : IDgIntElementDnDTargetWithGroup[] = gResolvedTargets.map(target => {
      (target as any).group = +g;
      return target as any;
    });
    
    for (let target of gResolvedTargetsWithGroup){
      if (isUnusedId(target.label)) target.label = unusedId(+g);
    }
    resolvedUnorderedTargets = resolvedUnorderedTargets.concat(gResolvedTargetsWithGroup);
  }
  
  
  // do ordering
  let targetMap = new Map<string, IDgIntElementDnDTargetWithGroup>();
  for (let target of resolvedUnorderedTargets) targetMap.set(target.label, target);
  let resolvedTargets : IDgIntElementDnDTargetWithGroup[] = [];
  for (let name of validTargetNames){
    const target = targetMap.get(name);
    if (target) resolvedTargets.push(target);
  }
  for (let g in groupedTargets){
    const label = unusedId(+g);
    const unusedTarget = targetMap.get(label);
    if (unusedTarget) resolvedTargets.push(unusedTarget);
  }
  
  // check for empty group
  let groupWithContent = new Set<number>();
  for (let target of resolvedTargets){
    const g = target.group ?? 1;
    if (target.content.length > 0) groupWithContent.add(g);
  }
  resolvedTargets = resolvedTargets.filter(target => groupWithContent.has(target.group));
  return resolvedTargets;
}

export function contentToContentArray(content: string): IDgIntDnDInlineContentElement[] {
  const contentArray: IDgIntDnDInlineContentElement[] = [];
  let currentIndex = 0;

  while (currentIndex < content.length) {
    if (content[currentIndex] === '[') {
      // Finding the closing bracket for a TARGET type
      const closeIndex = content.indexOf(']', currentIndex);
      if (closeIndex !== -1) {
        contentArray.push({
          ...generateDefaultElement(ElementType.DND_TARGET),
          type: DND_INLINE_CONTENT_TYPE.TARGET,
          value: content.substring(currentIndex + 1, closeIndex)
        });
        currentIndex = closeIndex + 1;
      }
    } else if (content.startsWith('\\{', currentIndex)) {
      // Finding the closing curly brace for a MATH type
      const closeIndex = content.indexOf('\\}', currentIndex);
      if (closeIndex !== -1) {
        contentArray.push({
          ...generateDefaultElement(ElementType.MATH),
          type: DND_INLINE_CONTENT_TYPE.MATH,
          value: content.substring(currentIndex + 2, closeIndex)
        });
        currentIndex = closeIndex + 2;
      }
    } else {
      // Handling TEXT type
      let nextSpecialIndex = content.length;
      const nextTargetIndex = content.indexOf('[', currentIndex);
      const nextMathIndex = content.indexOf('\\{', currentIndex);

      if (nextTargetIndex !== -1) {
        nextSpecialIndex = nextTargetIndex;
      }
      if (nextMathIndex !== -1 && nextMathIndex < nextSpecialIndex) {
        nextSpecialIndex = nextMathIndex;
      }

      contentArray.push({
        ...generateDefaultElement(ElementType.TEXT),
        type: DND_INLINE_CONTENT_TYPE.TEXT,
        value: content.substring(currentIndex, nextSpecialIndex)
      });
      currentIndex = nextSpecialIndex;
    }
  }

  return contentArray;
}
