import { IContentElementInteractiveDiagram } from './model'
import { DgIntConstructionType, IDgIntConstructionElement } from './common'

import { ensureDgIntElementStructureDnDTable, generateDefaultDgIntElementDnDTable, IDgIntElementDnDTable } from './constructions/dnd-table'
import { ensureDgIntElementStructureDnDInline, generateDefaultDgIntElementDnDInline, IDgIntElementDnDInline } from './constructions/dnd-inline'
import { ensureDgIntElementStructureDnDInline2, generateDefaultDgIntElementDnDInline2, IDgIntElementDnDInline2 } from './constructions/dnd-inline-2'
import { ensureDgIntElementStructureDnDGroup, generateDefaultDgIntElementDnDGroup, IDgIntElementDnDGroup } from './constructions/dnd-group'
import { ensureDgIntElementStructureDnDSorting, generateDefaultDgIntElementDnDSorting, IDgIntElementDnDSorting } from './constructions/dnd-sorting'
import { ensureDgIntElementStructureDnDTally, generateDefaultDgIntElementDnDTally, IDgIntElementDnDTally } from './constructions/dnd-tally'
import { ensureDgIntElementStructureDnDNumberline, generateDefaultDgIntElementDnDNumberline, IDgIntElementDnDNumberline } from './constructions/dnd-numberline'
import { ensureDgIntElementStructureDnDFreeform, generateDefaultDgIntElementDnDFreeform, IDgIntElementDnDFreeform } from './constructions/dnd-freeform'
import { ensureDgIntElementStructureDnDVenn, generateDefaultDgIntElementDnDVenn, IDgIntElementDnDVenn } from './constructions/dnd-venn'
import { ensureDgIntElementStructureDnDClozeMath, generateDefaultDgIntElementDnDClozeMath, IDgIntElementDnDClozeMath } from './constructions/dnd-cloze-math'
import { ensureDgIntElementStructureMcqHotspot, generateDefaultDgIntElementMcqHotspot, IDgIntElementMcqHotspot } from './constructions/mcq-hotspot'
import { ElementType } from '../models'
import { getCurrentVersion } from 'src/app/ui-item-maker/services/util'

export function generateDefaultElementInteractiveDiagram(elementType : string) : IContentElementInteractiveDiagram {
  const defaultConstructionType = DgIntConstructionType.DND_TABLE;
  return {
    elementType,
    styleProfiles: [],
    constructionType: defaultConstructionType,
    constructionElement: generateDefaultDgIntElement(defaultConstructionType),
    block_version_id: getCurrentVersion(ElementType.INTERACTIVE_DIAGRAM)
  }
}

export function generateDefaultDgIntElement(constructionType : DgIntConstructionType)  : IDgIntConstructionElement {
  switch (constructionType) {
    case DgIntConstructionType.DND_TABLE: return generateDefaultDgIntElementDnDTable();
    case DgIntConstructionType.DND_INLINE: return generateDefaultDgIntElementDnDInline();
    case DgIntConstructionType.DND_INLINE_2: return generateDefaultDgIntElementDnDInline2();
    case DgIntConstructionType.DND_CLOZE_MATH: return generateDefaultDgIntElementDnDClozeMath();
    case DgIntConstructionType.DND_GROUP: return generateDefaultDgIntElementDnDGroup();
    case DgIntConstructionType.DND_SORTING: return generateDefaultDgIntElementDnDSorting();
    case DgIntConstructionType.DND_TALLY: return generateDefaultDgIntElementDnDTally();
    case DgIntConstructionType.DND_NUMBERLINE: return generateDefaultDgIntElementDnDNumberline();
    case DgIntConstructionType.DND_FREEFORM: return generateDefaultDgIntElementDnDFreeform();
    case DgIntConstructionType.DND_VENN: return generateDefaultDgIntElementDnDVenn();
    case DgIntConstructionType.MCQ_HOTSPOT: return generateDefaultDgIntElementMcqHotspot();
    default: throw new Error(`Unknown interactive diagram construction type: ${constructionType}`);
  }
}

// ensure editing tool elements in constructionElement has according IContentElement structure
export function ensureDgIntElementStructure(element : IContentElementInteractiveDiagram)  : IDgIntConstructionElement {
  switch (element.constructionType) {
    case DgIntConstructionType.DND_TABLE: return ensureDgIntElementStructureDnDTable(element.constructionElement as IDgIntElementDnDTable);
    case DgIntConstructionType.DND_INLINE: return ensureDgIntElementStructureDnDInline(element.constructionElement as IDgIntElementDnDInline);
    case DgIntConstructionType.DND_INLINE_2: return ensureDgIntElementStructureDnDInline2(element.constructionElement as IDgIntElementDnDInline2);
    case DgIntConstructionType.DND_CLOZE_MATH: return ensureDgIntElementStructureDnDClozeMath(element.constructionElement as IDgIntElementDnDClozeMath);
    case DgIntConstructionType.DND_GROUP: return ensureDgIntElementStructureDnDGroup(element.constructionElement as IDgIntElementDnDGroup);
    case DgIntConstructionType.DND_SORTING: return ensureDgIntElementStructureDnDSorting(element.constructionElement);
    case DgIntConstructionType.DND_TALLY: return ensureDgIntElementStructureDnDTally(element.constructionElement as IDgIntElementDnDTally);
    case DgIntConstructionType.DND_NUMBERLINE: return ensureDgIntElementStructureDnDNumberline(element.constructionElement as IDgIntElementDnDNumberline);
    case DgIntConstructionType.DND_FREEFORM: return ensureDgIntElementStructureDnDFreeform(element.constructionElement as IDgIntElementDnDFreeform);
    case DgIntConstructionType.DND_VENN: return ensureDgIntElementStructureDnDVenn(element.constructionElement);
    case DgIntConstructionType.MCQ_HOTSPOT: return ensureDgIntElementStructureMcqHotspot(element.constructionElement as IDgIntElementMcqHotspot);
    default: return element.constructionElement;
  }
}
