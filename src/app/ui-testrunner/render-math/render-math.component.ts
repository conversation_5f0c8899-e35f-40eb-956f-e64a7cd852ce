import { Component, OnInit, ViewChild, ElementRef, Input, OnChanges, OnDestroy, SimpleChanges } from '@angular/core';
import katex from 'katex';
import { LangService } from '../../core/lang.service';
import { StyleprofileService, processText, transformThousandsSeparator, transformFrenchDecimal, latexToSpeakableText } from 'src/app/core/styleprofile.service';
import { HighlighterService } from '../highlighter.service';
import * as Sre from 'speech-rule-engine';
import 'speech-rule-engine/lib/mathmaps/fr.json';
@Component({
  selector: 'render-math',
  templateUrl: './render-math.component.html',
  styleUrls: ['./render-math.component.scss']
})
export class RenderMathComponent implements OnInit, OnDestroy, OnChanges {

  @Input() obj:any;
  @Input() prop:string;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() raw:string;
  @ViewChild('mathField', { static: true }) mathFieldRef: ElementRef;

  mathField:HTMLSpanElement;
  currentLatex;
  intervalTracker;
  isRendered:boolean;
  styleProfileChangeSub;

  constructor(
    private lang:LangService,
    private profile:StyleprofileService,
    public highlighter: HighlighterService
  ) { }

  ngOnInit() {
    this.styleProfileChangeSub = this.profile.getStyleProfileChanges().subscribe( hasStyleProfile => {
      if(hasStyleProfile) {
        this.updateLatex();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.updateLatex();
  }

  ngOnDestroy() {
    this.clearListener();  

    if(this.styleProfileChangeSub) {
      this.styleProfileChangeSub.unsubscribe();
    }
  }

  clearListener(){
    if (this.intervalTracker){
      clearInterval(this.intervalTracker);
    }
  }

  ngAfterViewInit() {
    // console.log('ngAfterViewInit')
    this.mathField = this.mathFieldRef.nativeElement;
    if(this.profile.getStyleProfile()){
      this.updateLatex();
    }
    // if (!this.raw){
    this.intervalTracker = setInterval(this.updateLatex.bind(this), 2000);
    // }
  }

  sanitizeLatex(latex:string){
    return processText(latex, this.profile.getStyleProfile()[this.lang.c()].renderStyling.math.transforms);
  }

  async renderLatex(latex: string) {
    latex = this.sanitizeLatex(latex);
    let speakableText: string;

    katex.render(latex, this.mathField, {
      throwOnError: false
    });

    if (this.obj && this.obj.altText) {
      speakableText = this.obj.altText;
    } else {
      let mathmlString: string;

      try {
        mathmlString = katex.renderToString(latex, {
          output: 'mathml',
          throwOnError: true,
        });
      } catch (e) {
        console.error("KaTeX to MathML conversion failed", e);
        return;
      }
      
      // Extract only the <math> element from KaTeX's wrapper output
      const mathMatch = mathmlString.match(/<math[\s\S]*?<\/math>/);
      const pureMathML = mathMatch ? mathMatch[0] : mathmlString;
      
      const currentLocale = this.lang.c();

      const sreConfig = {
        type: 'mathml',
        locale: currentLocale,
        domain: 'math',
        style: 'default',
        modality: 'speech',
        delay: true,
        custom: async (loc) => {
          if (loc === 'fr') {
            // The JSON import returns an object; stringify it for SRE
            const frJson = await import('speech-rule-engine/lib/mathmaps/fr.json');
            return JSON.stringify(frJson.default ?? frJson);
          }
          return null;
        }
      };
      
      Sre.setupEngine(sreConfig);
      await Sre.engineReady();
      
      speakableText = Sre.toSpeech(pureMathML);
      
      let processes =  this.profile.getStyleProfile()[this.lang.c()].voiceScript.math.filter(script => script.slug == "APPLY_REPLACEMENT");      
      speakableText = processText(speakableText, processes);
    }
    
    this.mathField.setAttribute('aria-label', speakableText);
    this.mathField.setAttribute('role', 'math');
  }

  updateLatex(isForcedChange:boolean=false){
    // if (this.isRendered){
    //   this.clearListener(); 
    //   return 
    // }
    // console.log('updateLatex', this.raw, this.mathField)
    if (this.raw && this.mathField){
      this.renderLatex(this.raw);
      this.isRendered = true;
      return;
    }
    else if (this.mathField && this.obj && this.obj[this.prop]){
      if ( isForcedChange || (this.obj[this.prop] !== this.currentLatex) ){
        this.currentLatex = this.obj[this.prop];
        // this.mathField.innerHTML = '$$'+this.currentLatex+'$$';
        // this.MathLive.renderMathInElement(this.mathField);
        this.renderLatex(this.currentLatex);
        this.isRendered = true;
      }
  
    }
  }

  static updateFontStyle(mathElement: HTMLElement, fontStyle: {[key: string]: string}) {
    const katexEl = mathElement.getElementsByClassName('katex')[0];
    if (katexEl == undefined) return;
    const mrelElements = mathElement.getElementsByClassName('mrel');
    const mathnormalEl = mathElement.getElementsByClassName('mathnormal');
    
    let styles = [];
    for (const [key, value] of Object.entries(fontStyle)) {
      styles.push(`${key}: ${value}`) 
    }
    katexEl.setAttribute("style", styles.join('; '));
    // Relational symbols like !=, >= etc. have font size Katex_main
    Array.from(mrelElements).forEach(mrel => mrel.setAttribute("style", "font-family: Katex_main"))
    
    if (fontStyle['font-family']){
      Array.from(mathnormalEl).forEach(el => (el as HTMLElement).style.fontFamily = fontStyle['font-family'])
    }
  }

}

