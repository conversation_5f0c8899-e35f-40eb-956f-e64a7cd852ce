import { Component, Input, Output, OnChanges, OnInit, SimpleChanges, EventEmitter, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';
import { LangService } from 'src/app/core/lang.service';
import { EStyleProfile, StyleprofileService } from 'src/app/core/styleprofile.service';
import { processItemConfigLangLink } from 'src/app/ui-scorer/panel-score-content/util/lang-link';
import { IQuestionConfig } from 'src/app/ui-testrunner/models';
import { getQuestionTitleFromMap, getQuestionTitles } from 'src/app/ui-testrunner/test-runner/util/question-titles';
import { AuthService, getFrontendDomain } from '../../api/auth.service';
import { STU_EX_OVERRIDES } from '../panel-reported-issues/model/override-categories';
import { REPORTING_SUBJECTS } from '../panel-withhold-exempt-modal/panel-withhold-exempt-modal.component';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { ScanInfoService } from '../scan-info.service';
import { ElementType} from "../../ui-testrunner/models"
import { InputFormat } from "../../ui-testrunner/element-render-input/model"
import { RoutesService } from '../../api/routes.service';
import { MyCtrlOrgService } from '../my-ctrl-org.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { IsrReportsComponent } from 'src/app/ui-partial/isr-reports/isr-reports.component';
import { ASSESSMENT } from '../../ui-teacher/data/types';
import { Subscription } from 'rxjs';
import {IStudentLookupResponseData, ITaqrScales, ITaqrScaleScores, IScoresLeader, IScoresScorer, ITaqrRecord} from './types';
import * as moment from 'moment-timezone';
import { renderOptionCaption } from '../../ui-scorer/panel-scoring/util/score-option-caption';
import { IScoreOption } from '../../ui-scorer/panel-scoring/types/score-option';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { ClassFilterId } from 'src/app/ui-schooladmin/my-school.service';
import { IIndividualOverrideForm } from './model/types';
import { OVERRIDE_TYPES } from '../panel-withhold-exempt-modal/panel-withhold-exempt-modal.component';
import { OnlineOrPaperService } from '../../ui-testrunner/online-or-paper.service';
import { AssessmentCode } from 'src/app/ui-schooladmin/data/mappings/eqao-g9';


interface IRouteParam {
  [key: string]: any
}

export enum Format {
  SCAN = "SCAN",
  TYPED = "TYPED"
}

enum Assessment_Format_State {
  ONLINE = "Online",
  PAPER = "Paper",
  NOT_ACCESSED = "Not Accessed"
}

export enum Modal {
  UPLOAD_SCAN = 'upload_scan',
  SCAN_UPLOAD = "SCAN_UPLOAD",
  VIEW_OSSLT_REPORT = "VIEW_OSSLT_REPORT",
  WITHHOLD = 'WITHHOLD',
  EXEMPTION_OVERRIDE = 'EXEMPTION_OVERRIDE',
  HUMAN_SCORE_OVERRIDE = 'HUMAN_SCORE_OVERRIDE'
}
export enum RESPONSE_TYPE {
  INPUT_LONGTEXT = 'input-longtext',
}

export enum TEST_WINDOW_TYPE {
  EQAO_G3P = 'EQAO_G3P',
  EQAO_G6J = 'EQAO_G6J',
  EQAO_G9M = 'EQAO_G9M',
  EQAO_G10L = 'EQAO_G10L',
}
export enum CLASS_TYPE {
  PRIMARY = 'PRIMARY',
  JUNIOR = 'JUNIOR'
}  
// enum UploadScanModal {
//   UPLOAD_SCAN = 'upload_scan',
//   SCAN_UPLOAD = "SCAN_UPLOAD",
// }


@Component({
  selector: 'panel-student-lookup',
  templateUrl: './panel-student-lookup.component.html',
  styleUrls: ['./panel-student-lookup.component.scss']
})
export class PanelStudentLookupComponent implements OnInit, OnChanges, OnDestroy {

  @Input() roleContext:any;
  
  @Input() isStudentFocus: boolean;
  @Input() isStudentRestore: boolean;
  @Input() uid: number;
  @Input() ts_id: number;
  @Input() ric_id: number;
  @Output() exceptionsChange = new EventEmitter();
  @Output() studentOverrided = new EventEmitter();

  @ViewChild('scanUploadInput', { static: false }) scanUploadInput: ElementRef<HTMLInputElement>;
  @ViewChild(IsrReportsComponent) isrReportsComponent!: IsrReportsComponent;
  
  isLoading:boolean;
  isSearching:boolean;

  STU_EX_OVERRIDES = STU_EX_OVERRIDES
  REPORTING_SUBJECTS = REPORTING_SUBJECTS
  Format = Format
  isShowRevokedISRs:boolean = false;

  searchForm:any = {};

  selectedStudent:any;
  selectedAttemptForm:any;
  selectedItemResponse:any;
  itemDisplay:IQuestionConfig;
  responseState:any = {};
  loadedAttempts = new Map(); // caching
  
  selectedLongTextItemStr : String = "";
  isLongTextItem : boolean = false;
  opinionEssayEditable = false;
  inputText = null;
  cancelOverrideResponse:boolean = false;

  twiddleStudents:any = {value: true};
  twiddleForm:any = {};
  twiddleResponse:any = {};

  isPJ:boolean = false;
  isOSSLT:boolean = false;
  isG9:boolean = false

  isPjScanPanelVisible:boolean = false;
  isViewAllScans:boolean = false;
  testAttemptScans;
  scanQuestions;
  lastSelectedAsmFormat:any = {};

  pageModal: PageModalController;
  Modal = Modal;
  checkedCheckboxes: { [key: string]: boolean } = {};

  excludeNotStartedAttempt:boolean = true;
  hasSearched:boolean = false;
  styleProfileSub;
  styleProfileMap: Map<number, EStyleProfile> = new Map();
  records:any[] = [];
  gridOptions:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:'OEN', field:'StudentOEN', width:100 }, // checkboxSelection:true, 
      { headerName:'First Name', field:'first_name', width:120 },
      { headerName:'Last Name', field:'last_name', width:150 },
      { headerName:'Assessment', field:'type_slug' },
      // { headerName:'Assessment Component', field:'assessment_name', width:250 },
      // { headerName:'# Screens Accessed', field:'numResponses', width:100 },
      { headerName:'Board Name', field:'sd_name' },
      { headerName:'School Name', field:'s_name' },
      { headerName:'Class', field:'sc_name' },
      { headerName:'Assigned On', field:'created_on', width:100 },
      { headerName:'Removed?', field:'is_revoked', width:100 },
      { headerName:'Removed On', field:'revoked_on' },
      { headerName:'Board Mident', field:'sd_code', width:100 },
      { headerName:'School Mident', field:'s_code', width:100 },
      { headerName:'Access Code', field:'access_code', width:100 },
      { headerName:'Admin. Window', field:'test_window_id', width:100 },
      { headerName:'Lang', field:'lang', width:100 },
      // { headerName:'attempt_id', field:'ta_id', width:100},
      { headerName:'uid', field:'uid', width:100},
      // { headerName:'Form Code', field:'slug' , width:200},
      { headerName:'Sample?', field:'is_sample', width:100},
      { headerName:'Session Closed?', field:'ts_is_closed', width:100},
      { headerName:'Session ID', field:'ts_id', width:100},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  taqrGridOptions:any = {
    columnDefs: [
      { headerName:'ID', field:'taqr_id', width:130, checkboxSelection:true },
      // { headerName:'Assessment', field:'long_name', width:100 },
      // { headerName:'test_form_id', field:'test_form_id'},
      { headerName:'Assessment', field:'type_slug' },
      { headerName:'Item ID', field:'test_question_id', width:100 },
      { headerName:'Label', field:'question_label' },
      { headerName:'section_id', field:'section_id'},
      { headerName:'Score', field:'score', width:100 },
      { headerName:'Weight', field:'weight', width:100 },
      { headerName:'NR', field:'is_nr', width:100 },
      { headerName:'Admin. Window', field:'test_window_id' },
      { headerName:'asmt slug', field:'slug' },
      { headerName:'module_id', field:'module_id', width:100 },
      { headerName:'ta_id', field:'ta_id'},
      { headerName:'updated_on', field:'updated_on'},
      { headerName:'sc_id', field:'sc_id'},
      { headerName:'sc_name', field:'sc_name'},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };


  selecteduploadScanInfo
  uploadScanInfoGridApi
  uploadScanInfoGridOptions:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:this.lang.tra('sa_upload_stu_sca_model_grid_col_upload_date'), field:'upload_date', width:230 , checkboxSelection:true},
      { headerName:this.lang.tra('sa_upload_stu_sca_model_grid_col_comment'), field:'comment', width:200},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  routeQuerySub: Subscription;

  constructor(
    private auth: AuthService,
    private lang: LangService,
    private route:ActivatedRoute,
    private router:Router,
    private profile: StyleprofileService,
    private whitelabel: WhitelabelService,
    private pageModalService: PageModalService,
    private scanInfo: ScanInfoService,
    private routes: RoutesService,
    public myCtrlOrg:MyCtrlOrgService,
    public loginGuard: LoginGuardService,
    public onlineOrPaper:OnlineOrPaperService
  ) { }

  
  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadReportingCategories();
    this.loadAssignees();
    this.routeQuerySub = this.route.queryParams.subscribe(queryParams => {
      this.excludeNotStartedAttempt = queryParams.excludeNotStartedAttempt == 'yes';
    })
    if (this.isStudentRestore){
      if (this.uid && this.ts_id){
        this.loadAutoStudent()
      }
    }
    this.styleProfileSub = this.profile.styleProfileSub.subscribe(loaded => {
      if(loaded && this.selectedTaqr){
        this.selectedItemResponse = this.selectedTaqr;
        this.selectedTaqr = null;
      }
    })
    this.identifyMode()
  }

  ngOnChanges(changes:SimpleChanges){
    if (this.isStudentFocus){
      if (changes.uid || changes.ts_id){
        this.loadAutoStudent()
      }
    }
  }

  ngOnDestroy(): void {
    if(this.styleProfileSub) {
      this.styleProfileSub.unsubscribe();
    }
    if(this.routeQuerySub) {
      this.routeQuerySub.unsubscribe();
    }
  }

  reportingCategories:any[] = [];
  async loadReportingCategories(){
    const {reportIssuesCategories, component_slugs} = await this.auth.apiFind('public/test-ctrl/schools/reported-issues-categories', {
      query: {
        ... this.roleContext
      }
    })
    this.reportingCategories = reportIssuesCategories
  }
  
  availableAssignees:any [];
  assigneeByUid = new Map()
  selectedAssignedUid;
  async loadAssignees(){
    this.availableAssignees = await this.auth.apiFind(this.routes.TEST_CTRL_REPORT_ISSUES_ASSIGNESS)
    for (let assignee of this.availableAssignees){
      this.assigneeByUid.set(assignee.uid, assignee);
    }
  }

  toggleInitReportIssue(studentSession:any){
    if (studentSession.newIssueForm){
      studentSession.newIssueForm = null;
    }
    else {
      studentSession.newIssueForm = {};
    }
  }

  isAutoLoadingStudent:boolean;
  async loadAutoStudent(){
    this.isAutoLoadingStudent = true;
    this.searchForm.uid = this.uid;
    this.searchForm.ts_id = this.ts_id;
    await this.searchStudent();
    const targetStudentSession = this.records[0]
    await this.loadStudentSession(targetStudentSession); // temp;
    this.isAutoLoadingStudent = false;
  }

  schoolLanguage:string;

  async searchStudent(){
    this.isLoading = true;
    this.isSearching = true;
    const {
      studentIdentifier,
      studentFirstName,
      studentLastName,
      schoolForeignId,
      schoolName,
      uid,
      ts_id,
      attemptId,
    } = this.searchForm;
    const attempts = await this.auth.apiFind('public/test-ctrl/schools/student', {
      query: {
        ... this.roleContext,
        studentIdentifier,
        studentFirstName,
        studentLastName,
        schoolForeignId,
        schoolName,
        uid,
        ts_id,
        attemptId,
        excludeNotStartedAttempt: this.excludeNotStartedAttempt
      }
    }).catch(err => {
      switch (err.message) {
        case 'ERR_NO_STUDENT_RECORD_FOUND':
          const errDataString = JSON.stringify(
            err.data.map(({ prop, val }) => ({ [prop]: val }))
          );
          this.loginGuard.quickPopup(`Can't find student with the following info ${errDataString}, please use Student Look Up feature to find the student's correct record.`)
          break
        default: 
          alert(err.message)
          break
      }     
    });
    this.hasSearched = true;
    let attemptSessionRef = new Map();
    this.records = [];
    this.schoolLanguage = attempts?.[0]?.lang ?? 'en';
    for (let attempt of attempts){
      const key = [attempt.ts_id, attempt.uid].join(';');
      if (!attemptSessionRef.has(key)){
        const baseRecord = { ... attempt, attempts:[] }
        this.records.push(baseRecord);
        attemptSessionRef.set(key, baseRecord)
      }
      attemptSessionRef.get(key).attempts.push(attempt)
    }
    this.isSearching = false;
    this.isLoading = false;
  }

  toggleExcludeNotStartedAttempt() {
    if(this.hasSearched) {
      this.searchStudent();
    }
  }

  exportCsv(){
    this.gridOptions.api.exportDataAsCsv();
  }
  exportResponseCsv(){
    this.gridOptions.api.exportDataAsCsv();
  }

  onSelected($event: RowNodeEvent){
    const selectedRows = this.gridOptions.api.getSelectedRows();
    this.loadStudentSession( selectedRows.length ? selectedRows[0] : undefined );
  }

  identifyMode(){
    const selectedTwType = this.myCtrlOrg.selectedWindow.type_slug
    this.isPJ = selectedTwType == TEST_WINDOW_TYPE.EQAO_G3P ||  selectedTwType == TEST_WINDOW_TYPE.EQAO_G6J
    this.isOSSLT = selectedTwType == TEST_WINDOW_TYPE.EQAO_G10L
    this.isG9 = selectedTwType == TEST_WINDOW_TYPE.EQAO_G9M
  }

  selectedAttempt;
  selectedStudentSession;
  async loadStudentSession(studentSession:any){
    this.selectedStudentSession = studentSession;
    this.selectedAttempt = null;
    this.selectedItemId = null;
    this.selectedItemResponse = null;
    this.selectedAttemptForm = null;
    this.trackSelected()
    this.loadStudentReportedIssues(studentSession); // no await
    this.loadStudentExceptions(studentSession); // no await
    if (studentSession){
      this.selectedStudent = studentSession;
      // await this.loadStudentFormData()
    }
  }

  trackSelected(){
    const studentSession = this.selectedStudentSession;
    this.router.navigate( [], {
      relativeTo: this.route,
      queryParams: {
        isStudentRestore: 'yes', 
        studentUid: studentSession.uid, 
        studentTsId: studentSession.ts_id, 
        excludeNotStartedAttempt: this.excludeNotStartedAttempt ? 'yes' : 'no',
      }, 
      queryParamsHandling: 'merge', 
    });
  }

  getAssigneeByUid(uid: number){
    const assignee = this.assigneeByUid.get(uid);
    if (assignee){
      return assignee.assigned_name;
    }
    return `[UID: ${uid}]`
  }

  async pendStudent(){
    const notes = prompt('Pending notes (optional)')
    if(notes === null) { // User clicked "Cancel" button
      return
    }

    const studentSession = this.selectedStudentSession;
    const {createdRecords, newReports, studentRevokedReports} = await this.auth
      .apiCreate('public/test-ctrl/schools/student-exceptions', {
        test_window_id: studentSession.test_window_id,
        uid: studentSession.uid,
        ric_id: this.ric_id,
        notes,
        is_pended: 1,
        category: STU_EX_OVERRIDES.PENDED,
        overrideOptions: this.individualOverrideForm
      })
    for (let record of createdRecords){
      this.selectedStudent.studentExceptions.push(record)
    }
    this.selectedStudent.studentReports = newReports
    this.selectedStudent.studentRevokedReports = studentRevokedReports
    this.exceptionsChange.emit()
  }
  
  async removeStudentException(exception: any) {
    this.loginGuard.confirmationReqActivate({
      caption: `Are you sure you want to remove exception from student?`,
      confirm: async () => {
        try {
          const { isSrUpdated, studentExceptionRecord, studentReports, studentRevokedReports } = await this.auth.apiRemove('public/test-ctrl/schools/student-exceptions', exception.id, {
              query: {
                  ric_id: exception.ric_id,
                  category: exception.category,
                  reporting_subject: this.isPJ ? exception.reporting_subject : null
              }
          });
          exception.is_revoked = 1;
          exception.revoked_by_uid = studentExceptionRecord.revoked_by_uid;
          exception.revoked_on = studentExceptionRecord.revoked_on;
          this.selectedStudent.studentReports = studentReports
          this.selectedStudent.studentRevokedReports = studentRevokedReports
          this.exceptionsChange.emit();
        } catch (error) {
          switch(error.message) {
            case 'MISSING_REQUIRED_USER_ROLE':
              this.loginGuard.quickPopup('Missing user role required for exemption override');
              break;
            default:
              // Handle any other error messages
              this.loginGuard.quickPopup(error.message);
          }
        }
      },
      close: () => {
          // Do nothing.
      },
  });
}


/** Return whether the user has changed any of the score options from their initial values */
 isScoreOverrideUpdated(): boolean {
  const scoringItems = this.cmc().scoringItems
  const isAnyUpdated = scoringItems.some(i => i.new_score_option_id)
  return isAnyUpdated;
 }

 /** Start the modal to update human scores from issue reviewer */
  async launchHumanScoreOverrideModal(){
    const taqr_id = this.renderItemResponseId(this.currentItemId())
    const query = {
      test_window_id: this.selectedStudentSession.test_window_id,
      tc_group_id: this.roleContext.tc_group_id
    }
    // Get more information about the expected marking items for this TAQR and its scoring scales
    const scoringItems = await this.auth.apiGet(this.routes.TEST_CTRL_HUMAN_SCORE_OVERRIDE, taqr_id, {query});

    // If there're already valid scores for an MWI, find the one that takes presedence and pre-select it as the existing choice
    const existingScoreScales = this.renderitemScoreScales(this.currentItemId())
    scoringItems.forEach(mwi => {
      const targetScale = existingScoreScales.find(s => s.mwi_id == mwi.mwi_id);
      if (!targetScale) return;
      const leaderScore = targetScale.scores.find(s => s.is_leader)
      const validScorerScore = targetScale.scores.find(s => !s.is_leader && !s.is_revoked && !s.is_invalid)
      if (leaderScore){
        mwi.score_option_id = leaderScore.score_option_id;
      } else if (validScorerScore) {
        mwi.score_option_id = validScorerScore.score_option_id;
      }
    })

    this.pageModal.newModal({
      type: Modal.HUMAN_SCORE_OVERRIDE,
      config: { 
        scoringItems,
        confirmationCaption: 'Save and Close',
        closeCaption: 'Close Without Saving'
       },
      finish: (config) => {
        this.applyHumanScoreOverride(config.scoringItems)
        .finally(() => {
          this.pageModal.closeModal();
        })
      }
    });  

  }

  /** Action the update to one or more human scores for the selected TAQR */
  applyHumanScoreOverride(scoringItems){
    const existingScoreScales = this.renderitemScoreScales(this.currentItemId())
    // Only send data about MWI scores that the user changed (e.g. TD changed but not CV)
    const changedScoreItems = scoringItems.filter(mwi => mwi.new_score_option_id)
    const changedScores: {mwi_id: number, score_option_id: number}[]  = changedScoreItems.map(s => {
      return {mwi_id: s.mwi_id, score_option_id: s.new_score_option_id}
    })
    if (!changedScores.length) return;
    // Update the score in the API
    const query = {
      tc_group_id: this.roleContext.tc_group_id
    }
    const taqr_id = this.renderItemResponseId(this.currentItemId())
    return this.auth.apiPatch(this.routes.TEST_CTRL_HUMAN_SCORE_OVERRIDE, taqr_id, {changedScores}, {query})
    .then(() => {
      // Update the new leader scores on the UI
      for (let item of changedScoreItems){
        // If this MWI already had any scores, find that scale, otherwise create new one
        let targetExistingScale = existingScoreScales.find(s => s.mwi_id == item.mwi_id)
        if (!targetExistingScale){
          const newScale = {
            slug: item.slug,
            score_profile_id: item.score_profile_id,
            mwi_id: item.mwi_id,
            scores: [],
            skill_code: item.skill_code
          }
          existingScoreScales.push(newScale)
          targetExistingScale = newScale;
        } else {
          const index = existingScoreScales.indexOf(targetExistingScale)
          existingScoreScales[index] = targetExistingScale;
        }
        const newSelectedScoreOption = item.options.find(o => o.id == item.new_score_option_id)
        // If there was already a leader score then update that one, otherwise add a new score to the list
        let existingLeaderScore = targetExistingScale.scores.find(s => s.is_leader)
        const newLeaderScore = {
          is_leader: true,
          raw_score_value: newSelectedScoreOption.raw_score_value,
          score_slug: newSelectedScoreOption.score_slug,
          score_option_id: newSelectedScoreOption.id,
          marked_on: moment().utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        }
        if (existingLeaderScore){
          const index = targetExistingScale.scores.indexOf(existingLeaderScore)
          targetExistingScale.scores[index] = newLeaderScore
        } else {
          targetExistingScale.scores.push(newLeaderScore)
        }
      }
      this.loginGuard.quickPopup('Successfully applied new score.')
    })
    .catch((err) => {
      console.error(err)
      this.loginGuard.quickPopup('Error applying new score.')
    })

  }


  applyOverride(category){
    if(category === 'WITHHOLD'){
      const config = { selectedStudent: this.selectedStudent, isPJ: this.isPJ}

      this.pageModal.newModal({
        type: Modal.WITHHOLD,
        config,
        finish: () => this.confirmOverride(category)
      });  
    } 
    else if(category === 'EXEMPTION_OVERRIDE'){
      const config = { selectedStudent: this.selectedStudent, isPJ: this.isPJ}

      this.pageModal.newModal({
        type: Modal.EXEMPTION_OVERRIDE,
        config,
        finish: () => this.confirmOverride(category),
        cancel: () => {
          this.checkedCheckboxes = {}
        }
      });  
    } 
    else if(category === 'INDIVIDUAL'){
      this.confirmOverride(category, this.individualOverrideForm.notes);
    }
    else{
      this.loginGuard.confirmationReqActivate({
        caption: `Are you sure you want to override student's outcome? Note: If the student have appeal it will set its status to Approved`,
        confirm: () => {
          this.confirmOverride(category)
        }, 
        close: () => {
          //Do nothing.
        },  
      })
    }
    
  }

  confirmOverride(category, notes?:string){
    if (!notes){
      notes = prompt('Notes on student exception (optional)')
    }
    const studentSession = this.selectedStudentSession;
    // todo: remove this pj hardcoding
    const pjReportingSubjectSelections = this.isPJ ? this.checkedCheckboxes : null
    this.individualOverrideForm.values.reporting_subject = pjReportingSubjectSelections

    if (category === STU_EX_OVERRIDES.INDIVIDUAL) {
      if (!this.isOSSLT && !this.individualOverrideForm.isCustomValues) {
        alert("Please insert custom result override values.")
        return
      }
      if (this.isPJ && !Object.values(pjReportingSubjectSelections).length) {
        alert("Please select a reporting subject to override.")
        return
      }
    }

    this.auth
      .apiCreate('public/test-ctrl/schools/student-exceptions', {
        test_window_id: studentSession.test_window_id,
        uid: studentSession.uid,
        ric_id: this.ric_id,
        is_pended: 0,
        notes,
        category,
        overrideOptions: this.individualOverrideForm
      }).then(res =>{
        const {createdRecords, newReports, studentRevokedReports} = res
        for (let record of createdRecords){
          this.selectedStudent.studentExceptions.push(record)
        }
        this.selectedStudent.studentReports = newReports
        this.selectedStudent.studentRevokedReports = studentRevokedReports
        this.individualOverrideForm.values.reporting_subject = {} // reset in case other pj exception changes the value
        this.exceptionsChange.emit()
        this.studentOverrided.emit()
      }).catch(error => {
        switch(error.message) {
          case 'MISSING_REQUIRED_USER_ROLE':
            this.loginGuard.quickPopup('Missing user role required for exemption override');
            break;
          default:
            // Handle any other error messages
            this.loginGuard.quickPopup(error.message);
        }
      }) 
  }

  // handles checkbox changes emitted from withhold/exemption override modal for PJ only
  handleCheckboxesChanged(checkboxValues: { [key: string]: boolean }): void {
    this.checkedCheckboxes = checkboxValues;
  }

  /**
   * This is used for enabling model footer "OK" button
   * @return {boolean} return true if user changed any input value
   */
  isCheckboxTouched(): boolean {
    return Object.keys(this.checkedCheckboxes).length > 0;
  }

  onSelectChange(value: any, prop: string): void {
    if (prop === 'reporting_subject') {
        this.checkedCheckboxes = {}; // Reset
        if (+value) { // 0 is considered as falsy
          this.checkedCheckboxes[+value] = true;
        }
    } else {
        this.individualOverrideForm.values[prop] = value;
    }
  }

  async loadStudentExceptions(studentSession:any){
    if (!studentSession.studentExceptions){
      studentSession.studentExceptions = [];
    }
    const {uid, ts_id, test_window_id} = studentSession;
    const {records, studentReports, studentRevokedReports, reportingSubjectConfig, overrideOptions} = await this.auth.apiGet('public/test-ctrl/schools/student-exceptions', 1, {
      query: {
        uid, 
        ts_id,
        test_window_id,
      }
    });
    // console.log('loadStudentExceptions', records)
    studentSession.studentExceptions = records
    studentSession.studentReports = studentReports
    studentSession.studentRevokedReports = studentRevokedReports
    studentSession.reportingSubjectConfig = reportingSubjectConfig
    studentSession.overrideOptions = overrideOptions
    const preservedParams = {};
    for (let prop in overrideOptions.options){
      preservedParams[prop] = this.individualOverrideForm.values[prop];
    }
    this.individualOverrideForm.values = preservedParams;
  }

  toggleAdvIndivOvrd(){
    this.individualOverrideForm.isCustomValues = !this.individualOverrideForm.isCustomValues
  }

  individualOverrideForm:IIndividualOverrideForm = {
    isCustomValues: false,
    context: {
      reporting_subject: null
    },
    notes: '',
    values: {
    }
  }
  
  getOverrideFields(key){
    return [... Object.keys(this.selectedStudent.overrideOptions[key])]
  }

  async loadStudentReportedIssues(studentSession:any){
    if (!studentSession.reportedIssues){
      studentSession.reportedIssues = [];
    }
    const {uid, ts_id, test_window_id} = studentSession;
    const {records} = await this.auth.apiGet(this.routes.TEST_CTRL_REPORT_ISSUES, uid, {
      query: {
        uid, 
        test_window_id,
        ts_id
      }
    });
    studentSession.reportedIssues = records;
  }

  async createReportedIssue(){
    const {uid, ts_id} = this.selectedStudent;
    const {message, categoryId} = this.selectedStudent.newIssueForm;

    const categorySelection =  this.lang.tra(this.reportingCategories.find(rc => rc.id == categoryId).slug);
    const newIssue = await this.auth.apiCreate(this.routes.TEST_CTRL_REPORT_ISSUES, {
      studentUids: [uid], 
      test_session_id: ts_id,
      msg: message,
      categoryId,
      categorySelection
    });
    alert('New issue has been reported');
    this.selectedStudent.newIssueForm = null;
    this.selectedStudent.reportedIssues.push({
      ric_id: newIssue.id,
      categorySelection: newIssue.category,
      msg: newIssue.description
    })
  }

  notYetAvailable(){
    alert('Processing of student exceptions is currently disabled on this test window.')
  }
  
  async loadStudentFormData(attempt){
    this.selectedAttempt = attempt;
    this.selectedItemId = null;
    this.selectedItemResponse = null;
    this.selectedAttemptForm = null
    if (this.selectedStudent){
      const {ta_id} = this.selectedAttempt;
      if (this.loadedAttempts.has(ta_id)){
        this.selectedAttemptForm = this.loadedAttempts.get(ta_id);
      }
      else {
        const responseData = await this.auth.apiGet(this.routes.TEST_CTRL_STUDENT_ATTEMPT_RESPONSES, ta_id, {
          query: {
            test_window_id: this.roleContext.test_window_id,
          }
        });
        responseData.itemRepsonseMap = {};
        responseData.taqrRecords.forEach(taqr => {
          responseData.itemRepsonseMap[''+taqr.test_question_id] = taqr;
        });
        this.imputeActiveSections(responseData);
        this.initQuestionAllTitles(responseData)
        this.initItemHumanScores(responseData)
        if (this.isPJ){
          this.initScanQuestions(responseData);
        }
        responseData.itemSelection = {}
        this.selectedAttemptForm = responseData;
        this.lastSelectedAsmFormat = this.getPJAsmFormat(responseData.umIsCrScanDefault)
      }
    }
  }
  questionTitleMap;
  questionLabelsById = new Map();
  sanitizeQDb(questionSrcDb:any){
    const questionSrc = new Map();
    Object.keys(questionSrcDb).forEach((itemId) => {
      const question = questionSrcDb[itemId]
      this.questionLabelsById.set(+itemId, question.label)
      questionSrc.set(+itemId, question)
    });
    return questionSrc;
  }

  //** Group and store any questions where a scan upload is allowed (PJ only) */
  initScanQuestions(responseData:IStudentLookupResponseData){
    responseData.scanQuestionInfo = []
    for (const questionId in responseData.formData.questionDb) {
      const question = responseData.formData.questionDb[questionId]
      const isInputTextQuestion = question.content.some(content => content.elementType == ElementType.INPUT && content.format == InputFormat.TEXT)
      if (isInputTextQuestion){
        const section = responseData.formData.sections.find(section => section.questions.includes(+questionId))
        const newRecord = {
          question,
          section,
          item_id: +questionId
        }
        responseData.scanQuestionInfo.push(newRecord)
      }
      responseData.scanQuestionInfo.sort((a, b) => a.section.sectionId - b.section.sectionId );
    }
  }

  /** Display section and question label as the potential destination of the scan */
  renderScanOverrideOption(scanQuestionInfo){
    const sectionCaption = scanQuestionInfo.section.caption
    const questionLabel = scanQuestionInfo.question.label
    return `${sectionCaption} - ${questionLabel}`
  }

  initItemHumanScores(responseData:IStudentLookupResponseData){
    const taqrRef = new Map();
    for (let taqr of responseData.taqrRecords){
      taqr._scalesScores = [];
      taqrRef.set(taqr.taqr_id, taqr)
    }
    const taqrScaleRef = new Map();
    const ensureScoreScaleArr = (taqr_id:number, mwi_id: number, score_profile_id:number, slug:string, skill_code:string) => {
      const taqr = taqrRef.get(taqr_id)
      let scale:ITaqrScales = taqrScaleRef.get((taqr_id+';'+score_profile_id))
      if (!scale){
        scale = {
            slug,
            score_profile_id,
            mwi_id,
            scores: [],
            skill_code
        }
        taqr._scalesScores.push(scale)
        taqrScaleRef.set((taqr_id+';'+score_profile_id), scale)
      }
      return scale
    }
    for (let score of responseData.scoresScorers){
      const {taqr_id, score_profile_id, item_slug, mwi_id, skill_code} = score
      const scale = ensureScoreScaleArr(taqr_id, mwi_id, score_profile_id, item_slug, skill_code);
      scale.scores.push({
        timestamp: score.marked_on,
        is_leader: false,
        is_revoked: score.is_revoked==1,
        is_invalid: score.is_invalid==1,
        raw_score_value: score.raw_score_value,
        score_slug: score.score_slug,
        score_option_id: score.score_option_id,
        flag_slug: score.flag_slug,
        skill_code: score.skill_code,
        marked_on: score.marked_on
      })
    }
    for (let score of responseData.scoresLeaders){
      const {taqr_id, score_profile_id, item_slug, mwi_id, skill_code} = score
      const scale = ensureScoreScaleArr(taqr_id, mwi_id, score_profile_id, item_slug, skill_code);
      scale.scores.push({
        timestamp: score.marked_on,
        is_leader: true,
        is_revoked: false,
        is_invalid: false,
        raw_score_value: score.raw_score_value,
        score_slug: score.score_slug,
        marked_on: score.marked_on,
        score_option_id: score.score_option_id,
      })
    }
    for (let taqr of responseData.taqrRecords){
      if (taqr._scalesScores.length){
        taqr._isCrScore = true
      }
    }
    console.log('responseData.taqrRecords', responseData.taqrRecords)
  }

  initQuestionAllTitles(responseData:any){
    this.questionTitleMap = getQuestionTitles(
      responseData.formData.sections, 
      this.sanitizeQDb(responseData.formData.questionDb), 
      responseData.formData.useQuestionLabel, 
      this.lang,
      responseData.formData.questionWordSlug,
    );
    // this.questionTitles.emit(this.questionTitleMap);
  }
  imputeActiveSections(selectedAttemptForm){
    // getSectionId
    if (selectedAttemptForm.formData.panelRouting){
      const sections = selectedAttemptForm.formData.sections.splice(0, 1);
      const firstModule = sections[0]
      const modulesToIgnore = new Map()
      modulesToIgnore.set(''+firstModule.moduleId, true);
      const modulesToAdd = new Map();
      selectedAttemptForm.taqrRecords.forEach(taqr => {
        const module_id = ''+taqr.module_id
        if (!modulesToIgnore.get(module_id)){
          modulesToAdd.set(module_id, true);
        }
      })
      for (let module of selectedAttemptForm.formData.panelModules){
        const module_id = ''+module.moduleId
        if (modulesToAdd.get(module_id)){
          sections.push({
            moduleId: module.moduleId,
            questions: module.questions,
            sectionName: module.sectionName,
            sectionNumber: module.sectionNumber,
          })
        }
      }
      selectedAttemptForm.formData.sections = sections;
    }
  }

  isShowRevokedExceptions:boolean;

  async renderItemNumber(index:number, itemId:number, firstSectionQ){
    //check if the first question in the section is a reading selection - modify the number returned item number accordingly
    const firstSecQTaqr = this.getTaqr(firstSectionQ);
    if (!firstSecQTaqr.itemDisplay && !firstSecQTaqr.responseState){
      firstSecQTaqr._isLoadingDetails = true;
      const {config, response_raw, scan_url, full_scan, is_scan_validated, style_profile} = await this.auth.apiGet('/public/test-ctrl/schools/student', firstSecQTaqr.taqr_id || -1, { query: {... this.roleContext, itemId} })
      if (config){
        let itemConfig = JSON.parse(config);
        this.styleProfileMap.set(itemConfig.id, style_profile);
        firstSecQTaqr.itemDisplay = processItemConfigLangLink(itemConfig, this.selectedAttemptForm.test_form_lang);
        firstSecQTaqr.responseState = JSON.parse(response_raw || '{}');
        firstSecQTaqr.responseScanUrl = scan_url;
        firstSecQTaqr.responseFullScan = full_scan;
        firstSecQTaqr.isScanValidated = is_scan_validated;
      }
      firstSecQTaqr._isLoadingDetails = false;
    }
    
    if(firstSecQTaqr.itemDisplay.isReadingSelectionPage == true){
      return (1*index);
    }
    return (1*index+1);
  }
  getTaqr(itemId:number){
    let taqr = this.selectedAttemptForm.itemRepsonseMap[''+itemId];
    if (!taqr){
      taqr = this.selectedAttemptForm.itemRepsonseMap[''+itemId] = {
        ... this.selectedAttemptForm.stuItemExRef[+itemId],
        isNoRecord: true
      }
    }
    return taqr;
  }
  renderItemName = (itemId:number) => this.questionLabelsById.get(+itemId) || this.getTaqr(itemId).question_label;
  renderitemScore = (itemId:number) => this.getTaqr(itemId).score;
  renderitemIsCr = (itemId:number) => this.getTaqr(itemId)._isCrScore;
  renderitemScoreScales = (itemId:number) => this.getTaqr(itemId)._scalesScores;
  renderitemScoreOf = (itemId:number) => `${this.getTaqr(itemId).score} / ${this.getTaqr(itemId).weight}`
  renderitemNoAccess = (itemId:number) => this.getTaqr(itemId).isNoRecord;
  renderitemNr = (itemId:number) => (this.getTaqr(itemId).is_nr==1);
  renderitemStart = (itemId:number) => this.getTaqr(itemId).created_on;
  renderitemEnd = (itemId:number) => this.getTaqr(itemId).updated_on;
  renderItemLatestScoreReceivedOn = (itemId:number) => {
    const scoreScales = this.getTaqr(itemId)._scalesScores;
    let latestMarkedOn:string;
    scoreScales.forEach(scale => {
      scale.scores.forEach(score => {
        if (!latestMarkedOn || score.marked_on > latestMarkedOn){
          latestMarkedOn = score.marked_on;
        }
      })
    })
    return latestMarkedOn ?  this.renderUtcDateEst(latestMarkedOn) : undefined;
  }
  renderItemFirstTouch = (itemId:number) => this.renderUtcDateEst(this.getTaqr(itemId).created_on);
  renderItemLastTouch = (itemId:number) =>  this.renderUtcDateEst(this.getTaqr(itemId).updated_on);
  renderItemResponseId = (itemId:number) => this.getTaqr(itemId).taqr_id;
  renderIsPaperFormat = (itemId:number) => this.getTaqr(itemId).is_paper_format;
  isItemProRated = (itemId:number) => this.getTaqr(itemId).is_prorated==1;
  isItemScoreOverride = (itemId:number) => this.getTaqr(itemId).is_score_override==1;
  isItemInvalidated = (itemId:number) => this.getTaqr(itemId).is_invalidated==1;
  renderItemScoreOverride = (itemId:number) => {
    if(itemId === this.currentItemId()) 
    return this.getTaqr(itemId).new_score;
  }
  isSelected = (itemId:number) => (this.selectedItemId == itemId);
  currentItemId = () => this.selectedItemId;

  async applyProration(item_id){
    await this.applyItemException(item_id, {
      is_prorated: 1, 
      is_score_override: 0, 
      new_score: null,
      is_invalidated: 0
    })
    alert('Pro-ration applied')
    this.getTaqr(item_id).is_prorated = 1
    this.getTaqr(item_id).is_score_override = 0
    this.getTaqr(item_id).new_score = null
    this.getTaqr(item_id).is_invalidated = 0
  }

  async applyItemScoreOverride(item_id){
    const newScore = prompt('New Score');
    if (newScore === ''){ return; }
    const new_score = parseFloat(newScore);
    if (isNaN(new_score)){
      alert('Cannot use this score: ' + new_score);
      return;
    }
    await this.applyItemException(item_id, {
      is_prorated: 0, 
      is_score_override: 1, 
      new_score,
      is_invalidated: 0
    })
    alert('Score override applied')
    this.getTaqr(item_id).is_prorated = 0
    this.getTaqr(item_id).is_score_override = 1
    this.getTaqr(item_id).new_score = new_score
    this.getTaqr(item_id).is_invalidated = 0
  }

  async removeScoreOverride(item_id){
    const isConfirmed = confirm('Are you sure that you want to remove this student item exception?')
    if (isConfirmed){
      const studentSession = this.selectedStudentSession;
      await this.auth
        .apiRemove('public/test-ctrl/schools/student-item-exceptions', 0, {
          query: {
            item_id,
            test_window_id: studentSession.test_window_id,
            uid: studentSession.uid,
          }
        })
      alert('Student item exception removed')
      this.getTaqr(item_id).is_prorated = 0
      this.getTaqr(item_id).is_score_override = 0
      this.getTaqr(item_id).new_score = null
      this.getTaqr(item_id).is_invalidated = 0
    }
  }

  async applyItemException(item_id, payload:{is_prorated: number, is_score_override: number, new_score: number | null, is_invalidated: number}){
    
    let notes = "";
    const {is_prorated, is_score_override, new_score, is_invalidated} = payload
    if(is_invalidated) {
      notes  = prompt('Please input the invalidation reason.')
    } else {
      notes = prompt('Rationale (optional)')
    }
    if(is_invalidated && !notes) {
      return notes;
    }

    const studentSession = this.selectedStudentSession;
    const {createdRecords} = await this.auth
      .apiCreate('public/test-ctrl/schools/student-item-exceptions', {
        test_window_id: studentSession.test_window_id,
        uid: studentSession.uid,
        item_id, 
        ric_id: this.ric_id, 
        notes, 
        is_prorated, 
        is_score_override, 
        new_score,
        ta_id: this.selectedAttempt.ta_id,
        is_invalidated,
      })
    return createdRecords;
  }

  applyStudentResponseOverride(){
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure that you want to override this student response?',
      confirm: () => {
        this.opinionEssayEditable = true;
        this.isToggleWritten = true;
      }
    });
  }

  textInputChange = (newInputText) => {
    if(newInputText){
      this.inputText = newInputText;
    }
  }

  /**
   * Replace the student's response for a question with a new TAQR given the response content
   * @param item_id - item ID for which the taqr is being overridden
   * @param format - overriding with scan or written response
   * @param newResponseContent - either the string of the new written response, or file path information of the new scan
   * @param isScanOverrideNewUpload - if overriding with a scan, indicate that it's a brand new upload (not switching existing scans)
   */
  async saveResponseOverrideInput(item_id:number, format:Format, newResponseContent, isScanOverrideNewUpload = false) {
    let isConfirmed;
    if (isScanOverrideNewUpload){
      isConfirmed = confirm(`Are you sure that you want to ${format==Format.TYPED? "save" : "confirm"}?`)
    } else {
      isConfirmed = true;
    }
    if (isConfirmed){
      const responseInputText = format==Format.TYPED ? newResponseContent : undefined
      const scanFilePath = format==Format.SCAN ? newResponseContent  : {} 

      const studentSession = this.selectedStudentSession;
      const attempt = this.selectedAttempt;

      await this.auth
      .apiCreate(this.routes.TEST_CTRL_STUDENT_ATTEMPT_RESPONSES, {
        responseInputText,
        scanFilePath,
        ta_id: attempt.ta_id,
        //ta_id: studentSession.ta_id,
        test_question_id: item_id,
        student_oen: studentSession.StudentOEN,
        student_uid: studentSession.uid,
        type_slug: studentSession.type_slug,
        test_window_id: studentSession.test_window_id,
        test_session_id: studentSession.ts_id,
        test_attempt_is_sample: attempt.is_sample ? 1 : 0,
        is_test_session_submitted: attempt.is_closed ? 1 : 0,
        test_form_id: attempt.test_form_id,
      },{
        query: {format}
      })
      .then(res => {
        if (format==Format.TYPED) this.loginGuard.quickPopup('Student response has been overridden.');
        this.opinionEssayEditable = false;
        this.cancelOverrideResponse = false;

        const itemResponse = this.selectedAttemptForm.itemRepsonseMap[''+item_id]
        itemResponse.taqr_id = res.id,
        itemResponse.responseState = JSON.parse(res.response_raw)
        itemResponse.score = res.score
        itemResponse.weight = res.weight
        itemResponse.is_nr = res.is_nr
        itemResponse.isNoRecord = false
        itemResponse.responseScanUrl = res.scan_url
        itemResponse.responseFullScan = res.full_scan
        itemResponse.is_scan_validated = res.is_scan_validated

        // If uploading a new scan to override and the all scans view is open, refresh them
        if (format == Format.SCAN && isScanOverrideNewUpload && this.isViewAllScans){
          this.loadAllScanData();
        }
        // If overwrote the selected questions with text and the all scan view is open, refresh it
        if (format == Format.TYPED && this.isPjScanPanelVisible && this.isViewAllScans){
          this.loadAllScanData();
        }
      })
    }

  }

  async applyItemInvalidation(item_id){
    const isConfirmed = confirm('Are you sure that you want to invalidate this response?')
    if(!isConfirmed) return;
    const response =  await this.applyItemException(item_id, {
      is_prorated: 0, 
      is_score_override: 0, 
      new_score: null,
      is_invalidated: 1,
    })
    if(response === null) return;
    if(response === "") {
      alert('missing invalidation reason');
      return;
    } 
    if(response) {
      alert('Invalidation applied')
      let taqr = this.selectedAttemptForm.itemRepsonseMap[''+item_id]
      if(taqr) {
        delete this.selectedAttemptForm.itemRepsonseMap[''+item_id]
      };
      this.getTaqr(item_id).isNoRecord = true 
    }
  }

  revertResponseInput() {
    this.cancelOverrideResponse = true;
    this.opinionEssayEditable = false;
    if (this.selectedItemResponse.responseScanUrl) this.isToggleWritten = false;
  }

  // async onResponseSelected($event: RowNodeEvent){
  //   const selectedRows = this.taqrGridOptions.api.getSelectedRows();
  //   if (selectedRows.length > 0){
  //     this.selectedItemResponse = selectedRows[0];
  //     await this.loadItemResponseAndDisplay();
  //   }
  //   else {
  //     this.selectedItemResponse = null;
  //   }
  // }

  selectedItemId
  selectedTaqr = null;
  isToggleWritten:boolean;
  async loadItemResponseAndDisplay(section:any, itemId:number, itemIndex:number,){
    this.isPjScanPanelVisible = false;
    this.isToggleWritten = false
    console.log(itemId);
    const position = {
      section: this.getSectionNumber(section),
      question: await this.renderItemNumber(itemIndex, itemId, section.questions[0])
    }
    if (this.getModuleId(section)) position["module"] = this.getModuleId(section);

    const taqr = this.getTaqr(itemId);
    this.selectedItemId = itemId;
    this.selectedItemResponse = null;
    if (!taqr.itemDisplay && !taqr.responseState){
      taqr._isLoadingDetails = true;
      const {config, response_raw, scan_url, full_scan, is_scan_validated, style_profile} = await this.auth.apiGet('/public/test-ctrl/schools/student', taqr.taqr_id || -1, { query: {... this.roleContext, itemId} })
      if (config){
        let itemConfig = JSON.parse(config);
        this.styleProfileMap.set(itemConfig.id, style_profile);
        taqr.itemDisplay = processItemConfigLangLink(itemConfig, this.selectedAttemptForm.test_form_lang);
        taqr.responseState = JSON.parse(response_raw || '{}');
        taqr.responseScanUrl = scan_url;
        taqr.responseFullScan = full_scan;
        taqr.isScanValidated = is_scan_validated;
      }
      taqr._isLoadingDetails = false;
    }
    const keys = Object.keys(taqr.responseState);
    let responseRaw = null;
    let responseRawKey = '';

    for (let key of keys) {
      const intKey = parseInt(key);
      if (!isNaN(intKey)) {
        responseRaw = taqr.responseState[key];
        responseRawKey = key;
      }
    }

    const isInputTextQuestion = taqr.itemDisplay.content.some(content => content.elementType == ElementType.INPUT && content.format == InputFormat.TEXT)
    // Previous condition: responseRaw && responseRaw.str && responseRaw.type === RESPONSE_TYPE.INPUT_LONGTEXT
    if(isInputTextQuestion){
      this.selectedLongTextItemStr = (responseRaw && responseRaw.str) ? responseRaw.str : '';
      this.isLongTextItem = true;
    } else {
      this.selectedLongTextItemStr = '';
      this.isLongTextItem = false;
    }
    taqr.position = position;
    if(!this.profile.getStyleProfile() || this.styleProfileMap.get(itemId) !== this.profile.getSelectedStyleProfile()){
      await this.profile.applyStyleProfileOrFallback(this.styleProfileMap.get(itemId), null, 'panel-student-lookup', 'loadItemResponseAndDisplay');
      this.selectedItemResponse = taqr;
    } else {
      this.selectedItemResponse = taqr;
      this.selectedTaqr = taqr;
    }
  }
  
  getItemCaption(sId: number, qId: number){
    return getQuestionTitleFromMap(this.questionTitleMap, sId, qId);
  }

  getSelectedItemPositionLabel(){
    const {section, module, question} = this.selectedItemResponse.position
    return `S${section}, ${module ? `M${module}, ` : ''} Q${question}`
  }

  getSectionName(section:any){
    return section.sectionName
  }

  getSectionNumber(section:any){
    return section.sectionNumber
  }

  getModuleId(module:any){
    return module.moduleId; // || module.sectionId
  }

  getRiLink(test_window_id:number, ric_id:number){
    return `#/${this.lang.c()}/test-ctrl/issue-reviewer/tw/${test_window_id}/action/reports?ric_id=${ric_id}`
  }

  initScanImgUpload() {
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure that you want to upload a new scan?',
      confirm: () => {
        this.scanUploadInput.nativeElement.click();
      }
    });
  }

  /** Return list of options that a scan can be reused for (all scannable questions except itself) */
  getOverrideScanOptions(scan){
    return this.selectedAttemptForm.scanQuestionInfo.filter(info => info.section.caption !== scan.section_caption)
  }  

  /** Open/close the scan override panel to the right of the response seleciton */
  togglePjScanPanel(){
    this.isViewAllScans = false;
    this.isPjScanPanelVisible = !this.isPjScanPanelVisible
  }

  /** Enable/disable viewing all scans in the test attempt */
  async toggleViewAllScans(){
    if(!this.isViewAllScans) {
      await this.loadAllScanData();
    }
    this.isViewAllScans = !this.isViewAllScans
  }

  /** Get information about all the current scans in the selected test attempt */
  async loadAllScanData(){
    this.testAttemptScans = await this.auth.apiFind(this.routes.TEST_CTRL_STUDENT_ATTEMPT_RESPONSE_SCANS, {
      query: {
        ta_id: this.selectedAttempt.ta_id,
        tc_group_id: this.roleContext.tc_group_id
      }
    });

    // Add info on question and session for each scan
    this.testAttemptScans.forEach(scan => {
      const x = this.selectedAttemptForm.taqrRecords
      const targetTaqr = this.selectedAttemptForm.taqrRecords.find(record => record.taqr_id == scan.taqr_id)
      const targetSection = this.selectedAttemptForm.formData.sections.find(section => section.questions.includes(targetTaqr?.test_question_id))
      scan.section_id = targetSection?.sectionId
      scan.section_caption =  targetSection?.caption
      scan.question_label = targetTaqr?.question_label
      // Default to initial item ID
      scan.override_item_id = undefined
    })
    this.testAttemptScans.sort((a, b) => a.section_id - b.section_id);
  }

  /** Save any rearrangement/discarding of scans selected */
  actionScanOverrides(){
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure you want to action all overrides?',
      confirm: () => {
        const overridePromises = []
        this.testAttemptScans.forEach((record) => {
          if (!+record.override_item_id) {
            return
          }
          // Handle only discarding a certain scan
          else if (+record.override_item_id == -1){
            // If another scan is being switched into this item, that process will discard this scan already so do nothing
            if (this.testAttemptScans.some(scan => scan.override_item_id == record.item_id)) {
              return
            }
            const discardOnlyPromise = this.auth
            .apiRemove(this.routes.TEST_CTRL_STUDENT_ATTEMPT_RESPONSE_SCANS, record.tasr_id, { 
              query: { taqr_id: record.taqr_id, tc_group_id: this.roleContext.tc_group_id }
            })
            .then(() => {
              // Remove scans from selected response display too
              const itemResponse = this.selectedAttemptForm.itemRepsonseMap[''+record.item_id]
              itemResponse.responseScanUrl = null;
              itemResponse.responseFullScan = null;
            })
            overridePromises.push(discardOnlyPromise);
          } else {
            const targetItemIdOfScan = record.override_item_id
            const scanFilePathInfo = {
              croppedFilePath: record.scan,
              uncroppedFilePath: record.full_scan
            }
            const overrideTargetItemScanPromise = this.saveResponseOverrideInput(targetItemIdOfScan, Format.SCAN, scanFilePathInfo)
            overridePromises.push(overrideTargetItemScanPromise)
          }
        })
        Promise.all(overridePromises)
        .then(res => {
          // Refresh the view to display uplodates
          this.loadAllScanData();
        })
        .catch(() => {
          this.loginGuard.quickPopup('Error saving overrides.')
        })
      }
    })
  }

  /** The button to action group scan overrides is disabled if there're no changes or they're incomptible */
  isActionScanOverridesDisabled(){
    const noOverrideChanges = !this.testAttemptScans.some(scan => +scan.override_item_id)
    return (noOverrideChanges || this.isActionScanOverridesComboError());
  }

  /** 
   * Is the user's combination of scan rearrangement choices invalid.
   * No two scans can have the same item as the final target.
   * E.g. can't have A -> B and A -> C.
   * Also cannot have only A -> B, need to explicitly select that current A will be discarded.
   * */
  isActionScanOverridesComboError(){
    let isComboError:boolean = false;
    const finalItemIdSet = new Set();
    this.testAttemptScans.forEach(scan => {
      const finalItemId = scan.override_item_id ?  +scan.override_item_id : +scan.item_id
      if (![0, -1].includes(finalItemId) && finalItemIdSet.has(finalItemId)){
        isComboError = true
      }
      finalItemIdSet.add(finalItemId)
    })
    return isComboError;
  }

  newScanFile;
  newScanFilePath;
  handleScanImgUpload(files: FileList){
    if(files.length !== 1) return
    const scanParams = {
      file: files.item(0), 
      test_window_id: this.selectedAttempt.ts_test_window_id,
      test_session_id: this.selectedAttempt.ts_id,
      oen: this.selectedStudentSession.StudentOEN,
      session_slug: this.selectedItemResponse.itemDisplay.meta.SCAN_ID,
      tc_group_id: this.roleContext.tc_group_id
    }
    this.scanInfo.uploadSingleScan(scanParams)
    .then((res) => {
      const {croppedFile, uncroppedFile, croppedFilePath, uncroppedFilePath} = res
      this.newScanFile =  {croppedFile, uncroppedFile}
      this.newScanFilePath = {croppedFilePath, uncroppedFilePath}
      this.uploadModalStart()
    })
    .finally(() => {
      this.scanUploadInput.nativeElement.value = '';
    });
  }

  cModal() { 
    return this.pageModal.getCurrentModal(); 
  }
  cmc() { return this.cModal().config; }

  uploadModalStart() {
    this.isNewScanConfirmed = false;
    const config = {scanFiles: this.newScanFile}
    this.pageModal.newModal({
      type: Modal.SCAN_UPLOAD,
      config,
      finish: this.uploadModalFinish
    });
  }

  uploadModalFinish = () => {
    this.pageModal.closeModal();
  }
  
  isNewScanConfirmed: boolean;
  overrideWithScans(){
    this.saveResponseOverrideInput(this.currentItemId(), Format.SCAN, this.newScanFilePath, true)
    .then(() => {
      this.isNewScanConfirmed = true
      this.isToggleWritten = false
    })
  }

    /**
   * Check if the selected attempt have report ready but no pdf
   * @params null
   * @returns boolean
   */
  showGeneratePDFBtn():boolean{
    //PJ
    if(this.isPJ){
      const EXCLUDE_WINDOWS = [39, 40] // Exclude old test window 39 40 which does not suppourt single report
      const notInBlockedWindow = !EXCLUDE_WINDOWS.includes(+this.roleContext.test_window_id)
      const hasReport = this.selectedStudent.studentReports.length > 0  //selected student have report
      const noGeneratedTestAttemptPDF = this.selectedAttempt && !this.selectedAttempt.bsrr_id && !this.selectedAttempt.bsrr2_id && !this.selectedAttempt.ansrg_id //selected attempt is not generated(bsrr_id null) or generating(bsrr2_id null). 
      return notInBlockedWindow && hasReport && noGeneratedTestAttemptPDF
    }


    //OSSLT
    if(this.isOSSLT && this.isOSSLTISRVersion2()){
      // Following new OSSLT ISR reporting rules 2024-2025: All registered student should have a report except pended status 
      return this.selectedStudentSession.studentExceptions.some(record => record.is_pended === 1 && record.is_revoked === 0) ? false : true
    }
    return false
  }

   /**
   * Check if the selected attempt pdf is generating
   * @params null
   * @returns boolean
   */
  pdfGenerating():boolean{
    //PJ
    if(this.isPJ){
      const EXCLUDE_WINDOWS = [39, 40] // Exclude old test window 39 40 which does not suppourt single report
      const notInBlockedWindow = !EXCLUDE_WINDOWS.includes(+this.roleContext.test_window_id)
      const hasReport = this.selectedStudent.studentReports.length > 0  //selected student have report
      const generatingTestAttemptPDF = this.selectedAttempt && this.selectedAttempt.bsrr2_id //selected attempt is generating(bsrr2_id not null).
      return notInBlockedWindow && hasReport && generatingTestAttemptPDF
    }

    //OSSLT
    if(this.isOSSLT && this.isOSSLTISRVersion2()){
      const generatingTestAttemptPDF = this.selectedAttempt && this.selectedAttempt.bsrr2_id //selected attempt is generating(bsrr2_id not null). 
      return generatingTestAttemptPDF
    }
    return false
  }

  /**
   * generate the selected attempt whole school's pdf
   * @params null
   * @returns null
   */
  generatePDF(){
    if(this.isPJ){
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra("This will generate the PDF for the school admin, Are you sure you want to do this?"),
        confirm: () => { 
          const assessment_slug = this.myCtrlOrg.selectedWindow.type_slug == TEST_WINDOW_TYPE.EQAO_G3P?ASSESSMENT.PRIMARY_OPERATIONAL:ASSESSMENT.JUNIOR_OPERATIONAL
          
          const queryparams = {
            schl_group_id: this.selectedStudent.schl_group_id,
            schoolsId:this.selectedStudent.schl_id, 
            assessment_slug,
            clientDomain:getFrontendDomain(),
            bypassAdminSignOff:true,
            tw_id:this.roleContext.test_window_id,
          }
          this.auth
            .apiCreate(this.routes.TEST_CTRL_REQUEST_PJ_SCHL_REPORT, {},{
              query: queryparams
            }).then(res =>{
              if(res.message === 'REPORT_GENERATING'){
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_generating_pj'),
                  confirm: () => {}
                });
              }
              if(res.message === 'REPORT_REGENERATING'){
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_regenerating'),
                  confirm: () => {}
                });
              }
              this.selectedAttempt.bsrr2_id = +res.bsrr_id
            })
            .catch(err => {
              if (err.message === 'REPORT_GENERATING') {
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_generating_pj'),
                  confirm: () => {}
                });
              }
              if (err.message === 'NO_DATA_FOR_REPORT') {
                //do nothing
              }
            })
        }
      })
    }
    if(this.isOSSLT && this.isOSSLTISRVersion2()){
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra("This will generate the PDF for the school admin, Are you sure you want to do this?"),
        confirm: () => { 
          const queryparams = {
            schl_group_id:this.selectedStudent.schl_group_id,
            tw_id:this.roleContext.test_window_id,
            assessment_slug: ClassFilterId.OSSLT,
            bypassAdminSignOff:true,
            clientDomain:getFrontendDomain(),
            getAdminISR:true,
            isVersion2:true
          }

          this.auth
            .apiCreate(this.routes.TEST_CTRL_OSSLT_SINGLE_REPORTS, {},{
              query: queryparams
            }).then(res =>{
              if(res.message === 'REPORT_GENERATING'){
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_generating_pj'),
                  confirm: () => {}
                });
              }
              if(res.message === 'REPORT_REGENERATING'){
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_regenerating'),
                  confirm: () => {}
                });
              }
              this.selectedAttempt.bsrr2_id = +res.bsrr_id
            })
            .catch(err => {
              if (err.message === 'REPORT_GENERATING') {
                this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra('sa_report_generating_pj'),
                  confirm: () => {}
                });
              }
              if (err.message === 'NO_DATA_FOR_REPORT') {
                //do nothing
              }
            })
        }
      })
    }
  }
  
  /**
   * show download single ISR PDF button
   */
  showDownloadSingleReportBtn(){
    // G9 
    if(this.isG9 && this.selectedAttempt && (this.selectedAttempt.asrg_id || this.selectedAttempt.ansrg_id)){
      return true;
    }
    
    // PJ
    if(this.isPJ && this.selectedAttempt && this.selectedAttempt.bsrr_id && +this.roleContext.test_window_id !== 39 && +this.roleContext.test_window_id !== 40){ // test window 39 and 40 does not have single report. they only have full school report, so we are blocking it from download for issue-reviwer.
      return true;
    }

    // OSSLT have pdf file for version2
    if(this.isOSSLT && this.isOSSLTISRVersion2() && this.selectedAttempt &&  this.selectedAttempt.bsrr_id ){
      //Following new OSSLT ISR reporting rules 2024-2025: All registered student should have a report except pended status 
      return this.selectedStudentSession.studentExceptions.some(record => record.is_pended === 1 && record.is_revoked === 0) ? false : true
    }
    return false;
  }

  /**
   * Download the single ISR pdf file
   * @param attemptId 
   * @param uid 
   */
  downloadIndividualReport(attemptId, uid){
    if(this.isG9 || this.isPJ || (this.isOSSLT && this.isOSSLTISRVersion2())){
      let noAvailableReport = false
      const queryParams = {
        attemptId,
        uid,
        clientDomain:getFrontendDomain(),
        schl_group_id:this.selectedStudent.schl_group_id
      }
      this.auth
        .apiFind(this.routes.TEST_CTRL_STUDENT_REPORTS, {
          query: queryParams
        }).then(res =>{
          const message = res.message  
          const fileURL = res.reportURL
          if(message === 'REPORT_GENERATED' && fileURL && !noAvailableReport){
            let a = document.createElement('a');
            a.href = fileURL;
            a.download = 'student_report.pdf';
            a.dispatchEvent(new MouseEvent('click'));
          }   
        }).catch(err => {
          noAvailableReport = true
          if (err.message === 'NO_DATA_FOR_REPORT') {
            alert('NO REPORT HAS BEEN GENERATED FOR THIS STUDENT')
          }
          if (err.message === 'REPORT_VALIDATING'){
            alert("The report has not yet been validated.")
          }
        })
    }
  }

  /**
   * Check if the selected osslt attempt have report associate with it
   * This is to check if the "View Report" button is showing on UI
   * @params null
   * @returns The result
   */
  showDisplayOSSLTSingleReportBtn(){  
    if(this.isOSSLT && this.selectedAttempt && this.selectedAttempt.sr_id && !this.isOSSLTISRVersion2()){ //OSSLT version2 have real dpf file
        return true
    }
    return false;
  }

  /**
   * Determine to show OSSLT PDF generation Button
   */
  isOSSLTISRVersion2(){
    //return true if its OSSLT and version is 2
    return this.isOSSLT && +this.myCtrlOrg.selectedWindow.isr_version === 2
  }

   /**
   * Open the osslt single report modal to show selected single report
   * @params null
   * @returns null
   */
  viewOSSLTReportModalStart() {  
    const config = {
      schl_group_id_input: this.selectedAttempt.schl_group_id,
      schoolsId_input : this.selectedAttempt.schl_id,
      singleStudentUid_input : this.selectedAttempt.uid
    }
    this.pageModal.newModal({
      type: Modal.VIEW_OSSLT_REPORT,
      config,
      finish: this.viewOSSLTReportModalFinish
    });
  }

  /**
   * Close the osslt single report modal
   * @params null
   * @returns null
   */
  viewOSSLTReportModalFinish = () => {
    this.pageModal.closeModal();
  }

  schlAdminUploadScansDownloadBtn(){
    if(!this.selectedAttemptForm  || !this.selectedAttemptForm.schlAdminUploadScans.length) return
    const config = {
    };
    this.pageModal.newModal({
      type: Modal.UPLOAD_SCAN,
      config,
      finish: ()=> { 
        this.pageModal.closeModal();
      },
      cancel: ()=> { 
        //clear all the class variable used by the modal.
        this.selecteduploadScanInfo = null
        this.uploadScanInfoGridApi = null
      }
    });
  }

  uploadScanInfoRecordSelectionChanged(params){
    const selectedRow = this.uploadScanInfoGridOptions.api.getSelectedRows();
    if (selectedRow.length > 0){
      this.selecteduploadScanInfo = selectedRow[0]
    }
    else {
      this.selecteduploadScanInfo = null;
    }
  }

  uploadScanInfoGridReady(params){
    this.uploadScanInfoGridApi = params.api;
  }

  configureQueryParams(){
    const params = 
    {
      query: {
        test_window_id: this.roleContext.test_window_id
      }
    }

    return params
  }
  downloadUploadScanInfoBtn(){
    const s3_file_link = this.selecteduploadScanInfo.s3_file_link
    const params = this.configureQueryParams();
    params.query["s3_file_link"] = s3_file_link
    this.auth.apiFind( this.routes.TEST_CTRL_ISSUE_REVW_SCHL_ADMIN_UPLOAD_STUDENT_SCANS, params)
    .then((res) => {
      const file_name = s3_file_link.split('/').pop() || 'upload_scan';
      let a = document.createElement('a');
      a.href = res;
      a.download = file_name;
      a.dispatchEvent(new MouseEvent('click'));
    }).catch(error =>{
      switch(error.message) {
        default:
          alert(error.message)
          break
      }
    }).finally(() => {
    });
  }
 
  /**
   * Determine if attempt have a report generating now
   * @params null
   * @returns boolean
   */
  reportGenerating(){
    if(this.selectedAttempt && this.selectedAttempt.report_generating){
      return true;
    }
    return false
  }

  /**
   * Determine if attempt can requet report (Currently limit to G9 only)
   * @params null
   * @returns boolean
   */
  showReportGenerateAvailableBtn(){
    const avaliableAsessment = ['G9_OPERATIONAL']
    if(this.selectedAttempt && this.selectedAttempt.report_generation_available && avaliableAsessment.includes(this.selectedAttempt.type_slug) && !this.selectedAttempt.ansrg_id){
      return true;
    }
    return false
  }


   /**
   * Request Report Generation for the selected test attempt
   * @params null
   * @returns boolean
   */
  requestReportGeneration(){
    const catagoriesListSelection = ['Only if student is fully participating', "Regardless of the student's participation status"]

    this.loginGuard.confirmationReqActivate({
      caption: '**This student might not be fully participating in the assessment**. \n\nPlease review this carefully. If an automatic report generation record (ASRG) is created for a student who is not fully participating, their ISR (Individual Student Report) will **not** be generated at the end of the window.',
      requireCheckboxInput: {
        checkboxCaption: 'I understand the risk and still want to request report generation for this student.'
      },
      categoriesLabel: 'I want to request report generation for this student',
      catagoriesList: catagoriesListSelection,
      categorySelectionChange: (selectedOption) => {
        console.log(selectedOption)
      },
      confirm: () => {
        const data = {} 
        const selectedCategory = this.loginGuard.getConfirmationReq().value.selectedCategory;
        const queryParams = {
            attemptId: this.selectedStudent.ta_id,
            uid : this.selectedStudent.uid,
            schl_class_group_id: this.selectedStudent.sc_group_id,
            is_fp_only: selectedCategory === catagoriesListSelection[1] ? 0 : 1 // Default to 1
        }
        this.auth
        .apiCreate(this.routes.TEST_CTRL_REQUEST_REPORT_GENERATION, data, {
          query: queryParams
        }).then(res =>{
          if(!this.selectedAttempt.report_generating) {
            this.selectedAttempt.report_generating = 1
            this.selectedAttempt.report_generation_available = 0
          }
        }).catch(err => {
          if (err.message === 'NO_ASRG_FOR_NFP') {
            this.loginGuard.quickPopup('Student did not fully participate in the assessment. No automatic report generation record created.')
          } else {
            alert(err)
          }
        })
      }
    })
  }

  printOSSLTReport(): void {
    this.isrReportsComponent.printOSSLTReport();
  }

  /**",
   * click to open/close the student revoked reports info
  */
  expandRevokedReports(){
    if(this.selectedStudent && this.selectedStudent.studentRevokedReports){
      if (!this.selectedStudent.hasOwnProperty('showRevokedReports')) {
        this.selectedStudent['showRevokedReports'] = false;
      }
      this.selectedStudent['showRevokedReports']  = !this.selectedStudent['showRevokedReports'] 
    }
  }

  /** 
   * Toggle button switch between cropped and uncropped version of scanned document
   */
  showScanCropped: boolean = true
  showScanButtonName: string = 'Show Uncropped Version'
  showScanCroppedOrFull(){
    this.showScanCropped = !this.showScanCropped;
    if(!this.showScanCropped){
      this.showScanButtonName = 'Show Cropped Version'
    }else{
      this.showScanButtonName = 'Show Uncropped Version'
    }
  }

  isStudentExempt(){
    return this.selectedStudent.is_exempt
  }
  
  isTaqrModuleIdSameAsSection(itemId:number, section){
    const taqr = this.getTaqr(itemId)
    const sectionModuleId = this.getModuleId(section)
    // if record doesnt exist or doesnt have module_id (OSSLT/PJ), return true so doesnt flag item in Issue Reviewer
    if(taqr.isNoRecord || !sectionModuleId){
      return true
    }
    return taqr.module_id === sectionModuleId
  }

  getTaqrModuleId(itemId:number){
    const taqr = this.getTaqr(itemId)
    return taqr.module_id
  }

  exportStudentLookupResults(){
    this.gridOptions.api.exportDataAsCsv();
  }

  /** Convert date string from UTC to EST and format*/
  renderUtcDateEst(dateTimeUtc:string){
    return moment.utc(dateTimeUtc).tz(this.auth.getTimezone()).format("MMM D, YYYY h:mm:ss A (zz)")
  }
  _renderOptionCaption(scoreOption:IScoreOption){
    let caption = renderOptionCaption(scoreOption);
    if (!caption && scoreOption.value){
      caption = String(scoreOption.value);
    }
    return caption; 
  }

  /** Change the score option selection */
  selectScoreOption(mwi, scoreOption){
    // If changing back to the initial choice - no update needed
    if (mwi.score_option_id && mwi.score_option_id == scoreOption.id){
      mwi.new_score_option_id = undefined;
    }
    // Changing to something else
    else {
      mwi.new_score_option_id = scoreOption.id
    }
  }

  /** If a user update was made, use that choice as selected option - otherwise use previously saved choice if available */
  isScoreOptionSelected(mwi, scoreOption){
    if (mwi.new_score_option_id){
      return mwi.new_score_option_id == scoreOption.id
    } else if (mwi.score_option_id){
      return mwi.score_option_id == scoreOption.id 
    }
  }

  /**
   * Fetches accurate exemption statuses for every subject based on admin exemptions, overrides
   */
  get isAnySubjectExempted() {
    if(!this.selectedStudent.reportingSubjectConfig) return false;
    
    const exemptionStatuses = this.selectedStudent.reportingSubjectConfig.map(subject => {
      const reportRecord = this.selectedStudent.studentReports.find(studentReport => studentReport.reporting_subject === subject.val)
      const matchingException = this.selectedStudent.studentExceptions.find(studentException => studentException.category === OVERRIDE_TYPES.EXEMPTION_OVERRIDE && studentException.is_revoked === 0 && studentException.reporting_subject === subject.val)
  
      if(reportRecord) {
        return reportRecord.exempt_reporting_subjects.includes(subject.val) || reportRecord.exemption_override_applied
      }
  
      if(matchingException){
        return matchingException.activated === 1 // exception exemption activated value outweight admin exemption
      }
  
      return subject.adminExemption || false
    });
      
    return exemptionStatuses.includes(true)
  }
  /**
   * Get PJ user_meta IsCrScanDefault value in string and updated_on timestamp
   * @param umIsCrScanDefault 
   * @returns {format: string, timestamp: string}
   */
  getPJAsmFormat(umIsCrScanDefault):{format: string, timestamp: string} {
    if(!this.isPJ) {
      return
    }

    const assessmentCode = AssessmentCode[this.myCtrlOrg.selectedWindow.type_slug.slice(0, -1)]
    const isPaperFormat = this.onlineOrPaper.getPaperVal(umIsCrScanDefault?.value, assessmentCode)
    return { 
      format: isPaperFormat ? Assessment_Format_State.PAPER : Assessment_Format_State.ONLINE, 
      timestamp: umIsCrScanDefault?.updated_on
    }
  }

  /**
   * Get taqr.is_paper_format value wording
   * @param is_paper_format
   * @returns {string}
   */
  getTAQRAsmFormat(is_paper_format:number | undefined):string { // undefined = not accessed, 0 = online, 1 = paper
    if (is_paper_format === undefined) {
      return Assessment_Format_State.NOT_ACCESSED
    } else {
      return is_paper_format ? `On ${Assessment_Format_State.PAPER}` : Assessment_Format_State.ONLINE
    }
  }
}
