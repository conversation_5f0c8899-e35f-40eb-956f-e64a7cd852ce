<div class="interaction-voiceovers">
    <h2>Interactions Voice-overs</h2>
    <div class="table-container" style="overflow-x: auto;">
      <form [formGroup]="form">
        <table class="is-fullwidth is-narrow" *ngIf="interactionVoiceovers.length > 0; else noVoiceovers">
          <thead>
            <tr>
              <th style="width: 60%;">Script</th>
              <th style="width: 20%;">Audio</th>
              <th>Synced</th>
              <th style="width: 10%;white-space: nowrap;">Entry ID</th>
              <th style="width: 10%;white-space: nowrap;">Element Type</th>
            </tr>
          </thead>
          <tbody formArrayName="scripts">
            <tr *ngFor="let interaction of interactionVoiceovers; let i = index">
              <td (click)="startEditing(i)">
                <ng-container *ngIf="editingIndex !== i; else editMode">
                  <div class="script-text" [title]="interaction.script">
                    {{ truncateText(interaction.script) }}
                  </div>
                </ng-container>
                <ng-template #editMode>
                  <div class="edit-container">
                    <textarea 
                      [formControlName]="i"
                      [id]="'script-textarea-' + i"
                      class="textarea is-small voiceover-edit-textarea"
                      style="min-height: 2em; min-width: 15rem; resize: both;"
                      (keydown)="onKeyDown($event, i)"
                      autoFocus
                      [attr.aria-label]="'Edit-script-' + (i + 1)"
                    ></textarea>
                    <div class="button-group">
                      <button 
                        class="button is-primary is-small update-script-btn"
                        (click)="updateScript(i, $event)"
                      >
                        Update Script
                      </button>
                      <button 
                        class="button is-light is-small cancel-btn"
                        (click)="cancelEditing($event)"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </ng-template>
              </td>
              <td>
                <audio [src]="getSecureUrl(interaction.entryId, interaction.audioUrl, interaction.script)" controls></audio>
              </td>
              <td class="has-text-centered">
                <span *ngIf="interaction.synced" class="icon has-text-success">
                  <i class="fas fa-check"></i>
                </span>
                <span *ngIf="!interaction.synced" class="icon has-text-danger">
                  <i class="fas fa-times"></i>
                </span>
              </td>
              <td>
                {{ interaction.entryId }}
              </td>
              <td>
                {{ interaction.elementType }}
              </td>
            </tr>
          </tbody>
        </table>
      </form>
      <ng-template #noVoiceovers>
        <p class="has-text-centered py-4">No interaction voice-overs available.</p>
      </ng-template>
    </div>
  </div>