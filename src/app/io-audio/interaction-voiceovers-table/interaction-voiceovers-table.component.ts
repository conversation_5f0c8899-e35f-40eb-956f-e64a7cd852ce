import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { InteractionVoiceover, VoiceoverStateService } from 'src/app/ui-testrunner/voiceover-state.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'interaction-voiceovers-table',
  templateUrl: './interaction-voiceovers-table.component.html',
  styleUrls: ['./interaction-voiceovers-table.component.scss']
})
export class InteractionVoiceoversTableComponent implements OnInit {
  @Input() interactionVoiceovers: InteractionVoiceover[] = [];
  @Output() scriptChange = new EventEmitter<{
    index: number, 
    element: {
      script: string,
      url: string,
      fileType: string,
      entryId: number
    }
  }>();

  form: FormGroup;
  editingIndex: number = -1;
  private voiceoverSubscription: Subscription;
  secureUrls: { [key: string]: SafeResourceUrl } = {};

  constructor(
    private fb: FormBuilder,
    private voiceoverState: VoiceoverStateService,
    private sanitizer: DomSanitizer
  ) {
    this.form = this.fb.group({
      scripts: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    const scriptsArray = this.form.get('scripts') as FormArray;
    scriptsArray.clear();
    
    this.interactionVoiceovers.forEach(interaction => {
      scriptsArray.push(this.fb.control(interaction.script));
      if (interaction.audioUrl) {
        this.secureUrls[`${interaction.script}_${interaction.entryId}`] = this.sanitizer.bypassSecurityTrustResourceUrl(interaction.audioUrl);
      }
    });
  }

  get scripts(): FormArray {
    return this.form.get('scripts') as FormArray;
  }

  startEditing(index: number): void {
    this.editingIndex = index;
    const script = this.interactionVoiceovers[index].script;
    const lineCount = (script.match(/\n/g) || []).length + 1;
    const initialHeight = Math.max(6, Math.min(lineCount * 1.2, 18));
    
    setTimeout(() => {
      const textarea = document.querySelector('.voiceover-edit-textarea') as HTMLTextAreaElement;
      if (textarea) {
        textarea.style.width = '60%';
        textarea.style.height = `${initialHeight}rem`;
      }
    });
  }

  stopEditing(event:Event): void {
    event.stopPropagation();
    this.editingIndex = -1;
  }

  updateScript(index: number, event:Event): void {
    const script = this.scripts.at(index).value;
    const interaction = this.interactionVoiceovers[index];
    
    const element = {
      script: script,
      url: interaction.audioUrl,
      fileType: 'audio/mp3',
      entryId: interaction.entryId
    };
    if(!interaction.entryId) {
      setTimeout(() => {
        alert('Entry ID is not found, update the voiceover failed.');
        this.scripts.at(index).setValue(interaction.script);
      });
      return;
    }

    // Update state through service
    this.voiceoverState.updateVoiceoverContent(
      interaction.entryId,
      interaction.script
    );

    this.scriptChange.emit({ index, element });
    
    // If the parent component returns the original script (due to empty input),
    // we'll update our form control
    if (!script) {
      setTimeout(() => {
        this.scripts.at(index).setValue(interaction.script);
      });
    }
    
    this.stopEditing(event);
  }

  onKeyDown(event: KeyboardEvent, index: number): void {
    // if (event.key === 'Enter' && !event.shiftKey) {
    //   event.preventDefault();
    //   this.updateScript(index);
    // }
    if (event.key === 'Escape') {
      this.editingIndex = -1;
    }
  }

  ngOnDestroy() {
    if (this.voiceoverSubscription) {
      this.voiceoverSubscription.unsubscribe();
    }
  }

  truncateText(text: string, maxLength: number = 50): string {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  }

  getSecureUrl(entryId: string, url: string, script: string): SafeResourceUrl {
    if (!this.secureUrls[`${script}_${entryId}`]) {
      this.secureUrls[`${script}_${entryId}`] = this.sanitizer.bypassSecurityTrustResourceUrl(url);
    }
    return this.secureUrls[`${script}_${entryId}`];
  }

  cancelEditing(event:Event): void {
    event.stopPropagation();
    this.editingIndex = -1;
  }
}
