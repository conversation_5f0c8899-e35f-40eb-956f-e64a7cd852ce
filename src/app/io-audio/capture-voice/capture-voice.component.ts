declare var MediaRecorder: any;

import { Component, OnInit, ViewChild, ElementRef, Input, EventEmitter, Output, OnChanges, SimpleChanges } from '@angular/core';
import { MicService } from '../../microphone/mic.service';
import { AuthService } from '../../api/auth.service';
import { bindFormControls } from '../../ui-item-maker/services/data-bind';
import { FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { RoutesService } from '../../api/routes.service';
import { LangService } from '../../core/lang.service';
import { ScriptGenService } from '../../ui-item-maker/script-gen.service';
import { DEFAULT_VOICEOVER_PROP } from 'src/app/ui-item-maker/element-config-mcq-option-info/element-config-mcq-option-info.component';
import { IContentElement,IVoiceover } from 'src/app/ui-testrunner/models';
import { IQuestionConfig } from 'src/app/ui-item-maker/item-set-editor/models';
import { Subscription } from 'rxjs';
import { ItemBankCtrl } from 'src/app/ui-item-maker/item-set-editor/controllers/item-bank';
import { InteractionVoiceover, VoiceoverStateService } from 'src/app/ui-testrunner/voiceover-state.service';
import { replaceS3Domains } from 'src/app/core/util/transform';

import { ItemBankSaveLoadCtrl } from 'src/app/ui-item-maker/item-set-editor/controllers/save-load';
import { ScriptAlertStateMachine } from '../script-alert-state-machines/script-alert.state';
export const getVoiceChange = (el: IContentElement | IQuestionConfig, voiceoverProp: string = DEFAULT_VOICEOVER_PROP) => {
  if (el && el[voiceoverProp]){
    return el[voiceoverProp].url + el[voiceoverProp].script
  }
}

export const getElVoice =  (el: IContentElement | IQuestionConfig, voiceoverProp: string = DEFAULT_VOICEOVER_PROP) => {
  if (!el[voiceoverProp]) {
    el[voiceoverProp] = {script:'', url:'', entryId:''}
  }
  return el[voiceoverProp]
}



@Component({
  selector: 'capture-voice',
  templateUrl: './capture-voice.component.html',
  styleUrls: ['./capture-voice.component.scss']
})
export class CaptureVoiceComponent implements OnInit, OnChanges {

  @ViewChild('recorder', { static: false }) recorder:ElementRef<HTMLElement>;
  @ViewChild('player', { static: false }) player:ElementRef<AudioNode>;

  @Input() element:any = {};
  @Input() title:string;
  @Input() isResponse:boolean;
  @Input() isSmall:boolean;
  @Input() isMagicEnabled:boolean = true;
  @Input() recordEndCallback:Function;
  @Input() scriptOverride:string;
  @Input() urlOverride:string;
  @Input() langOverride:string;
  @Input() scriptChanges:any;
  @Input() startNow?:boolean;
  @Input() isDisabled:boolean;
  @Input() fromItemVoiceOver?: boolean;
  @Input() itemBankCtrl:ItemBankCtrl;
  @Output() notifyRecordingStart = new EventEmitter();
  @Input() triggerMagic:Function;
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl;
  @Output() urlUpdate = new EventEmitter();


  constructor(
    private mic: MicService,
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
    private sanitizer: DomSanitizer,
    private scriptGen: ScriptGenService,
    private voiceoverState: VoiceoverStateService
  ) {
    this.alertStateMachine = new ScriptAlertStateMachine(this);
  }

  isEditing:boolean = false;
  isRecording:boolean = false;
  isUploading:boolean = false;
  shouldStop:boolean = false;
  isStopped:boolean = false;
  isShowingSpeechMarksMeta = new FormControl(false);
  mediaRecorder:any;
  script = new FormControl();
  speechMarks = new FormControl()
  audioUrl;
  playbackSpeed = new FormControl('1');
  interactionVoiceovers: InteractionVoiceover[] = [];
  private voiceoverSubscription: Subscription;
  
  // Script state tracking
  scriptState = {
    lastSaved: '',
    lastGenerated: '',
    hasUnsavedChanges: false
  };
  showScriptChangeAlert: boolean = false;
  scriptAlertMessage: string = '';
  genScriptMsg = 'To generate a script click on the magic wand button <i class="fa fa-magic"></i>.';
  saveScriptMsg = 'Your script has been modified but not saved. Please click the upload button <i class="fa fa-upload"></i> to save your changes.';
  
  // Script alert state machine
  private alertStateMachine: ScriptAlertStateMachine;
  
  ngOnInit() {
    if (this.scriptOverride){
      this.element.script = this.scriptOverride;
      this.script.disable();
    }
    if (this.urlOverride){
      this.element.url = this.urlOverride
    }
    bindFormControls(this.element, [
      {f: this.script, p:'script' },
    ]);
    this.updateFormElements();
    this.playbackSpeed.valueChanges.subscribe(()=>{
      const el:HTMLMediaElement = <any> this.player.nativeElement;
      if (el){
        el.playbackRate = +this.playbackSpeed.value;
      }
    });

    // Track script changes
    this.script.valueChanges.subscribe(value => {
      this.alertStateMachine.evaluateScript(value);
    });
    
    this.voiceoverSubscription = this.voiceoverState.voiceovers$
    .subscribe(voiceovers => {
      if (JSON.stringify(voiceovers) !== JSON.stringify(this.interactionVoiceovers)) {
        this.interactionVoiceovers = [...voiceovers];
      }
      if (this.element?.entryId) {
        const voiceover = voiceovers.find(v => v.entryId === this.element.entryId);
        if (voiceover) {
          this.element.script = voiceover.script;
          this.element.url = voiceover.audioUrl;
          this.updateFormElements();
        }
      }
    });
    if( this.fromItemVoiceOver && this.itemBankCtrl){
      // Force refresh voiceovers from current question
      this.refreshVoiceovers();
    }
  }

  ngOnDestroy() {
    if (this.voiceoverSubscription) {
      this.voiceoverSubscription.unsubscribe();
    }
  }

  refreshVoiceovers() {
    const currentQuestion = this.itemBankCtrl.getCurrentQuestionContent();
    //Ensure all voiceover objects have entryId through the autoSaveQuestionData 
    if(this.saveLoadCtrl){
      this.saveLoadCtrl.autoSaveQuestionData();
    }
    this.voiceoverState.clearInteractions();
    this.voiceoverState.generateNestedVoiceovers(currentQuestion);
  }

  updateFormElements(){
    // console.log('updateFormElements')
    this.updateScript();
    this.updateSpeechMarks();
    this.updateAudioUrl();
    this.SynchronizeScriptState();
  }

  SynchronizeScriptState() {
    // Synchronize script state with current values
    if (this.element && this.element.script) {
      this.scriptState.lastSaved = this.element.script;
    }
    
    // Evaluate current script value with state machine
    this.alertStateMachine.evaluateScript(this.script.value);
  }

  

  async onTriggerMagic(upload:boolean = true){
    // Update script state
    this.scriptState.lastGenerated = this.script.value || '';
    this.scriptState.hasUnsavedChanges = false;
    this.alertStateMachine.reset();
    
    // Save question data before triggering magic to ensure the entryId is updated
    if(this.saveLoadCtrl){
      await this.saveLoadCtrl.autoSaveQuestionData();
    }
    await this.triggerMagic();
    this.updateScript();
    
    if(upload) {
      await this.runTextToVoice(this.fromItemVoiceOver);
    }

    // Refresh voiceovers after magic generation
    if (this.fromItemVoiceOver && this.itemBankCtrl) {
      this.refreshVoiceovers();
    }
  }

  recorderEl;
  ngOnChanges(changes:SimpleChanges) {
    if (changes.element){
      bindFormControls(this.element, [
        {f: this.script, p:'script' },
      ]);
      this.updateFormElements();
    }
    if (changes.scriptOverride){
      this.script.setValue(changes.scriptOverride.currentValue);
    }
    if (changes.scriptChanges){
      this.updateScript();
      this.updateAudioUrl();
    }
    if (changes.isDisabled){
      if (this.isDisabled){
        this.script.disable();
      }
      else{
        this.script.enable();
      }
    }
    // if (changes.startNow) {
    //   if (this.startNow) {
    //     this.startRecording();
    //   }
    // }
    // if (this.element && this.recorder && this.recorderEl !== this.recorder.nativeElement){
    //   this.recorderEl = this.recorder.nativeElement;
    //   this.recorderEl.addEventListener('change', (e) => {
    //     const file = (<any>e.target).files[0];
    //     this.auth
    //       .uploadFile(file, file.name, 'voice', true)
    //       .then(res => {
    //         this.element.url = res.url;
    //         this.updateAudioUrl();
    //       })
    //   });
    // }
  }

  toggleEditing(){
    this.isEditing = !this.isEditing;
  }

  startRecording = () => {
    if (this.isRecording) return;
    this.isRecording = true;
    navigator
      .mediaDevices
      .getUserMedia({ audio: true, video: false })
      .then(this.startStream);
    this.notifyRecordingStart.emit();
  };

  stopRecording = () => {
    this.mediaRecorder.stop();
    this.isRecording = false;
    if(this.recordEndCallback){
      this.recordEndCallback();
    }
  };

  hasAudioUrl(){
    return !!this.element.url;
  }

  getAudioUrl(){
    return this.audioUrl;
  }

  updateScript(){
    if (this.element && this.element.script) {
      this.script.setValue(this.element.script);
    } else {
      this.script.reset();
    }
    
    // Let the state machine evaluate and update UI
    this.alertStateMachine.evaluateScript(this.script.value);
  }
  updateAudioUrl(){
    if (this.element && this.element.url){
      this.element.url = replaceS3Domains(this.element.url)
      this.audioUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.element.url);
    }
    else{
      this.audioUrl = null;
    }
    this.urlUpdate.emit({url: this.element.url});
  }

  updateSpeechMarks() {
    if (this.element && this.element.speechMarks){
      this.speechMarks.setValue(JSON.stringify(this.element.speechMarks)) ;
    }
    else{
      this.speechMarks.reset();
    }
  }

  updateSpeechMarksInElement($event) {
    if(this.speechMarks.value) {
      this.element.speechMarks = JSON.parse(this.speechMarks.value)
    }
  }


  startStream = (stream) => {
    const options = {mimeType: 'audio/webm'};
    const recordedChunks = [];
    this.mediaRecorder = new MediaRecorder(stream, options);
    this.mediaRecorder.addEventListener('dataavailable', (e) => {
      if (e.data.size > 0) {
        recordedChunks.push(e.data);
      }
    });
    this.mediaRecorder.addEventListener('stop', () => {
      this.isUploading = true;
      const blob = new Blob(recordedChunks, { 'type' : 'audio/webm' });
      const file = new File([blob], 'voice.wav');
      this.auth
        .uploadFile(file, file.name, 'authoring', true)
        .then(res => {
          this.element.fileType = 'audio/webm';
          this.element.url = res.url;
          this.isUploading = false;
          this.updateAudioUrl();

          if(this.recordEndCallback){
            this.recordEndCallback();
          }

        })
    });
    this.mediaRecorder.start();
  };

  async onInteractionScriptChange(event: {
    index: number, 
    element: {
      script: string,
      url: string,
      fileType: string,
      entryId: number
    }
  }) {
      // If the script is empty, don't update anything and revert back
    if (!event.element.script.trim()) {
      alert('Enter the script below to send it for text-to-voice processing.');
      // Reset the form control to the original script
      const originalScript = this.interactionVoiceovers[event.index].script;
      event.element.script = originalScript;
      this.voiceoverState.updateVoiceoverContent(
        event.element.entryId,
        originalScript
      );
    }
    // Update the script in the local array
    this.interactionVoiceovers[event.index].script = event.element.script;
    
    // Mark as not synced before voice processing
    this.interactionVoiceovers[event.index].synced = false;
    
    // Run text to voice with the new element
    await this.runTextToVoice(true, event.element, true);
  }

  isRunningTextToVoice:boolean;
  async runTextToVoice(fromItemVoiceOver?: boolean, interactionElement?: any, fromInteraction?: boolean) {
    let currentLang = this.lang.c();
    if (this.langOverride) {
      this.lang.setCurrentLanguage(this.langOverride);
    }
    
    // Reset script state tracking for the main element
    if (!interactionElement) {
      this.scriptState.lastSaved = this.script.value || '';
      this.scriptState.hasUnsavedChanges = false;
      this.alertStateMachine.reset();
    }
    
    this.isRunningTextToVoice = true;
    
    // Use either the interaction element or the main element
    const elementToUse = interactionElement || this.element;
    const scriptType = (<IVoiceover>this.element).scriptType;
    const isScriptTypeText = scriptType === 'text';
    const script = interactionElement ? 
      interactionElement.script : 
      (isScriptTypeText ? (this.script.value || this.element.script) : this.element.ssml);

    // Check for empty script when script type is text
    if (isScriptTypeText && !script && !interactionElement) {
      alert('Enter the script below to send it for text-to-voice processing.');
      this.isRunningTextToVoice = false;
      return;
    }

    try {
      await this.scriptGen.uploadNewVoice(
        script,
        elementToUse,
        this.lang.c(),
        fromItemVoiceOver,
      );
      
      // Update state if element has entryId
      const entryId = elementToUse.entryId;
      if (entryId) {
        this.voiceoverState.updateVoiceoverContent(
          entryId,
          script,
          elementToUse.url
        );
      }

      // Update local audio URL if this is the main element
      if (!interactionElement) {
        this.updateAudioUrl();
        this.updateSpeechMarks();
      }

      //If this is not from an interaction table, refresh voiceovers
      if (this.fromItemVoiceOver && this.itemBankCtrl && !fromInteraction) {
        this.refreshVoiceovers();
      }

      //Update the script in the template element from interaction table
      //For elements that are templates, and the template element does not contain elements of capture voice, 
      //we need to pass in the address of currentQuestion to modify the corresponding voice over.
      if(this.itemBankCtrl && fromInteraction){
        let currentQuestion = this.itemBankCtrl.getCurrentQuestionContent();
        let updatedVoiceover = {
          script: script,
          audioUrl: elementToUse.url,
          // synced: true,
          entryId: entryId
        } as InteractionVoiceover;
        if(!entryId || !updatedVoiceover.entryId) {
          console.log('Entry ID is not found, update the voiceover failed.');
          return;
        }
        
        this.voiceoverState.updateVoiceoverInTemplateItem(currentQuestion, updatedVoiceover);
      }

    } finally {
      this.isRunningTextToVoice = false;
      if (currentLang !== this.lang.c()) {
        this.lang.setCurrentLanguage(currentLang);
      }
    }
  }
}
