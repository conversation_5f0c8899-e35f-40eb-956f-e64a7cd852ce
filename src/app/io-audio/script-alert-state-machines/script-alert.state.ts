import { CaptureVoiceComponent } from "../capture-voice/capture-voice.component";

export enum ScriptAlertState {
    NoAlert,
    GenerateScriptAlert,
    SaveChangesAlert
}

export class ScriptAlertStateMachine {
    private currentState: ScriptAlertState = ScriptAlertState.NoAlert;
    private component: CaptureVoiceComponent;
    
    constructor(component: CaptureVoiceComponent) {
      this.component = component;
    }
    
    // Get current state
    get state(): ScriptAlertState {
      return this.currentState;
    }
    
    // Evaluate script content and determine state
    evaluateScript(value: string): void {
      const element = this.component.element;
      const scriptState = this.component.scriptState;
      const fromItemVoiceOver = this.component.fromItemVoiceOver;
      
      // Check if script is empty and element.script is also empty
      if ((!value || value.trim() === '') && (!element.script || element.script.trim() === '')) {
        this.transitionTo(ScriptAlertState.GenerateScriptAlert);
        return;
      }
      
      // If current value matches either the last saved or last generated value,
      // then we don't need to show the warning
      if ((scriptState.lastSaved && value === scriptState.lastSaved) || 
          (scriptState.lastGenerated && value === scriptState.lastGenerated)) {
        this.transitionTo(ScriptAlertState.NoAlert);
      } else if (fromItemVoiceOver && (scriptState.lastSaved || scriptState.lastGenerated)) {
        // Only show warning if we have a baseline to compare against (lastSaved or lastGenerated)
        this.transitionTo(ScriptAlertState.SaveChangesAlert);
      }
    }
    
    // Force transition to a specific state
    transitionTo(newState: ScriptAlertState): void {
      if (this.currentState !== newState) {
        this.currentState = newState;
        this.updateComponent();
      }
    }
    
    // Reset state machine to NoAlert state
    reset(): void {
      this.transitionTo(ScriptAlertState.NoAlert);
    }
    
    // Update component UI based on current state
    private updateComponent(): void {
      switch (this.currentState) {
        case ScriptAlertState.NoAlert:
          this.component.showScriptChangeAlert = false;
          this.component.scriptAlertMessage = '';
          break;
        
        case ScriptAlertState.GenerateScriptAlert:
          this.component.showScriptChangeAlert = true;
          this.component.scriptAlertMessage = this.component.genScriptMsg;
          break;
        
        case ScriptAlertState.SaveChangesAlert:
          this.component.showScriptChangeAlert = true;
          this.component.scriptAlertMessage = this.component.saveScriptMsg;
          break;
      }
    }
  }