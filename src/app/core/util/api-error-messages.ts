import { LangService } from '../lang.service';

/**
 * Options for API error message to slug conversion
 */
export interface ApiErrorMsgOptions {
  /** Whether this is a primary assessment context */
  isPrimary?: boolean;
  /** Whether this is a junior assessment context */
  isJunior?: boolean;
}

/**
 * Converts API error messages to translation slugs
 * Based on apiErrMsgToSlug from sa-students.component.ts and view-s-student-meta-update.component.ts
 * 
 * @param errMsg - The error message from the API
 * @param langService - The language service for translations
 * @param options - Optional context options for primary/junior assessments
 * @returns The translation slug for the error message
 */
export function apiErrMsgToSlug(
  errMsg: string, 
  langService: LangService, 
  options: ApiErrorMsgOptions = {}
): string {
  const { isPrimary = false, isJunior = false } = options;

  // FirstName
  if (errMsg === 'FirstName_NOT_NULL') {
    if (isPrimary || isJunior) {
      return 'brc_pj_firstname';
    }
    return 'brc_firstname';
  }
  if (errMsg === 'FirstName_CHAR_LEN_RANGE') {
    return 'brc_firstname_char_len_range';
  }

  // LastName
  if (errMsg === 'LastName_NOT_NULL') {
    if (isPrimary || isJunior) {
      return 'brc_pj_lastname';
    }
    return 'brc_lastname';
  }
  if (errMsg === 'LastName_CHAR_LEN_RANGE') {
    return 'brc_lastname_char_len_range';
  }

  // Student Type
  if (errMsg === 'StudentType_VAL_VALIDATE') {
    return 'brc_studenttype_val_invalid_1';
  }

  // StudentOEN
  if (errMsg === 'StudentOEN_NOT_NULL' || errMsg === 'StudentOEN_CHAR_LEN_RANGE' || errMsg === 'StudentOEN_CHAR_PATTERN' ||
      errMsg === 'StudentOEN_VAL_RANGE' || errMsg === 'StudentOEN_OEN_UNIQUE1') {
    return 'brc_student_oen_1';
  }
  if (errMsg === 'StudentOEN_OEN_UNIQUE2') {
    return 'brc_student_oen_2';
  }
  if (errMsg === 'StudentOEN_VAL_RANGE2') {
    return 'brc_student_oen_3';
  }

  // SASN
  if (errMsg === 'SASN_NOT_NULL') {
    return 'brc_student_sasn_1';
  }
  if (errMsg === 'SASN_VAL_RANGE') {
    return 'brc_sasn';
  }
  if (errMsg === 'SASN_UNIQUE') {
    return 'brc_sasn_unique';
  }

  // DateofBirth
  if (errMsg === 'DateofBirth_NOT_NULL' || errMsg === 'DateofBirth_CHAR_LEN_RANGE' || errMsg === 'DateofBirth_DATE_VALIDATE') {
    return 'brc_dateofbirth_1';
  }
  if (errMsg === 'DateofBirth_DATE_DIFF_GREATER') {
    return 'brc_dateofbirth_2';
  }
  if (errMsg === 'DateofBirth_DATE_DIFF_GREATER_OSSLT') {
    return 'brc_dateofbirth_6';
  }
  if (errMsg === 'DateofBirth_DATE_DIFF_SMALLER') {
    return 'brc_dateofbirth_3';
  }
  if (errMsg === 'DateofBirth_DATE_DIFF_GREATER_PJ') {
    return 'brc_dateofbirth_4';
  }
  if (errMsg === 'DateofBirth_DATE_DIFF_SMALLER_PJ') {
    return 'brc_dateofbirth_5';
  }

  // Gender
  if (errMsg === 'Gender_NOT_NULL' || errMsg === 'Gender_CHAR_LEN_RANGE' || errMsg === 'Gender_VAL_RANGE') {
    return 'brc_gender';
  }

  // ClassCode
  if (errMsg === 'ClassCode_NOT_NULL' || errMsg === 'ClassCode_VALIDE_CLASS') {
    return 'brc_classcode';
  }
  if (errMsg === 'ClassCode_VALIDE_CLASS_2') {
    return 'brc_classcode_3';
  }
  if (errMsg === 'ClassCode_CHAR_LEN_RANGE') {
    return 'brc_classcode_2';
  }

  // TermFormat
  if (errMsg === 'TermFormat_VAL_1') {
    return 'brc_mathclasswhen_2';
  }

  // LearningFormat
  if (errMsg === 'LearningFormat_NOT_NULL' || errMsg === 'LearningFormat_CHAR_LEN_RANGE' || errMsg === 'LearningFormat_VAL_RANGE') {
    return 'brc_learningformat';
  }

  // Grouping
  if (errMsg === 'Grouping_NOT_NULL' || errMsg === 'Grouping_VALIDE_GRPING') {
    return 'brc_grouping';
  }
  if (errMsg === 'Grouping_VALIDE_GRPING_2') {
    return 'brc_grouping_3';
  }
  if (errMsg === 'Grouping_CHAR_LEN_RANGE') {
    return 'brc_grouping_2';
  }

  // Homeroom
  if (errMsg === 'Homeroom_NOT_NULL' || errMsg === 'Homeroom_VALIDE_HOMEROOM') {
    return 'brc_homeroom';
  }
  if (errMsg === 'Homeroom_CHAR_LEN_RANGE') {
    return 'brc_homeroom_char_len_range';
  }

  // Grade
  if (errMsg === 'Grade3_VAL_RANGE' || errMsg === 'Grade6_VAL_RANGE') {
    return 'brc_grade_value';
  }

  // DateEnteredSchool
  if (errMsg === 'DateEnteredSchool_CHAR_LEN_RANGE' || errMsg === 'DateEnteredSchool_DATE_VALIDATE') {
    return 'brc_dateenteredschool_1';
  }
  if (errMsg === 'DateEnteredSchool_DATE_DIFF_SMALLER') {
    return 'brc_dateenteredschool_2';
  }
  if (errMsg === 'DateEnteredSchool_DATE_DIFF_SMALLER2') {
    return 'brc_dateenteredschool_3';
  }
  if (errMsg === 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY') {
    return 'brc_dateenteredschool_4';
  }
  if (errMsg === 'DateEnteredSchool_DATE_SMALLER_THAN_TW_END_DATE') {
    return 'brc_dateenteredschool_5';
  }

  // DateEnteredBoard
  if (errMsg === 'DateEnteredBoard_CHAR_LEN_RANGE' || errMsg === 'DateEnteredBoard_DATE_VALIDATE') {
    return 'brc_dateenteredboard_1';
  }
  if (errMsg === 'DateEnteredBoard_DATE_DIFF_SMALLER') {
    return 'brc_dateenteredboard_2';
  }
  if (errMsg === 'DateEnteredBoard_DATE_GREATER') {
    return 'brc_dateenteredboard_3';
  }
  if (errMsg === 'DateEnteredBoard_DATE_DIFF_SMALLER2') {
    return 'brc_dateenteredboard_4';
  }
  if (errMsg === 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY') {
    return 'brc_dateenteredboard_5';
  }
  if (errMsg === 'DateEnteredBoard_DATE_SMALLER_THAN_TW_END_DATE') {
    return 'brc_dateenteredboard_6';
  }

  // BornOutsideCanada
  if (errMsg === 'BornOutsideCanada_VAL_VALIDATE') {
    return 'brc_born_outside_canada_err_2';
  }
  if (errMsg === 'BornOutsideCanada_VAL_VALIDATE_2') {
    return 'brc_born_outside_canada_err_1';
  }

  // EligibilityStatus
  if (errMsg === 'EligibilityStatus_NOT_NULL' || errMsg === 'EligibilityStatus_CHAR_LEN_RANGE' || errMsg === 'EligibilityStatus_VAL_RANGE') {
    return 'brc_eligibilitystatus_1';
  }
  if (errMsg === 'EligibilityStatus_VAL_VALIDATE') {
    return 'brc_eligibilitystatus_2';
  }

  // LevelofStudyLanguage
  if (errMsg === 'LevelofStudyLanguage_VAL_VALIDATE') {
    return 'brc_levelofstudylanguage';
  }
  if (errMsg === 'LevelofStudyLanguage_VAL_VALIDATE_2') {
    return 'brc_levelofstudylanguage_2';
  }

  // DateOfFTE
  if (errMsg === 'DateOfFTE_VAL_VALIDATE') {
    return 'brc_dateoffte';
  }
  if (errMsg === 'DateOfFTE_VAL_VALIDATE_2') {
    return 'brc_dateoffte_2';
  }

  // Graduation
  if (errMsg === 'Graduating_VAL_VALIDATE') {
    return 'brc_grad_eligstatus';
  }

  // AccBraille
  if (errMsg === 'AccBraille_VAL_RANGE_2' || errMsg === 'AccBraille_CHAR_LEN_RANGE' || errMsg === 'AccBraille_VAL_RANGE') {
    return 'brc_braille_invalid';
  }

  // AccScribing_VAL_VALIDATE
  if (errMsg === 'AccScribing_VAL_RANGE') {
    return 'brc_scribing_invalid';
  }

  // IEP
  if (errMsg === 'IEP_VAL_VALIDATE') {
    return 'brc_iep_1';
  }
  if (errMsg === 'IEP_VAL_VALIDATE_2') {
    return 'brc_iep_2';
  }
  if (errMsg === 'IEP_VAL_VALIDATE_3') {
    return 'brc_iep_3';
  }
  if (errMsg === 'IEP_VAL_VALIDATE_4') {
    return 'brc_iep_4';
  }
  if (errMsg === 'IEP_VAL_VALIDATE_5') {
    return 'brc_iep_5';
  }

  // ELL
  if (errMsg === 'ELL_VAL_VALIDATE_1') {
    return 'brc_ell_1';
  }

  // LanguageLearner
  if (errMsg === 'LanguageLearner_VAL_VALIDATE_2' || errMsg === 'LanguageLearner_VAL_VALIDATE_3') {
    return 'brc_langlearner_2_3';
  }

  // AccBraille AccBreaks AccSign AccAudioResponse AccVideotapeResponse AccScribing AccAltVersion AccOther
  if (errMsg === 'AccBraille_VAL_VALIDATE' || errMsg === 'AccAudioVersion_VAL_VALIDATE' || errMsg === 'AccBreaks_VAL_VALIDATE' || errMsg === 'AccSign_VAL_VALIDATE' ||
      errMsg === 'AccAudioResponse_VAL_VALIDATE' || errMsg === 'AccVideotapeResponse_VAL_VALIDATE' || errMsg === 'AccScribing_VAL_VALIDATE' ||
      errMsg === 'AccAltVersion_VAL_VALIDATE' || errMsg === 'AccOther_VAL_VALIDATE' || errMsg === 'AccAssistiveTech_VAL_VALIDATE') {
    return 'brc_accbraille';
  }

  // PJ AccAssistiveTech_VAL_VALIDATE, AccBraille_VAL_VALIDATE_PJ, AccAudioVersion_VAL_VALIDATE_PJ, AccSign_VAL_VALIDATE_PJ, AccScribing_VAL_VALIDATE_PJ
  if (errMsg === 'AccAssistiveTech_VAL_VALIDATE_PJ' || errMsg === 'AccBraille_VAL_VALIDATE_PJ' || errMsg === 'AccAudioVersion_VAL_VALIDATE_PJ' ||
      errMsg === 'AccSign_VAL_VALIDATE_PJ' || errMsg === 'AccScribing_VAL_VALIDATE_PJ' || errMsg === 'AccAltVersion_VAL_VALIDATE_PJ') {
    return 'brc_accbraille_pj';
  }
  if (errMsg === 'AccAssistiveTech_VAL_VALIDATE2') {
    return 'brc_acctech_validate_2';
  }
  if (errMsg === 'AccAssistiveTech_VAL_VALIDATE3') {
    return 'brc_acctech_validate_3';
  }

  // AccScribing_VAL_VALIDATE_2
  if (errMsg === 'AccBraille_VAL_VALIDATE_2') {
    return 'brc_accbraille_2';
  }

  // AccAudioVersion_VAL_VALIDATE_2
  if (errMsg === 'AccAudioVersion_VAL_VALIDATE_2') {
    return 'brc_accaudioversion_2';
  }

  // Exemption_VAL_VALIDATE
  if (errMsg === 'Exemption_VAL_VALIDATE') {
    return 'brc_exemption';
  }

  // Exemption_VAL_VALIDATE_2
  if (errMsg === 'Exemption_VAL_VALIDATE_2') {
    return 'brc_exemption_2';
  }

  // Exemption_VAL_VALIDATE_3
  if (errMsg === 'Exemption_VAL_VALIDATE_3') {
    return 'brc_exemption_3';
  }

  // SpecEdNoExpectationReadWrite
  if (errMsg === 'SpecEdNoExpectationReadWrite_VAL_VALIDATE') {
    return 'brc_spec_no_expectation_readwrite';
  }

  // SpecEdNoExpectationMath
  if (errMsg === 'SpecEdNoExpectationMath_VAL_VALIDATE') {
    return 'brc_spec_no_expectation_math';
  }

  // SpecPermIEP SpecPermTemp SpecPermMoved LanguageLearnerEarly
  if (errMsg === 'SpecPermIEP_VAL_VALIDATE' || errMsg === 'SpecPermTemp_VAL_VALIDATE' || errMsg === 'SpecPermMoved_VAL_VALIDATE' || errMsg === 'LanguageLearnerEarly_VAL_VALIDATE') {
    return 'brc_specpermiep';
  }

  // SpecProvBreaks
  if (errMsg === 'SpecProvBreaks_VAL_VALIDATE') {
    return 'brc_specprovbreaks';
  }

  // NonParticipationStatus
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE') {
    return 'brc_nonparticipationstatus_1';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_2') {
    return 'brc_nonparticipationstatus_2';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_3') {
    return 'brc_nonparticipationstatus_3';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_4') {
    return 'brc_exempt_iep';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_5') {
    return 'brc_nonparticipationstatus_5';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_6') {
    return 'brc_nonparticipationstatus_6';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_7') {
    return 'brc_nonparticipationstatus_7';
  }
  if (errMsg === 'NonParticipationStatus_VAL_VALIDATE_8') {
    return 'brc_nonparticipationstatus_8';
  }

  // TermFormat additional validation
  if (errMsg === 'TermFormat_NOT_NULL' || errMsg === 'TermFormat_CHAR_LEN_RANGE' || errMsg === 'TermFormat_VAL_RANGE') {
    return 'brc_error_popup_semestering_model';
  }

  // Generic fallback for common error patterns
  if (errMsg.includes('NOT_NULL') || errMsg.includes('_NEED_TO_BE_NULL') || errMsg.includes('CHAR_LEN_RANGE') || errMsg.includes('VAL_RANGE')) {
    const fieldName = errMsg.split('_')[0];
    return langService.tra('brc_char_len_range', langService.c(), { FieldName: fieldName });
  }

  // Return original error message if no mapping found
  return errMsg;
}
