// const taqr_nr_clause = `taqr.is_nr is null`; //

export const SQL_SCAN_TAQR_REVIEWED = () => {
  return `
  select *
  from (
      select mw_id
          , taqr_id
          , sum(case when srbr_n >= 1 then 1 else 0 end) review_1
          , sum(case when srbr_n >= 2 then 1 else 0 end) review_2
      from (
          select srtp.mw_id, srtp.taqr_id, count(srbr.id) srbr_n, ifnull(max(srbr.is_flagged), 0) is_flagged
          from scan_review_taqr_pool srtp
          left join scan_review_batch_responses srbr
              on srbr.taqr_id = srtp.taqr_id
              and srbr.is_complete = 1
              and srbr.is_revoked = 0
          where srtp.is_locked = 0
          group by srtp.mw_id, srtp.taqr_id
      ) t
      where is_flagged = 0
      group by taqr_id
  ) t2
  where review_2 = 1
  ;
  `
};

// [marking_window_id]
export const SQL_SCAN_REVIEW_TAQR_POOL = (include_recent_reuploads: boolean, include_sample_schools: boolean, include_unclosed_test_attempts: boolean, days: number) => {
  return `
  select /*+ MAX_EXECUTION_TIME(1440000000)*/
        test_question_id
      , taqr_id
      , tw_id
      , mw_id
      , cached_tasr_id
      , scan_uploaded_on
      , pooled_on
      , srtp_id
  from (
      select taqr.test_question_id
          , taqr.id taqr_id
          , mwtw.test_window_id tw_id
          , mwtw.marking_window_id mw_id
          , srtp.id srtp_id
          , tasr.id cached_tasr_id
          , tasr.uploaded_on as scan_uploaded_on
          , srtp.created_on as pooled_on
      from marking_window_items_v2 mwi 
      join marking_window_test_window mwtw  
        on mwtw.marking_window_id = mwi.marking_window_id
      join test_sessions ts
        on ts.test_window_id = mwtw.test_window_id
        ${include_unclosed_test_attempts ? '' : 'and ts.is_closed = 1'}
        join school_class_test_sessions scts
        on scts.test_session_id = ts.id
      join school_classes sc
        on sc.id = scts.school_class_id
      join schools s
        on s.group_id = sc.schl_group_id
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
        ${include_sample_schools ? `` : `and sd.is_sample = 0`}
      join test_attempts ta
        on ts.id = ta.test_session_id
        and ta.started_on is not null
      join test_attempt_question_responses taqr
        on mwi.item_id = taqr.test_question_id
        and ta.id = taqr.test_attempt_id
      join test_attempt_scan_responses tasr
        on tasr.taqr_id = taqr.id
        and tasr.is_discarded = 0
        and tasr.is_pooled = 0
      left join scan_review_taqr_pool srtp
          on srtp.taqr_id  = tasr.taqr_id
      where mwi.marking_window_id in (?)
      group by tasr.taqr_id
  ) t
  where srtp_id is null
  ${include_recent_reuploads ? `or (pooled_on < scan_uploaded_on && scan_uploaded_on > NOW() - INTERVAL ${days} DAY)` : ``}
  ;
  `
};

// [marking_window_id]
export const SQL_MARKING_WINDOW_TAQR_RAW = (bypass_scan_review:boolean, include_unclosed_test_attempts: boolean, include_sample_schools: boolean, is_ft_pool_blocked:boolean, is_non_ft_pool_blocked:boolean) => {
  return `
  select /*+ MAX_EXECUTION_TIME(1440000000)*/ taqr.id
     , taqr.marking_window_id
     , taqr.mwi_id
     , taqr.taqr_id
     , taqr.schl_group_id
     , taqr.is_material
     ${bypass_scan_review ? '' : ', srtp_id'}
     ${bypass_scan_review ? '' : ', is_scan_confirmed'}
  from (
      select taqr.*
         , mtc.id mtc_id
         ${bypass_scan_review ? '' : ', srtp.id srtp_id'}
         ${bypass_scan_review ? '' : ', srtp.is_locked is_scan_confirmed'}
        , tasr.id tasr_id
      from (
          select /*+ JOIN_INDEX(taqr question_attempt) */ taqr.id id
              , mwi.marking_window_id
              , mwi.id mwi_id
              , taqr.id taqr_id
              , taqr.is_nr
              , sc.schl_group_id
              , ifNull(mwtw.is_material, 0) is_material
             , taqr.is_paper_format
          from marking_window_items_v2 mwi 
          join marking_window_test_window mwtw  
            on mwtw.marking_window_id = mwi.marking_window_id
          join test_sessions ts
            on ts.test_window_id = mwtw.test_window_id
        ${include_unclosed_test_attempts ? '' : 'and ts.is_closed = 1'}
        ${include_unclosed_test_attempts ? '' : 'and ts.closed_on < DATE_ADD(NOW(), INTERVAL -1 DAY)'}
          join school_class_test_sessions scts
            on scts.test_session_id = ts.id
          join school_classes sc
            on sc.id = scts.school_class_id
          join schools s
            on s.group_id = sc.schl_group_id
          join school_districts sd
            on sd.group_id = s.schl_dist_group_id
            ${include_sample_schools ? `` : `and sd.is_sample = 0`}
          join test_attempts ta
            on ts.id = ta.test_session_id
        ${include_unclosed_test_attempts ? '' : 'and ta.is_closed = 1'}
            and ta.started_on is not null
          join test_attempt_question_responses taqr
            on mwi.item_id = taqr.test_question_id
            and ta.id = taqr.test_attempt_id
          where mwi.marking_window_id in (?)
            ${is_ft_pool_blocked ? 'and mwi.is_item_ft = 0' : ''}
            ${is_non_ft_pool_blocked ? 'and mwi.is_item_ft = 1' : ''}
            and taqr.is_invalid = 0
      ) taqr
      left join marking_taqr_cache mtc
        on mtc.taqr_id = taqr.id
        and mtc.mwi_id = taqr.mwi_id
      ${bypass_scan_review ? '' : 'left join scan_review_taqr_pool srtp on srtp.taqr_id = taqr.taqr_id'}
    left join test_attempt_scan_responses tasr on tasr.taqr_id = taqr.taqr_id
      group by taqr.id, taqr.mwi_id
  ) taqr
  where mtc_id is null
    and (
  ${bypass_scan_review
    ?
    '(taqr.is_paper_format = 0 and taqr.is_nr = 0) or (tasr_id is not null)'
    :
    'is_scan_confirmed = 1 or (srtp_id is null and taqr.is_paper_format = 0 and taqr.is_nr = 0) or (srtp_id is null and tasr_id is not null)'}
  )
  `;
}

// inner query
// select taqr.id id
//      , mwi.marking_window_id
//      , mwi.id mwi_id
//      , taqr.id taqr_id
//      , sc.schl_group_id
//      , ifNull(mwtw.is_material, 0) is_material
// from marking_window_items mwi
// join marking_window_test_window mwtw
//   on mwtw.marking_window_id = mwi.marking_window_id
// join test_sessions ts
//   on ts.test_window_id = mwtw.test_window_id
// join school_class_test_sessions scts
//   on scts.test_session_id = ts.id
// join school_classes sc
//   on sc.id = scts.school_class_id
// join test_attempts ta
//   on ts.id = ta.test_session_id
//   and ta.is_closed = 1
//   and ta.started_on is not null
// join test_attempt_question_responses taqr
//   on mwi.item_id = taqr.test_question_id
//   and ta.id = taqr.test_attempt_id
// where mwi.marking_window_id in (7,8)
//   and taqr.is_invalid = 0
//   and taqr.is_nr = 0
// `


`
select taqr.id
	 , taqr.marking_window_id
     , taqr.mwi_id
	 , taqr.taqr_id
	 , taqr.schl_group_id
	 , taqr.is_material
from (
	select taqr.*
	     , mtc.id mtc_id
	from (
		select taqr.id id
		     , mwi.marking_window_id
		     , mwi.id mwi_id
		     , taqr.id taqr_id
		     , sc.schl_group_id
		     , ifNull(mwtw.is_material, 0) is_material
		from marking_window_items_v2 mwi 
		join marking_window_test_window mwtw  
		  on mwtw.marking_window_id = mwi.marking_window_id
		join test_sessions ts
		  on ts.test_window_id = mwtw.test_window_id
		join school_class_test_sessions scts
		  on scts.test_session_id = ts.id
		join school_classes sc
		  on sc.id = scts.school_class_id
		join schools s
		  on s.group_id = sc.schl_group_id
		join school_districts sd
		  on sd.group_id = s.schl_dist_group_id
		  and sd.is_sample  = 0
		join test_attempts ta
		  on ts.id = ta.test_session_id
		  and ta.is_closed = 1
		  and ta.started_on is not null
		join test_attempt_question_responses taqr
		  on mwi.item_id = taqr.test_question_id
		  and ta.id = taqr.test_attempt_id
		where mwi.marking_window_id in (8)
		  and taqr.is_invalid = 0
		  and taqr.is_nr = 0
	) taqr
	left join marking_taqr_cache mtc on mtc.taqr_id = taqr.id and mtc.mwi_id = taqr.mwi_id
) taqr
where mtc_id is null
;
`

const marking_window_taqr_pre = (isMaterialsIncl : boolean = true) => `
  from marking_taqr_cache mtc
`;
// and ${taqr_nr_clause} and ${taqr_invalid_clause}
// and mtc.is_material = 0
const marking_window_taqr_post = `
    where mtc.mwi_id = ? -- TO BE FED IN BY API
`;

// < 0.3 sec
// {taqr_ids}
export const SQL_SCORES_FROM_LEADERS_BY_TAQR = `
select mrsel.taqr_id
     , mrsel.response_set_id
     , mrsel.score_option_id
     , mso_scor.slug score_slug
     , mso_scor.raw_score_value
     , mso_scor.value
     , mwi.id as mwi_id
     , mwi.score_profile_id
     , mwi.slug item_slug
     , mwi.skill_code
     , max(log.created_on) as marked_on
from marking_response_selections mrsel 
join marking_taqr_cache mtc 
	on mtc.taqr_id = mrsel.taqr_id
	and (mtc.is_material = 0 or mtc.is_material is null)
join marking_window_items_v2 mwi 
	on mwi.id = mrsel.window_item_id
	and mwi.id = mtc.mwi_id
join marking_window_test_window mwtw
	on mwtw.marking_window_id  = mwi.marking_window_id
	and mwtw.is_qa  = 0
	and (mwtw.is_material = 0 or mwtw.is_material is null)
JOIN marking_score_profile_options mspo
    on mspo.id = mrsel.score_option_id
JOIN marking_score_options mso_scor
    on mso_scor.id = mspo.score_option_id
LEFT JOIN marking_response_selection_log log
  on log.marking_response_selections_id = mrsel.id
  and log.data like "%score_option_id%"
where mrsel.taqr_id in (:taqr_ids) and mrsel.is_expert_score = 1
group by mrsel.id
`

// < 0.4 sec
// {taqr_ids}
export const SQL_SCORES_FROM_SCORERS_BY_TAQR = `
  select mcbr.taqr_id
      , mwtw.test_window_id
      , mcbr.created_on
      , mcbr.marked_on
      , mcbr.is_revoked
      , mcbr.is_invalid
      , mso_flag.slug flag_slug
      , mso_scor.slug score_slug
      , mso_scor.raw_score_value
      , mrs.score_option_id
      , mso_scor.value
      , mwi.id as mwi_id
      , mwi.score_profile_id
      , mwi.slug item_slug
      , mwi.skill_code
  from marking_claimed_batch_responses mcbr
  join marking_claimed_batches mcb 
    on mcb.id = mcbr.claimed_batch_id 
  join marking_window_items_v2 mwi 
    on mwi.id = mcbr.window_item_id 
  join marking_window_test_window mwtw  
    on mwtw.marking_window_id  = mwi.marking_window_id 
    and mwtw.is_qa  = 0
    and (mwtw.is_material = 0  or mwtw.is_material is null)
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  LEFT JOIN marking_score_profile_flags mspf
      on mspf.id = mrs.score_flag_id
  LEFT JOIN marking_score_options mso_flag
      on mso_flag.id = mspf.score_option_id
  LEFT JOIN marking_score_profile_options mspo
      on mspo.id = mrs.score_option_id
  LEFT JOIN marking_score_options mso_scor
      on mso_scor.id = mspo.score_option_id
  where mcbr.is_marked = 1
    and mcbr.is_validity is null
    and mcb.is_training = 0
    and mcbr.taqr_id IN (:taqr_ids)
`

// [uid, marking_window_item_id]
export const SQL_NUM_TQAR_BY_SCH = `
    select mtc.schl_group_id
         , count(0) as num_responses
         , sum(mmcbr.counter) - sum(mmcbr.rescore_indic_counter) as num_assigned
    ${marking_window_taqr_pre(false)}
    left join (
        select mcbr.id
             , mcbr.taqr_id
             , mcbr.is_marked
             , mcb.marking_window_item_id
             , 1 as counter
             , min(mcbr.is_rescore_indic) rescore_indic_counter
        from marking_claimed_batches mcb
        join marking_claimed_batch_responses mcbr
          on mcb.id = mcbr.claimed_batch_id
          and mcbr.is_revoked = 0
          and mcbr.is_invalid is null
          and ((mcbr.is_rescore_indic =0) or (mcbr.is_rescore_indic =1 and uid != ?))
          and mcbr.is_validity is null
        where mcb.marking_window_item_id = ?
          and mcb.is_training = 0
        group by mcb.marking_window_item_id, mcbr.taqr_id
    ) mmcbr
      on mmcbr.taqr_id = mtc.taqr_id
      and mmcbr.marking_window_item_id = mtc.mwi_id
    ${marking_window_taqr_post}
      and mtc.is_material = 0
    group by mtc.schl_group_id
;`;

// [marking_window_item_id, uid]
export const SQL_NUM_TQAR_SCORED_ = `

`;

// [marking_window_item_id, uid]
export const SQL_NUM_TQAR_SCORED_BY_SCH_AND_UID = `
    select mcbr.schl_group_id, count(0) as num_responses
    from marking_claimed_batches mcb
    join marking_claimed_batch_responses mcbr
      on mcb.id = mcbr.claimed_batch_id
      and mcbr.is_revoked = 0
    where mcb.marking_window_item_id = ?
      and mcb.uid = ?
      and mcb.is_training = 0
    group by mcbr.schl_group_id
;
`;

// [marking_window_item_id, marking_window_item_id, excluded_schl_group_id]
// deprecated, this query is not ued anywhere
export const SQL_ITEM_TQAR_AVAIL = `
    select r.taqr_id, r.schl_group_id
    from (
        select mtc.taqr_id as taqr_id
             , mtc.schl_group_id
             , mmcbr.id as resp_claim_id
        ${marking_window_taqr_pre(false)}
        left join (
            select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
            from marking_claimed_batches mcb
            join marking_claimed_batch_responses mcbr
              on mcb.id = mcbr.claimed_batch_id
              and mcbr.is_revoked = 0
              and mcbr.is_invalid is null
            where mcb.marking_window_item_id = ?
              and mcb.is_training = 0
        ) mmcbr
            on mmcbr.taqr_id = mtc.taqr_id
            and mmcbr.marking_window_item_id = mtc.mwi_id
        ${marking_window_taqr_post}
          and mtc.is_material = 0
          and mtc.schl_group_id not in (?)
    ) r
    where r.resp_claim_id is null
`;

// [marking_window_item_id]
export const SQL_ITEM_BATCH_POLICIES = `
  select mwi.id AS window_item_id
       , mbap.* 
    from marking_window_items_v2 mwi 
    join marking_batch_alloc_policies mbap
      on mbap.id = mwi.batch_alloc_policy_id
   where mwi.id in (?) 
`

// [claimed_batch_id]
export const SQL_BATCH_RESP_SCORES = `
  SELECT mrs.*
  FROM marking_claimed_batch_responses mcbr
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  where mcbr.claimed_batch_id = ?
;`

// [window_item_id]
export const SQL_ITEM_SCORE_OPTIONS = `
  select mspo.id, mspo.order, mspo.is_offset, mso.slug, mso.value, mso.raw_score_value, mso.caption, mso.color, mspo.rubric_text
  from marking_window_items_v2 mwi
  join marking_score_profile_options mspo
    on mspo.score_profile_id = mwi.score_profile_id
  join marking_score_options mso
    on mso.id = mspo.score_option_id
  where mwi.id = ?
  order by mspo.order
;`

// [window_item_id]
export const SQL_ITEM_SCORE_FLAGS = (isFilterToSensitive:boolean=false) => `
  select mspf.id, mspf.order, mso.slug, mso.caption, mso.report_msg
  from marking_window_items_v2 mwi
  join marking_score_profile_flags mspf
    on mspf.score_profile_id = mwi.score_profile_id
  join marking_score_options mso
    on mso.id = mspf.score_option_id
  where mwi.id = ?
  ${isFilterToSensitive? `and mso.is_sensitive = 1` : ''}
  order by mspf.order
;`

// [uid, uid, mw_id, SCAN_CHECK_COUNT, claimPoolSize]
export const SQL_SCAN_BATCH_RESP_EDIT = `

`

// [is_editing, uid, uid, mw_id, SCAN_CHECK_COUNT, claimPoolSize]
// [uid, SCAN_CHECK_COUNT, claimPoolSize]
export const SQL_SCAN_BATCH_RESP_REVIEW = (mw_id:number, isEditing:boolean) => `
  select * from (
    select mwi.id mwi_id
        , srtp.taqr_id
        , srtp.cached_tasr_id tasr_id
        , ifnull(max(rev_other.prev), 0) rev_other_prev
        , ifnull(max(rev_self.prev), 0) rev_self_prev
        , rev_other.srb_id
    --
    -- all available responses
    from marking_window_items_v2 mwi 
    join marking_window_test_window mwtw  
      on mwtw.marking_window_id = mwi.marking_window_id
    join scan_review_taqr_pool srtp
        on srtp.tw_id = mwtw.test_window_id
        and srtp.test_question_id = mwi.item_id
        and srtp.is_locked = 0
    ${ isEditing ? `
      join scan_review_batch_responses srbr_preedit
      on srbr_preedit.taqr_id = srtp.taqr_id
      and srbr_preedit.is_flagged = 1
      and srbr_preedit.is_revoked = 0
    ` : ''}
    --
    -- taqrs marked by others  (in target editing mode)
    left join (
      select srtp.taqr_id
            , count(distinct srbr.id) prev
            , srb.id srb_id 
        from marking_window_items_v2 mwi 
        join marking_window_test_window mwtw  
          on mwtw.marking_window_id = mwi.marking_window_id
        join scan_review_taqr_pool srtp
          on srtp.tw_id = mwtw.test_window_id
          and srtp.test_question_id = mwi.item_id
          and srtp.is_locked = 0
        join scan_review_batch_responses srbr
          on srbr.taqr_id = srtp.taqr_id
          and srbr.is_revoked = 0
        join scan_review_batches srb
          on srbr.srb_id = srb.id
          and srb.is_editing = ${ isEditing ? 1 : 0 }
        where mwi.marking_window_id = ${+mw_id}
        group by srtp.taqr_id
    ) rev_other on rev_other.taqr_id = srtp.taqr_id
    --
    -- taqrs marked by self  (in target editing mode)
    left join (
      select srtp.taqr_id
            , count(distinct srbr.id) prev 
        from marking_window_items_v2 mwi 
        join marking_window_test_window mwtw  
          on mwtw.marking_window_id = mwi.marking_window_id
        join scan_review_taqr_pool srtp
          on srtp.tw_id = mwtw.test_window_id
          and srtp.test_question_id = mwi.item_id
          and srtp.is_locked = 0
        join scan_review_batch_responses srbr
          on srbr.taqr_id = srtp.taqr_id
          and srbr.is_revoked = 0
        join scan_review_batches srb
          on srbr.srb_id = srb.id
          and srb.is_editing = ${ isEditing ? 1 : 0 }
          and srb.uid = ?
        where mwi.marking_window_id = ${+mw_id}
        group by srtp.taqr_id
    ) rev_self on rev_self.taqr_id = srtp.taqr_id
    where mwi.marking_window_id = ${+mw_id}
    group by srtp.taqr_id
  ) t
  where rev_other_prev < ?
    and rev_self_prev = 0
  limit ?
`

// [window_item_id, uid, cutScore]
export const SQL_ITEM_FAILED_QT = `
  select mcb.id
  from marking_claimed_batches mcb
  join  marking_claimed_batch_responses mcbr
    on mcb.id  = mcbr.claimed_batch_id
  where mcb.marking_window_item_id = ?
    and mcb.component_type = 'QUALIFYING'
    and mcb.uid = ?
    and mcb.is_training = 1
    and mcbr.is_marked = 1
  group by mcb.id
  having count(case when
              mcbr.is_training_correct = 1
              then 1 end) / count(*) < ?
  order by mcb.created_on desc
;`

// [window_item_id]
export const SQL_ITEM_DISPLAY_CONFIG = `
  select tq.config
  from marking_window_items_v2 mwi
  join test_questions tq 
    on mwi.item_id = tq.id
  where mwi.id = ?
;`

// [window_item_id]
export const SQL_ITEM_SCOR_MATERIALS = `
  select mwi.id
       , mwi.caption as mwi_name
       , mwi.guide1_url as mwi_guide_url
       , mwi.rubric_url as mwi_rubric_url
       , mwi.anchors_url as mwi_anchors_url
       , mwi.reading_item_id
       , msp.description as msp_name
       , msp.guide1_url as msp_guide
       , msp.guide2_url as msp_video
  from marking_window_items_v2 mwi
  join marking_score_profiles msp
    on msp.id = mwi.score_profile_id
  where mwi.id = ?
`
// [window_id]
export const SQL_ITEM_LEAD_MATERIALS = `
  select mwi.id
       , mwi.slug, mwi.caption
       , mwi.guide1_url
       , mwi.guide1_updated_on
       , mwi.guide1_updated_by_uid
       , mwi.rubric_url
       , mwi.rubric_updated_on
       , mwi.rubric_updated_by_uid
       , mwi.anchors_url
       , mwi.anchors_updated_on
       , mwi.anchors_updated_by_uid
       , mwi.reading_item_id
  from marking_window_items_v2 mwi 
  where mwi.marking_window_id = ?
`

// [window_item_id]
export const SQL_ITEM_RUBRIC_OPTIONS = `
`

// [batch_response_id]
export const SQL_RESPONSE_STATE = `
select taqr.response_raw, taqr.id taqr_id
from marking_claimed_batch_responses mcbr
join test_attempt_question_responses taqr
  on mcbr.taqr_id = taqr.id
where mcbr.id = ?
;`

// [taqr_id]
export const SQL_RAW_RESPONSE_STATE = `
select taqr.response_raw, taqr.is_not_legible, taqr.is_scan_needs_reupload
from test_attempt_question_responses taqr
where taqr.id = ?
;`

// [window_item_id, taqr_id]
export const SQL_RESP_SELECTION = `
select mrsel.taqr_id as id
     , mrsel.id as mrsel_id
     , mrsel.tag1
     , mrsel.tag2
     , mrsel.score_option_id
     , mrsel.rationale
     , mrsel.rationale_hash
     , mrset.response_set_num
     , mrset.response_set_order
     , mrs.is_revoked
from marking_response_selections mrsel
RIGHT join marking_response_set_selections mrset
  on mrset.selection_id = mrsel.id
  and mrset.window_item_id = mrsel.window_item_id
right join marking_response_set mrs
  on mrs.id = mrset.set_id
where mrsel.window_item_id = ?
  and mrsel.taqr_id = ?

  UNION

select mrsel.taqr_id as id
     , mrsel.id as mrsel_id
     , mrsel.tag1
     , mrsel.tag2
     , mrsel.score_option_id
     , mrsel.rationale
     , mrsel.rationale_hash
     , mrset.response_set_num
     , mrset.response_set_order
     , mrs.is_revoked
from marking_response_selections mrsel
LEFT join marking_response_set_selections mrset
  on mrset.selection_id = mrsel.id
  and mrset.window_item_id = mrsel.window_item_id
left join marking_response_set mrs
  on mrs.id = mrset.set_id
where mrsel.window_item_id = ?
  and mrsel.taqr_id = ?
;`

// [batch_response_id]
export const SQL_BATCH_RESP_SCORE_ENTRY_BY_UID = `
  select id, score_option_id, score_flag_id, meta
  from marking_response_scores
  where batch_response_id = ?
    and uid = ?
;`

// [claimed_batch_id]
export const SQL_RESPONSE_SCORES = `
  select
  from marking_claimed_batch_responses mcbr
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
    and mrs.uid = ?
  where mcbr.claimed_batch_id = ?
;`

// [uid, window_item_id, window_item_id]
export const SQL_AVAIL_SCORE_RESPONSE = `
  select r.taqr_id
       , r.schl_group_id
       , 0 as is_invalid
       , 0 as is_nr
  from (
    select mtc.taqr_id
         , mtc.schl_group_id
         , max(mmcbr.id) as resp_claim_id
    ${marking_window_taqr_pre(false)}
    left join (
        select mcbr.id
             , mcbr.taqr_id
             , mcb.marking_window_item_id
             , min(mcbr.is_rescore_indic) is_rescore_indic
        from marking_claimed_batches mcb
        join marking_claimed_batch_responses mcbr 
          on mcb.id = mcbr.claimed_batch_id 
          and mcbr.is_revoked = 0 
          and ((mcbr.is_rescore_indic =0) or (mcbr.is_rescore_indic = 1 and uid = ?))
          and mcbr.is_invalid is null
        where mcb.marking_window_item_id = ?
          and mcb.is_training = 0
        group by mcbr.taqr_id
               , mcb.marking_window_item_id
    ) mmcbr
      on mmcbr.taqr_id = mtc.taqr_id
      and mmcbr.marking_window_item_id = mtc.mwi_id
    left join marking_response_selections mrsel
      on mrsel.taqr_id = mtc.taqr_id
      and mrsel.window_item_id = mtc.mwi_id
    where mtc.mwi_id = ?
      and mtc.is_material = 0
    and mtc.schl_group_id not in (?)
    and (mrsel.id is null or mrsel.is_expert_score = 0)
    group by mtc.taqr_id
  ) r
  where r.resp_claim_id is null
  LIMIT 3000
;`;

// [uid]
// < 0.2 sec
export const SQL_LEADER_MARKING_WINDOWS = (isRafi:boolean = false) => {
  return `
  -- SQL_LEADER_MARKING_WINDOWS (< 0.2 sec)
  select mw.id as window_id
       , mw.group_id
       , mw.name as window_name
       , mw.start_on
       , mw.end_on
       , mw.is_active
       , mw.is_paused
       , mw.is_archived
       , tw.type_slug as tw_type_slug
       , tw.id as tw_id
       , tw.title as tw_title
       , tw.academic_year as tw_academic_year
  from marking_windows mw
  join user_roles ur
    on ur.group_id = mw.group_id
    and role_type = ${isRafi ? '"mrkg_rafi"' : '"mrkg_ctrl"' }
    and is_revoked = 0
    and ur.uid = ?
  join marking_window_test_window mwtw
    on mwtw.marking_window_id = mw.id
    and (mwtw.is_material = 0 or mwtw.is_material is null)
  join test_windows tw
    on tw.id = mwtw.test_window_id
  group by mw.id
  order by mw.id desc;
  ;`
}


// Get cases where num_duplicate_scores records are created (only take those where scores are the same as only these can be unlinked)
// Either in one batch or in all the marking window
export interface IScorDuplicateConfig {
  mw_id?: number,
  claimed_batch_id?: number
}
export const SQL_GET_DUPLICATE_SCORE_RECORDS = (config: IScorDuplicateConfig) => `
  select 
  mrs.batch_response_id as mcbr_id, 
  mcb.uid as scorer_uid,
  count(*) as num_duplicate_scores,
  group_concat(mrs.id ORDER BY mrs.last_touched_on desc) as mrs_ids
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
    ${config.claimed_batch_id ? `and mcb.id = ${ +config.claimed_batch_id }` : ''}
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  join marking_window_items_v2 mwi 
    on mwi.id = mcbr.window_item_id
    ${config.mw_id ? `and mwi.marking_window_id = ${ +config.mw_id }` : ''}
  group by mrs.batch_response_id
  having count(*) > 1
  AND COUNT(DISTINCT mrs.score_option_id) = 1;
`

// []
export const SQL_MARKING_WINDOWS = `
select mw.id as window_id
     , mw.group_id
     , mw.name as window_name
     , mw.start_on
     , mw.end_on
     , mw.is_active
     , mw.is_paused
     , mw.is_archived
from marking_windows mw
where mw.is_active = 1
  and mw.is_paused = 0
;`

// [window_id]
export const SQL_LEAD_SCOR_STATS = `
  select
    t2.*
    , mimt_tm.status as tm_status
    , mimt_pr.status as pr_status
    , mimt_qt.status as qt_status
    , mimt_sc.status as scoring_status
    , mimt_sc.is_revoked as is_scoring_revoked
    , mimt_sc.cache_roll_val_exact as val_ex
    , mimt_sc.cache_roll_val_adj as val_adj
    , mimt_sc.cache_batches_rem as cache_batches_rem
    , mimt_sc.id as task_id
    , mimt_sc.created_on as invit_date
    , count(distinct mimpb.id) as num_sendbacks
  from (
    select t.*
      , um.value as profile_id
      , u.first_name
      , u.last_name
      , u.contact_email
      , a.email
    from (
    select ur.uid as uid
      , mimt.marking_window_item_id as window_item_id
      , mwi.marking_window_id
      , mwi.item_id
      , mwi.slug as item_slug
      , mwi.caption as item_caption
      from marking_windows mw
      join user_roles ur
        on ur.group_id = mw.group_id
        and ur.role_type = 'mrkg_mrkr'
        -- and ur.is_revoked = 0
      join marking_item_marker_tasks mimt
        on mimt.uid = ur.uid
      join marking_window_items_v2 mwi
        on mwi.marking_window_id = mw.id
        and mwi.id = mimt.marking_window_item_id
      where mw.id = ?
      group by ur.uid, mimt.marking_window_item_id
    ) t
    join users u
      on u.id = t.uid
    left join auths a
      on a.uid = t.uid
    left join user_metas um
      on u.id = um.uid
      and um.key_namespace = 'eqao_scoring'
      and um.key = 'ProfileId'
  ) t2
  left join marking_item_marker_tasks mimt_tm
    on mimt_tm.marking_window_item_id = t2.window_item_id
    and mimt_tm.uid = t2.uid
    and mimt_tm.component_type = 'TRAINING_MATERIALS'
  left join marking_item_marker_tasks mimt_pr
    on mimt_pr.marking_window_item_id = t2.window_item_id
    and mimt_pr.uid = t2.uid
    and mimt_pr.component_type = 'PRACTICE'
  left join marking_item_marker_tasks mimt_qt
    on mimt_qt.marking_window_item_id = t2.window_item_id
    and mimt_qt.uid = t2.uid
    and mimt_qt.component_type = 'QUALIFYING'
  left join marking_item_marker_tasks mimt_sc
    on mimt_sc.marking_window_item_id = t2.window_item_id
    and mimt_sc.uid = t2.uid
    and mimt_sc.component_type = 'SCORING'
  left join marking_item_marker_perf_blocks mimpb
    on  mimpb.window_item_id = t2.window_item_id
    and mimpb.uid = t2.uid
    and mimpb.is_revoked = 0
  group by t2.uid, t2.window_item_id
;`

// [window_id]
export const SQL_LEAD_SCOR_STATUS = `
SELECT DISTINCT
	a.uid,
	a.marking_window_item_id,
	a.timestamp,
  a.is_blocked,
  a.scoring_block_type_id,
  a.block_message,
  a.marking_window_id,
  sbt.slug as block_slug,
  sbt.caption as block_caption,
  sbt.category as block_category
FROM scorer_status_by_item_log a
LEFT JOIN scoring_block_types sbt on sbt.id = a.scoring_block_type_id
WHERE a.timestamp = (SELECT MAX(timestamp)
FROM scorer_status_by_item_log c
WHERE c.uid = a.uid
  and c.marking_window_item_id = a.marking_window_item_id)
  AND a.marking_window_id = ?;
`


// [window_id]
export const SQL_LEAD_CUMUL_RECALC = `
select *
     , ( case when (adj_index_exp is null) then 0 else 1 end) is_validity
     , ( case when (adj_index_exp is null) then null else ( case when (abs(over_under) = 0) then 1 else 0 end) end) is_validity_exact
     , ( case when (adj_index_exp is null) then null else (case when (abs(over_under) = 1) then 1 else 0 end) end) is_validity_adj
     , ( case when (adj_index_exp is null) then null else over_under end) is_validity_overunder
from (
	select mcbr.id mcbr_id
	     , mcb.uid
	     , mcb.marking_window_item_id wmi_id
	     , mcbr.taqr_id
	     , mcbr.is_validity  is_validity__pre
	     , mcbr.is_validity_exact  is_validity_exact__pre
	     , mcbr.is_validity_adj  is_validity_adj__pre
	     , mcbr.is_validity_overunder  is_validity_overunder__pre
	     , mso.adj_index
	     , mso2.adj_index adj_index_exp
	     , (mso.adj_index - mso2.adj_index) over_under
	from marking_window_items_v2 mwi 
	join marking_claimed_batches mcb on mcb.marking_window_item_id  = mwi.id 
	join marking_claimed_batch_responses mcbr on mcbr.claimed_batch_id  = mcb.id 
	join marking_response_scores mrs on mrs.batch_response_id = mcbr.id 
	join marking_score_profile_options mspo on mspo.id = mrs.score_option_id 
	join marking_score_options mso on mso.id = mspo.score_option_id 
	left join marking_response_selections mrs2 on mrs2.id  = mcbr.response_selection_id 
	left join marking_score_profile_options mspo2 on mspo2.id = mrs2.score_option_id 
	left join marking_score_options mso2 on mso2.id = mspo2.score_option_id
	where mwi.marking_window_id in (?)
	  and mcbr.is_validity = 1
	  and mcbr.is_marked = 1
) t
order by wmi_id, taqr_id, adj_index_exp, over_under
`

// [window_id]
export const SQL_LEAD_SCOR_STATS_COMPLETION = `
  select t5c.*
      , t5c.num_responses_scored/t5c.batch_size as num_batches_scored
  from (
  select t5b.*
      , count(mcbr.id) as num_responses_scored
  from (
  select t5.*
      , sum(mcbr.is_validity) as num_validity_total
      , sum(mcbr.is_validity_exact) as num_validity_exact
      , sum(mcbr.is_validity_adj) as num_validity_adj
      , avg(mso.value) as avg_score_value
      , (t5.num_batch_expired - t5.num_batch_expired_empty) as num_batch_expired_partial
  from (
  select t2.*
      , mwi.item_id
      , mwi.slug as item_slug
      , mwi.caption as item_caption
      , mbap.batch_size
      , sum(mcb.is_revoked_on_empty) as num_batch_expired_empty
      , count(distinct mcb.id) num_batches_claimed_total
      , (count(distinct mcb.id) - sum(mcb.is_marked * (1-mcb.is_revoked)) - sum(mcb.is_revoked)) num_batches_claimed
      , sum(mcb.is_marked) as num_batches_completed2
      , sum(mcb.is_revoked) as num_batch_expired
  from (

    select mimt.uid
      , mimt.marking_window_item_id as window_item_id
      , mimt.id as task_id
      , mimt.cache_roll_val_exact as num_rol_validity_exact
      , mimt.cache_roll_val_adj as num_rol_validity_adj

    from marking_window_items_v2 mwi
    join marking_item_marker_tasks mimt
      on mimt.marking_window_item_id = mwi.id
      and mimt.component_type = 'SCORING'
    where mwi.marking_window_id = ?

  ) t2
  left join marking_claimed_batches mcb
    on mcb.uid = t2.uid
    and mcb.marking_window_item_id = t2.window_item_id
    and mcb.is_training = 0
  left join marking_window_items_v2 mwi
    on mwi.id = t2.window_item_id
  left join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  group by t2.uid, t2.window_item_id
  ) t5
  left join marking_claimed_batches mcb
    on mcb.uid = t5.uid
    and mcb.marking_window_item_id = t5.window_item_id
    and mcb.is_training = 0
  left join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
    and mcbr.is_marked = 1
    and is_validity = 1
  left join marking_response_scores mrs
    on mrs.uid = t5.uid
    and mrs.batch_response_id = mcbr.id
  left join marking_score_profile_options mspo
    on mspo.id = mrs.score_option_id
  left join marking_score_options mso
    on mso.id = mspo.score_option_id
  group by t5.uid, t5.window_item_id
  ) t5b
  left join marking_claimed_batches mcb
    on mcb.uid = t5b.uid
    and mcb.marking_window_item_id = t5b.window_item_id
    and mcb.is_training = 0
  left join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
  and mcbr.is_marked = 1
  group by t5b.uid, t5b.window_item_id
  ) t5c
  ;
`;

// [window_id]
export const SQL_LEAD_SCOR_STATS_EVENTS = `
	  select mimt.uid
		     , mimt.marking_window_item_id as window_item_id
         , max(mimt.id) as task_id
         , sum(mimt.cache_hours_spent) active_time_total
    from marking_window_items_v2 mwi
		join marking_item_marker_tasks mimt
		  on mimt.marking_window_item_id = mwi.id
		  and mimt.component_type = 'SCORING'
		where mwi.marking_window_id = ?
    group by mimt.uid, mimt.marking_window_item_id
;`;

// [window_id]
export const SQL_LEAD_SCOR_STATS_LAST_ACTIVE_ON = `
  select mcb.uid
       , mcb.marking_window_item_id
       , MAX(mrs.last_touched_on) as last_active_on
  from marking_window_items_v2 mwi
  join marking_claimed_batches mcb
    on mcb.marking_window_item_id = mwi.id
    and mcb.component_type in ('QUALIFYING', 'SCORING')
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
  join marking_response_scores mrs
    on mrs.batch_response_id = mcbr.id
  where mwi.marking_window_id = ?
  group by mcb.uid, mcb.marking_window_item_id;
;`;

// [window_id]
export const SQL_LEAD_ITEM_REL = `
select 
rel_item.window_item_id
, rel_item.num_compared_taqrs as rel_item_num_compared_taqrs
, rel_item.num_exact as rel_item_num_exact
, (rel_item.num_exact + rel_item.num_adj_low + rel_item.num_adj_high) as rel_item_num_exact_adj
from marking_reliability_summary rel_item
join marking_window_items_v2 mwi 
on mwi.id = rel_item.window_item_id
where mwi.marking_window_id = ?;
`

// [window_id]
export const SQL_LEAD_SCORER_REL = `
select 
rel_scorer.window_item_id
, rel_scorer.uid
, rel_scorer.num_compared_taqrs as rel_num_compared_taqrs
, rel_scorer.num_exact as rel_num_exact
, (rel_scorer.num_exact + rel_scorer.num_adj_low + rel_scorer.num_adj_high) as rel_num_exact_adj
from marking_reliability_summary_by_scorer rel_scorer
join marking_window_items_v2 mwi 
on mwi.id = rel_scorer.window_item_id
where mwi.marking_window_id = ?;
`

// [window_item_id]
export const SQL_ITEMS_REALLOC_CANDIDATES = (limit:number) => `
select mcbrt.mcbr_id from (
  select mcbr.id mcbr_id
     , mcb.marking_window_item_id window_item_id
     , mcbr.taqr_id
     , max(mcbr.is_rescore_indic) is_rescore_indic
  from marking_claimed_batches mcb
  join marking_claimed_batch_responses mcbr
    on mcbr.claimed_batch_id = mcb.id
    and mcb.is_training = 0
    and mcb.is_leader = 0
    and mcbr.is_revoked = 0
    and mcbr.is_invalid is null
    and mcbr.is_validity is null
  where mcb.marking_window_item_id = ?
  group by mcbr.taqr_id
) mcbrt
where mcbrt.is_rescore_indic = 0
limit ${+limit}
`

// [window_item_id, window_item_id]
export const SQL_ITEM_AVAIL_CLAIM = `
select /*+ MAX_EXECUTION_TIME(1440000000)*/ distinct r.taqr_id, r.schl_group_id
from (
	select mtc.taqr_id as taqr_id
		 , mtc.schl_group_id
		 , mmcbr.id as resp_claim_id
	from marking_taqr_cache mtc
	left join (
		select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
		from marking_claimed_batches mcb
		join marking_claimed_batch_responses mcbr
		  on mcb.id = mcbr.claimed_batch_id
		  and mcbr.is_revoked = 0
          and mcbr.is_rescore_indic = 0 -- included
          and mcbr.is_invalid is null
		where mcb.marking_window_item_id = ?
		  and mcb.is_training = 0
	) mmcbr
		on mmcbr.taqr_id = mtc.taqr_id
		and mmcbr.marking_window_item_id = mtc.mwi_id
  left join marking_response_selections mrsel
    on mrsel.taqr_id = mtc.taqr_id
    and mrsel.window_item_id = mtc.mwi_id
	where mtc.mwi_id = ?
	  and mtc.is_material = 0
    and (mrsel.id is null or mrsel.is_expert_score = 0)
) r
where r.resp_claim_id is null
`
// [window_item_id, window_item_id]
export const SQL_ITEM_AVAIL_SCORE = `
select /*+ MAX_EXECUTION_TIME(1440000000)*/ distinct r.taqr_id, r.schl_group_id
from (
	select mtc.taqr_id as taqr_id
		 , mtc.schl_group_id
		 , mmcbr.id as resp_claim_id
	from marking_taqr_cache mtc
	left join (
		select mcbr.id, mcbr.taqr_id, mcb.marking_window_item_id
		from marking_claimed_batches mcb
		join marking_claimed_batch_responses mcbr
		  on mcb.id = mcbr.claimed_batch_id
		  and mcbr.is_revoked = 0
      and mcbr.is_rescore_indic = 0 -- included
      and mcbr.is_marked = 1
      and mcbr.is_invalid is null
		where mcb.marking_window_item_id = ?
		  and mcb.is_training = 0
	) mmcbr
		on mmcbr.taqr_id = mtc.taqr_id
		and mmcbr.marking_window_item_id = mtc.mwi_id
  left join marking_response_selections mrsel
    on mrsel.taqr_id = mtc.taqr_id
    and mrsel.window_item_id = mtc.mwi_id
	where mtc.mwi_id = ?
	  and mtc.is_material = 0
    and (mrsel.id is null or mrsel.is_expert_score = 0)
) r
where r.resp_claim_id is null
`

// [window_item_id]
export const SQL_ITEMS_REALLOC_SUMMARY = `
select mcbrt.window_item_id
     , sum(mcbrt.taqr_tally) num_responses
     , sum(mcbrt.w_rescore_tally) num_responses_w_rescore
     , sum(mcbrt.rescore_indic_tally) num_rel_realloc
     , sum(mcbrt.rescore_indic_tally)/sum(mcbrt.taqr_tally) perc_reliability_target_rescore
     , (sum(mcbrt.w_rescore_tally)/sum(mcbrt.taqr_tally))-1 perc_reliability_actual_rescore
from (
	select mcbr.id
		 , mcb.marking_window_item_id window_item_id
		 , mcbr.taqr_id
		 , count(distinct mcbr.taqr_id) taqr_tally 
     , count(distinct mcbr.id) w_rescore_tally 
		 , max(mcbr.is_rescore_indic) rescore_indic_tally
	from marking_claimed_batches mcb
	join marking_claimed_batch_responses mcbr
		on mcbr.claimed_batch_id = mcb.id
        and mcb.is_training = 0
        and mcb.is_leader = 0
		and mcbr.is_revoked = 0
		and mcbr.is_invalid is null
        and mcbr.is_validity is null
	where mcb.marking_window_item_id in (?)
	group by mcb.marking_window_item_id, mcbr.taqr_id
) mcbrt
group by mcbrt.window_item_id
`

// [uid, window_item_id, component_type]
export const SQL_SCORER_SCORING_TASK = `
select mimt.id
     , mimt.status
     , mimt.allow_revisit
     , cache_batches_rem
     , mimt.is_pass_fail
from  marking_item_marker_tasks mimt
where mimt.uid = ?
  and mimt.marking_window_item_id = ?
  and mimt.component_type = ?
  and mimt.is_revoked = 0
`

// [window_id]
export const SQL_SCORER_SCORING_TASKS = `
select mimt.id
  , mimt.marking_window_item_id as window_item_id
  , mimt.uid
from marking_window_items_v2 mwi
join marking_item_marker_tasks mimt
  on mimt.marking_window_item_id = mwi.id
  and mimt.component_type = 'SCORING'
  and mimt.is_revoked = 0
where mwi.marking_window_id = ?
`

// [window_id]
export const SQL_LEAD_ITEM_STATS = `
select t5.*
  , t5.taqr_total as mrs_stage1
  , sum(__mrs_tag1_or_score_option) as mrs_stage2
  , sum(__mrs_score_option) as mrs_stage3
  , sum(if((__mrs_score_option = 1 AND __mrs_is_proposed = 1) or __mrs_is_included = 1, 1, 0)) as mrs_stage4
from (
  select t4.*
    , if (((mrs.tag1  is null) or (mrs.tag1 = "")) and ((mrs.score_option_id is null) or (mrs.score_option_id = "")), 0, 1) as __mrs_tag1_or_score_option
    , if ((mrs.tag2  is null) or (mrs.tag2 = ""), 0, 1) as __mrs_is_proposed
    , if ((mrs.score_option_id is null) or (mrs.score_option_id = ""), 0, 1) as __mrs_score_option
	  , if (rset.id is not null, 1, 0) as __mrs_is_included
  from (
    select t3.*
    , count(distinct mtc.taqr_id) as taqr_total
    from
    (
      select t2.*
          , sum(t2.__num_assigned) as num_assigned
          , sum(t2.__is_marked) as num_marked
          , sum(t2.__is_random_alloc) as num_random_alloc
          , sum(t2.__is_dbl_marked) as num_dbl_marked
      from (
        select t.*
            , max(mcbr.is_marked) as __is_marked
            , IF(sum(mcbr.is_marked)>1, 1, 0) as __is_dbl_marked
            , max(mcbr.is_rescore_indic) as __is_random_alloc
            , count(mcbr.id) as __num_assigned
        from (
          SELECT mwi.*
              , mwi.cached_taqr_tally as num_responses
              , msp.short_name as score_profile_name
              , msp.description as score_profile_description
              , mbap.description as policy_description
              , mbap.notes as policy_notes
              , mbap.max_batch_num_claim
              , mbap.batch_size
              , mbap.batch_validity_num
              , mbap.claim_dur_hours
              , mbap.access_rolling_batch_num
              , mbap.access_rolling_min_exact_rate
              , mbap.access_rolling_min_exact_adj_rate
              , mbap.rescore_rolling_batch_num
              , mbap.rescore_rolling_min_exact_rate
              , mbap.rescore_rolling_min_exact_adj_rate
              , mbap.auto_rescore_rate
          FROM marking_window_items_v2 mwi
          join marking_score_profiles msp
            on msp.id = mwi.score_profile_id
          join marking_batch_alloc_policies mbap
            on mbap.id = mwi.batch_alloc_policy_id
          where mwi.marking_window_id = ?
          group by mwi.id
        ) t
        left join (
          select mcbr0.id, mcbr0.window_item_id, mcbr0.taqr_id, mcbr0.is_marked, mcbr0.is_random_alloc, mcbr0.is_rescore_indic
          from marking_claimed_batches mcb
          join marking_claimed_batch_responses mcbr0
            on mcbr0.claimed_batch_id = mcb.id
            and mcbr0.is_revoked = 0
            and mcbr0.is_validity is null
          where mcb.is_training = 0
        ) mcbr
        on mcbr.window_item_id = t.id
        group by t.id, mcbr.taqr_id
      ) t2
      group by t2.id
    ) t3
    left join marking_taqr_cache mtc
    on t3.marking_window_id = mtc.marking_window_id and t3.id = mtc.mwi_id
    group by t3.id
  ) t4
  left join marking_response_selections mrs
  on mrs.window_item_id = t4.id
  and (
      mrs.tag1 is not null
    or mrs.score_option_id is not null
    or mrs.is_included = 1
  )
  left join marking_response_set_selections mrset
      on mrset.selection_id = mrs.id
      and mrset.window_item_id = mrs.window_item_id
  left join marking_response_set rset
    on mrset.set_id = rset.id and rset.set_type_id > 0
) t5
group by t5.id
;`

export const SQL_NUM_TQAR_BY_ITEM = `
    select t.mwi_id
         , count(0) as num_responses
    from (
        select mwi.id as mwi_id
             , count(0) as num_responses
        ${marking_window_taqr_pre(false)}
        where mtc.mwi_id IN (?)
          and mtc.is_material = 0
    ) t
    group by t.mwi_id
;`;

// select t4.* , count(0) as num_responses FROM ( select t3.* 	FROM ( ) t3 join
// marking_window_test_window mwtw   on mwtw.marking_window_id =
// t3.marking_window_id left join test_sessions ts   on ts.test_window_id =
// mwtw.test_window_id left join school_class_test_sessions scts   on
// scts.test_session_id = ts.id left join school_classes sc   on sc.id =
// scts.school_class_id left join test_attempts ta   on ts.id =
// ta.test_session_id left join test_attempt_question_responses taqr   on ta.id
// = taqr.test_attempt_id   and t3.item_id = taqr.test_question_id group by
// t3.id,taqr.id ) t4 group by t4.id and ${taqr_nr_clause} and
// ${taqr_invalid_clause} [window_id]
export const SQL_LEAD_ITEMS_VALIDITY_OVERVIEW_CORE = `
  SELECT mwi.*
      , mbap.batch_size
      , mbap.batch_validity_num
  FROM marking_window_items_v2 mwi
  join marking_batch_alloc_policies mbap
    on mbap.id = mwi.batch_alloc_policy_id
  where mwi.marking_window_id = ?
  group by mwi.id
;`

// {window_item_id, setId, offset, limit}
export const SQL_TAQR_EXEMPLARS_BY_ITEM = (reqTagOrScore: boolean, reqScore: boolean, reqProp: boolean, inclOverride:boolean, setId: number, offset?: number, limit?: number) => `
  select *
  from (
    select
        mtc.taqr_id as id
      , RAND(mtc.taqr_id) as sort_order
      , mrsel.id as mrsel_id
      , mrsel.tag1
      , mrsel.tag2
      , mrsel.score_option_id
      , mrsel.is_expert_score
      , mrsel.rationale
      , mrsel.is_included
      , mrsel.is_excluded
      , mrset.id as mrset_id
      , mrset.set_id
      , mrset.response_set_order
      , mrset.response_set_num
      , mrs.is_revoked
      , mtc.is_material
    ${marking_window_taqr_pre()}
    left join marking_response_selections mrsel
      on mrsel.taqr_id = mtc.taqr_id
      and mrsel.window_item_id = mtc.mwi_id
    right join marking_response_set_selections mrset
      on mrset.selection_id = mrsel.id
      and mrset.window_item_id = mrsel.window_item_id
    right join marking_response_set mrs
      on mrs.id = mrset.set_id
    where mtc.mwi_id = :window_item_id

    UNION

    select
        mtc.taqr_id as id
      , RAND(mtc.taqr_id) as sort_order
      , mrsel.id as mrsel_id
      , mrsel.tag1
      , mrsel.tag2
      , mrsel.score_option_id
      , mrsel.is_expert_score
      , mrsel.rationale
      , mrsel.is_included
      , mrsel.is_excluded
      , mrset.id as mrset_id
      , mrset.set_id
      , mrset.response_set_order
      , mrset.response_set_num
      , mrs.is_revoked
      , mtc.is_material
    ${marking_window_taqr_pre()}
    left join marking_response_selections mrsel
      on mrsel.taqr_id = mtc.taqr_id
      and mrsel.window_item_id = mtc.mwi_id
    left join marking_response_set_selections mrset
      on mrset.selection_id = mrsel.id
      and mrset.window_item_id = mrsel.window_item_id
    left join marking_response_set mrs
      on mrs.id = mrset.set_id
    where mtc.mwi_id = :window_item_id
  ) t
  where (t.is_excluded = 0 or t.is_excluded is null)

  ${reqTagOrScore || reqScore || reqProp ? `and (
    ( true
      ${/* Stage 2*/ reqTagOrScore ? `and ((t.tag1 is not null and t.tag1 != '') or (t.score_option_id is not null and t.score_option_id != ''))` : ''}
      ${/* Stage 3, 4*/ reqScore ? `and (t.score_option_id is not null and t.score_option_id != '')` : ''}
      ${/* Stage 4*/ reqProp ? `and (t.tag2 is not null and t.tag2 != '')` : ''}
    )
    ${/* Stage 4*/ inclOverride ? `or (t.set_id is not null and t.set_id != '')` : ''}
  )` : ''}

  ${/* Set view for specific set */ setId ? 'and t.set_id = :setId ' : ''}
  order by t.sort_order
  ${limit ? 'limit :limit' : ''}
  ${offset ? 'offset :offset' : ''};
;`;


// {taqr_ids, window_item_id}
export const SQL_NONMAT_SCORER_SCORES = `
  -- SQL_NONMAT_SCORER_SCORES, < 0.3 sec, taqr_ids list always <= 500 due to previous limits
  select 
  mcbr.taqr_id
  , mrs.score_option_id as scorer_score_option_id
  , mcbr.marked_on as scorer_marked_on
  from marking_claimed_batch_responses mcbr
  join marking_claimed_batches mcb
      on mcb.id = mcbr.claimed_batch_id
      and mcb.component_type = "SCORING"
  join marking_response_scores mrs
      on mrs.batch_response_id = mcbr.id
  where mcbr.window_item_id = :window_item_id
    and mcbr.taqr_id in (:taqr_ids)
    and mcbr.is_revoked = 0
    and (mcbr.is_invalid = 0 or mcbr.is_invalid is null)
    and (mcbr.is_validity = 0 or mcbr.is_validity is null)
    and mcbr.is_marked = 1
  group by mcbr.taqr_id;
`


// [taqr_id, window_item_id]
export const SQL_TAQR_EXEMPLAR = `
  select mrs.*
  from marking_response_selections mrs
  where mrs.taqr_id = ?
    and mrs.window_item_id = ?
`

// [window_item_id, component_type]
export const SQL_ITEM_COMPONENT_RESPONSE_SET = `
SELECT mrs.id
     , mrs.set_type_id
     , mrs.set_type_variant_num
FROM marking_response_set_types mrst
join marking_response_set mrs
  on mrs.set_type_id = mrst.id
  and mrs.window_item_id = ?
  and is_revoked is null
where mrst.component_type = ?
order by mrs.set_type_variant_num
;`

// [response_set_id]
export const SQL_ITEM_COMPONENT_SET_RESPONSES = `
SELECT mrss.*
from marking_response_set_selections mrss
where mrss.set_id = ?
order by mrss.response_set_order
;`

// [window_item_id, uid]
export const SQL_ITEM_SCORER_VALIDITY_RESPONSES = `
SELECT t.*
FROM (
	SELECT mrss.id
		 , mrss.selection_id as response_selection_id
		 , mrss.taqr_id
		 , mc.id as is_prev_assigned
	FROM marking_response_set_types mrst
	join marking_response_set mrs
	  on mrs.set_type_id = mrst.id
	  and mrs.window_item_id = :window_item_id
	  and is_revoked is null
	join marking_response_set_selections mrss
	  on mrss.set_id = mrs.id
	left join (
		select mcbr.id, mcbr.response_selection_id, mcb.uid
		from marking_claimed_batches mcb
    join marking_claimed_batch_responses mcbr
		  on mcbr.is_validity = 1
		  and mcbr.is_validity_repooled is NULL
      and mcbr.claimed_batch_id = mcb.id
    where mcb.uid = :uid
    and mcb.marking_window_item_id = :window_item_id
  ) mc
    on mc.response_selection_id = mrss.id
	where mrst.slug = 'V'
) t
where t.is_prev_assigned is NULL
group by t.response_selection_id
;`

// [c]
export const SQL_SCORE_OPTION_VAL = `
  select mspo.id, mspo.order, mso.adj_index, mso.value
  from marking_score_profile_options mspo
  join marking_score_options mso
    on mso.id = mspo.score_option_id
  where mspo.id in (?)
`

// [uid, window_item_id]
export const SQL_SCORER_REPORT = `
select count(DISTINCT mcb.id) num_batches_claimed
     , count(DISTINCT mcbr.id ) num_responses_scored
     , count(case when mcbr.is_validity_overunder > 0 then 1 end) as num_validity_over
     , count(case when mcbr.is_validity_overunder < 0 then 1 end) as num_validity_under
     , sum(mcbr.is_validity) as num_validity_total
	 , sum(mcbr.is_validity_exact) as num_validity_exact
	 , sum(mcbr.is_validity_adj) as num_validity_adj
from marking_claimed_batches mcb
left join marking_claimed_batch_responses mcbr
  on mcbr.claimed_batch_id = mcb.id
  and mcbr.is_marked = 1
where mcb.uid = ?
  and mcb.marking_window_item_id = ?
  and mcb.is_training = 0
;`


// [uid, window_item_id]
export const SQL_SCORER_REPORT_VALIDITY_DETAIL = `
select 
 IF (mcbr.is_validity_overunder > 0, 1, 0) as is_validity_over,
 IF (mcbr.is_validity_overunder < 0, 1, 0) as is_validity_under,
 mcbr.is_validity_exact,
 mso.value as scorer_value, 
 mso_lead.value as expected_value,
 count(*) as count
from marking_claimed_batches mcb 
 join marking_claimed_batch_responses mcbr
  on mcbr.claimed_batch_id = mcb.id
  and mcbr.is_marked = 1
  and is_validity = 1
left join marking_response_scores mrs
	on mrs.batch_response_id = mcbr.id
left join marking_score_profile_options mspo
	on mspo.id = mrs.score_option_id
left join marking_score_options mso
	on mspo.score_option_id = mso.id
left join marking_response_selections mrsel
	on mrsel.taqr_id = mcbr.taqr_id
    and mrsel.window_item_id = mcb.marking_window_item_id
left join marking_score_profile_options mspo_lead
	on mspo_lead.id = mrsel.score_option_id
left join marking_score_options mso_lead
	on mspo_lead.score_option_id = mso_lead.id
where mcb.uid = ?
  and mcb.marking_window_item_id = ?
  and mcb.is_training = 0
group by mso.value, mso_lead.value
order by mcbr.is_validity_exact DESC, is_validity_under DESC, mso_lead.value
;`

// [marking_window_item_id, taqr_id]
export const SQL_SCOR_PREV_SCORING_CHECK = `
select mcbr.id
     , mcbr.taqr_id
     , mcb.marking_window_item_id
from marking_window_items_v2 mwi
join marking_claimed_batches mcb
	on mcb.marking_window_item_id = mwi.id
join marking_claimed_batch_responses mcbr
  on mcb.id = mcbr.claimed_batch_id
  and mcbr.is_revoked = 0
  and mcbr.is_marked = 1
  and mcbr.is_invalid is null
where mcb.is_training = 0
	and mcb.marking_window_item_id = ?
	and mcbr.taqr_id = ?
limit 1
`

// ~7-13 sec when it pulls 14.5k records (window id 41)
// [window_id]
export const SQL_SCOR_LEAD_RESPONSES_AVAIL = (config:IScorLeadResponesConfig) => `
-- SQL_SCOR_LEAD_RESPONSES_AVAIL
select 
mtc_agg.taqr_id
, mtc_agg.item_id
, mtc_agg.slug as item_slug
, mtc_agg.caption as item_caption
, mtc_agg.window_item_id
, mtc_agg.is_item_ft
, if(tasr.id is not null, 1, 0) as is_scan
 from (
	select 
	mtc.taqr_id
	, mwi.id as item_id
	, mwi.slug
	, mwi.caption
	, mwi.id as window_item_id
	, mwi.is_item_ft
	from marking_taqr_cache mtc
	join marking_window_items_v2 mwi
		on mtc.mwi_id = mwi.id
		and mwi.marking_window_id = ?
		and mtc.is_material = 0
    ${ config.windowItemId ? `and mwi.id = ${ +config.windowItemId }`: ``}
    ${ config.responseId ? `and mtc.taqr_id = ${ +config.responseId }`: ``}
	left join marking_claimed_batch_responses mcbr
		on mcbr.window_item_id = mtc.mwi_id
		and mcbr.taqr_id = mtc.taqr_id
		and mcbr.is_revoked = 0
		and mcbr.is_invalid is null
	left join marking_claimed_batches mcb
		on mcb.id = mcbr.claimed_batch_id
		and mcb.is_training = 0
	where mcbr.id is null or mcb.id is null
	group by mtc.mwi_id, mtc.taqr_id
) mtc_agg
left join test_attempt_scan_responses tasr
  on tasr.taqr_id = mtc_agg.taqr_id
  and tasr.is_discarded = 0
left join marking_response_selections mrsel
  on mrsel.taqr_id = mtc_agg.taqr_id
  and mrsel.window_item_id = mtc_agg.window_item_id
where (mrsel.id is null or mrsel.is_expert_score = 0)
limit ${ +config.limit };
`


// [window_id]
interface IScorLeadResponesConfig {
  limit: number,
  isFlags?:boolean,
  isValidity?:boolean,
  isQualifying?:boolean,
  isSensitive?:boolean,
  isScan?:boolean,
  uid?: number,
  windowItemId?: number,
  scoredBefore?: string,
  scoredAfter?: string,
  responseId?: number,
  scorProfileId?: number
}
export const SQL_SCOR_LEAD_RESPONSES = (config:IScorLeadResponesConfig) => `
  SELECT mrs_agg.*
       , mso_scor.id mso_scor_id
       , mso_scor.slug as slug
       , mso_scor.value as score
       , mso_flag.slug as flag
       , mso_flag.is_sensitive
       , mspo_lead.id as leader_score_option_id
       , mso_lead.value as leader_score
       , mso_lead.slug as leader_slug
       , CONCAT(scor.first_name, " ", scor.last_name) as scorer_name
       , scor.first_name
       , scor.last_name
       , scor.contact_email
       , um.value as profile_id
       , taqr.created_on as response_created_on
       , taqr.updated_on as response_updated_on
       , taqr.is_not_legible as scan_not_legible
       , taqr.is_scan_needs_reupload as scan_needs_reupload
       ${
        config.isScan ? ``: `
          , if(tasr.taqr_id is not null, 1, 0) as is_scan
        `
        }
       ${
        config.isSensitive ? `
          , um_oen.value student_number
          , um_dob.value student_dob
          , CONCAT(u.first_name, ' ', u.last_name) student_name
          , sc.name sc_name
          , s.foreign_id s_code
          , s.name s_name
        `: ``
      }
  FROM (
    select mrs.id as mrs_id
        , mrs.last_touched_on as \`timestamp\`
        , mrs.uid
        , mrs.inspected
        , mrs.score_option_id
        , mrs.score_flag_id
        , mrs.meta as meta
        , mcbr.id mcbr_id
        , mcbr.taqr_id
        , mcbr.claimed_batch_id
        , mcbr.is_validity_exact
        , mcbr.is_validity_adj
        , mcbr.is_validity
        , mcbr.is_revoked
        , mcbr.is_invalid
        , mcb.component_type
        , mwi.id as item_id
        , mwi.slug as item_slug
        , mwi.caption as item_caption
        , mwi.is_item_ft as is_item_ft
        , mcbr.window_item_id
    from marking_response_scores mrs
    join marking_claimed_batch_responses mcbr
      on mcbr.id = mrs.batch_response_id
      and mcbr.is_marked = 1
    join marking_claimed_batches mcb
      on mcbr.claimed_batch_id = mcb.id
    ${
      config.isQualifying ? `
      and mcb.component_type = "QUALIFYING"
      ` :
      `
      and mcb.is_training = 0
      `
    }
    join marking_window_items_v2 mwi 
      on mwi.id = mcb.marking_window_item_id 
    ${ 
      config.isFlags ? `
        join marking_score_profile_flags mspf
          on mspf.id = mrs.score_flag_id
        join marking_score_options mso_flag
          on mso_flag.id = mspf.score_option_id
          and mso_flag.is_sensitive = ${ config.isSensitive ? 1 : 0 }
        ${
          config.isScan ? `
          and mso_flag.slug = "lbl_scan_issue_abr"
          `: ``
        }
      `: ``
    }
    where mwi.marking_window_id in (?)
    ${
      config.windowItemId ? `
      and mwi.id in (${ +config.windowItemId })
      `: ``
    }
    ${
      config.isFlags ? `
      and mrs.score_flag_id is not null
      `: ``
    }
    ${
      config.isValidity ? `
      and mcbr.is_validity = 1
      `: ``
    }
    ${
      config.uid ? `
      and mrs.uid = ${ +config.uid }
      `: ``
    }
    ${ 
      config.scoredBefore ? `
      and mrs.last_touched_on < '${ config.scoredBefore }'
      `: ``
    }
    ${ 
      config.scoredAfter ? `
      and mrs.last_touched_on > '${ config.scoredAfter }'
      `: ``
    }
    ${ 
      config.responseId ? `
      and mcbr.taqr_id = '${ +config.responseId }'
      `: ``
    }
    order by mrs.id desc
    ${
      config.limit ? `
      limit ${ +config.limit }
      `: ``
    }

  ) mrs_agg
  LEFT JOIN marking_response_selections mrsel
    on mrsel.taqr_id = mrs_agg.taqr_id
    AND mrs_agg.window_item_id = mrsel.window_item_id
    AND mrsel.is_expert_score = 1
  LEFT JOIN users scor
    on scor.id = mrs_agg.uid
  LEFT JOIN marking_score_profile_flags mspf
    on mspf.id = mrs_agg.score_flag_id
  LEFT JOIN marking_score_options mso_flag
    on mso_flag.id = mspf.score_option_id
  LEFT JOIN marking_score_profile_options mspo
    on mspo.id = mrs_agg.score_option_id
  LEFT JOIN marking_score_options mso_scor
    on mso_scor.id = mspo.score_option_id
  LEFT JOIN marking_score_profile_options mspo_lead
    on mspo_lead.id = mrsel.score_option_id
  LEFT JOIN marking_score_options mso_lead
    on mso_lead.id = mspo_lead.score_option_id
  LEFT JOIN user_metas um
    on um.uid = mrs_agg.uid
    and um.key_namespace = 'eqao_scoring'
    and um.key = 'ProfileId'
  LEFT JOIN test_attempt_question_responses taqr
    on taqr.id = mrs_agg.taqr_id
  ${
    config.isScan ? ``: `
    LEFT JOIN (
      select
        distinct taqr_id from test_attempt_scan_responses
    ) tasr
      on taqr.id = tasr.taqr_id
    `
  }
  ${
    config.isSensitive ? `
    LEFT JOIN test_attempts ta
      on ta.id = taqr.test_attempt_id
    left join school_class_test_sessions scts
      on scts.test_session_id  = ta.test_session_id
    left join school_classes sc
      on sc.id = scts.school_class_id
    left join schools s
      on s.group_id  = sc.schl_group_id
    left join users u
      on u.id = ta.uid
    left join user_metas um_oen
      on um_oen.key_namespace = 'eqao_sdc'
      and um_oen.key  = 'StudentOEN'
      and um_oen.uid = ta.uid
    left join user_metas um_dob 
      on um_dob.key_namespace = 'eqao_sdc'
      and um_dob.key  = 'DateofBirth'
      and um_dob.uid = ta.uid
    `: ``
  } 
  ${ 
    config.scorProfileId ? `
    where um.value = '${ +config.scorProfileId }'
    `: ``
  }
;`


export const SQL_SCOR_LEAD_RELIABILITY_RESPONSES = (config:IScorLeadResponesConfig) => `
-- SQL_SCOR_LEAD_RELIABILITY_RESPONSES, up to ~ 0.15 sec duration / 30 sec fetch
  select 
  mwi.id as item_id
  , mwi.caption as item_caption
  , taqr.id taqr_id
  , taqr.created_on as response_created_on
  , taqr.updated_on as response_updated_on
  , CONCAT("[", um_1.value, "] ", scorer_1.first_name, " ", scorer_1.last_name) as scorer_name
  , CONCAT("[", um_1.value, "] ", scorer_1.first_name, " ", scorer_1.last_name) as ref_scorer_name
  , CONCAT("[", um_2.value, "] ", scorer_2.first_name, " ", scorer_2.last_name) as rel_scorer_name
  , mcbr_1.id mcbr_id
  , mso_scor_1.value as ref_score
  , mso_scor_2.value as rel_score
  , mso_scor_1.adj_index ref_adj_index
  , mso_scor_2.adj_index rel_adj_index
  , mso_scor_1.slug
  , mrs_1.score_option_id
  , mspo_lead.id as leader_score_option_id
  , mso_lead.value as leader_score
  , mso_lead.slug as leader_slug
  , mrs_1.id mrs_id
  , mrs_2.id mrs_rel_id
  , IF(mrs_1.inspected = 1 AND mrs_2.inspected = 1, 1, 0) inspected
  from marking_claimed_batch_responses mcbr_1
  join marking_claimed_batches mcb_1
    on mcb_1.id = mcbr_1.claimed_batch_id
    and mcb_1.is_training = 0
    and mcbr_1.is_marked = 1
    and mcbr_1.is_revoked = 0
    and mcbr_1.is_invalid is null
    and mcbr_1.is_validity is null
    and mcbr_1.is_rescore_indic = 1
  join marking_claimed_batch_responses mcbr_2
    on mcbr_2.taqr_id = mcbr_1.taqr_id
    and mcbr_1.id < mcbr_2.id
    and mcbr_2.is_revoked = 0
    and mcbr_2.is_invalid is null
    and mcbr_2.is_validity is null
  join marking_claimed_batches mcb_2
      on mcb_2.id = mcbr_2.claimed_batch_id
      and mcb_2.marking_window_item_id = mcb_1.marking_window_item_id
  join marking_window_items_v2 mwi 
    on mwi.id = mcb_1.marking_window_item_id 
  join marking_response_scores mrs_1
    on mrs_1.batch_response_id = mcbr_1.id
  join marking_response_scores mrs_2
    on mrs_2.batch_response_id = mcbr_2.id
  join test_attempt_question_responses taqr
    on taqr.id = mcbr_1.taqr_id
  left join marking_score_profile_options mspo_1
    on mspo_1.id = mrs_1.score_option_id
  left join marking_score_options mso_scor_1
    on mso_scor_1.id = mspo_1.score_option_id
  left join marking_score_profile_options mspo_2
    on mspo_2.id = mrs_2.score_option_id
  left join marking_score_options mso_scor_2
    on mso_scor_2.id = mspo_2.score_option_id
  left join marking_response_selections mrsel
    on mrsel.taqr_id = mcbr_1.taqr_id
    and mrsel.window_item_id = mcb_1.marking_window_item_id
    and mrsel.is_expert_score = 1
  left join marking_score_profile_options mspo_lead
    on mspo_lead.id = mrsel.score_option_id
  left join marking_score_options mso_lead
    on mso_lead.id = mspo_lead.score_option_id
  left join users scorer_1
    on scorer_1.id = mcb_1.uid
  left join user_metas um_1
    on um_1.uid = mcb_1.uid
    and um_1.key_namespace = 'eqao_scoring'
    and um_1.key = 'ProfileId'
  left join users scorer_2
    on scorer_2.id = mcb_2.uid
  left join user_metas um_2
    on um_2.uid = mcb_2.uid
    and um_2.key_namespace = 'eqao_scoring'
    and um_2.key = 'ProfileId'
  where mwi.marking_window_id in (?)
  ${
    config.windowItemId ? `
    and mwi.id in (${ +config.windowItemId })
    `: ``
  }
  ${ 
    config.scorProfileId ? `
    and (um_1.value = '${ +config.scorProfileId }' or um_2.value = '${ +config.scorProfileId }')
    `: ``
  }
  ${ 
    config.scoredBefore && config.scoredAfter ? `
    and ((mrs_1.last_touched_on < '${ config.scoredBefore }' and mrs_1.last_touched_on > '${ config.scoredAfter }') or (mrs_2.last_touched_on < '${ config.scoredBefore }' and mrs_2.last_touched_on > '${ config.scoredAfter }'))
    `: ``
  }
  ${ 
    config.scoredBefore && !config.scoredAfter ? `
    and (mrs_1.last_touched_on < '${ config.scoredBefore }' or mrs_2.last_touched_on < '${ config.scoredBefore }')
    `: ``
  }
  ${ 
    config.scoredAfter && !config.scoredBefore ? `
    and (mrs_1.last_touched_on > '${ config.scoredAfter }' or mrs_2.last_touched_on > '${ config.scoredAfter }')
    `: ``
  }
  ${ 
    config.responseId ? `
    and taqr.id = '${ +config.responseId }'
    `: ``
  }
  ${
    config.limit ? `
    limit ${ +config.limit }
    `: ``
  }
`

export const SQL_SCOR_LEAD_RESPONSES_SCORER = (config:IScorLeadResponesConfig) => `
  SELECT mrs_agg.uid,
    scor.first_name,
    scor.last_name,
    scor.contact_email,
    um.value as profile_id
  FROM (
    SELECT DISTINCT(mrs.uid)
    FROM marking_response_scores mrs
    JOIN marking_claimed_batch_responses mcbr
      on mcbr.id = mrs.batch_response_id
      and mcbr.is_marked = 1
    JOIN marking_claimed_batches mcb
      on mcbr.claimed_batch_id = mcb.id
      and mcb.is_training = 0
    JOIN marking_window_items_v2 mwi 
      on mwi.id = mcb.marking_window_item_id
    WHERE mwi.marking_window_id in (?)
    ORDER BY mrs.id desc
    ${
      config.limit ? `
        LIMIT ${ +config.limit }
      `: ``
    }
  ) mrs_agg
  LEFT JOIN users scor
    on scor.id = mrs_agg.uid
  LEFT JOIN user_metas um
    on um.uid = mrs_agg.uid
    and um.key_namespace = 'eqao_scoring'
    and um.key = 'ProfileId'
;`

// [window_id]
/*
export const SQL_SCOR_LEAD_BATCHES = `select mcb.id as batch_id,
mcb.batch_size, mcb.created_on, mcb.completed_on, mcb.is_marked, mcb.uid, mwi.id as item_id, mwi.caption, mwi.slug, u.first_name, u.last_name, u.contact_email
from marking_claimed_batches mcb
left join marking_window_items mwi on mwi.id = mcb.marking_window_item_id
left join users u on u.id = mcb.uid
where mwi.marking_window_id = ?;`
*/
export const SQL_SCOR_LEAD_BATCHES = `
select mcb.id as batch_id,
           mcb.batch_size,
           mcb.created_on,
           mcb.completed_on,
           mcb.is_marked,
           mcb.uid,
           mwi.id as item_id,
           mwi.caption,
           mwi.slug,
           u.first_name,
           u.last_name,
           u.contact_email,
           um.value as profile_id
      from marking_claimed_batches mcb
 left join marking_window_items_v2 mwi 
        on mwi.id = mcb.marking_window_item_id
 left join users u on u.id = mcb.uid
 left join user_metas um
        on um.uid = mcb.uid
       and um.key_namespace = 'eqao_scoring'
       and um.key = 'ProfileId'
where mwi.marking_window_id = ?
and mcb.is_training = 0
order by mcb.id  desc
;`


/** Find which responses are in the sets of this item, and relevant eligibility and gender info if required */
export const SQL_FT_SAMPLE_RESPONSES_IN_SETS = (sample_size: number|undefined = undefined, is_sample_even_gender_split = false) => {
  return (`
    select distinct setsel.taqr_id
    ${ sample_size ?  ', tes.id as tes_id' : ''}
    ${ sample_size && is_sample_even_gender_split ? ', um.value as gender' : ''}
    from 
    marking_response_set mrset
    join marking_response_set_selections setsel
      on setsel.set_id = mrset.id
      and mrset.window_item_id = :mwi_id
    join marking_taqr_cache mtc
      on setsel.taqr_id = mtc.taqr_id
      and mtc.is_material = 0
      and mtc.mwi_id = :mwi_id
    ${ sample_size ? `
      left join test_attempt_question_responses taqr
        on setsel.taqr_id = taqr.id
        and taqr.is_invalid = 0
      left join test_attempts ta
        on taqr.test_attempt_id = ta.id
        and ta.is_invalid = 0
      left join tw_exceptions_students tes
        on ta.uid = tes.uid
        and tes.test_window_id = :test_window_id
        and tes.is_revoked = 0
        and tes.category in (:ftExcCategories)
        ` : ''
    }
    ${ sample_size && is_sample_even_gender_split ? `
      left join user_metas um
        on um.key_namespace = "eqao_sdc"
        and um.key = "Gender"
        and um.uid = ta.uid
    ` : ''}
      where mrset.window_item_id = :mwi_id
      and (mrset.is_revoked = 0 or mrset.is_revoked is null)
  `)
}

/** Find which responses to keep - limit sample size and only take M/F gender if required  */
export const SQL_FT_SAMPLE_RESPONSES_TO_KEEP = (size_limit: number|undefined = undefined, limit_gender_value?: string) => {
    return (`
      select distinct mtc.taqr_id 
      from marking_taqr_cache mtc
      join test_attempt_question_responses taqr
        on taqr.id = mtc.taqr_id
        and taqr.is_invalid = 0
      join test_attempts ta
        on taqr.test_attempt_id = ta.id
        and ta.is_invalid = 0
      join tw_exceptions_students tes
        on ta.uid = tes.uid
        and tes.test_window_id = :test_window_id
        and tes.is_revoked = 0
        and tes.category in (:ftExcCategories)
      ${size_limit && limit_gender_value ? `
        join user_metas um
          on um.key_namespace = "eqao_sdc"
          and um.key = "Gender"
          and um.uid = ta.uid
          and um.value = ${limit_gender_value}
      ` : ''}
      left join marking_response_set_selections setsel
          on setsel.taqr_id = mtc.taqr_id
      left join marking_response_set mrset
          on setsel.set_id = mrset.id
          and mrset.window_item_id = mtc.mwi_id
      where mtc.mwi_id = :mwi_id and mrset.id is null
      and mtc.is_material = 0
      ${size_limit ? `ORDER BY RAND() LIMIT ${size_limit}` : ''}
    `)
  }

// < 5 sec (background process)
// [marking_window_item_id]
export const SQL_ITEM_SUMMARY_RESPONSES = `
  select 
    mtc.taqr_id,
    mtc.is_material,
    mcbr.id as mcbr_id,
    mcbr.is_marked,
    mcbr.is_invalid,
    mcbr.is_revoked,
    mcbr.is_validity,
    mcbr.is_validity_exact,
    mcbr.is_validity_adj,
    mcbr.is_rescore_indic,
    mcb.id as mcb_id,
    mcb.is_leader,
    mrs.score_option_id as leader_score,
    mrs.is_expert_score
    from marking_taqr_cache mtc
    join marking_window_items_v2 mwi
      on mtc.mwi_id = mwi.id
        and mwi.id = ?
    left join marking_claimed_batch_responses mcbr
      on mcbr.taqr_id = mtc.taqr_id
        and mcbr.window_item_id = mwi.id
        and mcbr.is_revoked = 0
    left join marking_claimed_batches mcb
        on mcb.id = mcbr.claimed_batch_id
      and mcb.is_training = 0
    left join marking_response_selections mrs
      on mrs.taqr_id = mtc.taqr_id
      and mrs.window_item_id = mtc.mwi_id
    where (mrs.is_expert_score = 1 and mtc.is_material = 0) or mcb.id is not null;
`;

  // [marking_window_id]
  // Background process - ~25 sec on mw 51, mirror db
  export const SQL_MCBR_OUTDATED_SCORES_TO_INVALIDATE = `
  -- SQL_MCBR_OUTDATED_SCORES_TO_INVALIDATE
      select /*+ MAX_EXECUTION_TIME(1440000000)*/
    distinct 
    taqr.id as taqr_id,
    mcbr.id as mcbr_id,
    IF(mcbr.marked_on < tasr.uploaded_on, 1, 0) as is_scan_reuploaded,
    mso.value as original_score_given,
    mcbr.marked_on as response_marked_on,
    taqr.created_on as response_started_on,
    taqr.updated_on as response_last_updated_on,
    tasr.uploaded_on as scan_reuploaded_on,
    tau.approved_on as unsubmission_manual_approved_on
    from marking_window_items_v2 mwi
    join marking_claimed_batch_responses mcbr
      on mwi.id = mcbr.window_item_id 
      and mcbr.is_marked = 1
      and mcbr.is_revoked = 0
      and mcbr.is_invalid is null
    join marking_claimed_batches mcb
		  on mcbr.claimed_batch_id = mcb.id
      and mcb.is_leader = 0
    join test_attempt_question_responses taqr
      on taqr.id = mcbr.taqr_id
      and taqr.is_invalid = 0
    join test_attempts ta
		  on ta.id = taqr.test_attempt_id
	  left join test_attempt_unsubmissions tau
		  on tau.test_session_id = ta.test_session_id
		  and tau.student_uid = ta.uid
      and tau.is_revoked = 0
    left join marking_response_scores mrs
		  on mrs.batch_response_id = mcbr.id
    left join marking_score_profile_options mspo
      on mspo.id = mrs.score_option_id
    left join marking_score_options mso
      on mso.id = mspo.score_option_id
    left join test_attempt_scan_responses tasr
      on taqr.id = tasr.taqr_id
      and tasr.is_discarded = 0
    where (mcbr.marked_on < taqr.updated_on or mcbr.marked_on < tasr.uploaded_on)
      and mwi.marking_window_id = ?
    group by mcbr.id;
  `;
