import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadReporting } from '../../../../util/db-raw';
import { ReportIssue } from '../../educator/session-reported-issue/session-reported-issue.class';
import _ from 'lodash';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';
import { generatePrefixFromString } from '../../../../util/string-helpers';

interface Data {}

interface ServiceOptions {}

interface QueryParams {
  test_window_ids_array: number[];
  checking_days?: number;
}

enum subjectPrefix {
  COMPONENT_SLUGS = 'COMPONENT_SLUGS'
}

export class MultiModulesChecking implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Check attempt's double modules and create reported issue on issue-reviewer view
   * @param data 
   * test_window_ids: []number  
   * check_previous_days: number/undefine  check attempts that are last touch in "check_previous_days".  undefine will check all test attempts   
   * operational_only: boolean 
   * @param params
   * @returns checking attempts count 
   */
  async create (data: any, params?: Params): Promise<Data> {
    if (!params || ! params.query ){
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const { test_window_ids, check_previous_days, operational_only } = data;
    const created_by_uid = await currentUid(this.app, params);
    const checking_days = +check_previous_days
    const is_operational_only = !!operational_only
    
    //pull test windows that require double module. Checking from db instead from input
    const doubelModuleCheckWindows = await dbRawReadReporting(this.app, {}, `
       -- 94ms on mirror db
        select tw.id as test_window_id
          from test_windows tw 
         where tw.is_allow_multiple_module_check = 1
    ;`);
    const test_window_ids_array:number[] = doubelModuleCheckWindows.map( (doubelModuleCheckWindow:any) => { return doubelModuleCheckWindow.test_window_id})

    const query_params:QueryParams = { test_window_ids_array }
    if(checking_days){
      query_params.checking_days = checking_days
    }
    //get attempts ids in test windows
    const testAttemptIdRecords:any[]= await dbRawReadReporting(this.app, query_params, `
       select /*+ MAX_EXECUTION_TIME(1440000000)*/
              distinct
              ta.id
         from test_attempts ta
         join test_window_td_alloc_rules twtdar 
           on twtdar.id = ta.twtdar_id
          and twtdar.is_questionnaire = 0  
          and twtdar.test_window_id in (:test_window_ids_array)
          ${is_operational_only?'and twtdar.is_sample = 0':''}
        where ta.is_invalid = 0
          and ta.started_on is not null
          ${checking_days?'and ta.last_touch_on >= (NOW() - INTERVAL :checking_days DAY)':''}
    ;`);

    const testAttemptIds:any[] = testAttemptIdRecords.map( (testAttemptIdRecord:any) => {return testAttemptIdRecord.id });

    //asynchronous check double module for each test attempts
    (async () =>{
      for(let testAttemptIdChuncks of _.chunk(testAttemptIds, 1000)){ // chunk it into 1000 attempts each time to reduce the db work load.
        await this.checkDoubleModule(testAttemptIdChuncks, created_by_uid);
      }
    })()
    
    logger.info({
      slug: 'MULTI_MODULES_CHECK',
      test_window_ids: test_window_ids
    })

    return {test_attempt_count: testAttemptIds.length}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Check attempt's double modules and create reported issue on issue-reviewer view
   * @param testAttemptIds
   * @param created_by_uid 
   * @returns 
   */
  async checkDoubleModule(testAttemptIds: number[], created_by_uid:number ){
    const isEmailDisabled = false;

    // Check if any attempts have double module accessed
    // Took 515 second to process 81924 test attempts records  ~ 160 test attempts per second.
    const doubleModuleData = await dbRawReadReporting(this.app, {testAttemptIds}, `
       -- Took 515 second to process 81924 test attempts records  ~ 160 test attempts per second.
       select /*+ MAX_EXECUTION_TIME(1440000000)*/
              distinct
              ta.test_session_id as test_session_id
            , scts.school_class_id as school_class_id
            , taqr.test_attempt_id
            , taqr.section_id
            , 'Double Module Accessed' as categorySelection
            , ta.uid as student_uid 
            , twtdar.component_slug 
            , count(distinct taqr.module_id) as module_count
            , group_concat(distinct taqr.module_id) as access_modules
         from test_attempts ta
         join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
         join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and taqr.is_invalid = 0
         join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        where ta.id in (:testAttemptIds)
     group by taqr.test_attempt_id, taqr.section_id
       having module_count > 1
    ;`);

    //created reported issue if any doubleModule attempts was found
    const doubleModuleAcessCategory = 'Double Module Access'
    const prefix = generatePrefixFromString(subjectPrefix.COMPONENT_SLUGS)
    for(let doubleModuleDataChunck of _.chunk(doubleModuleData, 5)){ // chuck it into 5 each time to reduce the db work load.
      await Promise.all(doubleModuleDataChunck.map( async doubleModuleStudent =>{
        //filter out those already have reported issue
        const student_uid = doubleModuleStudent.student_uid
        const section_id = doubleModuleStudent.section_id
        const test_attempt_id = doubleModuleStudent.test_attempt_id
        const exsitingReportedIssue = await dbRawRead(this.app, { doubleModuleAcessCategory, student_uid, test_attempt_id, section_id }, `
            -- Query Took 90ms to run 
            select ric.id
              from reported_issues_common ric
              join reported_issues ri on ri.reported_issues_common_id = ric.id and ri.testtaker_uid = :student_uid
             where ric.category = :doubleModuleAcessCategory
               and JSON_EXTRACT(ric.description, '$.section_id') = :section_id
               and JSON_EXTRACT(ric.description, '$.test_attempt_id') = :test_attempt_id
               and ric.is_resolved = 0
        ;`);
        // report already existed return
        if(exsitingReportedIssue.length > 0){
          return
        }

        //create report if non exist
        const ReportIssues:ReportIssue = {
          test_session_id: doubleModuleStudent.test_session_id,
          student: [doubleModuleStudent.student_uid],
          msg: JSON.stringify(doubleModuleStudent),
          categorySelection: doubleModuleAcessCategory,
          school_class_id:doubleModuleStudent.school_class_id, 
          subjectsSelected: doubleModuleStudent.component_slug ? `${prefix}_${doubleModuleStudent.component_slug}` : ''
        };
        await this.app.service('public/educator/session-reported-issue').createReportedIssue(created_by_uid, ReportIssues, isEmailDisabled)
      }))
    }      
  }
}
