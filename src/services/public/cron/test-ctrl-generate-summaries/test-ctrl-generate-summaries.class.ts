import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { CourseType } from '../../../public/test-ctrl/schools/summary/summary.class'
import { storeInS3 } from '../../../upload/upload.listener';
import logger from './../../../../logger';
import { dbRawReadReporting } from '../../../../util/db-raw';

interface Data {}

export interface ICreateSummaryCache {
  tc_group_id: number,
  test_window_id: number,
  type_slug: string
}

export enum UserGroupType {
  All = 'All',
  Registered = 'Registered',
  BoardCompletedTech = 'BoardCompletedTech',
  CompletedTech = 'CompletedTech',
  CompletedSample = 'CompletedSample',
  CompletedSampleAndTech = 'CompletedSampleAndTech',
  StartedOper = 'StartedOper',
  CompletedOper = 'CompletedOper',
  StuReportsAccessed = 'StuReportsAccessed',
}

interface ServiceOptions {}

export class TestCtrlGenerateSummaries implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: ICreateSummaryCache, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    //throw error if data have test window id which mean the aws event scheduler trigger this end point is outdated.
    //This is to prevent when this get push to prod, and we didn't update the event scheduler on aws causinng this end point to be called multiple times.
    if(data.test_window_id){
      throw new Errors.BadRequest('OUTDATED_INPUT_FORMAT');
    }

    //pull test windows that require test controller summaries
    const ctrlSummariesCheckWindows = await dbRawReadReporting(this.app, {}, `
      -- 94ms on mirror db
       select tw.id as test_window_id
            , tw.type_slug
            , tw.test_ctrl_group_id as tc_group_id
         from test_windows tw 
        where tw.is_allow_check_ctrl_summaries = 1
    ;`);
    
    //asynchronous iterate through the test window that need test controller summaries one test window each time
    (async () =>{
      for( let ctrlSummariesCheckWindow of ctrlSummariesCheckWindows ){
        const test_window_id = ctrlSummariesCheckWindow.test_window_id
        const type_slug = ctrlSummariesCheckWindow.type_slug
        const tc_group_id = ctrlSummariesCheckWindow.tc_group_id
        data.test_window_id = test_window_id
        data.type_slug = type_slug
        data.tc_group_id = tc_group_id
        try {
          await this.generateTestWindowSummaries(data, params)
        } catch (e) {
          logger.error('test-ctrl-generate-summaries', { test_window_id,  type_slug });
        }
      }
    })()
    return {}
  }

  /**
   * calculate the new value for test controller summaries for a test window
   * @param data {tc_group_id, test_window_id, type_slug}
   * @param params 
   * @returns 
   */
  async generateTestWindowSummaries(data: ICreateSummaryCache, params?: Params){
    if (params) {
      const {
        tc_group_id,
        test_window_id,
        type_slug
      } = data;

      if (!test_window_id) {
        throw new Errors.BadRequest('MISSING_FS_ID');
      }

      const courseTypeVals: string[] = Object.values(CourseType);

      if(!(courseTypeVals.includes(type_slug))) {
        throw new Errors.BadRequest('ERR_COURSE_TYPE_PARAM');
      }

      let newParams: any = {
        ...params,
        ...{
          query: {
            ...params.query,
            ...{
              bustCache: true,
              test_window_id,
              type_slug,
              tc_group_id
            }
          }
        }
      };
      try {
        //process already asynchonize running on parent function, so add await here
        await this.generateSummaries(newParams, test_window_id, type_slug);
      } catch (e) {
        logger.error('test-ctrl-generate-summaries', { test_window_id, type_slug });
      }

      return {};
    }
    return {};
  }

  async generateSummaries(newParams:any, test_window_id:number, type_slug:string) {
    try {
      let cacheSummary: any = {};
      let cacheDetail: any = {};
      const keys = Object.keys(UserGroupType);

      const boards = await this.app.service('public/test-ctrl/schools/boards').find(newParams); // id
      const schools = await this.app.service('public/test-ctrl/schools/school').find(newParams); // school district id
      const teachers = await this.app.service('public/test-ctrl/schools/teachers').find(newParams); // uid
      const classes = await this.app.service('public/test-ctrl/schools/classes').find(newParams); // classid
      const students = await this.app.service('public/test-ctrl/schools/students').find(newParams); // uid

      const filePaths:any = {};
      for (let key of keys) {
        newParams.query['col'] = key;
        
        cacheSummary[key] = await this.app.service('public/test-ctrl/schools/summary').get(0, newParams);

        cacheDetail[key] = {
          boards,
          schools,
          teachers,
          classes,
          students
        }

        const timestamp = (new Date()).valueOf();
        const filePath = 'test-ctrl-summaries-detail/'+'TW'+test_window_id+'-'+type_slug+'/'+key+'/'+timestamp+'.json';
        filePaths[key] = filePath;
        await storeInS3(filePath, JSON.stringify(cacheDetail[key]));
      }

      const cacheSummaryStringify = JSON.stringify(cacheSummary);
      const cacheDetailStringfy = JSON.stringify(filePaths);
      this.app
        .service('db/write/test-controller-dashboard-cache')
        .create({
          summary_cache: cacheSummaryStringify,
          detail_cache: cacheDetailStringfy,
          timestamp: dbDateNow(this.app),
          test_window_id
        });
    } catch (e) {
      logger.error('test-ctrl-generate-summaries', { test_window_id, type_slug });
    }

  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
