// Initializes the `public/cron/responses/out-of-order-check` service on path `/public/cron/responses/out-of-order-check`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { OutOfOrderCheck } from './out-of-order-check.class';
import hooks from './out-of-order-check.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/cron/responses/out-of-order-check': OutOfOrderCheck & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/cron/responses/out-of-order-check', new OutOfOrderCheck(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/cron/responses/out-of-order-check');

  service.hooks(hooks);
}
