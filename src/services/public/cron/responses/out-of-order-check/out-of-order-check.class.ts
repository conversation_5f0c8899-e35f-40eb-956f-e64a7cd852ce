import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { Knex } from 'knex';
import { listObjectsV2, streamFromS3 } from '../../../../upload/upload.listener';
import { Readable } from 'stream';
import { createGunzip } from 'zlib';
import readline from 'readline';
import _ from 'lodash';
import { currentUid } from '../../../../../util/uid';
import { ReportIssue } from '../../../educator/session-reported-issue/session-reported-issue.class';
import { dbDateNow } from '../../../../../util/db-dates';
import { STU_EX_OVERRIDES } from '../../../test-ctrl/schools/student-exceptions/student-exceptions.class';

const RESPONSE_SAVE_PREFIX = 'processed_logs/' + 'public_student_session_question/'; // TODO: remove hardcoding here and in the lambda pre-processor
const RESPONSE_SAVE_URL = '/public/student/session-question?';

interface Data {}

interface ServiceOptions {}

enum ResponseSaveAuditStatus {
  INIT = '01_INIT',
  PROCESSING_LOGS = '02_PROCESSING_LOGS',
  REMOVING_EXISTING_RECORDS = '03_REMOVING_EXISTING_RECORDS',  
  REPORTING_ISSUES = '04_REPORTING_ISSUES',
  APPLYING_EXCEPTIONS = '05_APPLYING_EXCEPTIONS',
  SAVING_ISSUES = '06_SAVING_ISSUES',
  COMPLETE = '07_COMPLETE',
  ERROR = 'ERROR',
}

interface LastResponse {
  attempt_id: number;
  item_id: number;
  timestamp: string; // ISO timestamp with 'Z' timezone indicator
  clientTimestamp: number; // the actual numerical timestamp
  lastTimestamp: any;
  lastClient: any;
  taqr_id?: number;
  test_window_id?: number;
  uid?: number;
  test_session_id?: number;
  component_slug?: string; // for PJ
  school_class_id?: number;
  ric_id?: number; // reported_issues_common's id
}

export class OutOfOrderCheck implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const knex: Knex = this.app.get('knexClientReadReporting');
    const results = await knex('response_save_audit_log')
      .select([
        'id',
        knex.raw('DATE_FORMAT(`date`, "%Y-%m-%d") as `date`'),
        'status',
        'created_by_uid',
        'created_on',
        'updated_on',
        'completed_on',
        'failure_reason'
      ])
      .orderBy('id', 'desc');
    return results;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('Missing parameters')
    }

    // Validate date parameter
    let dateStr = params?.query?.date;
    const {date, year, month, day} = parseDateQueryParam(dateStr);

    // Create audit log to track progress
    const uid = await currentUid(this.app, params);
    const auditId = await this.initAuditLog(dateStr, uid);

    // Run the audit in the background
    this.startAudit(params, auditId, uid, year, month, day);

    return { auditId };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async startAudit(params: Params, auditId: number, uid: number, year: number, month: number, day: number) {
    try {
      // Get log bucket from API config
      const LOGS_BUCKET = this.app.get('processDataBucket');
      if (!LOGS_BUCKET) {
        throw new Error('processDataBucket is not defined in the config');
      }

      // Get a list of log file paths from s3 for the specified date
      const logFiles = await this.getLogFilesFromDate(LOGS_BUCKET, year, month, day);
      if (logFiles.length === 0) {
        throw new Errors.NotFound(`No responses found for ${year}-${month}-${day}`)
      }

      // Group the log file paths by UID
      const logsByUid = groupLogsByUID(logFiles);
      if (!logsByUid) { // Basically an assert
        throw new Errors.Unprocessable('Failed to group logs by uid');
      }

      // Process logs for each UID group
      await this.updateLogStatus(auditId, ResponseSaveAuditStatus.PROCESSING_LOGS);
      const flaggedSaves = await this.processLogs(LOGS_BUCKET, logsByUid, auditId);

      if (flaggedSaves.length > 0) {
        // Remove any existing records from the database, then insert the latest ones afterward
        await this.updateLogStatus(auditId, ResponseSaveAuditStatus.REMOVING_EXISTING_RECORDS);
        await this.removeExistingRecords(flaggedSaves, uid, auditId);

        // Create reported issues
        await this.updateLogStatus(auditId, ResponseSaveAuditStatus.REPORTING_ISSUES);
        await this.createReportedIssues(flaggedSaves, uid, auditId);

        // Apply pending status to block ISR generation
        await this.updateLogStatus(auditId, ResponseSaveAuditStatus.APPLYING_EXCEPTIONS);
        await this.applyExceptions(flaggedSaves, uid, params);

        // Save flaaged responses to the DB
        await this.updateLogStatus(auditId, ResponseSaveAuditStatus.SAVING_ISSUES);
        await this.saveFlaggedRecords(flaggedSaves, auditId);
      }

      // End of process
      await this.updateLogStatus(auditId, ResponseSaveAuditStatus.COMPLETE);

    } catch (e: any) {
      // Save error to audit log
      await this.updateLogStatus(auditId, ResponseSaveAuditStatus.ERROR, { errMsg: e.message });
      throw e;
    }
  }

  /** Gets all attempts touched on the date, ordered by uid
   */
  async getAttemptsFromDate(testWindowIds: number[], date: Date) {
    const start = new Date(date);
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);

    const knex: Knex = this.app.get('knexClientReadReporting');
    const query = knex('test_attempts as ta')
      .join('test_window_td_alloc_rules as twtar', 'twtar.id', 'ta.twtdar_id')
      .select('ta.id as test_attempt_id', 'ta.uid')
      .whereBetween('ta.last_touch_on', [start, end])
      .whereIn('twtar.test_window_id', testWindowIds)
      .orderBy('uid');

    return await query;
  }

  /** Gets all log files that are touched on the date
   */
  async getLogFilesFromDate(bucket: string, year:number, month:number, day:number) {
    let prefix = RESPONSE_SAVE_PREFIX;
    prefix += `year=${year}/month=${String(month).padStart(2, '0')}/day=${String(day).padStart(2, '0')}/`;

    let files = await listObjectsV2(bucket, prefix)
    files = files.filter( (o: string) => o.endsWith('.gz'));

    return files;
  }

  buildProgress(index: number, total: number): string {
    return `(${index}/${total})`;
  }

  areResponseRawsEqual(jsonStrA: string, jsonStrB: string): boolean {
    const normalizeJsonString = (str: string): string => {
      try {
        const obj = JSON.parse(str);
        if (obj?.__meta?.createdOn !== undefined) { // `createdOn` should not be compared
          delete obj.__meta.createdOn;
        }
        return JSON.stringify(obj);
      } catch {
        return str; // Return original if not valid JSON
      }
    };

    const normalizedA = normalizeJsonString(jsonStrA);
    const normalizedB = normalizeJsonString(jsonStrB);

    return normalizedA === normalizedB;
  }

  async processLogs(bucket: string, logsByUid: Map<string, string[]>, auditId: number) {
    let flaggedSaves = [];

    const total = logsByUid.size;
    let index = 0;

    await this.updateLogStatus(auditId, ResponseSaveAuditStatus.PROCESSING_LOGS, { progress: this.buildProgress(index, total) });

    for (let [uidPartKey, logFiles] of logsByUid.entries()) {
      console.log(`uids ${uidPartKey}, ${logFiles.length} files ${(new Set(logFiles)).size} unique`)

      // Get the latest response entries from the log files
      const latest = await latestResponses(bucket, logFiles);
      console.log(`\t${latest.size} responses`)

      // Identify issues where the timestamps and response_raw don't match
      let problems = Array.from(latest.values()).filter( (a: LastResponse) => a.lastTimestamp !== a.lastClient && !this.areResponseRawsEqual(a.lastTimestamp.requestBody?.response_raw, a.lastClient.requestBody?.response_raw))
      console.log(`\t${problems.length} flagged`)

      // Populate additional details for the flagged responses
      problems = await this.populateResponseDetails(problems);
      
      // Collect all flagged issues 
      flaggedSaves.push(...problems);

      // Update progress
      index++;
      await this.updateLogStatus(auditId, ResponseSaveAuditStatus.PROCESSING_LOGS, { progress: this.buildProgress(index, total) });
    }

    console.log(`total number of flagged saves: ${flaggedSaves.length}`)

    return flaggedSaves;
  }

  async populateResponseDetails(responses: LastResponse[]) {
    if (responses.length === 0) return [];

    // Prepare composite key tuples
    const keys = responses.map((response) => [response.attempt_id, response.item_id]);
  
    // Query the DB for TAQR records
    const knex: Knex = this.app.get('knexClientReadReporting');
    const taqrs = await knex('test_attempt_question_responses as taqr')
      .select('taqr.id', 'taqr.test_attempt_id', 'taqr.test_question_id', 'twtar.test_window_id', 'ta.uid', 'ta.test_session_id', 'twtar.component_slug', 'scts.school_class_id')
      .join('test_attempts as ta', 'ta.id', 'taqr.test_attempt_id')
      .join('test_window_td_alloc_rules as twtar', 'twtar.id', 'ta.twtdar_id')
      .join('school_class_test_sessions as scts', 'scts.test_session_id', 'ta.test_session_id')
      .whereIn(['taqr.test_attempt_id', 'taqr.test_question_id'], keys)
      .groupBy('taqr.id')
      ;

    // Index the TAQR results for quick lookup
    const taqrMap = new Map();
    for (const taqr of taqrs) {
      const key = `${taqr.test_attempt_id}-${taqr.test_question_id}`;
      taqrMap.set(key, taqr);
    }

    // Fill in additional fields
    const missingTAQR: { attemptId: number, outputIndex: number }[] = [];
    const output = responses.map((response, index) => {
      const key = `${response.attempt_id}-${response.item_id}`;
      const taqr = taqrMap.get(key);
      if (!taqr) { missingTAQR.push({ attemptId: response.attempt_id, outputIndex: index }) }
      return { 
        ...response,
        taqr_id: taqr?.id,
        test_window_id: taqr?.test_window_id,
        uid: taqr?.uid,
        test_session_id: taqr?.test_session_id,
        component_slug: taqr?.component_slug,
        school_class_id: taqr?.school_class_id,
      };
    });

    // Fill in additional fields for any records missing TAQR
    if (missingTAQR.length > 0) {
      const attemptIds = missingTAQR.map((response) => response.attemptId);
      const attempts = await knex('test_attempts as ta')
        .select('ta.id', 'twtar.test_window_id', 'ta.uid', 'ta.test_session_id', 'twtar.component_slug', 'scts.school_class_id')
        .join('test_window_td_alloc_rules as twtar', 'twtar.id', 'ta.twtdar_id')
        .join('school_class_test_sessions as scts', 'scts.test_session_id', 'ta.test_session_id')
        .whereIn('ta.id', attemptIds)
        .groupBy('ta.id')
        ;

      const attemptMap = new Map(attempts.map((attempt) => [attempt.id, attempt]));

      missingTAQR.forEach((response) => {
        const { attemptId, outputIndex: idx } = response;
        const attempt = attemptMap.get(attemptId);
        if (attempt) {
          const { test_window_id, uid, test_session_id, component_slug, school_class_id } = attempt;
          const outputRecord = output[idx];
          outputRecord.test_window_id = test_window_id;
          outputRecord.uid = uid;
          outputRecord.test_session_id = test_session_id;
          outputRecord.component_slug = component_slug;
          outputRecord.school_class_id = school_class_id;
        }
      });
    }

    return output;
  }

  async removeExistingRecords(responses: LastResponse[], uid: number, audit_id: number) {
    // Revoke existing records and resolve associated reported issues
    // Keep the 'PENDED' exception, as it may have come from other sources and it also needs to be applied later regardless

    // Prepare composite key tuples
    const keys = responses.map((response) => [response.attempt_id, response.item_id]);
  
    // Query the DB for existing records
    const knexRead: Knex = this.app.get('knexClientReadReporting');
    const existingRecords = await knexRead('response_save_issues')
      .select('id', 'ric_id')
      .whereIn(['test_attempt_id', 'item_id'], keys)
      .where({
        'is_resolved': 0,
        'is_revoked': 0,
      });
      
    if (existingRecords.length > 0) {
      const knexWrite: Knex = this.app.get('knexClientWrite');

      // revoke existing records
      const ids = existingRecords.map((record) => record.id);
      await knexWrite('response_save_issues')
        .whereIn('id', ids)
        .update({ 
          is_revoked: 1,
          revoked_on: dbDateNow(this.app),
          revoked_by_uid: uid,
          revocation_reason: `Response found in new audit (id: ${audit_id})`,
        });

      // resolve associated reported issues
      const ricIds = existingRecords.map((record) => record.ric_id).filter((ricId) => ricId != null);
      if (ricIds.length > 0) {
        await knexWrite('reported_issues_common')
          .whereIn('id', ricIds)
          .update({ 
            is_resolved: 1,
            resolved_on: dbDateNow(this.app),
            resolved_by_uid: uid,
          });
      }
    }

    console.log('removed existing records');
  }

  async createReportedIssues(responses: LastResponse[], created_by_uid: number, audit_id: number) {
    const issueCategory = 'Out-of-Order Response Save'
    const isEmailDisabled = true; // prevent sending email to support
    const chunkSize = 5;

    for (let responseChunck of _.chunk(responses, chunkSize)) {
      await Promise.all(responseChunck.map(async (response) => {
        const { attempt_id, item_id, taqr_id, uid, test_session_id, component_slug, school_class_id } = response;
        if (test_session_id && uid && school_class_id) { // required params to create a reported issue, populated by the function populateResponseDetails
          const record: ReportIssue = {
            test_session_id: test_session_id,
            student: [uid],
            msg: JSON.stringify({ audit_id, attempt_id, item_id, taqr_id }),
            categorySelection: issueCategory,
            school_class_id: school_class_id,
            subjectsSelected: component_slug ? `CS_${component_slug}` : '', // CS: prefix of COMPONENT_SLUGS
          };
          const ric = await this.app.service('public/educator/session-reported-issue').createReportedIssue(created_by_uid, record, isEmailDisabled);
          response.ric_id = ric.id;
        }
      }))
    }

    console.log(`created reported issues`);
  }

  async applyExceptions(responses: LastResponse[], uid: number, params: Params) {
    // If the ISR has already been generated, it will revoke the existing report and trigger regeneration
    // Reference: /public/test-ctrl/schools/student-exceptions - Function: applyActionsToActiveReports

    // Group by test window id
    const grouped = new Map<number, number[]>();
    for (const response of responses) {
      const { test_window_id, uid } = response;
      if (!test_window_id || !uid) continue;
      if (!grouped.has(test_window_id)) grouped.set(test_window_id, []);
      // ensure unique uid
      const group = grouped.get(test_window_id)!;
      if (!group.includes(uid)) {
        group.push(uid);
      }
    }

    // Apply exceptions via the student exception endpoint
    for (const [testWindowId, uids] of grouped) {
      const data = {
        test_window_id: testWindowId,
        uids,
        is_pended: 1, 
        category: STU_EX_OVERRIDES.PENDED,
        notes: 'Pending investigation for response save issue',
      };
      await this.app.service('public/test-ctrl/schools/student-exceptions').create(data, params); // requires `test_ctrl_issue_revw` role (ensure cron user has it)
    }

    console.log(`applied exceptions`);
  }

  async saveFlaggedRecords(responses: LastResponse[], audit_id: number) {
    const knex: Knex = this.app.get('knexClientWrite');
    const records = responses.map((response) => ({
      audit_id,
      test_window_id: response.test_window_id,
      uid: response.uid,
      test_attempt_id: response.attempt_id,
      item_id: response.item_id,
      taqr_id: response.taqr_id,
      log_server: response.lastTimestamp,
      log_server_api_timestamp: response.lastTimestamp.timestamp,
      log_server_client_timestamp: new Date(response.lastTimestamp.requestBody.clientTimestamp),
      log_client: response.lastClient,
      log_client_api_timestamp: response.lastClient.timestamp,
      log_client_client_timestamp: new Date(response.lastClient.requestBody.clientTimestamp),
      ric_id: response.ric_id,
    }));
    await knex('response_save_issues').insert(records);
    console.log(`saved flagged records`);
  }

  async initAuditLog(date: string, created_by_uid: number) {
    const knex: Knex = this.app.get('knexClientWrite');
    const id = await knex('response_save_audit_log').insert({ date, created_by_uid });
    return id[0];
  }

  async updateLogStatus(auditId: number, status: ResponseSaveAuditStatus, options?: { progress?: string; errMsg?: string }) {
    const knex: Knex = this.app.get('knexClientWrite');
    await knex('response_save_audit_log')
      .where('id', auditId)
      .update({
        status: status + (options?.progress ? ` ${options.progress}` : ''),
        updated_on: dbDateNow(this.app),
        completed_on: status === ResponseSaveAuditStatus.COMPLETE ? dbDateNow(this.app) : null,
        failure_reason: options?.errMsg,
      });
  }
}
function parseDateQueryParam(dateStr: string): { date: Date; year: number; month: number; day: number; } {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateStr)) {
    throw new Errors.BadRequest('Invalid date format. Use YYYY-MM-DD.');
  }

  const [year, month, day] = dateStr.split('-').map(Number);
  const date = new Date(year, month - 1, day);

  if (isNaN(date.getTime())) {
    throw new Errors.BadRequest('Invalid date value.');
  }

  const now = new Date();
  if (date.getTime() > now.getTime()) {
    throw new Errors.BadRequest('Date cannot be in the future.');
  }

  return { date, year, month, day };
}

function partitionByUids(attempts: {uid: number; test_attempt_id: number}[], partSize: number) {
  const parts: Map<string, {uid: number; test_attempt_id: number}[]> = new Map();
  for (let attempt of attempts) {
    let partKey = Math.floor(attempt.uid / partSize).toFixed(0);
    if (!parts.has(partKey)) {
      parts.set(partKey, []);
    }
    parts.get(partKey)?.push(attempt);
  }
  return parts;
}

function filterLogsByUid(logKeys: string[], uidPartKey: string) {
  const pattern = 'uids=' + uidPartKey + '0000-';
  return logKeys.filter( (o: string) => o.includes(pattern));
}


function groupLogsByUID(logKeys: string[]) {
  const logUidRegex = /uids=(\d+)/
  const groups: Map<string, string[]> = new Map();
  for (const key of logKeys) {
    const match = key.match(logUidRegex);
    if (match) {
      let uid = match[1];
      if (!groups.has(uid)) {
        groups.set(uid, [])
      }
      groups.get(uid)?.push(key);
    }
  }
  return groups;
}

/** Gets the latest response by `.timestamp` and `.clientTimestamp`
  *
  * Returned as a map of
  * `'attempt_id;item_id' => {attempt_id, item_id, timestamp, clientTimestamp, lastTimestamp, lastClient}`
  */
async function latestResponses(bucket: string, logFiles: string[]) {
  const collection: Map<string, LastResponse> = new Map();

  // NOTE: technically, this could be done concurrently on each file
  for (const logFile of logFiles) {
    // TODO: how to present these monitoring return values
    const { skippedLogs, missingDataCounts } = await _latestResponses(bucket, logFile, collection)
  }
  return collection;
}

/** Gets the latest saved value for each response bi `.timestamp` and `.clientTimestamp`
  *
  * Helper function to work within a single log file
  */
async function _latestResponses(bucket: string, logFile: string, acc?: Map<string, LastResponse>) {
  if (!acc) {
    acc = new Map();
  }
  const logs = streamFromS3(bucket, logFile) as Readable;
  const gunzipStream = createGunzip();
  const rl = readline.createInterface({
    input: logs.pipe(gunzipStream),
    crlfDelay: Infinity,
  });

  let skippedLogs: any[] = [];
  let missingDataCounts: {[key: string]: any[]} = {};
  for await (const line of rl) {
    try {
      const resp = JSON.parse(line);
      // NOTE: only the `create` method requests with url starts with `/public/student/session-question?` are actual saves
      if (resp.method !== "create" || resp.level !== "info" || !resp.originalUrl.startsWith(RESPONSE_SAVE_URL)) {
        skippedLogs.push(resp);
        continue;
      }
      // Pull out the related values
      const {
        test_attempt_id: attempt_id,
        test_question_id: item_id,
        clientTimestamp } = resp.requestBody;
      const timestamp = resp.timestamp;
      if (!clientTimestamp || !item_id || !attempt_id || !timestamp) {
        if (!clientTimestamp) {
          missingDataCounts.clientTimestamp = missingDataCounts.clientTimestamp ?? [];
          missingDataCounts.clientTimestamp.push(resp);
        }
        if (!timestamp) {
          missingDataCounts.timestamp = missingDataCounts.timestamp ?? [];
          missingDataCounts.timestamp.push(resp);
        }
        if (!attempt_id) {
          missingDataCounts.attempt_id = missingDataCounts.attempt_id ?? [];
          missingDataCounts.attempt_id.push(resp);
        }
        if (!item_id) {
          missingDataCounts.item_id = missingDataCounts.item_id ?? [];
          missingDataCounts.item_id.push(resp);
        }
        continue;
      }

      // Get current max
      const respKey = `${attempt_id};${item_id}`;
      const prev = acc.get(respKey);
      if (!prev) {
        acc.set(respKey, {
          attempt_id,
          item_id,
          timestamp,
          clientTimestamp,
          lastTimestamp: resp,
          lastClient: resp,
        });
        continue
      }

      if (timestamp > prev.timestamp) {
        prev.timestamp = timestamp;
        prev.lastTimestamp = resp;
      }
      if (clientTimestamp > prev.clientTimestamp) {
        prev.clientTimestamp = clientTimestamp;
        prev.lastClient = resp;
      }
    }
    catch (err) {
      // TODO: what errors need to be caught and returned in the API?
      throw err;
    }

  }
  if (Object.keys(missingDataCounts).length > 0) {
    console.log(`\tLogs missing data in logfile: ${logFile}`);
    // console.log(`requests: ${JSON.stringify(missingDataCounts)}`);
  }

  return { lastSaves: acc, skippedLogs, missingDataCounts };
}
