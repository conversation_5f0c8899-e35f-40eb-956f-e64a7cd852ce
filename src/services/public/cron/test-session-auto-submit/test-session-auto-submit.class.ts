import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import ExcelJS, { Workbook } from 'exceljs';
import { Stream } from 'stream';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { storeInS3 } from '../../../upload/upload.listener';
import { Readable } from 'stream';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import logger from '../../../../logger';
import { dbDateNow } from '../../../../util/db-dates';
import { RedisKeyTestSession, getRedisTestSession, patchRedisTestSession } from '../../../../redis/table-key-operations/test-session';
import { getTestWindowTestSessions, removeTestWindowTestSessions } from '../../../../redis/relation-key-operations/test-window-test-sessions';
import { KTestSession, KTestWindowAutoSubmit, KTestWindowAutoSubmitMessage, TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT, checkRedisKey, getRedisLock } from '../../../../redis/redis';
import { getRedisTestSessionSubSession } from '../../../../redis/table-key-operations/test-session-sub-session';
import { RedisKeyTestAttempt, getRedisTestAttemptsFromTestSessionId } from '../../../../redis/table-key-operations/test-attempt';
import { getRedisTestAttemptSubSessionsFromTestAttemptId  } from '../../../../redis/table-key-operations/test-attempt-sub-session';
import moment, { Moment } from 'moment';
import { getRedisSchoolClass } from '../../../../redis/table-key-operations/school-class';
import _ from 'lodash';


const CRONCHECKTIME = 5 // 5 minutes

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class TestSessionAutoSubmit implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data> {
    
    const timeOfDay = moment.utc()

      // Step 1. Pull all test windows that are active and have auto submission configurations
      const auto_close_test_windows = await dbRawRead(this.app, [], `
        SELECT tw.id,
             tw.auto_close_start_on,
             tw.auto_close_end_on,
             tw.auto_close_grace_period_m,
             tw.auto_close_default_extension_m
        FROM test_windows tw
        WHERE is_active = 1
        AND tw.auto_close_start_on IS NOT NULL
        AND tw.auto_close_end_on IS NOT NULL
        AND tw.auto_close_grace_period_m IS NOT NULL
        AND tw.auto_close_default_extension_m IS NOT NULL;
      ;`);

      // Step 2. Filter test windows that have current time passed 
      //      2.1 auto_close_start_on  
      const testWindowsInAutoSubmitRange = auto_close_test_windows.filter(testWindow => {
        const autoSubmissionStartOn = this.convertTimeStrIntoDate(testWindow.auto_close_start_on)
        // return test window if it falls in  grace_start_time < now() 
        const graceMinutes = testWindow.auto_close_grace_period_m * 60000
        const start_on_date = moment.utc(autoSubmissionStartOn)
        const grace_start_time=  start_on_date.subtract(graceMinutes);
        return (timeOfDay.valueOf() >= grace_start_time.valueOf())
      })

    logger.info({
      slug: 'AUTO_SUBMISSION_DATA:testWindowsInAutoSubmitRange',
      data: {
        timeOfDay: timeOfDay,
        twHaveAutoSubmissionConfig: auto_close_test_windows,
        testWindowsInAutoSubmitRange: testWindowsInAutoSubmitRange,
      }
    });
  
    //Step 3. Get all active test sessions in each testWindowsInAutoSubmitRange  (from Redis)
    await Promise.all(
        testWindowsInAutoSubmitRange.map(async (testWindowInRange) => {
        this.runAutoSubmitJobByTestWindow(testWindowInRange, timeOfDay)
      })
    );
 

    return testWindowsInAutoSubmitRange
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  convertTimeStrIntoDate(timeStr:string){
    const timeArray = timeStr.split(":");
    const convertedTime = new Date();
    convertedTime.setUTCHours(parseInt(timeArray[0], 10));
    convertedTime.setUTCMinutes(parseInt(timeArray[1], 10));
    convertedTime.setUTCSeconds(parseInt(timeArray[2], 10));
    return convertedTime
  }

  adjustCloseDateIfNeeded(startOn: Date, closeOn: Date){
    if(startOn > closeOn){
      closeOn.setDate(closeOn.getDate() + 1)
    }
  }

  extensionTimeIntervals = (timeOfDay:Moment, autoCloseAfter:any, extensionTime:number) => {
    let numExtensions = 0;
    while (true) {
      // Sets new auto_close_after time (adds n extensionTime) once timeOfDay is within the closest
      // incremement of auto_close_after + n(extentionTime)
      const currentExtensionTime = extensionTime * numExtensions * 60000;
      const autoCloseAfterTime = moment.utc(autoCloseAfter);
      const autoCloseAfterTimeWithExtensionTime = moment.utc(autoCloseAfterTime.clone().add(currentExtensionTime));
      if (timeOfDay.isBefore(autoCloseAfterTimeWithExtensionTime)) {
        return autoCloseAfterTimeWithExtensionTime;
      }
      numExtensions++;
    }
  }

  /**
   * Get all test sessions on Redis by test window.
   * Process test sessions by chunk
   * @param testWindowInRange 
   * @param jobRunTime 
   * @param IsSentPopup Is sending auto-submit popup to invigilator view
   * @param isDryRun Dry run of true will not auto-submit ta and tass
   * @returns 
   */
  async runAutoSubmitJobByTestWindow(testWindowInRange:any, jobRunTime:Moment, IsSentPopup = true, isDryRun = false){
    const currentTime = moment().utc() // For logging time lapse purpose
    const testWindowTestSessionIds = await getTestWindowTestSessions(this.app, testWindowInRange.id)
    const testWindowTestSessions = await Promise.all(testWindowTestSessionIds.map( async (testSessionId:Id) => {
      const redisTestSessionKey = KTestSession(testSessionId)
      // make sure test session is still in Redis
      const sessionInRedis = await checkRedisKey(this.app, redisTestSessionKey)
      if(sessionInRedis){
        const testSession = await getRedisTestSession(this.app, testSessionId)
        return testSession
      }else{
        // if test session isn't in Redis, remove it from testWindowTestSession list
        removeTestWindowTestSessions(this.app, testWindowInRange.id, [testSessionId])
        return null
      }
    }))

    // removes null values from the list of testWindowTestSessions
    const filteredTestWindowTestSessions = testWindowTestSessions.filter((testSession) => testSession !== null)

    // Process Redis test sessions by chunk
    let testSessionsRequireWarning: any[] = []
    let testSessionsRequireAutoClose: any[] = []
    let testSessions: any[] = []
    let checkingTestAttempts: any[] = []
    let checkingTestAttemptSubsessions: any[] = []
    let autoSubmittedTestAttemptSubsessions: any[] = []
    for(let testSessionChunks of _.chunk(filteredTestWindowTestSessions, 1000)) {
      const data = await this.filterTestSessions(testSessionChunks, testWindowInRange, jobRunTime, IsSentPopup, isDryRun)
      if (data) {
        testSessionsRequireWarning = testSessionsRequireWarning.concat(data.testSessionsRequireWarning || []);
        testSessionsRequireAutoClose = testSessionsRequireAutoClose.concat(data.testSessionsRequireAutoClose || []);
        checkingTestAttempts = checkingTestAttempts.concat(data.checkingTestAttempts || []);
        checkingTestAttemptSubsessions = checkingTestAttemptSubsessions.concat(data.checkingTestAttemptSubsessions || []);
        autoSubmittedTestAttemptSubsessions = autoSubmittedTestAttemptSubsessions.concat(data.autoSubmittedTestAttemptSubsessions || []);
      }
    }

    const jobEndTime = moment().utc()
    const duration = moment.duration(jobEndTime.diff(currentTime))
    logger.info({
      slug: 'AUTO_SUBMISSION_DATA:processMetrics',
      test_window_id: testWindowInRange.id,
      data: {
        jobStartTime: currentTime,
        jobEndTime: jobEndTime,
        jobDuration: `${duration.get('seconds')} sec`,
        filteredTestWindowTestSessions: filteredTestWindowTestSessions.length,
        testSessionsRequireWarning: testSessionsRequireWarning.length,
        testSessionsRequireAutoClose: testSessionsRequireAutoClose.length,
        checkingTestAttempts: checkingTestAttempts.length,
        checkingTestAttemptSubsessions: checkingTestAttemptSubsessions.length,
        autoSubmittedTestAttemptSubsessions: autoSubmittedTestAttemptSubsessions.length
      }
    });
    return {
      testSessionsRequireWarning,
      testSessionsRequireAutoClose,
      testSessions,
      checkingTestAttempts,
      checkingTestAttemptSubsessions,
      autoSubmittedTestAttemptSubsessions
    }
  }

  /**
   * Step 4. filter auto close test session candidates into two different cetegories: testSessionsRequireWarning or testSessionsRequireAutoClose
   * Send auto-submit warning to invigilator for test sessions in auto_close_after grace period.
   * Proceed with auto-submission for test sessions in auto-close range
   * @param testWindowTestSessions 
   * @param testWindow 
   * @param timeOfDay 
   * @param IsSentPopup 
   * @param isDryRun 
   * @returns 
   */
  async filterTestSessions(testWindowTestSessions:any, testWindow:any, timeOfDay:Moment, IsSentPopup = true, isDryRun = false) {
    const testSessionsRequireWarning: RedisKeyTestSession[]= []
    const testSessionsRequireAutoClose: RedisKeyTestSession[] = []

    //converts test window auto close times to corresponding time today
    const auto_submission_start_on = this.convertTimeStrIntoDate(testWindow.auto_close_start_on)
    const auto_submission_close_on = this.convertTimeStrIntoDate(testWindow.auto_close_end_on)

    // adjusts auto_submission_close_on to next day if smaller than auto_submission_start_on 
    this.adjustCloseDateIfNeeded(auto_submission_start_on, auto_submission_close_on)
    const auto_close_after:any =  this.extensionTimeIntervals(timeOfDay, auto_submission_start_on, testWindow.auto_close_default_extension_m)

    for (const testSession of testWindowTestSessions){
      // 1. Assigning auto_close_after, is_messaged, is_auto_closed values to new test session auto close candidates 
      // 2. Resetting auto_close_after, is_messaged, is_auto_closed  values for test session that did not close previously 
      // const unclosedTestSessionsFromPreviousDays = testSession.auto_close_after < auto_submission_start_on && !testSession.is_auto_closed
      if(IsSentPopup) {
        if(!testSession.auto_close_after || testSession.auto_close_after.getTime() + 60000 < auto_submission_start_on.getTime()){    
          testSession.auto_close_after = auto_close_after
          const is_messaged = 0
          testSession.is_messaged = is_messaged

          const payload = {
            auto_close_after,
            is_messaged,
          }
          // updates test session in redis with payload auto submission values
          patchRedisTestSession(this.app, testSession.id, payload, false)  
        }

        //check #1:  timeOfDay >= (auto_close_after - auto_close_grace_period_m) 
        //check #2:  timeOfDay < auto_close_after
        //check #4 : isMessaged = 0
        const graceTime = testWindow.auto_close_grace_period_m * 60000;
        const closeAfterWithoutGraceTime = moment.utc(testSession.auto_close_after)
        const autoCloseGraceTime = moment.utc(closeAfterWithoutGraceTime.clone().subtract(graceTime));
        const autoCloseTime = moment.utc(testSession.auto_close_after)
        const currTimeInGracePeriodWindow = timeOfDay.valueOf() >= autoCloseGraceTime.valueOf()
        // checks if test session is a candidate for websocket warning message
        if(currTimeInGracePeriodWindow && timeOfDay.isSameOrBefore(autoCloseTime) && !testSession.is_messaged){
          testSessionsRequireWarning.push(testSession)
        }
      }

      // check #1: timeOfDay>= autoCloseAfter
      // check #2: timeOfDay <= auto_submission_close_on
      const currTimePassedAutoClose = timeOfDay >= testSession.auto_close_after
      const currTimeIsBeforeAutoSubmissionWindowClose = timeOfDay.valueOf() <= auto_submission_close_on.getTime()
      if(currTimePassedAutoClose && currTimeIsBeforeAutoSubmissionWindowClose){
        testSessionsRequireAutoClose.push(testSession)
      }
    }

    // calls function that warns teachers when grace period is reached that auto submission starts when grace is over 
    if (IsSentPopup) {
      this.testSessionWebsocketWarning(testSessionsRequireWarning, testWindow)
    }

    //log auto submission attempt data 
    logger.info({
      slug: 'AUTO_SUBMISSION_DATA:testWindowTestSessions',
      test_window_id: testWindow.id,
      timeOfDay: timeOfDay,
      data: {
        auto_submission_start_on: auto_submission_start_on,
        auto_submission_close_on: auto_submission_close_on,
        testWindowTestSessions
      }
    });
    
    logger.info({
      slug: 'AUTO_SUBMISSION_DATA:testSessionsRequireWarning',
      test_window_id: testWindow.id,
      timeOfDay: timeOfDay,
      data: {
        auto_submission_start_on: auto_submission_start_on,
        auto_submission_close_on: auto_submission_close_on,
        testSessionsRequireWarning
      }
    });
    
    logger.info({
      slug: 'AUTO_SUBMISSION_DATA:testSessionsRequireAutoClose',
      test_window_id: testWindow.id,
      timeOfDay: timeOfDay,
      data: {
        auto_submission_start_on: auto_submission_start_on,
        auto_submission_close_on: auto_submission_close_on,
        testSessionsRequireAutoClose
      }
    });
    
    // calls function that closes test attempt subsessions (test attemps if sub session is last) for when auto_close_after is reached
    const autoCloseData = await this.testSessionAutoClose(testSessionsRequireAutoClose, testWindow, timeOfDay, isDryRun)

    return {
      testSessionsRequireWarning,
      testSessionsRequireAutoClose,
      ...autoCloseData
    }
  }

  /**
   * Step 5. Trigger Websocket communication to all teachers in test windows where grace period is reached
   * @param testSessions 
   * @param testWindow 
   * @returns 
   */
  async testSessionWebsocketWarning (testSessions: RedisKeyTestSession[], testWindow:any) {
    //Use Redlock to controll the same test window process to make sure only 1 process is running
    let testWindowAutoSubmitMessageLock: any
    const testWindowId = testWindow.id
    try{
      testWindowAutoSubmitMessageLock = await getRedisLock(this.app, [KTestWindowAutoSubmitMessage(testWindowId)], TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT);
    } catch (error){
      if(testWindowAutoSubmitMessageLock){
        await testWindowAutoSubmitMessageLock.release();
      }
      return;
    }

    for(let testSession of testSessions){
      try{
        let responseData = {
          eventType: 'autoCloseWarning',
          testWindow,
          testSession,
        }

        // stores classes that have already received a message so test sessions in the same class don't trigger websocket communcation multiple times 
        const testSessionClassesAlreadyMessaged:any = []
        const testSessionClass = await getRedisSchoolClass(this.app,testSession.school_class_id)
        // checks if the class is already messaged
        if(!testSessionClassesAlreadyMessaged.includes(testSessionClass)){
          await this.app
          .service('public/educator/websocket/connected-users')
          .sendToAllTeachers(testSessionClass.group_id, responseData)
          testSessionClassesAlreadyMessaged.push(testSession)
        }    
        // updates test session when messaged =
        await patchRedisTestSession(this.app, testSession.id, {is_messaged: 1})
      }
      catch(error){
        logger.error('TEST_SESSION_AUTO_SUBMISSION_MESSAGE_ERROR', {
          test_session_id: testSession.id
        }, error);
      }
      testWindowAutoSubmitMessageLock = await testWindowAutoSubmitMessageLock.extend(TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT) 
    }
    testWindowAutoSubmitMessageLock.release();
  };

  /**
   * Step 6. Submit all testSessionsRequireAutoClose sub sessions (maybe test attempts if sub session is last)
   * @param testSessions 
   * @param testWindow 
   * @param timeOfDay 
   * @param isDryRun 
   * @returns 
   */
  async testSessionAutoClose (testSessions: RedisKeyTestSession[], testWindow: any, timeOfDay:Moment, isDryRun:boolean = false) {
    let testWindowAutoSubmitLock: any
    if(!isDryRun){
      const testWindowId = testWindow.id
      try{
        testWindowAutoSubmitLock = await getRedisLock(this.app, [KTestWindowAutoSubmit(testWindowId)], TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT);
      } catch (error){
        logger.error('TEST_SESSION_AUTO_SUBMISSION_ERROR:RedLockTaken:testWindowAutoSubmitLock', {
          testWindowId: testWindowId,
          timeOfDay: timeOfDay,
          testSessions: testSessions,
        }, error);
        if(testWindowAutoSubmitLock){
          await testWindowAutoSubmitLock.release();
        }
        return;
      }
    }

    //Use Redlock to control the same test window process to make sure only 1 process is running
    const autoSubmissionStartOn = this.convertTimeStrIntoDate(testWindow.auto_close_start_on)
    const autoSubmissionCloseOn = this.convertTimeStrIntoDate(testWindow.auto_close_end_on)

    this.adjustCloseDateIfNeeded(autoSubmissionStartOn,autoSubmissionCloseOn)
    const checkingTestAttempts = []
    const checkingTestAttemptSubsessions = []
    const autoSubmittedTestAttemptSubsessions = []

    for(let session of testSessions){
      try{
        const testAttempts = await getRedisTestAttemptsFromTestSessionId(this.app, session.id)
        for(let testAttempt of testAttempts){
          const testAttemptSessionSubSessions = await getRedisTestAttemptSubSessionsFromTestAttemptId(this.app, testAttempt.id)
          for(let subSession of testAttemptSessionSubSessions){
            const testSessionSubSessionRecord = await getRedisTestSessionSubSession(this.app, subSession.sub_session_id)
            // only close sub session if student started and test attempt is not submitted
            const testAttemptIsSubmitted = testAttempt.is_submitted || testAttempt.is_auto_submitted
            const autoCloseOnNotPassed = (timeOfDay.valueOf() < autoSubmissionCloseOn.getTime())
            if(subSession.started_on && !subSession.is_submitted && !subSession.is_auto_submitted && !testAttemptIsSubmitted && autoCloseOnNotPassed ){
              if (!isDryRun) { // Do not proceed with auto-submission if is dry run
                const tassConfig = {
                  id:subSession.id, 
                  closesTestAttempt: !!testSessionSubSessionRecord.is_last, 
                  twtdar_order: testSessionSubSessionRecord.twtdar_order, 
                  test_session_id: subSession.test_session_id, 
                  attemptUid: subSession.uid, 
                  ssOrder: testSessionSubSessionRecord.order, 
                  autoOpenNext: false
                }
                await this.app.service('public/student/session')
                // (auto) submits tass and if tsss is last sub session, submits test attempt as well
                .closeAttempt(<number> testAttempt.id, 3257644, false, {}, subSession.test_session_id, tassConfig, false, true );
                autoSubmittedTestAttemptSubsessions.push(subSession.id)
              }
              checkingTestAttemptSubsessions.push(subSession)
            }
          }
          checkingTestAttempts.push(testAttempt)
        }
      }
      catch(error){
        logger.error('TEST_SESSION_AUTO_SUBMISSION_ERROR',{
          test_session_id: session.id,
        }, error);
      }
      if (!isDryRun) {
        testWindowAutoSubmitLock = await testWindowAutoSubmitLock.extend(TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT) 
      }
    }

    if (!isDryRun) {
      logger.info({
        slug: 'TEST_WINDOW_AUTO_SUBMISSION_DATA:testSessions',
        test_window_id: testWindow.id,
        timeOfDay: timeOfDay,
        data: {
          autoSubmissionStartOn: autoSubmissionStartOn,
          autoSubmissionCloseOn: autoSubmissionCloseOn,
          testSessions,
        }
      });

      logger.info({
        slug: 'TEST_WINDOW_AUTO_SUBMISSION_DATA:checkingTestAttempts',
        test_window_id: testWindow.id,
        timeOfDay: timeOfDay,
        data: {
          autoSubmissionStartOn: autoSubmissionStartOn,
          autoSubmissionCloseOn: autoSubmissionCloseOn,
          checkingTestAttempts
        }
      });

      logger.info({
        slug: 'TEST_WINDOW_AUTO_SUBMISSION_DATA:checkingTestAttemptSubsessions',
        test_window_id: testWindow.id,
        timeOfDay: timeOfDay,
        data: {
          autoSubmissionStartOn: autoSubmissionStartOn,
          autoSubmissionCloseOn: autoSubmissionCloseOn,
          checkingTestAttemptSubsessions
        }
      });

      logger.info({
        slug: 'TEST_WINDOW_AUTO_SUBMISSION_DATA:autoSubmittedTestAttemptSubsessions',
        test_window_id: testWindow.id,
        timeOfDay: timeOfDay,
        data: {
          autoSubmissionStartOn: autoSubmissionStartOn,
          autoSubmissionCloseOn: autoSubmissionCloseOn,
          autoSubmittedTestAttemptSubsessions
        }
      });
    
      await testWindowAutoSubmitLock.release();
    }

    return {
      testSessions,
      checkingTestAttempts,
      checkingTestAttemptSubsessions,
      autoSubmittedTestAttemptSubsessions
    }
  }
}


