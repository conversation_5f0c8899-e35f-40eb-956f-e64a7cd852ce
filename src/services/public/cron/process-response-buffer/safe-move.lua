-- KEYS[1] = list key for 'PendingSubmWrites'
-- KEYS[2] = list key for 'ProcessingSubmWrites'
-- KEYS[3] = key pattern for 'submData:{test_attempt_id}'
-- KEYS[4] = key pattern for 'archive:{test_attempt_id}'
-- ARGV[1] = CHUNK_SIZE

local chunkSize = tonumber(ARGV[1])
local movedItems = {}
local readItems = {}
local processingItemsCount = 0

local function extractTimestamp(jsonString)
    local match = jsonString:match('%.timestamp":"(.-)"')
    return match or ""
end

for i = 1, chunkSize do
    local item = redis.call('LMOVE', KEYS[1], KEYS[2], 'LEFT', 'RIGHT')
    if not item then break end -- Stop if the list is empty
    table.insert(movedItems, item)
    processingItemsCount = processingItemsCount + 1
end

for _, item in ipairs(movedItems) do
    local test_attempt_id, test_question_id = item:match('([^:]+):([^:]+)')
    local submKey = KEYS[3] .. test_attempt_id
    local archiveKey = KEYS[4] .. test_attempt_id

    local currentData = redis.call('HGET', submKey, test_question_id)
    if currentData then
        table.insert(readItems, currentData)

        local currentDataTimestamp = extractTimestamp(currentData)
        local newDataTimestamp = currentDataTimestamp

        local previousData = redis.call('HGET', archiveKey, test_question_id)
        local previousDataTimestamp = previousData and extractTimestamp(previousData)

        if currentDataTimestamp and (not previousDataTimestamp or currentDataTimestamp > previousDataTimestamp) then
            redis.call('HSET', archiveKey, test_question_id, currentData)
            redis.call('HDEL', submKey, test_question_id)
        end
    end
end

table.insert(readItems, processingItemsCount)

return readItems

