// Initializes the `public/anon/ldb-config` service on path `/public/anon/ldb-config`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { LdbConfig } from './ldb-config.class';
import hooks from './ldb-config.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/anon/ldb-config': LdbConfig & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/anon/ldb-config', new LdbConfig(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/anon/ldb-config');

  service.hooks(hooks);
}
