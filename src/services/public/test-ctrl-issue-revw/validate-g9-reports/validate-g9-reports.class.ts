import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { result } from 'lodash';
import { currentUid } from '../../../../util/uid';
import _ from 'lodash';
import { OPERATIONAL_ASSESSMENT_TYPE } from '../../test-ctrl/schools/student-exceptions/student-exceptions.class';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';

interface Data {}

interface ServiceOptions {}

export class ValidateG9Reports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const { test_window_id} = params.query;
    const test_windows = await dbRawReadSingle(this.app, [test_window_id], `
        select * from test_windows tw where tw.id = ? 
    ;`)

    const report_require_validate:string = test_windows.report_require_validate
    let isCheckGuestClass = false
    const pendingValidateAttempts = await this.getPendingValidatedAttempts(test_window_id, isCheckGuestClass)

    isCheckGuestClass = true
    const pendingValidateGuestClassAttempts = await this.getPendingValidatedAttempts(test_window_id, isCheckGuestClass)

    const pendingValidateNfpReport = await dbRawRead(this.app, {test_window_id}, `
      -- Benchmark: 0.078 sec
         select sc.group_id as group_id
              , concat(schl.name, "(",schl.foreign_id,")") as school
              , concat (sc.name, "(", sc.access_code,")") as class
              , ansrg.created_on as requested_on
              , null as report_generated_on
              , ansrg.student_pdf_generated_on as pdf_renerated_on
              , CASE 
                    WHEN COUNT(ansrg.eqao_validated_on IS NULL OR NULL) > 0 THEN NULL
                    ELSE MAX(ansrg.eqao_validated_on)
                END AS validated_on
          from auto_nfp_student_report_generations ansrg
          join school_classes sc on sc.group_id = ansrg.schl_class_group_id
          join schools schl on schl.group_id = sc.schl_group_id
          join school_semesters ss on ss.id = sc.semester_id AND ss.test_window_id = (:test_window_id)
         where ansrg.student_pdf_generated_on is not null
           and ansrg.is_revoked = 0
           and ansrg.test_window_id = (:test_window_id)
      group by sc.id  
    `)

    let fullReportsMap = new Map();
    [...pendingValidateAttempts, ...pendingValidateGuestClassAttempts, ...pendingValidateNfpReport].forEach(report => {
      const existingReport = fullReportsMap.get(report.group_id);
      if (!existingReport) {
        // If no existing report, add the current one
        fullReportsMap.set(report.group_id, report);
      } else {
        // If an existing report is found, prioritize `null` for `validated_on`
        const updatedReport = {
          ...existingReport,
          validated_on: existingReport.validated_on === null || report.validated_on === null
                        ? null : existingReport.validated_on
        };
        fullReportsMap.set(report.group_id, updatedReport);
      }
    });
    
    let fullPendingValidateReports = Array.from(fullReportsMap.values());
    
    const sortedFullPendingValidateReports = _.orderBy(fullPendingValidateReports, ['school', 'class'], ['asc', 'asc']);

    return { report_require_validate, pendingValidateReports: sortedFullPendingValidateReports }
  }

  async getPendingValidatedAttempts(test_window_id:number, checkGuestClass:boolean = false){
    const pendingValidateAttempts = await dbRawRead(this.app, [test_window_id], `
      -- Benchmark: 1.9 sec, cost 34561 (was 190842)
            SELECT sc.group_id AS group_id
                 , concat(schl.name, "(",schl.foreign_id,")") AS school
                 , concat(sc.name, "(", sc.access_code,")") AS class
                 , asrg.created_on AS requested_on
                 , asrg.student_report_generated_on AS report_generated_on
                 , asrg.student_pdf_generated_on AS pdf_renerated_on
                 , CASE 
                       WHEN COUNT(asrg.eqao_validated_on IS NULL OR NULL) > 0 THEN NULL
                       ELSE MAX(asrg.eqao_validated_on)
                   END AS validated_on
             from test_window_td_alloc_rules twtar 
             join test_attempts ta on ta.twtdar_id = twtar.id 
              and ta.is_invalid = 0
             join auto_student_report_generations asrg on asrg.student_uid = ta.uid
              and asrg.test_attempt_id = ta.id
              and asrg.is_revoked = 0
              and asrg.student_pdf_generated_on is not null
             join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
            ${checkGuestClass ? 
                `
                  join school_classes sc_invig on sc_invig.id = scts.school_class_id
                  join school_classes_guest scg on scg.invig_sc_group_id = sc_invig.group_id
                  join school_classes sc on sc.group_id = scg.guest_sc_group_id
                ` :
                `join school_classes sc on sc.id = scts.school_class_id`
            }
             join user_roles ur on ur.group_id = sc.group_id 
              and ur.uid = asrg.student_uid
             join schools schl on schl.group_id = sc.schl_group_id
            where twtar.generate_report = 1
              and twtar.test_window_id = ?
         GROUP BY sc.id
      ;`)

   return pendingValidateAttempts
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (schl_class_group_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const fromIssueReviewer = true;
    params.query.schl_class_group_id = schl_class_group_id

    const bulk_student_results_g9_reports = (await dbRawRead(this.app, [schl_class_group_id], `
      select * from bulk_student_results_g9_reports where class_group_id = ? and is_revoked != 1 order by report_generate_on DESC
    ;`))

    if(bulk_student_results_g9_reports.length === 0){
      throw new Errors.BadRequest('NO_BULK_REPORT_FOUND')
    }
    
    // If 's3_report_link' is missing for some classes, regenerate the report.
    if(!bulk_student_results_g9_reports[0].s3_report_link){
      const generateNewReport = await this.app.service("public/educator/g9-reports").getBulkReport(params, fromIssueReviewer)
      return generateNewReport
    }
    
    const s3Link = generateS3DownloadUrl( bulk_student_results_g9_reports[0].s3_report_link)
    return {message: 'REPORT_GENERATED', reportURL: s3Link}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }

    const { test_window_id, report_require_validate } = params.query;


    if(test_window_id){
      this.app.service('db/write/test-windows').patch(test_window_id, { report_require_validate })
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (schl_class_group_id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query || !schl_class_group_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    
    const created_by_uid = await currentUid(this.app, params);

    let isCheckGuestClass = false
    const pendingValidateAttempts = await this.getPendingValidatedAttemptsToSignOff(schl_class_group_id, isCheckGuestClass)
    isCheckGuestClass = true
    const pendingValidateGuestClassAttempts = await this.getPendingValidatedAttemptsToSignOff(schl_class_group_id, isCheckGuestClass)
    const pendingValidateNfpReports = await dbRawRead(this.app, [DBD_U_ROLE_TYPES.schl_student, schl_class_group_id], `
      -- Benchmark: 0.031 sec
      SELECT ansrg.id AS ansrg_id
      FROM user_roles ur
      JOIN auto_nfp_student_report_generations ansrg ON ansrg.student_uid = ur.uid AND ansrg.is_revoked != 1
      WHERE ur.role_type = ?
      AND ur.group_id = ?
      AND ansrg.student_pdf_generated_on IS NOT NULL
      AND ansrg.eqao_validated_on IS NULL
    ;`)

    const asrgIds:any[] = pendingValidateAttempts.concat(pendingValidateGuestClassAttempts)
      .map((pendingValidateAttempt:any) => pendingValidateAttempt.asrg_id);
    const ansrgIds:any[] = pendingValidateNfpReports.map((pendingValidateNfpReport:any) => pendingValidateNfpReport.ansrg_id);

    const validated_on = dbDateNow(this.app);
    let results:any[] = []
    for(const asrgId of asrgIds) {
      const result = await this.app.service('db/write/auto-student-report-generations')
        .patch(asrgId, {
          eqao_validated_on: validated_on,
          validated_by_uid: created_by_uid
        })
      results.push(result)
    }

    for(const ansrgId of ansrgIds) {
      const result = await this.app.service('db/write/auto-nfp-student-report-generations')
        .patch(ansrgId, {
          eqao_validated_on: validated_on,
          validated_by_uid: created_by_uid
        })
      results.push(result)
    }
    return {validated_on: results.length ? results[0].eqao_validated_on: null}
  }

  async getPendingValidatedAttemptsToSignOff(schl_class_group_id:Id, checkGuestClass:boolean = false){
    const pendingValidateAttempts = await dbRawRead(this.app, [OPERATIONAL_ASSESSMENT_TYPE.G9_OPERATIONAL, schl_class_group_id], `
      -- Benchmark: 0.047 sec
      SELECT asrg.id AS asrg_id  
		    FROM school_classes sc
        ${checkGuestClass ? 
          `
            JOIN school_classes_guest scg ON scg.guest_sc_group_id = sc.group_id 
            JOIN school_classes sc_invigilated ON sc_invigilated.group_id = scg.invig_sc_group_id
            JOIN school_class_test_sessions scts ON scts.school_class_id = sc_invigilated.id
          ` :
          `JOIN school_class_test_sessions scts ON scts.school_class_id = sc.id`
        } 
            AND scts.slug = ?
        JOIN test_attempts ta ON ta.test_session_id = scts.test_session_id
		    JOIN auto_student_report_generations asrg ON asrg.student_uid = ta.uid AND asrg.test_attempt_id = ta.id
		   WHERE asrg.student_pdf_generated_on IS NOT NULL
         AND asrg.eqao_validated_on IS NULL    
         AND asrg.is_revoked = 0
         AND sc.group_id = ?
    ;`)

    return pendingValidateAttempts
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
