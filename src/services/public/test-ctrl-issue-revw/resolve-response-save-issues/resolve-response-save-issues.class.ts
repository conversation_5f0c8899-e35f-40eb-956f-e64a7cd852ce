import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface IPatchData {
  action: ResolveAction,
  is_force_update?: boolean,
}

interface ServiceOptions {}

enum ResolveAction {
  NONE = 'NONE',
  USE_SERVER = "USE_SERVER",
  USE_CLIENT = "USE_CLIENT",
}

export class ResolveResponseSaveIssues implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const includeResolved = params?.query?.include_resolved === '1' ? 1 : 0; // default FALSE
    const knex: Knex = this.app.get('knexClientReadReporting');
    const results = await knex('response_save_issues as rsi')
      .select([
        'rsi.id',
        'rsi.audit_id',
        'rsi.created_on',
        'rsi.test_window_id',
        'rsi.uid',
        'rsi.ric_id',
        'rsi.test_attempt_id',
        'twtar.slug',
        'twtar.is_questionnaire',
        'twtar.is_sample',
        'ta.is_submitted as ta_is_submitted',
        'ta.is_closed as ta_is_closed',
        'rsi.item_id',
        'rsi.taqr_id',
        'taqr.is_invalid as taqr_is_invalid',
        'taqr.updated_on as taqr_updated_on',
        'taqr.response_raw as taqr_response_raw',
        'rsi.log_server',
        'rsi.log_server_api_timestamp',
        'rsi.log_server_client_timestamp',
        'rsi.log_client',
        'rsi.log_client_api_timestamp',
        'rsi.log_client_client_timestamp',
        'rsi.is_resolved',
        'rsi.resolved_on',
        'rsi.resolved_by_uid',
        'rsi.action',
      ])
      .join('test_attempts as ta', 'ta.id', 'rsi.test_attempt_id')
      .leftJoin('test_attempt_question_responses as taqr', 'taqr.id', 'rsi.taqr_id')
      .leftJoin('test_window_td_alloc_rules as twtar', 'twtar.id', 'ta.twtdar_id')
      .where('rsi.is_revoked', 0)
      .modify((query) => { if (!includeResolved) query.where('rsi.is_resolved', 0) })
      .orderBy('rsi.id', 'desc');
    return results;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: IPatchData, params?: Params): Promise<Data> {
    if (!id || !data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const { action, is_force_update } = data;
    const isForceUpdate = (is_force_update === true); // default false

    // Validate action
    if (!Object.values(ResolveAction).includes(action)) {
      throw new Errors.BadRequest('UNKNOWN_ACTION');
    }

    const uid = await currentUid(this.app, params);

    // Get issue from the DB
    const issue = await this.getResponseSaveIssue(id as number);

    // Block request if the attempt is not submitted or not closed (can be bypassed using `is_force_update` parameter)
    await this.checkTestAttempt(issue.test_attempt_id, isForceUpdate);

    // Update TAQR if action is not NONE
    const taqrChanges = await this.updateTAQR(issue, action);

    // Resolve reported issue
    await this.resolveReportedIssue(issue.ric_id, uid);

    // Mark response save issue as resolved
    await this.resolveResponseSaveIssue(id as number, uid, action);

    return { taqrChanges };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  async getResponseSaveIssue(id: number) {
    const knex: Knex = this.app.get('knexClientReadReporting');
    const result = await knex('response_save_issues')
      .select(['id', 'test_attempt_id', 'taqr_id', 'log_server', 'log_client', 'log_server_api_timestamp', 'log_client_api_timestamp', 'ric_id'])
      .where('id', id)
      .where('is_resolved', 0)
      .where('is_revoked', 0)
      .first();

    if (!result) {
      throw new Errors.BadRequest('ISSUE_NOT_FOUND');
    }

    return result;
  }

  async updateTAQR(issue: any, action: ResolveAction) {
    const { taqr_id, log_server, log_client, log_server_api_timestamp, log_client_api_timestamp } = issue;

    let changes = [];

    if (action !== ResolveAction.NONE) {
      const logs = {
          [ResolveAction.USE_CLIENT]: log_client,
          [ResolveAction.USE_SERVER]: log_server,
        };
        const updatedTimes = {
          [ResolveAction.USE_CLIENT]: log_client_api_timestamp,
          [ResolveAction.USE_SERVER]: log_server_api_timestamp,
        };

        const log = JSON.parse(logs[action]);
        const updatedTime = updatedTimes[action];

        const taqrPatchData = {
          response_raw: log.requestBody.response_raw,
          updated_on: updatedTime,
          isNROverrideAllowed: true,
          isScoreOverrideAllowed: true, 
          isResponseOverrideAllowed: true, 
          isUpdatedTimeOverrideAllowed: true,
          isBackupOverrideAllowed: true,
        }
        changes = await this.app.service('public/support/student-taqr').patch(taqr_id, taqrPatchData) as any[];
      }

      return changes;
  }

  async resolveReportedIssue(ricId: number, uid: number) {
    if (ricId) {
      const knex: Knex = this.app.get('knexClientWrite');
      await knex('reported_issues_common')
        .where('id', ricId)
        .update({ 
          is_resolved: 1,
          resolved_on: dbDateNow(this.app),
          resolved_by_uid: uid,
        });
    }
  }

  async resolveResponseSaveIssue(id: number, uid: number, action: ResolveAction) {
    const knex: Knex = this.app.get('knexClientWrite');
    await knex('response_save_issues')
      .where('id', id)
      .update({ 
        is_resolved: 1,
        resolved_on: dbDateNow(this.app),
        resolved_by_uid: uid,
        action,
      });
  }

  async checkTestAttempt(attemptId: number, isForceUpdate: boolean) {
    const knex: Knex = this.app.get('knexClientReadReporting');
    const attempt = await knex('test_attempts')
      .select(['id', 'is_submitted', 'is_closed'])
      .where('id', attemptId)
      .first();

    if (!attempt) {
      throw new Errors.BadRequest('ATTEMPT_NOT_FOUND');
    }

    if (!isForceUpdate && attempt.is_submitted !== 1 && attempt.is_closed !== 1) {
      throw new Errors.BadRequest('ATTEMPT_NOT_COMPLETED');
    }
  }
}
