// Initializes the `public/test-ctrl-issue-revw/resolve-response-save-issues` service on path `/public/test-ctrl-issue-revw/resolve-response-save-issues`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ResolveResponseSaveIssues } from './resolve-response-save-issues.class';
import hooks from './resolve-response-save-issues.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl-issue-revw/resolve-response-save-issues': ResolveResponseSaveIssues & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl-issue-revw/resolve-response-save-issues', new ResolveResponseSaveIssues(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl-issue-revw/resolve-response-save-issues');

  service.hooks(hooks);
}
