import { Id, NullableId, <PERSON><PERSON><PERSON>, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import EqaoDataInstance from '../../../../util/eqao-data-instance';
import { dbRawRead, dbRawWrite, dbRawReadSingle } from '../../../../util/db-raw';
import { NotificationCentre} from "../../notifications/data/types";
import _ from 'lodash';
import logger from '../../../../logger';
const Mustache = require('mustache');
import { Knex } from 'knex';
import {RequestQueryMode, FileFormatType, ALT_VERSION_REQUEST_STATUS, ACTIONS, IRequestQueryConfig, AccessLinkType,ParamTypeDetails, FileTypeDetails, RequestData, TemplateFormatType, TargetStatus} from './types'
import {ALT_REQ_QUERY, findMaxOffsetDates} from './queries'
import qs from 'qs';
import moment from 'moment'

interface ServiceOptions {}

interface Data{}

export class AltVersionRequests implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  instance: EqaoDataInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.instance = new EqaoDataInstance(app);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data> | Promise<Data>> {

    if(!params || !params.query) {
      throw new Errors.BadRequest("Invalid params");
    }
    const {requestQueryMode, requestType, offset, limit, clientFilters, clientSort} = params.query;

    // Convert the filters and sort back into objects
    const filters = clientFilters ? qs.parse(clientFilters): undefined
    const sort = clientSort ? qs.parse(clientSort) : undefined

    const config = {requestType, offset, limit, filters, sort}
    // Get requests in full detail
    if (requestQueryMode == RequestQueryMode.ALT_CONTROLLER_DETAIL) {
      return this.getRequests(RequestQueryMode.ALT_CONTROLLER_DETAIL, config)
    }

    // For alt controller table, get both the requests and the total count
    const [requests, count] = await Promise.all([
      this.getRequests(RequestQueryMode.ALT_CONTROLLER_BASIC, config),
      this.getRequestsCount(RequestQueryMode.ALT_CONTROLLER_BASIC, config)
    ]);
    return {requests, count}
  }


  async getShippingRequests() {
    return this.getRequests(RequestQueryMode.SHIPPING, {});
  }

  /**
   *
   * @param requestMode - Category determining restrictions and how much detail to return
   * @param config - Supplementary data
   * @returns An array of requests
   */
  async getRequests(requestMode: RequestQueryMode, config: IRequestQueryConfig) {
    const db:Knex = this.app.get('knexClientRead');
    const query = ALT_REQ_QUERY(db, requestMode, config)
    const requests = await query

    requests.forEach((req:Partial<RequestData>) => this.addExtraRenderedProperties(req));

    return requests;
  }

  /**
   * Automatically send the notification and change the status to Operational Sent for any requests that qualify
   * @param config - Any specifications: `student_uid` restricts to looking for only requests for this student, `school_class_group_id` to only the class
   */
  async handleOpAutoSend(config: {student_uid?: number, school_class_group_id?: number, school_class_id?: number}){

    const requests: Partial<RequestData> = await this.getRequests(RequestQueryMode.OPERATIONAL_AUTO_SEND, config)

    const uid = 0; // For system message

    // Note and return for which requests the auto actions was successful vs. failed
    const successReqIds:number[] = []
    const failureReqIds:number[] = []

    await Promise.all(requests.map((req: any) => {
      const infoId = req.req_info_id
      // Proceed to send the notification and change the status
      return this.app.service('public/alt-version-ctrl/alt-version-requests').patchWithNotification (req.id, req, uid, ACTIONS.Operational)
      .then(() => {
        successReqIds.push(req.id)
      })
      .catch ((err) => {
        failureReqIds.push(req.id)
        // Record any error in the process in the request record
        this.app.service('db/write/alt-version-information')
        .patch(infoId, {auto_op_send_error: `${err.code}. ${err.name}: ${err.message}`});
      })
    }));

    return {successReqIds, failureReqIds}

  }


  /** Append additional properties, based on the ones pulled from db to the request data */
  addExtraRenderedProperties(request:Partial<RequestData>){
    // Rearrange formats into a bullet list
    const formats = request.requested_formats?.split(', ').map(format => `<li>${format}</li>`)
    request.requested_formats_list = `<ul>${formats?.join('')}</ul>`
  }

  /**
   *
   * @param requestMode - Category determining restrictions and how much detail to return
   * @param config - Supplementary data
   * @returns Total number of requests that would satisfy the mode and data passed
   */
  async getRequestsCount(requestMode: RequestQueryMode, config: IRequestQueryConfig){
    const db:Knex = this.app.get('knexClientRead');
    const fullQuery = ALT_REQ_QUERY(db, requestMode, config, true);
    const countQuery = db.raw(`SELECT COUNT(*) as count FROM (${fullQuery.toQuery()}) query`);
    const result = await countQuery;
    const count = result[0][0].count
    return count
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const requestData:any[] = await this.getRequests(RequestQueryMode.ALT_CONTROLLER_DETAIL, {requestId: +id});
    return requestData?.[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: Id, data: Partial<RequestData>, params: Params) { // Promise<Data>
    if(!params || !params.query) {
      throw new Errors.BadRequest("Invalid params");
    }

    const action = params.query?.action;

    if(!action) {
      return await this.app.service('db/write/alt-version-requests')
      .patch(id, data);
    } else if(action == ACTIONS.Shipping) {
      return await this.patchShipping(id, data);
    }


    // Get the latest data available about the request
    const requestData:any[] = await this.getRequests(RequestQueryMode.ALT_CONTROLLER_DETAIL, {requestId: +id});
    const requestDetails = requestData?.[0];
    const uid = await currentUid(this.app, params);
    return this.patchWithNotification(+id, requestDetails, uid, action);
  }


  async chooseTemplateForRequest(requestDetails: Partial<RequestData>, req_status: TargetStatus) {
    const {req_info_id, lang, assessment_type} = requestDetails;
    if(!req_info_id || !lang || !assessment_type) throw new Errors.BadRequest("MISSING_INFO");

    // Choose depending on which formats were requested, choose the type of template
    let format = TemplateFormatType.PDF_OTHER
    if (requestDetails.requests_braille_regular) format = TemplateFormatType.BRAILLE_REGULAR
    else if (requestDetails.requests_online_only) format = TemplateFormatType.ONLINE_ONLY

    const res = await this.app.service('public/alt-version-ctrl/request-notification-templates').getIndividualTemplate({req_info_id, format, req_status, lang, assessment: assessment_type})
    return {
      targetTemplate: res.template,
      isTargetTemplateCustom: res.isCustom
    };
  }



  async patchWithNotification (requestId: number, requestDetails: Partial<RequestData>, uid: number, action: ACTIONS) { // Promise<Data>

    const infoId = requestDetails.req_info_id;
    if(!infoId) throw new Errors.BadRequest("REQ_INFO_ID_REQUIRED");

    // Validate the action input, check that it's valid for current status, check shipping info when required, match the target status based on the action
    let targetStatus:TargetStatus;
    const currStatus = requestDetails.status;
    switch(action){
      case(ACTIONS.Approve):
        // Only currently pending requests can be approved
        if (currStatus !== ALT_VERSION_REQUEST_STATUS.Pending) throw new Errors.BadRequest("INVALID_ACTION_FOR_CURR_STATUS");
        // If shipping required, check that necessary info is present for approve message
        if (requestDetails.shipping_required) {
          const {shipping_company_sample, tracking_number_sample, tracking_url_sample} = requestDetails
          if (!shipping_company_sample || !tracking_number_sample || !tracking_url_sample) throw new Errors.BadRequest("MISSING SHIPPING INFO");
        }
        if (!requestDetails.tw_is_alt_file_access) throw new Errors.BadRequest('FILES INACCESSIBLE FOR ADMINS/TEACHERS IN THIS TEST WINDOW (configure in Test Controller)')
        targetStatus = TargetStatus.SAMPLE
        break;
      case(ACTIONS.Operational):
        // Only currently approved requests can be changed to operational link send
        if (currStatus !== ALT_VERSION_REQUEST_STATUS.Approved) throw new Errors.BadRequest("INVALID_ACTION_FOR_CURR_STATUS");
        // If shipping required, check that necessary info is present for operational message
        if (requestDetails.shipping_required) {
          const {shipping_company_operational, tracking_number_operational, tracking_url_operational, tracking_number_return, tracking_url_return} = requestDetails
          if (!shipping_company_operational || !tracking_number_operational || !tracking_url_operational || !tracking_number_return || !tracking_url_return) throw new Errors.BadRequest("MISSING SHIPPING INFO");
        }
        if (!requestDetails.tw_is_alt_file_access) throw new Errors.BadRequest('FILES INACCESSIBLE FOR ADMINS/TEACHERS IN THIS TEST WINDOW (configure in Test Controller)')
        targetStatus = TargetStatus.ASSESSMENT
        break;
      case(ACTIONS.Reject): 
        // Pending, approved, or operational sent actions can all be cancelled
        if (currStatus == ALT_VERSION_REQUEST_STATUS.Canceled) throw new Errors.BadRequest("INVALID_ACTION_FOR_CURR_STATUS");
        targetStatus = TargetStatus.CANCEL
        break;
      default:
        throw new Errors.BadRequest("NO_ACTION_REACHED");
    }

    //Find a matching template
    const {targetTemplate, isTargetTemplateCustom} = await this.chooseTemplateForRequest(requestDetails, targetStatus)
    if (!targetTemplate) throw new Errors.BadRequest("MISSING NOTIFICATION TEMPLATE");
    if (!isTargetTemplateCustom && !targetTemplate.is_finalized) throw new Errors.BadRequest("DEFAULT TEMPLATE NOT FINALIZED");
    if (!targetTemplate.subject?.trim() || !targetTemplate.body?.trim()) throw new Errors.BadRequest("INCOMPLETE TEMPLATE");

    const requestLang = requestDetails.lang
    if (!requestLang) throw new Errors.NotFound('MISSING_REQUEST_LANG') //should not happen

    // Get data for the access links / paths to files (only needed in Approval or Operational, not Cancellation)
    const accessFilePaths: {[key in AccessLinkType]?: string} = {}
    const accessLinks: {[key in AccessLinkType]?: string} = {}
    if (action == ACTIONS.Approve || action == ACTIONS.Operational) {

      //Find all the access file paths the notification will need
      const accessPathByFileType: {[key in FileFormatType]?:string} = await this.app.service('public/alt-version-ctrl/access-files').findAccessFilePathsForReq(requestDetails, targetStatus)

      // Prepare data to write used access links and file paths into the request's data
      for (const [fileType, filePath] of Object.entries(accessPathByFileType)) {
        const accessLinkType = FileTypeDetails.find(d => d.fileType == fileType)?.accessLinkType
        if (!accessLinkType) throw new Errors.NotFound('ACCESS_LINK_TYPE_NOT_FOUND') //should not happen
        if (accessLinkType) {
          accessFilePaths[accessLinkType] = filePath;
          //Find the param used int he URL, then compose the string
          const paramType = ParamTypeDetails.find(req => req.linkType == accessLinkType && req.status == targetStatus)?.paramType
          if (!paramType) throw new Errors.NotFound('PARAM_TYPE_NOT_FOUND') //should not happen
          // The link is to a Vretta page, on which the download of the file is available if the user is authorized
          const accessLink = `/#/${requestLang}/alt-version-req-ctrl/access-file-download/?requestId=${requestId}&type=${paramType}`
          accessLinks[accessLinkType] = accessLink;
        }
        //Render all access links into a single string to replace the placeholder
        requestDetails.renderedAccessLinks = await this.app.service('public/alt-version-ctrl/access-files').createAccessLinkString(requestId, accessPathByFileType, targetStatus, requestLang)
      }
    }

    // Send the notification using the template and data in the request to replace placeholders
    await this.initNotification(requestDetails, targetTemplate, uid)

    // Change the status and update other request-related data
    switch(action) {
      case ACTIONS.Operational:
        await this.app.service('db/write/alt-version-information')
          .patch(infoId, {
            operational_link_pdf_regular: accessLinks?.[AccessLinkType.PDFRegular] || null
            , operational_link_pdf_large: accessLinks?.[AccessLinkType.PDFLarge] || null
            , operational_link_mp3: accessLinks?.[AccessLinkType.mp3] || null
            // ASL is no longer used
            // , operational_link_asl: accessLinks?.[AccessLinkType.asl] || null
            , operational_link_ebraille: accessLinks?.[AccessLinkType.eBraille] || null
            , operational_file_path_pdf_regular: accessFilePaths?.[AccessLinkType.PDFRegular] || null
            , operational_file_path_pdf_large: accessFilePaths?.[AccessLinkType.PDFLarge] || null
            , operational_file_path_mp3: accessFilePaths?.[AccessLinkType.mp3] || null
            // , operational_file_path_asl: accessFilePaths?.[AccessLinkType.asl] || null
            , operational_file_path_ebraille: accessFilePaths?.[AccessLinkType.eBraille] || null
            , operational_notif_template_cache: JSON.stringify(targetTemplate)
            // Overwrite errors in operational auto-sending if operational action succeeds now
            , auto_op_send_error: null
          });
        return await this.app.service('db/write/alt-version-requests')
          .patch(requestId, {
            alt_version_req_status_id: ALT_VERSION_REQUEST_STATUS.Operational_Link_Send,
            operational_link_access_date: dbDateNow(this.app),
            operational_link_sent: 1,
            operational_send_on: dbDateNow(this.app),
            operational_send_by_uid: uid
          }
        );

      case ACTIONS.Approve:
        await this.unlinkTestAttempt(requestId, uid, action)
        await this.app.service('db/write/alt-version-information')
          .patch(infoId, {
            sample_link_pdf_regular: accessLinks?.[AccessLinkType.PDFRegular] || null
            , sample_link_pdf_large: accessLinks?.[AccessLinkType.PDFLarge] || null
            , sample_link_mp3: accessLinks?.[AccessLinkType.mp3] || null
            // , sample_link_asl: accessLinks?.[AccessLinkType.asl] || null
            , sample_link_ebraille: accessLinks?.[AccessLinkType.eBraille] || null
            , sample_file_path_pdf_regular: accessFilePaths?.[AccessLinkType.PDFRegular] || null
            , sample_file_path_pdf_large: accessFilePaths?.[AccessLinkType.PDFLarge] || null
            , sample_file_path_mp3: accessFilePaths?.[AccessLinkType.mp3] || null
            // , sample_file_path_asl: accessFilePaths?.[AccessLinkType.asl] || null
            , sample_file_path_ebraille: accessFilePaths?.[AccessLinkType.eBraille] || null
            , sample_notif_template_cache: JSON.stringify(targetTemplate)
          });
        return await this.app.service('db/write/alt-version-requests')
          .patch(requestId, {
            alt_version_req_status_id: ALT_VERSION_REQUEST_STATUS.Approved,
            approved_on: dbDateNow(this.app),
            approved_by_uid: uid
          }
        );

      case ACTIONS.Reject:
        // If cancelling from Approved/Operational Sent, not from Pending - unlink any attempts
        if (currStatus == ALT_VERSION_REQUEST_STATUS.Approved || currStatus == ALT_VERSION_REQUEST_STATUS.Operational_Link_Send) {
          await this.unlinkTestAttempt(requestId, uid, action)
        }
        await this.app.service('db/write/alt-version-information')
          .patch(infoId, {
            cancel_notif_template_cache: JSON.stringify(targetTemplate)
        });
        return await this.app.service('db/write/alt-version-requests')
          .patch(requestId, {
            alt_version_req_status_id: ALT_VERSION_REQUEST_STATUS.Canceled,
            rejected_on: dbDateNow(this.app),
            rejected_by_uid: uid,
          }
        );
    }
  }

  async patchShipping(id: NullableId, data: Partial<RequestData>) {
    return await this.app.service('db/write/alt-version-requests')
    .patch(id, {
      // alt_version_req_status_id: ALT_VERSION_REQUEST_STATUS.Shipment_Send,
      shipping_company_sample: data.shipping_company_sample,
      tracking_number_sample: data.tracking_number_sample,
      tracking_url_sample: data.tracking_url_sample,
      shipping_company_operational: data.shipping_company_operational,
      tracking_number_operational: data.tracking_number_operational,
      tracking_url_operational: data.tracking_url_operational,
      tracking_number_return: data.tracking_number_return,
      tracking_url_return: data.tracking_url_return,
      shipping_delivered_date_sample: data.shipping_delivered_date_sample,
      shipping_delivered_date_operational: data.shipping_delivered_date_operational
    });
  }

  async unlinkTestAttempt(altRequestId:any, created_by_uid:any, altReqAction:ACTIONS){
    const activeTestAttempts = await dbRawRead(this.app, [altRequestId], `
      select ta.*
        from alt_version_requests avr
        join alt_version_information avri on avri.id = avr.req_info_id
        join test_attempts ta on ta.uid = avri.student_uid and ta.is_invalid = 0
        join test_sessions ts on ts.id = ta.test_session_id and ts.test_window_id = avri.test_window_id and ts.is_closed = 0 and ts.is_cancelled = 0
        where avr.is_revoked = 0
          and avr.id = ?
    ;`);
    const activeTestAttemptIds:any[] = activeTestAttempts.map( (ata:any) => {return ata.id });

    await Promise.all( activeTestAttemptIds.map( async (activeTestAttemptId:any) => {
      const attemptInvalidationReason = `Alt_req_id: ${altRequestId}, action: ${altReqAction}`
      // unlink test attempts
      await this.app.service('public/support/test-attempt-reset').unlinkAndInvalidateTestAttempt(activeTestAttemptId, created_by_uid, attemptInvalidationReason)
    }))

    let log_data = {
      altRequestId,
      activeTestAttemptIds
    }

    let log_entry = {
      created_by_uid,
      slug: 'ALTERNATIVE_CONTROLLER_RESET_TEST_ATTEMPT',
      data: JSON.stringify(log_data)
    }

    logger.info(log_entry);
  }

  /**
   * / Send the notification using the template and data in the request to replace placeholders
   * @param data - details about the request from which placeholders are to be filled in
   * @param notifTemplate - an object of type `{subject: "..."; body: "..."}` with the appropriate content, including placeholders in content in {{double}} or {{{tripple}}} braces
   * @param uid - ID of user performing the status change action
   */
  async initNotification(data: Partial<RequestData>, notifTemplate:any, uid:number) {

    const db:Knex = this.app.get('knexClientRead');
    const {schl_group_id, class_group_id} = data;

    // Get the admins from the student's school and the teacher from the student's class
    const recipients:Partial<NotificationCentre.IUserMessageRecipient>[] = []

    // Pull preliminary user_roles to be filtered further
    const urRecordsPrelim = await db('user_roles as req')
    .select('id', 'uid', 'group_id', 'role_type')
    .where('is_revoked', '!=', 1)
    .where(function(){
      this.where({ role_type: 'schl_admin', group_id: schl_group_id})
      .orWhere({ role_type: 'schl_teacher', group_id: schl_group_id})
      .orWhere({ role_type: 'schl_teacher', group_id: class_group_id})
    })

    // Filter for target user_roles record needed for the messages
    const urRecords = urRecordsPrelim.filter(record => {
      // include admins of the school
      if (record.role_type == 'schl_admin') return true
      // include teacher of the school only if it's also a teacher of the class (for message center, need to use the school-level user_roles record)
      else if (record.role_type == 'schl_teacher' && record.group_id == schl_group_id){
        if (urRecordsPrelim.some(record2 => record2.uid == record.uid && record2.role_type == "schl_teacher" && record2.group_id == class_group_id)) {
          return true
        }
        else return false;
      }
      else return false;
    })

    if (!urRecords.length) throw new Errors.NotFound('NO VALID SCHOOL ADMIN OR TEACHER RECIPIENTS')

    // Prepare recipients of the message
    urRecords.forEach((record:any) => {
      const newRecipient: Partial<NotificationCentre.IUserMessageRecipient> = {
        recipient_uid: record.uid,
        recipient_group_id: record.group_id,
        recipient_role_id: record.id
      }
      recipients.push(newRecipient)
    });

    // What each placeholder in the template, if it exists, is replaced with
    const templateParams = {
      STUDENT_NAME: data.student_name,
      ACCESS_LINK_LIST_PLACEHOLDER: data.renderedAccessLinks,
      SAMPLE_TRACKING_NUM: data.tracking_number_sample,
      SAMPLE_TRACKING_URL: data.tracking_url_sample,
      ASSESSMENT_TRACKING_NUM: data.tracking_number_operational,
      ASSESSMENT_TRACKING_URL: data.tracking_url_operational,
      RETURN_TRACKING_NUM: data.tracking_number_return,
      RETURN_TRACKING_URL: data.tracking_url_return,
      REQUESTED_FORMATS_LIST: data.requested_formats_list,
      REQUESTED_BRAILLE_FORMAT: data.requested_braille_format,
    }
    // Fill the data into the tempalte
    const subjectMarkdown = Mustache.render(notifTemplate.subject, templateParams);
    const bodyMarkdown = Mustache.render(notifTemplate.body, templateParams);

    const notification: Partial<NotificationCentre.IUserMessage> = {
      subject: subjectMarkdown,
      message: bodyMarkdown,
      sender_uid: uid,
      is_auto: 1,
      channel_id: 2
    }

    return this.app.service('public/notifications/ntf-ctrl/message').sendNotification(notification, recipients)
    .catch(() => {
      throw new Errors.BadRequest("ERROR SENDING NOTIFICATION")
    });

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }


  /**
   * Called when a operational session is (re)scheduled - check if students in the class have alt reqs to be automatically operational-sent now
 * (do a faster check than using the general alt requests query)
   * @param school_class_id - id of class
   * @param session_start - the newest start time of the operational session
   * @param test_window_id - test window of the session
   * @returns promise true/false
   */
  async isClassOpSendRequired(school_class_id: number, session_start: string | Date, test_window_id: number): Promise<boolean>{
    // If the session is not within the interval, don't need to check any requests
    const newSessionStart = moment(session_start);
    const { brailleMax, otherMax } = findMaxOffsetDates();
    const isInBrailleInterval = newSessionStart.isBefore(moment(brailleMax))
    const isInOtherInterval = newSessionStart.isBefore(moment(otherMax))
    if (!isInBrailleInterval && !isInOtherInterval) return false;

    // Check if there's at least one relevant request to be auto-operational sent
    const CLASS_STUDENTS_APPROVED_REQ_IN_TW = `
    select req.id as req_id from school_classes sc
    join user_roles ur
        on ur.group_id = sc.group_id
        and sc.id = :school_class_id
        and ur.role_type = 'schl_student'
        and ur.is_revoked = 0
    join alt_version_information info
      on ur.uid = info.student_uid
    join alt_version_requests req on req.req_info_id = info.id
    and req.alt_version_req_status_id = :approvedStatus
    and info.test_window_id = :test_window_id
    ${(isInBrailleInterval && !isInOtherInterval) ? 'and info.braille_format in (1,2,3,4,16,17,18,19)' : ''}
    `
    
    const classStudentsApprovedReqsInTw = await dbRawReadSingle(this.app, {test_window_id, school_class_id, approvedStatus: ALT_VERSION_REQUEST_STATUS.Approved}, CLASS_STUDENTS_APPROVED_REQ_IN_TW)
    if (!classStudentsApprovedReqsInTw?.req_id) return false;

    return true;
  }


  /**
 * Called when a student is moved to a new class
 * Check if student has alt requests that need to be automatically operational-sent now since student has operational session upcoming
 * (do a faster check than using the general alt requests query)
 * @param student_uid - student uid
 * @param test_window_id (optional) - if provided will only check conditions in that test window
 * @param school_class_id - the newest class Id of the student
 * @returns promise of true/false
 */
  async isStudentOpSendRequired(student_uid: number, test_window_id: number | undefined, school_class_id: number) : Promise<boolean> {

    // Find if student has approved requests in that window (there's at most one), if not return false
    const STUDENT_APPROVED_REQ = `
      select req.id as req_id
      , CASE WHEN info.braille_format in (1,2,3,4,16,17,18,19) then 1 else 0 END as shipping_required
      from alt_version_requests req
      join alt_version_information info on req.req_info_id = info.id
      where info.student_uid = :student_uid 
      ${test_window_id ? 'and info.test_window_id = :test_window_id' : ''}
      and req.alt_version_req_status_id = :approvedStatus
    `
    const studentApprovedReq = await dbRawReadSingle(this.app, {test_window_id, student_uid, approvedStatus: ALT_VERSION_REQUEST_STATUS.Approved}, STUDENT_APPROVED_REQ)
    if (!studentApprovedReq?.req_id) return false;

    // Find if the class has an operational session that starts soon enough to auto operational send
    const CLASS_HAS_UPCOMING_OP_SESS = `
    select ts.id as session_id
    from school_class_test_sessions scts
    join test_sessions ts on ts.id = scts.test_session_id
    where scts.slug like '%OPERATIONAL'
    and scts.school_class_id = :school_class_id
    ${test_window_id ? 'and ts.test_window_id = :test_window_id' : ''}
    and ts.date_time_start < :maxStartDate
    and ts.is_cancelled = 0
    `
    const { brailleMax, otherMax } = findMaxOffsetDates();
    const maxStartDate = studentApprovedReq.shipping_required ? brailleMax : otherMax;
    const classUpcomingOppSess = await dbRawReadSingle(this.app, {school_class_id, test_window_id, maxStartDate}, CLASS_HAS_UPCOMING_OP_SESS)
    if (!classUpcomingOppSess?.session_id) return false;

    return true

  }
}
