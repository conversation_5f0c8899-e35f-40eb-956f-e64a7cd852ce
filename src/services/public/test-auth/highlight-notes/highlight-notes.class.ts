import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';

interface Data {
  [key: string]: any;
}

interface ServiceOptions {}

export interface IHighlightNoteInfo {
  config_map: string,
  test_question_id: number,
  test_question_auth_note_id: number;
  test_question_version_id?: number|undefined,
  test_question_suggestion_version_id?: number|undefined,
  modeType?: ModeType,
}

export enum ModeType {
  SUGGESTION = "SUGGESTION",
  REAL = "REAL",
}

export class HighlightNotes implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any | Paginated<any>> {
    if (!params || !params.query) throw new Errors.BadRequest('REQ_PARAMS_MISS');
    
    const {test_question_id, target_revision_id, lang} = params.query;

    let test_question_suggestion_version_id;
    let test_question_version_id;

    if (target_revision_id){
      test_question_version_id = +target_revision_id;
    } else {
      // Find the current version and suggestion version from existing highlights
      const currVersions = await dbRawReadSingle(this.app, {test_question_id, lang}, `
        select 
          max(nh.test_question_version_id) as test_question_version_id,
          max(nh.test_question_suggestion_version_id) as test_question_suggestion_version_id
        from test_question_auth_note_highlights as nh
        where nh.test_question_id = :test_question_id
        and nh.lang = :lang
      `);
      test_question_suggestion_version_id = currVersions?.test_question_suggestion_version_id;
      test_question_version_id = currVersions?.test_question_version_id;
    }

    // Get info on non-deleted notes attached to this question and suggestion version
    const highlightRecords = await dbRawRead(this.app, {test_question_id, lang, test_question_version_id, test_question_suggestion_version_id}, `
      select nh.*
      from (
        select nh.*,
               ROW_NUMBER() OVER (
                 PARTITION BY nh.test_question_auth_note_id, nh.test_question_version_id, nh.test_question_suggestion_version_id
                 ORDER BY nh.id DESC
               ) as rn
        from test_question_auth_note_highlights as nh
        where nh.test_question_id = :test_question_id
        and nh.config_map != '[]'
        and (
          ${test_question_version_id ? 'nh.test_question_version_id = :test_question_version_id' : 'false'}
          ${test_question_suggestion_version_id ? 'or nh.test_question_suggestion_version_id = :test_question_suggestion_version_id' : ''}
        )
      ) nh
      join test_question_auth_notes as n
        on n.id = nh.test_question_auth_note_id
      where nh.rn = 1
      and nh.lang = :lang
      and n.is_deleted != 1;
    `);

    // Filter out suggestion version rows that don't have a corresponding version row with the same note_id
    const noteIdsWithVersionRows = new Set(
      highlightRecords
        .filter(record => record.test_question_version_id)
        .map(record => record.test_question_auth_note_id)
    );
    
    const filteredHighlightRecords = highlightRecords.filter(record => {
      // Keep all version rows
      if (record.test_question_version_id) return true;
      // Keep suggestion rows only if there's a version row with the same note_id
      if (record.test_question_suggestion_version_id) {
        return noteIdsWithVersionRows.has(record.test_question_auth_note_id);
      }
      return false;
    });

    // Rearrange data
    const highlightNoteIds = []
    const highlightInfo = {[ModeType.REAL]: {}, [ModeType.SUGGESTION]: {}}
    const realMap = highlightInfo[ModeType.REAL]
    const suggMap = highlightInfo[ModeType.SUGGESTION]
    
    for (const record of filteredHighlightRecords) {
      const noteId = record.test_question_auth_note_id
      highlightNoteIds.push(noteId)
      const configSelection = JSON.parse(record.config_map);
      configSelection.forEach((selection:any) => {
        const {prop, entryId, start, end, isWhole, isImage, isPassage} = selection;
        const mapKey = entryId + "-" + prop
        if (record.test_question_version_id) {
          // @ts-ignore {}
          if (!realMap[mapKey]) realMap[mapKey] = [{noteId, start, end, isWhole, isImage, isPassage}]
          // @ts-ignore
          else realMap[mapKey].push({noteId, start, end, isWhole, isImage, isPassage})
        } 
        else if (record.test_question_suggestion_version_id) {
          // @ts-ignore
          if (!suggMap[mapKey]) suggMap[mapKey] = [{noteId, start, end, isWhole, isImage, isPassage}]
          // @ts-ignore
          else suggMap[mapKey].push({noteId, start, end, isWhole, isImage, isPassage})
        }
      })

    }
    return {highlightNoteIds, highlightInfo};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!Array.isArray(data)) throw Errors.BadRequest;
    if (!params || !params.query) throw new Errors.BadRequest('REQ_PARAMS_MISS');
    const {lang, resaveOtherLang, test_question_id, new_test_question_version_id} = params.query;

    const newRecordPromises = data.map((highlightInfo:IHighlightNoteInfo) => {
      return this.app.service('db/write/test-question-auth-note-highlights').create({...highlightInfo, lang})
    })

    // When a new version of the question is created, the version applies to both languages (whereas suggestions are separate)
    // E.g. even if saving on english side, any comments on french side should be applied to the new version
    // Find the latest mappings (greatest question version ID), write them for the new version
    if (resaveOtherLang) {
      const otherLang = lang == 'en' ? 'fr' : 'en';
      const recordsToResave = await dbRawRead(this.app, {test_question_id, otherLang}, `
        SELECT *
        FROM test_question_auth_note_highlights AS nh
        WHERE nh.test_question_version_id IN (
          SELECT MAX(test_question_version_id)
          FROM test_question_auth_note_highlights
          WHERE test_question_id = :test_question_id
            AND lang = :otherLang
        )
        AND nh.test_question_id = :test_question_id
        AND nh.lang = :otherLang;
      `);
      recordsToResave?.forEach(record => {
        recordsToResave.push(
          this.app.service('db/write/test-question-auth-note-highlights').create({
            ...record,
            id: undefined,
            test_question_version_id: new_test_question_version_id
          })
        )
      })
    
    }

    return Promise.all(newRecordPromises)

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  //** Used for when user manually adjusts the interval of a highlight*/
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest('REQ_PARAMS_MISS');
    const {test_question_id, lang, mode, test_question_auth_note_id} = params.query;
    const config_map = JSON.stringify(data.config_map);

    // Find the current version and suggestion version, use the one that matches the view the request was made from
    const {test_question_suggestion_version_id, test_question_version_id} = await this.app.service('public/test-auth/questions').findLatestQuestionAndSuggIds(test_question_id, lang)
    
    const highlightRecord = await dbRawReadSingle(this.app, {test_question_id, test_question_auth_note_id, test_question_version_id, test_question_suggestion_version_id}, `
      select id from test_question_auth_note_highlights
      where test_question_id = :test_question_id
      and test_question_auth_note_id = :test_question_auth_note_id
      ${(mode == ModeType.REAL) ? 
        'and test_question_version_id = :test_question_version_id' : 
        'and test_question_suggestion_version_id = :test_question_suggestion_version_id'
      }
    `)

    // Overwrite the config map with new one
    if (highlightRecord){
      return this.app.service('db/write/test-question-auth-note-highlights').patch(highlightRecord.id, {config_map})
    } else {
      return {};
    }

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
