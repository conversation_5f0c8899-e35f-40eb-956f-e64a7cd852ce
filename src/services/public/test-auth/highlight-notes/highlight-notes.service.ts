// Initializes the `public/test-auth/highlight-notes` service on path `/public/test-auth/highlight-notes`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { HighlightNotes } from './highlight-notes.class';
import hooks from './highlight-notes.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/highlight-notes': HighlightNotes & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/highlight-notes', new HighlightNotes(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/highlight-notes');

  service.hooks(hooks);
}
