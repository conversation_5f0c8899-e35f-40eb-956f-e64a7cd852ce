import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import axios from 'axios';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { Redis } from 'ioredis';
import { REDIS_SYS_DATA_EXPIRE, redisExpireAt, KStyleProfile } from '../../../../redis/redis'
interface Data {}

interface ServiceOptions {}

export class StyleProfiles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query || (!params.query.slug && !params.query.id)) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {slug, pullFromS3, isGetS3URL } = params.query;

    const redis: Redis = this.app.get('redis');
    const styleRef = {
      column: params.query.id?'id':'slug',
      val: params.query.id? params.query.id:slug
    }
    // Fetch style profile from redis/db
    const style_profile_redisKey = KStyleProfile(styleRef.val);
    let styleProfileJsonString = await redis.get(style_profile_redisKey);
    
    // if data not exit in redis, store data into redis if s3 don't use redis
    if(!styleProfileJsonString){ 
      //get styleprofile from db //? QC4 duration Slug: 0.038 sec / 0.000013 sec | ID:  0.057 sec / 0.000021 sec
      const styleProfile = await this.app.service('db/read/style-profiles').db().where(styleRef.column, styleRef.val).first();// TODO either update the way authoring retrieves style profile so it uses ID or update how were retrieving when using slug
      const theStyleProfile = styleProfile;

      //align the s3 expiry times on the URL and the redis data
      const S3_url_expireSeconds = REDIS_SYS_DATA_EXPIRE 
      // store the generaeted S3_url to style_profile.
      theStyleProfile.s3_url = theStyleProfile.s3_link.length?generateS3DownloadUrl(theStyleProfile.s3_link, S3_url_expireSeconds):''
      

      styleProfileJsonString = JSON.stringify(theStyleProfile)
      //store style+profile into redis
      const redis_expire_time = REDIS_SYS_DATA_EXPIRE - 60 // make redis expire 60 eariler than the S3 expire time as buffer.
      const promises = [ 
        redis.set(style_profile_redisKey, styleProfileJsonString, 'EX', redis_expire_time)
      ] as Promise<any>[];
      await Promise.all(promises)
    }

    const styleProfile: any = JSON.parse(styleProfileJsonString ?? '{}');
    // only return S3 url link if the client request it and there exist one.
    if(parseInt(isGetS3URL) && styleProfile.s3_url?.length){ 
      return {
        isS3URL: true,
        s3_url: styleProfile.s3_url,
        slug: styleProfile.slug
      }
    }
    // return style_profile record as default.
    return {
      isS3URL: false,
      styleProfile,
      slug: styleProfile.slug
    };
  }  

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !data) throw new Errors.BadRequest;
    const res = await this.app.service('db/write/style-profiles').patch(id, {
      config: JSON.stringify(data)
    });
    if(res.slug){
      const redis: Redis = this.app.get('redis');
      const style_profile_redisKey = KStyleProfile(res.slug);
      redis.set(style_profile_redisKey, JSON.stringify(res), 'EX', REDIS_SYS_DATA_EXPIRE)    
    }
    return res;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
