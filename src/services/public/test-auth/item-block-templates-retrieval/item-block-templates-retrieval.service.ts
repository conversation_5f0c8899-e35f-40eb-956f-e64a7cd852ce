// Initializes the `public/test-auth/item-block-templates-retrieval` service on path `/public/test-auth/item-block-templates-retrieval`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ItemBlockTemplatesRetrieval } from './item-block-templates-retrieval.class';
import hooks from './item-block-templates-retrieval.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/item-block-templates-retrieval': ItemBlockTemplatesRetrieval & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/item-block-templates-retrieval', new ItemBlockTemplatesRetrieval(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/item-block-templates-retrieval');

  service.hooks(hooks);
}
