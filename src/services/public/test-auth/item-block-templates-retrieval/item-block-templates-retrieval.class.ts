import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { ALWAYS_ALLOWED_DOMAINS } from '../../../../util/domain-lock';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ItemBlockTemplatesRetrieval implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    const groupAccess = await this.getGroupAccess(uid);

    const group_ids = groupAccess.map((group) => group.group_id)

    let templates = [];

    if(group_ids.length > 0){
      templates = await dbRawRead(this.app, {group_ids}, `
        select tqt.id
             , tqt.name template_name
             , tqt.icon_url
             , tqt.current_version_id
             , CASE WHEN tqtv.template_config IS NOT NULL AND tqtv.template_config != '[]' THEN 1 ELSE 0 END AS is_simplified
             , tqt.is_archived
             , tqt.description
             , tqt.domain_lock
             , tqtag.bank_group_id
             , tqt.meta
        from test_question_templates tqt 
        join test_question_template_versions tqtv
          on tqtv.id = tqt.current_version_id
        left join test_question_template_auth_groups tqtag 
          on tqtag.tqt_id = tqt.id
          and tqtag.is_revoked = 0
        where tqt.is_revoked = 0
          and (tqtag.bank_group_id is null OR tqtag.bank_group_id in (:group_ids))
        order by name;
      `)
    }

    const templateVersions = await dbRawRead(this.app, {}, `
      select tqtv.id
        , tqtv.tqt_id
        , concat(u.first_name, ' ', u.last_name) created_by
        , tqtv.created_on
      from test_question_templates tqt 
      join test_question_template_versions tqtv
        on tqtv.tqt_id = tqt.id
      join users u 
        on u.id = tqtv.created_by_uid
      where tqt.is_revoked = 0
      order by tqtv.tqt_id;
    `)

    const filteredTemplates = templates.filter((template) => {
      return this.isDomainAllowed(params, template.domain_lock);
    })

    return {templates: filteredTemplates, templateVersions};
  }

  async getGroupAccess(uid: number): Promise<{id: number, uid: number, role_type: string, group_id: number}[]> {
    return await dbRawRead(this.app, {role_type: [DBD_U_ROLE_TYPES.test_item_author, DBD_U_ROLE_TYPES.test_item_author_rev, DBD_U_ROLE_TYPES.test_item_author_super], uid}, `
      select ur.id, ur.uid, ur.role_type, ur.group_id from user_roles ur 
      where ur.uid = :uid 
      and ur.role_type in (:role_type)
      and ur.is_revoked = 0
      and (
        ur.expires_on is null 
        or ur.expires_on > CURRENT_TIMESTAMP()
      )
      group by ur.group_id;
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    // todo: these are only needed for the authoring
    const {tqt_version_ids} = <any> data;
    if(!tqt_version_ids || !tqt_version_ids.length) {
      return [];
    }
    return dbRawRead(this.app, {tqt_version_ids}, `
      select tqtv.tqt_id
          , tqt.current_version_id 
          , tqtv.id tqtv_id
          , tqtv.content_config 
          , tqtv.template_config 
          , tqtv.meta_adjusters 
          , tqtv.migrationOptions 
      from test_question_template_versions tqtv 
      join test_question_templates tqt
      on tqtv.tqt_id = tqt.id 
      and tqtv.is_revoked = 0
      where tqtv.id in (:tqt_version_ids)
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  isDomainAllowed(params: Params, domainLock?:string) {
    if (domainLock){
      let currentDomain = '';
      if (params && params.headers){
        currentDomain = `${params.headers.origin}/`;
      }
      if (!ALWAYS_ALLOWED_DOMAINS.includes(currentDomain) && domainLock !== currentDomain) {
        return false;
      }
    }

    return true;
  }
}
