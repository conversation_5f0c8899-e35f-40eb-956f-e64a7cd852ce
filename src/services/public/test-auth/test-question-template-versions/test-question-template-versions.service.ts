// Initializes the `public/test-auth/test-question-template-versions` service on path `/public/test-auth/test-question-template-versions`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TestQuestionTemplateVersions } from './test-question-template-versions.class';
import hooks from './test-question-template-versions.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/test-question-template-versions': TestQuestionTemplateVersions & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/test-question-template-versions', new TestQuestionTemplateVersions(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/test-question-template-versions');

  service.hooks(hooks);
}
