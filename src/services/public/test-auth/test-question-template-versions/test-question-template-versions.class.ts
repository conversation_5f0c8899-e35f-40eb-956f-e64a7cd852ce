import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class TestQuestionTemplateVersions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return await dbRawReadSingle(this.app, {id}, `
      select tqtv.tqt_id
        , tqtv.id tqtv_id
        , tqtv.content_config
        , tqtv.template_config
        , tqtv.meta_adjusters
        , tqtv.migrationOptions
      from test_question_template_versions tqtv 
      where tqtv.id = (:id)
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    const newData = {
      tqt_id: data.tqt_id,
      content_config: data.content_config || '[]',
      template_config: data.template_config || '[]',
      meta_adjusters: data.meta_adjusters || '[]',
      migrationOptions: data.migrationOptions || '[]',
      created_on: dbDateNow(this.app),
      created_by_uid: uid
    }

    const user = await dbRawReadSingle(this.app, {uid}, `
      SELECT id, concat(u.first_name, ' ', u.last_name) name
      FROM users u
      WHERE u.id = :uid;
    `)
    const newVersion = await this.app.service('db/write/test-question-template-versions').create(newData)

    return {...newVersion, created_by: user.name}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
