import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {dbDateNow} from "../../../../util/db-dates";
import {Errors} from "../../../../errors/general";

interface Data {}

interface ServiceOptions {}

export class NoteFiles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const {
      test_question_auth_note_id,
      file_name,
      file_url
    } = <any> data;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid

    const note = await this.app.service('db/read/test-question-auth-notes').get(test_question_auth_note_id);

    if (+uid !== +note.created_by_uid) {
      throw new Errors.Forbidden('NOT_YOUR_COMMENT');
    }

    return this.app
      .service('db/write/test-question-auth-note-files')
      .create({
        test_question_auth_note_id,
        file_name,
        file_url
      });
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid

    if (id) {
      const file = await this.app.service('db/read/test-question-auth-note-files').get(+id);
      const note = await this.app.service('db/read/test-question-auth-notes').get(+file?.test_question_auth_note_id);
      if (+uid !== +note.created_by_uid) {
        throw new Errors.Forbidden('NOT_YOUR_COMMENT');
      }
      return this.app.service('db/write/test-question-auth-note-files').remove(+id);
    }
    throw new Errors.BadRequest();
  }
}
