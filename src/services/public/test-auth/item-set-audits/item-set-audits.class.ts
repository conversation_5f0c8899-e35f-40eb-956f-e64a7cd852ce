import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { generateS3DownloadUrl, storeInS3 } from '../../../upload/upload.listener';
import { lzDecompressProps } from '../../../../util/lzstring';

interface Data {
  item_set_id: number,
  audit_slug: string,
  audit_results: string,
  num_issues: number,
  lang: string,
  item_id: number
}

interface ServiceOptions {}

export class ItemSetAudits implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<any> {
    if(!params || !params.query || !params.query.lang) {
      throw new Errors.BadRequest();
    }
    const item_set_id = id;
    const lang = params.query.lang;
    const item_id = params.query.item_id ?? -1; // Item ID default to -1 if nullish 

    //? Mirror: (0.038 sec / 0.0000091 sec cost 1306.96)
    const res = await dbRawRead(this.app, { lang, item_set_id, item_id }, `
      SELECT 
        isa.id AS id,
        isa.audit_slug,
        isa.audited_on,
        isa.audited_by_uid,
        isa.num_issues,
        isa.lang,
        u.contact_email AS email,
        COALESCE(CONCAT(u.first_name, ' ', u.last_name), 'Unknown User') AS name,
        isa.audit_results_path,
        isa.item_id
      FROM item_set_audits isa
      INNER JOIN (
        SELECT 
          audit_slug,
          MAX(id) AS latest_id
        FROM item_set_audits
        WHERE 
          lang = :lang AND 
          item_set_id = :item_set_id AND
          ${item_id !== -1 ? 'item_id = :item_id' : 'item_id IS NULL'}
        GROUP BY audit_slug
      ) latest_isa
        ON isa.id = latest_isa.latest_id 
        AND isa.lang = :lang
        AND isa.item_set_id = :item_set_id
        ${item_id !== -1 ? 'AND isa.item_id = :item_id' : 'AND isa.item_id IS NULL'}
      LEFT JOIN users u ON isa.audited_by_uid = u.id;
    `);
    res.forEach(isa =>{
      const resultPath = isa.audit_results_path;
      if(resultPath){
        let results_link;
        try {
          results_link = generateS3DownloadUrl(resultPath);
        } catch {}
        isa.results_link = results_link;
        delete isa.audit_results_path;
      }
    })
    return res;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    if(!data || !params) throw new Errors.BadRequest();

    // Compress compressed value in the payload
    data = lzDecompressProps(data, ['audit_results'])
    
    const { item_set_id, audit_slug, audit_results, num_issues, lang, item_id } = data;

    // find the last publish
    const testDesignRecord =  await this.getLastPublishedTestDesignRecord(+item_set_id)
    let is_superceded = 0;
    
    
    // Check if the same audit was ran since the last publish
    if(testDesignRecord && testDesignRecord.length){
      const records = <any[]> await this.app.service('db/read/item-set-audits').find({
        query: {
          item_set_id,
          audit_slug,
          audited_on : { $gt: testDesignRecord[0].created_on},
          lang
        },
        paginate: false
      });

      if(records && records.length) is_superceded = 1;
    }

    const newRecord = await this.app.service('db/write/item-set-audits').create({
      item_set_id,
      audit_slug,
      audited_by_uid: await currentUid(this.app, params),
      audited_on: dbDateNow(this.app),
      is_superceded,
      num_issues,
      lang,
      item_id
    })

    const auditLogId = newRecord.id;
    const timestamp = (new Date()).valueOf();
    const file_path = 'item_set_audit_results/'+ item_set_id +'/'+ audit_slug +'/'+ auditLogId+'/'+auditLogId+'-'+timestamp+'.json';
    await storeInS3(file_path, audit_results);

    await this.app
      .service('db/write/item-set-audits')
      .patch(auditLogId, { audit_results_path: file_path} )    

    return newRecord;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async getLastPublishedTestDesignRecord(source_item_set_id: number){

    const testDesignRecords = await dbRawRead(this.app, [source_item_set_id], `
        select *
        from test_designs
        where source_item_set_id = ?
         and is_revoked = 0
        group by id 
        order by created_on desc
        limit 1
      `);

      return testDesignRecords
  }

}
