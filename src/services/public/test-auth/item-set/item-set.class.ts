import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { hashGivenString } from '../../../../util/secret-codes';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import { NotifType } from '../notifications/notifications.class';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { SQL_ITEM_PSYCH_STATS_HIST } from './model/sql';

interface Data {
  group_id?: number,
  slug?: string,
  name?: string,
  description?: string,
  hash?: string,
  compositeAsmtTemplateQsId?: number[],
  test_design_question_sets?: number[],
  is_test_design?: number,
}
interface PaginatedData {
  total: number;
  limit: number;
  skip: number;
  data: Data[];
}

interface ServiceOptions {}

export class ItemSet implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }




  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    if(!userInfo.uid) {
      throw new Errors.BadRequest('MISSING_UID');
    }
    const {getArchived} = params?.query
    if(+getArchived){
      return await fetchQuestionSets(this.app, userInfo.uid, 0, 1);
    } else{
      return await fetchQuestionSets(this.app, userInfo.uid, 0, 0);
    }
  }

  async checkItemSetAccess(itemSet: any, uid: number, is_archived:number = 0) {
    const hasAccess = await this.app
      .service('auth/user-role-actions')
      .checkUserGroupRoles([itemSet.group_id, itemSet.single_group_id], uid, [DBD_U_ROLE_TYPES.test_item_author, DBD_U_ROLE_TYPES.test_item_author_rev, DBD_U_ROLE_TYPES.bc_auth_base, DBD_U_ROLE_TYPES.eqao_auth_read_only])
    if (!hasAccess){
      throw new Errors.Forbidden('GROUP_ROLE_REQ')
    }
  }

  async getItemSetQuestionIds(question_set_id: number){
    const itemSetMeta = await dbRawReadSingle(this.app, {question_set_id}, `
      select test_design_question_sets
      from temp_question_set
      where id = :question_set_id
    `)
    const parentSetIds = [
      question_set_id,
      ...JSON.parse(itemSetMeta.test_design_question_sets || '[]')
    ]
    const items = await dbRawRead(this.app, {parentSetIds}, `
      select id
      from test_questions
      where question_set_id IN (:parentSetIds)
        and is_archived = 0
    `)
    return items.map(q => q.id);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const question_set_id = <number> id;
    if (params && params.headers && params.query){

      const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
      const uid = <number> userInfo.uid;
      const snapshotFilter = params.query.snapshotFilter

      const payload = await this.app
        .service('db/read/temp-question-set')
        .get(id);

      const groupId = payload.group_id;

      const lockedDomain = payload.locked_domain;
      let currentDomain = '';
      if (params && params.headers) currentDomain = `${params.headers.origin}/`;

      if (lockedDomain && currentDomain !== 'http://localhost:4200/') {
        if (lockedDomain !== currentDomain) {
          throw new Errors.Forbidden('DOMAIN_LOCKED');
        }
      }
      await this.checkItemSetAccess(payload, uid);

      let questions:any[] = [];
      let childItemBanksInfo:any[] = [];

      if (payload.is_test_design){
        const childQuestionSets:number[] = JSON.parse(payload.test_design_question_sets);
        childItemBanksInfo = await this.app
        .service('db/read/temp-question-set')
        .db()
        .select(['id','group_id','slug','name','description'])
        .whereIn('id', childQuestionSets)
        .andWhere('is_archived', 0);
        const nonArchivedQuestionSets = childItemBanksInfo.map((IBank)=>IBank.id);
        for(let i=0; i<nonArchivedQuestionSets.length; i++){
          const childQuestionSetId = nonArchivedQuestionSets[i];
          const childQuestions = await this.app
            .service('public/test-auth/questions')
            .loadQuestionsByQuestionSet(childQuestionSetId, false, {snapshotFilter});
          questions = questions.concat(childQuestions)
        }
      }
      else{
        const retrieveArchived = !!params.query?.getArchived;
        questions = await this.app
          .service('public/test-auth/questions')
          .loadQuestionsByQuestionSet(question_set_id, false, undefined, retrieveArchived);
      }


      const res = await this.app.service('db/read/user-metas').db()
      .where('uid', uid)
      .where('key', 'show_comments')
      .where('key_namespace', 'test_auth')
      .select('value');

      let showComments;
      if(res && res.length) {
        showComments = res[0].value;
      }

      const availableTags = await this.app.service('db/read/item-tags').db()
      .where('group_id', groupId)
      .where('is_deleted', 0)

      const qIds = questions.map( q => q.id);

      const historicalItemRegisterQuery = (isSummary:boolean=false) => `
        select twtar.test_window_id
             , twtar.slug  twtar_slug
             , min(tw.is_qa) is_qa -- has it ever been used in a non-UAT environment
              ${ isSummary ? `
                , tqr.test_design_id -- deprecated
                , GROUP_CONCAT(tqr.test_design_id) test_design_ids
                , tw.title tw_title
                , tw.date_start 
                , tw.date_end 
              ` : '' }
              ${ !isSummary ? `
                , tqr.*
              ` : '' }
        from test_windows tw 
        join test_window_td_alloc_rules twtar 
          on twtar.test_window_id = tw.id 
          and twtar.is_questionnaire = 0 
	        and twtar.is_sample = 0 
          and tw.is_qa = 0 -- only include real operational 
        join test_question_register tqr
          on tqr.test_design_id = IFNULL(twtar.tqr_ovrd_td_id, twtar.test_design_id) 
        where tw.is_qa = 0 
          and tw.is_bg = 0
          and tqr.question_id IN (:questionIdsLookup)
        ${ !isSummary ? `group by tw.id, tqr.question_id` : '' /* group by tw.id, tqr.test_design_id, tqr.question_id */}
        ${ isSummary ? `group by tw.id` : ''  /* group by tw.id, tqr.test_design_id */} 
        order by twtar.test_window_id desc, tqr.test_design_id desc
      `

      const questionIdsLookup = [-1].concat(qIds)
      const historicalItemRegisterSummary = await dbRawRead(this.app, {questionIdsLookup}, historicalItemRegisterQuery(true)) // why don't we just derive this
      const historicalItemRegister = await dbRawRead(this.app, {questionIdsLookup}, historicalItemRegisterQuery())
      historicalItemRegister.forEach(record=> {
        delete record.id // no need to store the tqr.id
        delete record.test_form_id // no need to store the tqr.test_form_id
        delete record.item_bank_id // no need to store the tqr.item_bank_id
      })

      const historicalItemStats = await dbRawRead(this.app, {item_ids:questionIdsLookup}, SQL_ITEM_PSYCH_STATS_HIST)

      const db:Knex = this.app.get('knexClientRead');

      const tagLinks = await db('item_tag_link as l')
      .join('item_tags as t', 't.id', 'l.tag_id')
      .whereIn('l.item_id', qIds)
      .where('l.is_valid', 1)
      .where('t.is_deleted', 0)
      .select('l.tag_id', 'l.item_id')

      const scoringInfo = <any[]> await this.app
        .service('public/test-auth/test-question-scoring-info')
        .find({query: { question_ids: qIds} , paginate: false})

      const availableScoringCodes = <any[]> await this.app
        .service('public/test-auth/test-question-scoring-codes')
        .find();

      // Get the current stage and assignees of the questions
      const stageInfoByQuestion = await this.app
      .service('public/test-auth/question-workflow-stages')
      .getStageInfoByQIds(qIds);

      const editorInfoByQuestion = await this.app
      .service('public/test-auth/question-workflow-stages')
      .getAssignedStageInfoByQIds(qIds, 2);

      // Get # of pending graphic requests of the questions
      const pendingGraphicReqCountByQuestion = await this.app
      .service('public/test-auth/question-graphic-requests')
      .getPendingCountByQIds(qIds);

      const defaultProgressBar = await this.getDefaultProgressBarConfigs()

      return  {
        ... payload,
        questions,
        scoringInfo,
        availableScoringCodes,
        childItemBanksInfo,
        showComments,
        availableTags,
        tagLinks,
        historicalItemRegisterSummary,
        historicalItemRegister,
        stageInfoByQuestion,
        historicalItemStats,
        editorInfoByQuestion,
        pendingGraphicReqCountByQuestion,
        defaultProgressBar
      }
    }
    throw new Errors.BadRequest();
  }

  async getDefaultProgressBarConfigs(){
    // Mirror 0.033 sec / 0.000028 sec
    return dbRawRead(this.app, [], `
      SELECT slug, config FROM test_progress_bar_config  
    `)
  }

  async create (data: Data, params?: Params): Promise<Data> {

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {
      group_id,
      slug,
      name,
      description,
      is_test_design,
      compositeAsmtTemplateQsId,
    } = data;

    let test_design_question_sets;
    if (!!is_test_design){
      test_design_question_sets = JSON.stringify([]);
    }

    const payload:any = {
      test_design_question_sets
    };

    if (compositeAsmtTemplateQsId){
      const qsTemplate = await this.app
        .service('db/write/temp-question-set')
        .get(+compositeAsmtTemplateQsId);
      Object.keys(qsTemplate).forEach(fieldName => {
        if(fieldName != 'single_group_id'){
          payload[fieldName] = qsTemplate[fieldName]
        }
      });
      delete payload['id'];
      if (qsTemplate.is_test_design == 0){
        payload['test_design_question_sets'] = JSON.stringify([qsTemplate.id]);
      }
    }

    const newRecord = await this.app
      .service('db/write/temp-question-set')
      .create({
        ... payload, // params fed in after override whatever is in the payload
        group_id,
        slug,
        name,
        description,
        is_test_design,
      });

    const uid = await currentUid(this.app, params);

    this.app.service('public/test-auth/notifications').createNotif({
      config: {
        notifType: NotifType.CREATED_ASSESSMENT,
        itemId: newRecord.id,
        uid
      },
      forDevCoords: true
    }, params)

    return newRecord;

  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {oldHash, content, name, description, languages, style_profile, override} = <any> data;
    if (id && params){
      const uid = await currentUid(this.app, params);
      const lastSavedHashRecord = await this.app
        .service('db/read/temp-question-set')
        .db()
        .where('id', id)
        .select('hash')
      const lastSavedHash = lastSavedHashRecord[0].hash;
      if ((oldHash !== lastSavedHash) && (!override)){
        throw new Errors.BadRequest('REQ_OVERRIDE')
      }
      const hashBasis = JSON.stringify(data);
      const hash = hashGivenString(hashBasis);
      const patchRecord:any = { hash}
      if (name){ patchRecord.name = name };
      if (description){ patchRecord.description = description };
      if (languages){ patchRecord.languages = languages };
      if (style_profile) {patchRecord.style_profile = style_profile};

      const record = await this.app
        .service('db/write/temp-question-set')
        .patch(id, patchRecord)
      const question_set_id = record.id
      await this.app
        .service('db/write/temp-question-set-log')
        .create({ question_set_id, content, hash, updated_by_uid:uid })
      return {hash}
    }
    throw new Errors.GeneralError()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const is_archived = params?.query?.isRecovering? 0 : 1; 
    const payload = {
      is_archived: is_archived,
      archived_on: dbDateNow(this.app),
    }
    const res = await this.app
    .service('db/read/temp-question-set')
    .patch(id, payload);

    const uid = await currentUid(this.app, params);
    this.app.service('public/test-auth/notifications').createNotif({
      config: {
        notifType: NotifType.ARCHIVED_ASSESSMENT,
        itemId: res.id,
        uid: uid
      },
      forDevCoords: true
    }, params)
    return {}


  }
}
export const item_set_query = `
    SELECT 
        qs.id AS id,
        qs.single_group_id AS single_group_id,
        qs.group_id AS group_id,
        qs.slug AS slug,
        qs.name AS name,
        qs.description AS description,
        qs.is_test_design AS is_test_design,
        qs.test_design_question_sets AS test_design_question_sets,
        qsit.num_items AS num_items,
        qgm.uid AS uid
    FROM 
        questionset_group_members qgm
    JOIN 
        temp_question_set qs 
        ON (qs.is_archived = :isArchived AND qs.group_id = qgm.group_id AND qs.is_test_design = :isTestDesign)
    LEFT JOIN 
        question_set_item_tally qsit 
        ON qsit.id = qs.id
    WHERE 
        qgm.uid = :userID
    GROUP BY 
        qs.id, qgm.uid

    UNION

    SELECT 
        qs2.id AS id,
        qs2.single_group_id AS single_group_id,
        qs2.group_id AS group_id,
        qs2.slug AS slug,
        qs2.name AS name,
        qs2.description AS description,
        qs2.is_test_design AS is_test_design,
        qs2.test_design_question_sets AS test_design_question_sets,
        qsit2.num_items AS num_items,
        qsm.uid AS uid
    FROM 
        question_set_single_members qsm
    JOIN 
        temp_question_set qs2 
        ON (qs2.is_archived = :isArchived AND qs2.single_group_id = qsm.single_group_id AND qs2.is_test_design = :isTestDesign)
    LEFT JOIN 
        question_set_item_tally qsit2 
        ON qsit2.id = qs2.id
    WHERE 
        qsm.uid = :userID
    GROUP BY 
        qs2.id, qsm.uid

    ORDER BY 
        id, uid;
  `;
// Usage with dbRawRead function
export const fetchQuestionSets = async (app: any, userID: number, isTestDesign: number, isArchived: number) => {
  const props = {
    isArchived,
    isTestDesign,
    userID
  }
  
  const results = await dbRawRead(app, props, item_set_query);
  return results;
};
