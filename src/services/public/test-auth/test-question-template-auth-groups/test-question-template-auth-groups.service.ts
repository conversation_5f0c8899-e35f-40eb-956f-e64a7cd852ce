// Initializes the `public/test-auth/test-question-template-auth-groups` service on path `/public/test-auth/test-question-template-auth-groups`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TestQuestionTemplateAuthGroups } from './test-question-template-auth-groups.class';
import hooks from './test-question-template-auth-groups.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/test-question-template-auth-groups': TestQuestionTemplateAuthGroups & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/test-question-template-auth-groups', new TestQuestionTemplateAuthGroups(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/test-question-template-auth-groups');

  service.hooks(hooks);
}
