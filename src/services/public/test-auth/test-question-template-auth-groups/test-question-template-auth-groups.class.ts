import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class TestQuestionTemplateAuthGroups implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if(!data.tqt_id || !data.bank_group_id) {
      throw new Errors.BadRequest('MISSING_DATA');
    }

    const uid = await currentUid(this.app, params)
    const newGroup = {
      tqt_id: data.tqt_id,
      bank_group_id: data.bank_group_id,
      created_by_uid: uid,
    }
    return await this.app.service('db/write/test-question-template-auth-groups').create(newGroup);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return await this.app.service('db/write/test-question-template-auth-groups').patch(id, data);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query || !params.query.tqt_id) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const tqt_id = params.query.tqt_id;
    const groupToRevoke = await dbRawRead(this.app, {tqt_id, bank_group_id: id}, `
      SELECT * FROM test_question_template_auth_groups
      WHERE tqt_id = :tqt_id
      AND bank_group_id = :bank_group_id;
    `)

    const uid = await currentUid(this.app, params)
    return await Promise.all(groupToRevoke.map(async (group) => {
      return await this.app.service('db/write/test-question-template-auth-groups').patch(group.id, {is_revoked: 1, revoked_by_uid: uid, revoked_on: dbDateNow(this.app)});;
    })); 
  }
}
