import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import moment from 'moment';
import {ChangeLogType} from './../question-change-log/types'
import { dbRawRead, dbRawReadSingle} from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Suggestions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {lang, question_id, is_responded, get_review_suggestion} = params.query;

    // variable to check if we are going to only include responded suggestions if 0 return all suggestions
    const includeResponded = +is_responded === 0 ? 0 : 1;

    let created_before;
    // variable to check if were going to get suggestion before touched by reviewer
    if(+get_review_suggestion){
      created_before = (await this.app.service('public/test-auth/question-change-log').getLatestStageTransitionByOrder(question_id, lang, 3)).created_on.toISOString();
    }
    
    //question_set_roles_by_uid
    const suggestion = await this.findSuggestion(lang, question_id, includeResponded, created_before)

    let changes:any = '{}';
    let annotations:any = '{}';

    if(suggestion) {
      changes = suggestion.changes || '{}';
      annotations = suggestion.annotations || '{}';
    }

    changes = JSON.parse(changes);
    annotations = JSON.parse(annotations);

    //Need to return user info for each uid in the changes map to display their names
    const uids = new Set<string>();

    for(const entryId in changes) {
      if(changes[entryId].uid) {
        uids.add(changes[entryId].uid);
      }

      const ignoreProps = ['uid', 'changed_on']
      for(const prop in changes[entryId]) {
        if(ignoreProps.includes(prop)) {
          continue;
        }
        if(changes[entryId][prop].uid) {
          uids.add(changes[entryId][prop].uid);
        }
      }
    }

    // Include UIDs of authors of annotated word diffs to return their info
    for (const entryId in annotations) {
      for (const prop in annotations[entryId]) {
        annotations[entryId][prop].forEach((annotation:any) => {
          if (annotation.author) uids.add(annotation.author)
        });
      }
    }

    const uidArr = Array.from(uids);

    const users = await this.app.service('db/read/users')
    .db()
    .whereIn('id', uidArr)
    .select('id','first_name', 'last_name');

    const userInfo:any = {};
    for(const user of users) {
      userInfo[user.id] = user;
    }

    // const highlightInfo = await this.app.service('public/test-auth/highlight-notes').find({...params, query: {...params.query, test_question_id: question_id}})
    return {suggestion, userInfo};
  }

  findSuggestion (lang:string, question_id:number, isResponded:number = 1, created_before?:Date) {
    return dbRawReadSingle(this.app, {question_id, lang, created_before}, `
      select s.*, sv.id as version_id ${created_before? ', sv.config as sv_config' : ''}
      from test_question_suggestions s
      join test_question_versions v 
        on s.origin_question_version_id = v.id
      join test_question_suggestion_versions sv
        on sv.test_question_suggestion_id = s.id
      where v.test_question_id = :question_id
      and s.lang = :lang
      ${created_before? 'and sv.created_on < :created_before' : ''}
      ${isResponded? 'and s.is_responded = 0' : ''}
      order by s.created_on desc, sv.created_on desc
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {

    const {origin_question_version_id, test_question_id, config, lang} = <any>data;
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);
    // Create new suggestion
    const newSuggestion = await this.app.service('db/write/test-question-suggestions').create({
      created_by_uid: uid,
      origin_question_version_id,
      last_updated_by_uid: uid,
      config,
      lang
    })
    // Also save in version history
    const suggestionVersion = await this.app.service('db/write/test-question-suggestion-versions').create({
      config, 
      test_question_suggestion_id: newSuggestion.id, 
      created_by_uid: uid
    })
    
    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.TRACKING_CHANGES_ENTER,
      log_data: {
        origin_question_version_id,
        suggestion_id: suggestionVersion.id,
        suggestion_version_id: suggestionVersion.id,
      }
    }, params)

    return { ...newSuggestion, versionId: suggestionVersion.id}

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {is_responded, config, origin_question_version_id, lang, changes, annotations, is_reject_sugg, test_question_id, comment} = <any>data;

    if(!params?.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {question_id} = params.query;

    const uid = await currentUid(this.app, params);

    if(!id) {
      //Try to find
      const existingSuggestion = await this.findSuggestion(lang, question_id)
      if(existingSuggestion) {
        id = existingSuggestion.id;
      } else {
        return this.create({origin_question_version_id, config, lang});
      }
    }

    if(config) {
      
      //Save info regarding who made changes to which element / field and when.
      const existingSugg = await this.app.service('db/read/test-question-suggestions').get(<any>id);

      let existingChanges = existingSugg.changes || '{}';

      existingChanges = JSON.parse(existingChanges);

      for(const changeEntry of changes) {
        if(!existingChanges[changeEntry.entryId]) {
          existingChanges[changeEntry.entryId] = {};
        }
        if(changeEntry.prop) {
          existingChanges[changeEntry.entryId][changeEntry.prop] = {uid , changed_on: Date.now()};
        } else {
          existingChanges[changeEntry.entryId] = {uid , changed_on: Date.now()};
        }
      }

      const patchChanges = JSON.stringify(existingChanges);

      // If an annotation diff has an author but no date, it was just added before this save, so label with current time
      const currentTime = moment().utc().format();
      for (const entryId in annotations) {
        for (const prop in annotations[entryId]) {
          annotations[entryId][prop].forEach((annotation:any) => {
            if (annotation.author && !annotation.date) annotation.date = currentTime
          });
        }
      }

      const patchAnnotations = JSON.stringify(annotations);
      // Update suggestion
      const patchedSuggestion = await this.app.service('db/write/test-question-suggestions').patch(id, {
        config, 
        last_updated_on: dbDateNow(this.app), 
        last_updated_by_uid: uid, 
        changes: patchChanges,
        annotations: patchAnnotations
      })

      // Save in version history
      const suggVersion = await this.app.service('db/write/test-question-suggestion-versions').create({
        config,
        test_question_suggestion_id: id,
        created_by_uid: uid,
      })

      // Make a record in the log
      this.app.service('public/test-auth/question-change-log').create({
        test_question_id,
        lang,
        log_type: ChangeLogType.SUGGESTION_EDIT,
        log_data: {
          is_reject_sugg: !!is_reject_sugg,
          comment,
          suggestion_id: id,
          suggestion_version_id: suggVersion.id,
        }
      }, params)


      return {...patchedSuggestion, version_id: suggVersion.id};
    }
    if(is_responded == 1) {
      const patchedSuggestion = this.app.service('db/write/test-question-suggestions').patch(id, {is_responded, responded_on: dbDateNow(this.app)})

      // Make a record in the log
      this.app.service('public/test-auth/question-change-log').create({
        test_question_id,
        lang,
        log_type: ChangeLogType.TRACKING_CHANGES_LEAVE,
      }, params)

      return patchedSuggestion;
    }
    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
