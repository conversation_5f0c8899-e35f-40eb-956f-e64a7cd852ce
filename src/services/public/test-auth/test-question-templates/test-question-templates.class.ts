import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ICreateTemplateData {
  icon_url: string,
  name: string,
  reference_item: string,
  content_config: string,
}

const defaultMeta = {
  is_math_compatible: false
}

interface ServiceOptions {}

export class TestQuestionTemplates implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: ICreateTemplateData, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {icon_url, name, reference_item, content_config} = data;
    const created_by_uid = await currentUid(this.app, params)
    const template = await this.app.service('db/write/test-question-templates').create({
        name
      , icon_url
      , reference_item
      , created_by_uid
      , created_on: dbDateNow(this.app)
      , meta: JSON.stringify(defaultMeta)
    });

    const templateVersion = await this.app.service('db/write/test-question-template-versions').create({
      tqt_id: template.id
      , content_config
      , template_config: '[]'
      , meta_adjusters: '[]'
      , migrationOptions: '[]'
      , created_by_uid
      , created_on: dbDateNow(this.app)
    })

    await this.app.service('db/write/test-question-templates').patch(template.id, {
      current_version_id: templateVersion.id
    });
    template.template_name = template.name;
    return template;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return await this.app.service('db/write/test-question-templates').patch(id, data);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query || (!params.query.is_archived && !params.query.is_revoked)) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const is_archived = params.query.is_archived
    const is_revoked = params.query.is_revoked
    const UID = await currentUid(this.app, params);
    if(is_revoked) {
      return await this.app.service('db/write/test-question-templates').patch(id, {is_revoked: 1, revoked_by_uid: UID, revoked_on: dbDateNow(this.app)});
    }
    if(is_archived == 1) {
      return await this.app.service('db/write/test-question-templates').patch(id, {is_archived: 1, archived_by_uid: UID, archived_on: dbDateNow(this.app)});
    } else {
      return await this.app.service('db/write/test-question-templates').patch(id, {is_archived, archived_by_uid: null, archived_on: null});
    }
  }
}
