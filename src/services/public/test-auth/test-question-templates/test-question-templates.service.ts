// Initializes the `public/test-auth/test-question-templates` service on path `/public/test-auth/test-question-templates`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TestQuestionTemplates } from './test-question-templates.class';
import hooks from './test-question-templates.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/test-question-templates': TestQuestionTemplates & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/test-question-templates', new TestQuestionTemplates(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/test-question-templates');

  service.hooks(hooks);
}
