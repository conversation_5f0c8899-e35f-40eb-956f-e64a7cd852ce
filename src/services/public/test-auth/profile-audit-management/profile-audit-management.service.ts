// Initializes the `public/test-auth/profile-audit-management` service on path `/public/test-auth/profile-audit-management`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ProfileAuditManagement } from './profile-audit-management.class';
import hooks from './profile-audit-management.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/profile-audit-management': ProfileAuditManagement & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/profile-audit-management', new ProfileAuditManagement(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/profile-audit-management');

  service.hooks(hooks);
}
