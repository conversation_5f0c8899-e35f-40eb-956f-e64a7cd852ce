import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';

interface Data {
  group_id: number
  config: {[slug:string] : ExcludedAuditEntry};
}

interface ExcludedChecks {
  [checkId: string]: boolean;
}

interface ExcludedAuditEntry {
  isExcluded: boolean;
  excludedChecks: ExcludedChecks;
};

interface AuditGroupConfig {
  id: number;
  group_id: number;
  config: {[slug:string] : ExcludedAuditEntry};
}


interface ServiceOptions {}

export class ProfileAuditManagement implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<AuditGroupConfig> {
    let result = await this.app.service("db/read/audit-profile-config").find({
      query: {
      group_id: +id,
      $select: ['config'],
      },
      paginate: false,
      limit: 1
    }) as AuditGroupConfig[];
    // if (!result || !result.length) {
    //   const questionSetParams = await this.app.service("db/write/question-set-parameters").get(+id);
    //   if(questionSetParams?.extend_id) {
    //     result = await this.app.service("db/read/audit-profile-config").find({
    //       query: {
    //         group_id: questionSetParams.extend_id,
    //         $select: ['config'],
    //       },
    //       paginate: false,
    //       limit: 1
    //     }) as AuditGroupConfig[];
        
    //   }
    // }
    return result?.[0];
    
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<Data> {
    const uid = await currentUid(this.app, params);

    const query = {
        group_id: data.group_id,
        $select: ['id', 'group_id', 'config']
    };

    const existing = await this.app.service("db/read/audit-profile-config").find({
        query,
        paginate: false,
        limit: 1
    }) as AuditGroupConfig[];

    if (!existing.length) {
        // Create new record
        const createdData = {
            config: JSON.stringify(data.config), // Pass the object directly
            created_by: uid,
            updated_by: uid,
            group_id: data.group_id
        };
        return this.app.service("db/write/audit-profile-config").create(createdData);
    } else {
        // Update first existing record by group_id
        const existingId = existing[0].id;
        const updatedData = {
            config: JSON.stringify(data.config), // Pass the object directly
            updated_by: uid,
            group_id: data.group_id
        };
        return this.app.service("db/write/audit-profile-config").patch(existingId, updatedData);
    }
}


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented;
  }
}

