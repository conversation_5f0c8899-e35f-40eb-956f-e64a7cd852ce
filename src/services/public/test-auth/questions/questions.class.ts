import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { DBD_U_ITEM_TYPES } from '../../../../constants/db-extracts';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawWrite } from '../../../../util/db-raw';
import { NotifType } from '../notifications/notifications.class';
import { dbDateToMoment, calculateDbDiff } from '../../../../hooks/_util';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Knex } from 'knex'
import { currentUid } from '../../../../util/uid';
import {ChangeLogType} from './../question-change-log/types'
interface Data {
  id: number,
  order: number,
  question_set_id:number, 
  question_label:string, 
  config:string,
  diff?:string,
  created_on: string,
  change_note?: string,
  created_by_uid: number,
  is_archived: number,
  is_editing: number,
  last_touched_by?: string,
  last_touched_by_uid?: number,
  isRecentUse?: boolean,
  item_asset_version_ids?: number[],
  item_asset_ids?: number[]
  item_type?: DBD_U_ITEM_TYPES,
  parent_id?: number,
  is_accept_sugg?: number,
  lang?: string,
  isSaveLogged?: boolean,
  affected_langs?: string[],
  comment?: string
}

interface ServiceOptions {}

export class Questions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  //**Find the current version and suggestion version */
  async findLatestQuestionAndSuggIds(test_question_id:number, lang:string){
    const suggestion = await this.app.service('public/test-auth/suggestions').findSuggestion(lang, test_question_id);
    const version = await this.app.service('public/test-auth/questions').findLatestVersion(test_question_id);

    let test_question_suggestion_version_id, test_question_version_id;
    if (suggestion) test_question_suggestion_version_id = suggestion.version_id;
    if (version) test_question_version_id = version.id;

    return {test_question_suggestion_version_id, test_question_version_id}
  }

  findLatestVersion(test_question_id:number){
    return dbRawReadSingle(this.app, {test_question_id}, `
      select id 
      from test_question_versions
      where test_question_id = :test_question_id
      order by created_on desc;
    `)
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {question_set_id, is_list_only} = params.query
      return this.loadQuestionsByQuestionSet(question_set_id, is_list_only);
    }
    return [];
  }
  
  async loadQuestionsByQuestionSet(question_set_id:number, is_list_only:boolean=false, config?:{snapshotFilter?:string}, retrieveArchived = false){
    if (!config){ config = {}; }

    //Order secondarily by question label to preserve previous ordering for older item banks where order was not set yet.
    await dbRawWrite(this.app, [question_set_id], 
      `SET @row_number = -1;
      SET @prev_parent_id = NULL;
      UPDATE test_questions
        SET \`order\` = CASE 
          WHEN @prev_parent_id IS NULL AND parent_id IS NOT NULL THEN @row_number:=0
          WHEN parent_id != @prev_parent_id THEN @row_number:=0 
          ELSE (@row_number:=@row_number + 1)
          END,
          parent_id = @prev_parent_id := parent_id
      WHERE question_set_id = ?
            AND is_archived = 0
            ORDER BY parent_id, \`order\`, question_label`
      );
      
    let dbQuery = this.app
      .service('db/read/test-questions')
      .db()
      .where('question_set_id', question_set_id)
      .where('is_archived', retrieveArchived? 1 : 0)
      .where(function() {
        if (retrieveArchived) {
          // If retrieveArchived is true, filter by parent_id being null or undefined as we dont want sequences
          this.where('parent_id', null).orWhere('parent_id', 'undefined').orWhere('parent_id', '');
        }
      })
      .orderBy('parent_id')
      .orderBy('order')
      .orderBy('question_label');
    
    if (is_list_only){
      dbQuery = dbQuery.select(['id', 'item_version_code', 'question_label'])
    }
    const questions:any[] = await dbQuery;
    if (config.snapshotFilter){
      const questionIds:number[] = [];
      const questionRef = new Map();
      questions.forEach(q => {
        questionRef.set(q.id, q)
        questionIds.push(q.id);
      });
      const configOverrides = await dbRawRead(this.app, [questionIds, config.snapshotFilter], `
        select tqv.test_question_id id
            , tqv.config 
        from (
          select test_question_id, max(id) id
          from test_question_versions
          where test_question_id IN (?)
          and created_on < ?
          group by test_question_id
        ) tqv0
        join test_question_versions tqv
          on tqv.id = tqv0.id
      `);
      configOverrides.forEach(overrideRecord => {
        // note, if some items were created later than the given date, they will not be in the override list and just the latest version of the daata will be used
        const question = questionRef.get(overrideRecord.id);
        if (question){
          question.config = overrideRecord.config;
        }
      })
    }
    return questions;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    
    if (params){

      const uid = await currentUid(this.app, params);
      const isSkipRecentUse = !! params.query?.isSkipRecentUse

      const questionRecord = await this.app
          .service('db/read/test-questions')
          .get(id);
      
      if (!isSkipRecentUse){
        let isRecentUse:boolean = false;
        const lastMod = await this.app
            .service('db/read/test-question-versions')
            .db()
            .where('test_question_id', id)
            .orderBy('created_on', 'desc')
            .limit(1)
            .select(['id', 'created_on']);
    
        if (questionRecord.is_editing === 1 && questionRecord.last_touched_by_uid !== uid){
          const msSinceLastUpdate = calculateDbDiff(questionRecord.last_touched_on);
          if (msSinceLastUpdate < 35*1000){
            isRecentUse = true;
          }
        }
       
        questionRecord.version_id = lastMod[0].id;
        questionRecord.updated_on = lastMod[0].created_on;
        questionRecord.isRecentUse = isRecentUse;
      }
  
      return questionRecord

    }

    throw new Errors.BadRequest()
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const {question_set_id, question_label, config, is_editing, last_touched_by, order, parent_id} = data;

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    let item_type = data.item_type || DBD_U_ITEM_TYPES.item;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = <number> userInfo.uid
    const newRecord = await this.app
        .service('db/write/test-questions')
        .create({
          question_set_id, 
          order,
          question_label, 
          config,
          created_by_uid,
          last_touched_by_uid: created_by_uid,
          last_touched_by,
          is_editing,
          last_touched_on: dbDateNow(this.app),
          item_type, 
          parent_id
        })
    
    const versionRecord = await this.app
    .service('db/write/test-question-versions')
    .create({
      test_question_id: newRecord.id, 
      question_label, 
      config,
      diff: '{}',
      created_by_uid,
    })

    //Put the new item into the FIRST workflow stage in both en/fr
    await Promise.all(
      ['en', 'fr'].map(lang => {
        this.app.service('public/test-auth/question-workflow-stages').create(
          { stage_order: 1},
          { ...params, query: { test_question_id: newRecord.id, lang }}
        );
      })
    )

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id: newRecord.id,
      lang: "both",
      log_type: ChangeLogType.ITEM_CREATION,
      log_data: {
        test_question_version_id: versionRecord.id,
      }
    }, params)

    this.app.service('public/test-auth/notifications').createNotif({
      config: {
        notifType: NotifType.CREATED_ITEM,
        itemId: newRecord.id,
        uid: userInfo.uid
      },
      forDevCoords: true
    }, params)

    return <any> {id: newRecord.id, version_id: versionRecord.id};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {question_label, config, is_editing, last_touched_by, diff, order, parent_id, change_note, is_accept_sugg, lang, isSaveLogged, affected_langs, comment} = data;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = <number> userInfo.uid;
    const last_touched_by_uid = created_by_uid;
    let newVersionId;
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if (!config){

      if(order != null) {
        await this.app.service('db/write/test-questions')
        .patch(id, {
          order,
          last_touched_on: dbDateNow(this.app),
          last_touched_by_uid,
          last_touched_by, 
          parent_id
        })
      } else {
        // just indicating that it is open for editing
        await this.app
          .service('db/write/test-questions')
          .patch(id, {
            is_editing,
            last_touched_on: dbDateNow(this.app),
            last_touched_by_uid,
            last_touched_by
        })
      }
    }
    else{
      // actual update
      const updatedRecord = await this.app
      .service('db/write/test-questions')
      .patch(id, {
        question_label, 
        config,
        order,
        is_editing,
        last_touched_on: dbDateNow(this.app),
        last_touched_by_uid,
        last_touched_by,
        parent_id
      })
      // versioning
      const newRecord = await this.app
        .service('db/write/test-question-versions')
        .create({
          test_question_id: id, 
          question_label, 
          config,
          diff,
          message: change_note,
          created_by_uid,
          affected_langs: JSON.stringify(affected_langs)
        })
      newVersionId = newRecord.id

      // Make a record in the log
      if (isSaveLogged){
        const logLang = affected_langs?.length == 1 ? affected_langs[0] : 'both'
        this.app.service('public/test-auth/question-change-log').create({
          test_question_id: id,
          lang: logLang,
          log_type: ChangeLogType.REAL_EDIT,
          log_data: {
            is_accept_sugg: !!is_accept_sugg,
            test_question_version_id: newVersionId,
            comment
          }
        }, params)
      }

      //create asset usage records, map through array and create
      if(data.item_asset_version_ids){
        await Promise.all(
          data.item_asset_version_ids.map(version => {            
            return this.app
              .service('db/write/item-version-asset-usage')
              .create({
                item_version_id: newRecord.id,
                asset_version_id: version,
                created_by_uid,
                created_on: dbDateNow(this.app)
              });
          })
        )
      }

      if(data.item_asset_ids) {
        const existingLinks = await this.app.service('db/read/item-asset-link').db()
        .where('item_id', id)
        .select();

        const item_asset_ids = <number[]>data.item_asset_ids;

        const extraAssetIds = existingLinks.filter( (l:any) => l.is_valid && !item_asset_ids.includes(l.asset_id) ).map( (l:any) => l.asset_id);

        await this.app.service('db/write/item-asset-link').db()
        .where('item_id', id)
        .whereIn('asset_id', extraAssetIds)
        .update({
          is_valid: 0,
          last_updated: dbDateNow(this.app),
          last_updated_by_uid: userInfo.uid
        })

        const existingInvalidAssetIds =  existingLinks.filter((l:any) => !l.is_valid && item_asset_ids.includes(l.asset_id)).map( (l:any) => l.asset_id)

        await this.app.service('db/write/item-asset-link').db()
        .where('item_id', id)
        .whereIn('asset_id', existingInvalidAssetIds)
        .update({
          is_valid: 1,
          last_updated: dbDateNow(this.app),
          last_updated_by_uid: userInfo.uid
        })


        const existingAssetIds = existingLinks.map( (l:any) => l.asset_id)
        const createAssetIds = item_asset_ids.filter( asset_id => !existingAssetIds.includes(asset_id))
        
        const createProms = [];
        for(const assetId of createAssetIds) {
          createProms.push(
            this.app.service('db/write/item-asset-link').create(
              {
                item_id: id,
                asset_id: assetId,
                created_by_uid: userInfo.uid,
                last_updated_by_uid: userInfo.uid
              }
            )
          );
        }

        await Promise.all(createProms);



      }
      
      if(diff) {
        const diffArr = JSON.parse(diff);
        const paramChanges = diffArr.filter((d:any) => d.path && d.path[0] === 'meta');
        const noteChanges = diffArr.filter((d:any) => d.path && d.path[0] === 'notes' && !(d.kind ==='N' && !d.rhs))
        const uploadChanges = diffArr.filter((d:any) => d.path && d.path[0] === 'uploads' && 
          ((d.kind === 'A' && d.item && d.item.rhs && d.item.rhs.upload) 
          ||( (d.kind == 'N' || d.kind === 'E') && d.path.length === 3 && d.path[2] ==='upload' && d.rhs))
          )

        const notifyDevCoords = (notifType: NotifType) => {
          this.app.service('public/test-auth/notifications').createNotif({
            config: {
              notifType,
              uid: userInfo.uid,
              itemId: id
            },
            forDevCoords: true
          }, params)
        }
        if(paramChanges.length) {
          notifyDevCoords(NotifType.PARAM_CHANGES)
        }

        if(noteChanges.length) {
          notifyDevCoords(NotifType.EDITED_NOTES)
        }

        if(uploadChanges.length) {
          notifyDevCoords(NotifType.ATTACHED_FILE)
        }

        if(!paramChanges.length && !noteChanges.length && !uploadChanges.length) {
          notifyDevCoords(NotifType.EDITED_ITEM)
        }
      }
      
    }
    
    return <any> {id, versionId: newVersionId};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    const last_touched_by_uid = <number> userInfo.uid;
    const isRecovering = params?.query?.recoverQuestion === 'true'? 0 : 1;


    //If there are children also archive them
    await dbRawWrite(this.app, [ isRecovering, last_touched_by_uid, id, id], 
      ` UPDATE test_questions
        SET is_archived = ?,
        last_touched_by_uid = ?,
        last_touched_on = '${dbDateNow(this.app)}'
        WHERE (id = ?
        OR parent_id = ?)
        AND is_archived = ${isRecovering? 0 : 1};
        `
      );    

    return <any> {id};
  }
}
