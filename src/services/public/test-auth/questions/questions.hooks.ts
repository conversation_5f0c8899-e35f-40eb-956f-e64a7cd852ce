import * as authentication from '@feathersjs/authentication';
import { hasRoleAction } from '../../../../hooks/has-role-action';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
const { disallow, iff, required } = require('feathers-hooks-common');
// Don't remove this comment. It's needed to format import lines nicely.

const { authenticate } = authentication.hooks;

export default {
  before: {
    all: [ 
      authenticate('jwt'),
      // hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    find: [
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    get: [
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    create: [
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
      required('question_set_id'),
      required('question_label'),
      required('config'),
      required('last_touched_by'),
      required('is_editing'),
    ],
    update: [
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
      // required('question_label'), 
      // required('config'), 
      required('is_editing'), 
      required('last_touched_by'),
    ],
    patch: [
      // Don't put restriction here, because an editor should be able to bring a question out of tracking mode by moving stages: Item in Edit -> Final Review
    ],
    remove: [
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
