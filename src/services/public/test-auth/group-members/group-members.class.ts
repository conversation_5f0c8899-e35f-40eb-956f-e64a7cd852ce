import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import {IUser} from "../../../db/schemas/users.schema";
import {filter} from "compression";
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { Knex } from 'knex';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class GroupMembers implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const group_id = params.query.group_id;

      if(!group_id) {
        throw new Errors.BadRequest('MISSING_AUTH_GROUP_ID');
      }

      const single_group_id = params.query.single_group_id;
      const getAuthEmails = params.query.getAuthEmails;

      let members;
      let single_members;
      if(single_group_id) {
        single_members = <any> await dbRawRead(this.app, {single_group_id}, getQuestionSetSingleMembers());
      }

      members = <any> await dbRawRead(this.app, {group_id}, getQuestionSetGroupMembers());


      const userRoleInfo = new Map<number,  {role_types:DBD_U_ROLE_TYPES[], is_single: boolean, expires_on?: string, user?: any}>();

      const initRoleTypeData = (member: any, is_single: boolean) => {
        if(userRoleInfo.has(member.uid)) {
          (<any>userRoleInfo.get(member.uid)).role_types.push(member.role_type);
        } else {
          userRoleInfo.set(member.uid, {
            role_types: [member.role_type],
            is_single,
            expires_on: member.expires_on
          });
        }
      }

      members.forEach((member: any) => {
        initRoleTypeData(member, false);
      });

      if(single_members && single_members.length) {
        single_members.forEach((member:any) => {
          userRoleInfo.delete(member.uid);
        })
        single_members.forEach((member:any) => {
          initRoleTypeData(member, true); //Overwrites any of the role type data from the auth group members if it exists
        });
      }

      const uids = Array.from(userRoleInfo.keys());

      const db:Knex = this.app.get('knexClientRead');
      const users = <any> await db('users as u')
      .whereIn('u.id', uids)
      .leftJoin('auths as a', 'a.uid', 'u.id')
      .where( (builder) => {
        return builder.where('u.is_claimed', 1).orWhereRaw(`(SELECT COUNT(*) FROM auths as a2 WHERE a2.email = u.contact_email) = 0`)
      })
      .select('u.*', 'a.email as auth_email') //Don't show unclaimed users where an auth already exists.

      for(const user of users) {
        (<any>userRoleInfo.get(user.id)).user = user;
      }

      const userInfoVals = Array.from(userRoleInfo.values());
      //filter out the unclaimed users where auth already exists
      const mappedUsers = userInfoVals.filter(userInfo => userInfo.user).map((userInfo) => {
        return {
          id: userInfo.user.id,
          first_name: userInfo.user.first_name,
          last_name: userInfo.user.last_name,
          contact_email: getAuthEmails ? (userInfo.user.is_claimed ? userInfo.user.auth_email : userInfo.user.contact_email) : userInfo.user.contact_email,
          is_claimed: userInfo.user.is_claimed,
          is_selected: false,
          role_types: userInfo.role_types,
          is_single: userInfo.is_single,
          expires_on: userInfo.expires_on
        }
      })
      return mappedUsers
    }
    throw new Errors.BadRequest( JSON.stringify(params ? params.query : 'NO_PARAMS') );
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}

export const getQuestionSetSingleMembers = () => {
  // 0.022 sec / 0.000037 sec Mirror
  return `
    SELECT 
        ur.id AS id,
        ur.group_id AS single_group_id,
        ur.uid AS uid,
        ur.expires_on AS expires_on,
        g.description AS description,
        ur.role_type AS role_type,
        qs.group_id AS group_id
    FROM 
        u_groups g
    JOIN 
        user_roles ur 
        ON (
            ur.group_id = g.id 
            AND ur.is_revoked = 0 
            AND (ur.expires_on IS NULL OR ur.expires_on > NOW())
        )
    JOIN 
        temp_question_set qs 
        ON (qs.single_group_id = g.id)
    WHERE 
        g.group_type = 'single_questionbank'
        AND ur.group_id = :single_group_id
    ORDER BY 
        g.id;
  `
}

export const getQuestionSetGroupMembers = () => {
  // 0.028 sec / 0.000033 sec Mirror
 return `
  SELECT 
      ur.id AS id,
      ur.group_id AS group_id,
      ur.uid AS uid,
      ur.expires_on AS expires_on,
      g.description AS description,
      ur.role_type AS role_type
    FROM 
        u_groups g
    JOIN 
        user_roles ur 
        ON (
            ur.group_id = g.id 
            AND ur.is_revoked = 0 
            AND (ur.expires_on IS NULL OR ur.expires_on > NOW())
        )
    WHERE 
        g.group_type = 'questionbank'
        AND group_id = :group_id
    ORDER BY 
        g.id;
 `
}