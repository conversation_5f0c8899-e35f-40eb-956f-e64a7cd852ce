import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class QuestionRevisions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {test_question_id} = (<any> params).query;
    return await dbRawRead(this.app, [test_question_id], testQuestionRevisionQuery)
  }

  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const revision = await this.app.service('db/read/test-question-versions').get(id);

    const highlightInfo = await this.app.service('public/test-auth/highlight-notes').find({...params, query: {...params.query, target_revision_id: id}})
    return {revision, highlightInfo}
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {message} = <any>data;
    if(message) {
      return this.app.service('db/write/test-question-versions').patch(id, {message});
    }
    return {};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}

// Mirror 0.414 sec / 0.594 sec
const testQuestionRevisionQuery = `
  SELECT 
      tqv.id AS id,
      tqv.test_question_id AS test_question_id,
      tqv.question_label AS question_label,
      tqv.created_on AS created_on,
      tqv.created_by_uid AS created_by_uid,
      tqv.message AS message,
      u.first_name AS first_name,
      u.last_name AS last_name,
      u.contact_email AS contact_email,
      tqv.affected_langs AS affected_langs
  FROM 
      test_question_versions tqv
  JOIN 
      users u ON u.id = tqv.created_by_uid
  WHERE 
    tqv.test_question_id = ?
  ORDER BY 
    tqv.created_on DESC
  LIMIT 1000000;
`
