import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {dbDateNow} from "../../../../util/db-dates";
import {Errors} from "../../../../errors/general";
import { dbRawRead } from '../../../../util/db-raw';
import { getAccessRoleUids, getGroupId, NotifType } from '../notifications/notifications.class';
import {IHighlightNoteInfo, ModeType} from './../highlight-notes/highlight-notes.class'
import { ILanguage } from '../../test-question-register/tqr-publish/types';

interface Data {}

export enum ENoteItemType {
  QUESTION = 'question',
  ASSET = 'asset',
  ASSET_GROUP = 'assetGroup',
  HIGHLIGHT = 'highlight',
  GRAPHIC_REQUEST = 'graphicRequest'
}

export enum CommentType {
  ASSIGNED,
  CONTRIBUTED,
  CREATED
}

interface ServiceOptions {}

interface IItemAuthNote {
  id: number;
  text: string;
  parent_note_id?: number;
  item_id: number;
  has_child: number;
  is_resolved: number;
  assigned_uid?: number;
  created_by_first_name: string;
  created_by_last_name: string;
  created_by_uid: number;
  uploads?: IItemAuthNoteFile[];
  assigned_access_level?: string;
  question_config_map?: {entryId: number, prop: string, start: number, end: number}
  suggestion_config_map?: {entryId: number, prop: string, start: number, end: number};
}

export interface IItemAuthNoteFile {
  id?: number;
  test_question_auth_note_id: number;
  file_name?: string;
  file_url: string;
}

export interface IHighlightInfo {
  test_question_version_id?: number,
  test_question_suggestion_version_id?: number,
  config_map: { entryId: number, prop: string, start: number, end: number }
}

export class Notes implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }


  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const {item_id, lang, is_resolved, getPinned, graphic_request_id} = params.query

      let item_type = params.query.itemType;
      if(!item_type) {
        item_type = ENoteItemType.QUESTION
      }

      let notes:IItemAuthNote[];
      let highlightInfo;

      //getPinned boolean was converted to string
      if(getPinned && JSON.parse(getPinned)) {
        const rawResolved = `AND is_resolved = ?`
        const rawQuery = `
        SELECT n.* FROM test_question_auth_notes n
        JOIN test_questions q
        ON q.id = ?
        WHERE n.item_id IN (
            SELECT q2.id
            FROM test_questions q2
            WHERE q2.question_set_id = q.question_set_id
            AND q2.is_archived = 0
          )
        AND n.is_pinned = 1
        AND n.is_deleted = 0
        ${is_resolved === undefined ? "" : rawResolved}
        ORDER BY created_on ASC
        `

        let params = [item_id]
        if(is_resolved !== undefined) {
          params.push(is_resolved);
        }
        const topLevelPinnedNotes = await dbRawRead(this.app, params, rawQuery);
        notes = [];
        notes= notes.concat(topLevelPinnedNotes);
        let parentIds = topLevelPinnedNotes.map( n => n.id);

        let childNotes = [];
        do{
          childNotes = await this.app.service('db/read/test-question-auth-notes').db()
          .whereIn('parent_note_id', parentIds)
          .orderBy('created_on', 'asc')
          .where('is_deleted', 0)
          .where('item_type', item_type)
          .modify(queryBuilder => {
            if(is_resolved !== undefined) {
              queryBuilder.where('is_resolved', is_resolved);
            }
          })
          notes = notes.concat(childNotes);
          parentIds = childNotes.map( (n:any) => n.id);
        } while(childNotes && childNotes.length);
      } else {

        // Get the ID of highlighted comments from another table (only consider current version Id and suggestion Id)
        const hinlightNoteInfo = await this.app.service('public/test-auth/highlight-notes').find({
          ...params, query: {test_question_id: item_id, lang}
        })
        const highlightNoteIds = hinlightNoteInfo.highlightNoteIds
        highlightInfo = hinlightNoteInfo.highlightInfo
        notes = await this.app.service('db/read/test-question-auth-notes').db()
            .where('item_id', item_id)
            .orderBy('created_on', 'asc')
            .where('is_deleted', 0)
            .modify(queryBuilder => {

              // For regular question notes, look for either all normal notes via question Id, or for highlight comments via IDs already found above
              // Otherwise disergard highlight notes
              if (item_type == ENoteItemType.QUESTION) {
                queryBuilder.where(function () {
                  this.where('item_type', ENoteItemType.QUESTION)
                    .orWhere(function () {
                      this.where('item_type', ENoteItemType.HIGHLIGHT)
                        .whereIn('id', highlightNoteIds);
                    });
                });
              } 
              // If called from the grpahic request section, look for the request ID
              else if (item_type == ENoteItemType.GRAPHIC_REQUEST) {
                queryBuilder.where('item_type', ENoteItemType.GRAPHIC_REQUEST)
                            .where('graphic_request_id', graphic_request_id);
              }
              else {
                queryBuilder.where('item_type', item_type);
              }

              //If is_resolved is unspecified, just get all the notes
              //Get highlight notes regardless of resolved status
              if(is_resolved !== undefined) {
                queryBuilder.where(function () {
                  this.where('is_resolved', is_resolved)
                  .orWhere('item_type', ENoteItemType.HIGHLIGHT)
                })
              }
            })
            .limit(1000000);

        // For notes that are highlights / comments on content - get their extra info
      }



      const noteIds = notes.map(note => note.id);

      const _files = <any> await this.app
        .service('db/read/test-question-auth-note-files')
        .find({
          query: {
            $limit: 1000000000,
            test_question_auth_note_id: {$in: noteIds},
          }
        });
      const files = _files.data || [];

      notes.forEach(note => {
        let temp: IItemAuthNoteFile[] = [];
        files.forEach((file: IItemAuthNoteFile) => {
          if (+note.id === +file.test_question_auth_note_id) {
            temp.push(file);
          }
        });
        note.uploads = temp;
      });

      return [{notes, highlightInfo}];
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {

    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {itemNote, highlightInfo, lang} = <any> data;

    const {
      text,
      overwrite_created_on, // correct order for imported comments
      parent_note_id,
      item_id,
      item_type,
      graphic_request_id,
      revision,
      has_child,
      is_pinned,
      assigned_uid = null,
    } = <any> itemNote;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    const created_by_uid = <number> userInfo.uid
    const created_by_first_name = <string> userInfo.firstName
    const created_by_last_name = <string> userInfo.lastName

    const is_resolved = 0;

    const getParentPropHelper = async (prop: string, child_note_id: number, accumulator: Set<any>, exclude?: Set<any>) : Promise<any> => {
      if(!child_note_id) {
        return;
      }
      const parent = await this.app.service('db/read/test-question-auth-notes').get(child_note_id);
      const parentVal = parent[prop];
      const parent_note_id = parent.parent_note_id;
      if(parentVal) {
        if(!exclude || !exclude.has(parentVal)) {
          accumulator.add(parentVal);
        }
        await getParentPropHelper(prop, parent_note_id, accumulator);
      }
    }

    let excludeSet = new Set<any>();

    if(parent_note_id) {

      const assignedUidSet = new Set<any>();
      await getParentPropHelper('assigned_uid', parent_note_id, assignedUidSet);

      const assignedRolesSet = new Set<any>();
      await getParentPropHelper('assigned_access_level', parent_note_id, assignedRolesSet);

      const assignedRolesArr = Array.from(assignedRolesSet);
      const assignedRoleUids = await getAccessRoleUids(this.app, item_type, item_id, assignedRolesArr);

      for(const uid of assignedRoleUids) {
        assignedUidSet.add(uid); //eliminates potential duplicates
      }

      const assigned_uids = Array.from(assignedUidSet);

      this.app.service('public/test-auth/notifications').createNotif({
        config: {
          notifType: NotifType.REPLIED_ASSIGNED_COMMENT,
          uid: userInfo.uid,
          itemId: item_id,
          noteItemType: item_type
        },
        uids: assigned_uids
      }, params)

      //Notify all the uids of those who wrote the parents' replies, minus the ones from the assigned set
      const createdUidSet = new Set<any>();
      excludeSet = assignedUidSet;
      await getParentPropHelper('created_by_uid', parent_note_id, createdUidSet, excludeSet);

      this.app.service('public/test-auth/notifications').createNotif({
        config: {
          notifType: NotifType.REPLIED_COMMENT,
          uid: userInfo.uid,
          itemId: item_id,
          noteItemType: item_type
        },
        uids: Array.from(createdUidSet)
      }, params)

      excludeSet = new Set<any>([...excludeSet, ...createdUidSet]);
    }

    this.app.service('public/test-auth/notifications').createNotif({
      config: {
        notifType: NotifType.POSTED_COMMENT,
        uid: userInfo.uid,
        itemId: item_id,
        noteItemType: item_type
      },
      forDevCoords: true,
      excludeUids: Array.from(excludeSet)
    }, params)

    const newAuthNote = await this.app
    .service('db/write/test-question-auth-notes')
    .create({
      text,
      parent_note_id,
      item_id,
      created_by_uid,
      created_by_first_name,
      created_by_last_name,
      has_child,
      is_resolved,
      assigned_uid,
      is_pinned,
      created_on: overwrite_created_on ? overwrite_created_on : dbDateNow(this.app),
      item_type,
      graphic_request_id,
      test_question_version_id: revision
    });

    // Also save in version history
    this.app.service('db/write/test-question-auth-note-versions').create({
      test_question_auth_note_id: newAuthNote.id,
      text
    })

    // If creating a highlight comment, save the linking info
    if (highlightInfo) await this.saveHighlightNoteInfo(highlightInfo, item_id, newAuthNote.id, lang, params)

    return newAuthNote;

  }

  async saveHighlightNoteInfo(highlightInfo:IHighlightNoteInfo[], item_id: number, note_id: number, lang:ILanguage, params: Params){
    // Find the current version and suggestion version, include the that matches the input
    const {test_question_suggestion_version_id, test_question_version_id} = await this.app.service('public/test-auth/questions').findLatestQuestionAndSuggIds(item_id, lang)
    
    const data = highlightInfo.map((info:IHighlightNoteInfo) => {
      const data:IHighlightNoteInfo = {
        config_map: info.config_map,
        test_question_id: item_id,
        test_question_auth_note_id: note_id
      };
      if (info.modeType === ModeType.REAL) {
        data.test_question_version_id = test_question_version_id
      } else {
        data.test_question_suggestion_version_id = test_question_suggestion_version_id
      }
      return data;
    })
    return this.app.service('public/test-auth/highlight-notes').create(data, {...params, query: {lang}})
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private resolveSingle(id: number, last_updated_by_uid: number, propName: string, val: number) {

    const data: any = {
      last_updated_by_uid,
      last_updated_on: dbDateNow(this.app)
    };

    data[propName] = val;

    if(val === 1 && propName === 'is_resolved' || propName === 'is_deleted') {
      data.assigned_uid = null;
      data.assigned_access_level = null;
    }
    return this.app
      .service('db/write/test-question-auth-notes')
      .patch(id, data);
  }


  private async resolveAll(note_id: number, uid: number, propName: string, val: number, params: Params) {

    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');

    }
    if(propName == null || val == null) {
      return;
    }

    const note: IItemAuthNote = await this.app.service('db/read/test-question-auth-notes').get(note_id);
    const itemId = note.item_id;

    if( (val === 1 && (propName === 'is_resolved' || propName === 'is_deleted') ) || (propName === 'is_shared') ) {
      const oppositeVal = (val === 1 ? 0 : 1);
      const unresolvedNotes: IItemAuthNote[] = await this.app.service('db/read/test-question-auth-notes').db()
        .where('item_id', itemId)
        .where(propName, oppositeVal)
        .orderBy('created_on', 'asc')
        .limit(1000000);
      const haveRemoved = new Map();
      haveRemoved.set(note_id, true);

      const contributedToUids:number[] = [];
      //resolve/delete the note_id and all children
      unresolvedNotes.forEach(note => {
        const parent = note.parent_note_id;
        if (parent === null) {return;}
        if (haveRemoved.get(parent)) {
          const id = note.id;
          haveRemoved.set(id, true);
          this.resolveSingle(+id, uid, propName, val);
          contributedToUids.push(note.created_by_uid);
        }
      });
      await this.resolveSingle(note_id, uid, propName, val);

      const {
        group_id,
        item_id,
        note_item_type
      } = params.query;

      const createResolveNotification = (commentType: CommentType, uids: number[]) => {
        this.app.service('public/test-auth/notifications').createNotif({
          config: {
            notifType: NotifType.RESOLVED_COMMENT,
            uid,
            itemId: item_id,
            noteItemType: note_item_type,
            commentResolveType: commentType
          },
          uids
        }, params)
      }

      if(propName === 'is_resolved') {
        createResolveNotification(CommentType.CREATED, [note.created_by_uid]);
        //Use note.assigned_uid - the value before the resolution happened and assigned_uid was nullified
        if(note.assigned_uid) {
          createResolveNotification(CommentType.ASSIGNED, [note.assigned_uid]);
        }

        if(note.assigned_access_level) {
          const roleUids = await getAccessRoleUids(this.app, note_item_type, item_id, [note.assigned_access_level])
          if(roleUids.length) {
            createResolveNotification(CommentType.ASSIGNED, roleUids);
          }
        }

        if(contributedToUids.length) {
          createResolveNotification(CommentType.CONTRIBUTED, contributedToUids);
        }
      }

    } else {
      //unresolve the note_id and all parents
      const resolvedNotes: IItemAuthNote[] = await this.app.service('db/read/test-question-auth-notes').db()
      .where('item_id', itemId)
      .where('is_resolved', 1)
      .orderBy('created_on', 'desc')
      .limit(1000000);

      let nextIdToUnresolve: number|undefined = note_id;
      resolvedNotes.forEach(note => {
        if(nextIdToUnresolve === undefined) {
          return;
        }

        if(note.id === nextIdToUnresolve) {
          this.resolveSingle(note.id, uid, propName, val);
          nextIdToUnresolve = note.parent_note_id;
        }
      })
    }
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {

    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {
      item_id,
      note_item_type
    } = params.query;

    const {
      assigned_uid,
      is_resolved,
      is_deleted,
      has_child,
      is_pinned,
      is_shared,
      assigned_access_level,
      text,
      parent_note_id,
    } = <any> data;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const last_updated_by_uid = <number> userInfo.uid;

    if(is_resolved !== undefined || is_deleted !== undefined || is_shared !== undefined) {

      let propName = "";
      let val;
      if(is_resolved !== undefined) {
        propName = 'is_resolved';
        val = is_resolved;
      } else if(is_deleted !== undefined) {
        propName = 'is_deleted';
        val = is_deleted;
      } else {
        propName = 'is_shared';
        val = is_shared;
      }

      if (id) {
        await this.resolveAll(+id, last_updated_by_uid, propName, val, params);
        return {};
      } else {
        throw new Errors.BadRequest('Invalid ID');
      }
    }

    let patchData:any = {
      last_updated_by_uid,
      last_updated_on: dbDateNow(this.app),
    };
    if (typeof assigned_uid !== 'undefined') {
      // @ts-ignore
      patchData['assigned_uid'] = assigned_uid;
      if(assigned_uid !== null) {
        this.app.service('public/test-auth/notifications').createNotif({
          config: {
            notifType: NotifType.ASSIGNED_COMMENT,
            uid: userInfo.uid,
            itemId: item_id,
            noteItemType: note_item_type
          },
          uids: [assigned_uid]
        }, params)
      }
    }
    if (typeof has_child !== 'undefined' && has_child !== null) {
      // @ts-ignore
      patchData['has_child'] = has_child;
    }

    if(is_pinned != null ) {
      patchData['is_pinned'] = is_pinned;
    }

    if(assigned_access_level !== undefined) {
      patchData['assigned_access_level'] = assigned_access_level

      if(assigned_access_level !== null) {
        if(item_id) {
          const uids = await getAccessRoleUids(this.app, note_item_type, item_id, [assigned_access_level])

          this.app.service('public/test-auth/notifications').createNotif({
            config: {
              notifType: NotifType.ASSIGNED_COMMENT,
              uid: userInfo.uid,
              itemId: item_id,
              noteItemType: note_item_type
            },
            uids
          }, params)
        }
      }
    }

    if(text && text.length) {
      patchData['text'] = text;
      patchData['is_text_edited'] = 1;
      // Save in version history
      this.app.service('db/write/test-question-auth-note-versions').create({
        test_question_auth_note_id: id,
        text
      })
    }

    if(parent_note_id != null) {
      patchData['parent_note_id'] = parent_note_id;
    }

    return this.app
      .service('db/write/test-question-auth-notes')
      .patch(id, patchData);
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
