import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { LANG_TO_LANGCODE, LANG_TO_VOICE,synthesizeSpeech } from '../../../upload/upload.listener';
import { SynthesizeSpeechInput, TextType } from 'aws-sdk/clients/polly';
import short from 'short-uuid';
const translator = short();
interface Data {
  script?:string,
  scriptType?: TextType,
  lang?:string,
  url?:string,
}

interface ServiceOptions {}

export class TextVoice implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<any> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    // defaults
    const defaultParams: any = {
      Text: data.script,
      TextType: data.scriptType as TextType || 'text',
      VoiceId: LANG_TO_VOICE[data.lang as string],
      LanguageCode: LANG_TO_LANGCODE[data.lang as string]
    }

    const defaultFilePath = [
      'voice_uploads',
      userInfo.uid,
      Date.now()
    ].join('/');

    const uuid = translator.new();
   
    // generate audio
    const audioParams: SynthesizeSpeechInput = {
      ...defaultParams,
      OutputFormat: "mp3",
    }

    const audioPath = `${defaultFilePath}/voice-${uuid}.mp3`

    const {url: audioUrl, data: audioBlob} = await synthesizeSpeech(audioParams, audioPath).catch(this.handleError);

    // generate speech marks
    const speechMarkParams: SynthesizeSpeechInput = {
      ...defaultParams,      
      OutputFormat: "json",
      SpeechMarkTypes: ['ssml', 'word', 'sentence']
    }

    const speechMarkPath = `${defaultFilePath}/speech-mark-${uuid}.marks`

    let pollySpeechMarks: {url?: string, data?: any} = {};
    if(speechMarkParams.TextType !== 'text'){
      pollySpeechMarks = await synthesizeSpeech(speechMarkParams, speechMarkPath).catch(this.handleError);
    }

    return {url: audioUrl, speechMarkUrl: pollySpeechMarks.url, speechMarkData: pollySpeechMarks.data};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private handleError = (err: {msg: string, code: string}) => {
    throw new Errors.BadRequest(err.code || err.msg)
  }
}
