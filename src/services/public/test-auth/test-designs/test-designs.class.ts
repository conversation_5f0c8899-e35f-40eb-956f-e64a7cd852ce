import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { storeInS3 } from '../../../upload/upload.listener';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { arrToMap } from '../../../../util/param-sanitization';
import { dbDateNow } from "../../../../util/db-dates";
import logger from '../../../../logger';
import axios from 'axios';
const _ = require('lodash');
import { Knex } from 'knex';
import { IStyleProfileDb } from '../../../db/schemas/style-profile.schema';

export enum STAGES {
  START = 'START - Process init',
  //---by fraction:
  ONE_ONE = '1.1/3 - Download form cache',
  ONE_TWO = '1.2/3 - Store Forms',
  ONE_THREE = '1.3/3 - Update test question register',
  ONE_FOUR = '1.4/3 - Store similarity slugs',
  //---
  TWO = '2/3 - Update module items',
  TWO_SKIPPED = 'Skipped 2/3 - Update module items',
  THREE = '3/3 - Update module stages',
  THREE_SKIPPED = 'Skipped 3/3 - Update module stages',
  FINAL = 'FINAL - publishing complete',
  STALLED = 'STALLED - Publishing Stalled'
}

const PUBLISH_TIMEOUT = 30 * 60 * 1000 // 30 min timeout in ms

interface Data {source_item_set_id:number, name:string, forms:string[], framework:string, lang:string, test_design_id?:number, source_tf_id: string, form_cache: string, style_profile_id:number}
interface ServiceOptions {}

export class TestDesigns implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {source_item_set_id} = params.query;
      //? QC4 duration 0.024 sec / 0.000022 sec
      const testDesignRecords = await dbRawRead(this.app, [source_item_set_id], `
        select td.id
          , td.name
          , td.created_on
          , td.is_public
          , count(tf.id) num_forms
          , td.is_publishing_in_progress
          , td.curr_fraction_publishing
          , td.last_stage_completed
          , td.published_on
          , td.is_error
          , td.style_profile_id
          , td.last_updated_on
          , td.full_output
          , tdso.id tdso_id
          , tdso.is_approved
          , tdso.compared_to_td_id 
          , tdso.approved_on
          , tdso.created_on started_on
          , CONCAT(approved_by_u.first_name, ' ', approved_by_u.last_name) approved_by
          , CONCAT(created_by_u.first_name, ' ', created_by_u.last_name) started_by
        from test_designs td
        left join test_forms tf
          on tf.test_design_id = td.id
        left join test_design_sign_off tdso
        	on tdso.test_design_id = td.id
        	and tdso.is_revoked = 0
        left join users approved_by_u 
        	on approved_by_u.id = tdso.approved_by_uid
        left join users created_by_u
        	on created_by_u.id = tdso.created_by_uid 
        where td.source_item_set_id = ?
         and td.is_revoked = 0
        group by td.id 
        order by td.created_on desc
        limit 50;
      `);
      const testDesignIds = testDesignRecords.map(td => td.id);
      const testDesignRef = arrToMap(testDesignRecords, 'id');
      const twtdarRecords = await dbRawRead(this.app, [testDesignIds.concat(-1)], `
        select twtar.test_design_id td_id
             , tw.id tw_id
             , tw.title
             , tw.is_qa 
             , tw.date_start
             , tw.date_end
             , twtar.id twtar_id
             , twtar.type_slug 
             , twtar.slug
             , twtar.order
             , twtar.user_metas_filter 
        from test_window_td_alloc_rules twtar 
        join test_windows tw on tw.id = twtar.test_window_id 
        where twtar.test_design_id in (?)
      `);
      const stalledAudits: number[] = []
      for (let td of testDesignRecords){ // check if td publishing has stalled
        const lastUpdated = new Date(td.last_updated_on ?? td.created_on).getTime() ?? Date.now(); // if no last updated on use created on or use current time
        const timeSinceLastUpdated = Date.now() - lastUpdated;
        if(td.is_publishing_in_progress && timeSinceLastUpdated > PUBLISH_TIMEOUT ){
          stalledAudits.push(td.id);
          // this will update the test design in db
          this.logError(td.id, td.full_output, STAGES.STALLED + ` Time before stalling: ${Math.round(timeSinceLastUpdated / 1000 / 60 / 60)} min`)
          // this ensure client recieves updated values
          td.is_publishing_in_progress = 0;
          td.is_error = 1;

          // client doesn't use the following no point in sending
          delete td.full_output;
          delete td.last_updated_on;
        }
      }
      for (let twtdar of twtdarRecords){
        const td = testDesignRef.get(twtdar.td_id);
        if (td){
          td.twtdars = td.twtdars || [];
          td.twtdars.push(twtdar);
        }
      }
      return testDesignRecords;
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async create (data: Data, params?: Params): Promise<any> {
    if(!params) {
      throw new Errors.BadRequest();
    }
    
    const {source_item_set_id, framework, name} = data;
    let fullOutput: string = STAGES.START;
    let test_design_id:number | undefined = data.test_design_id;
    const {form_cache} = data;
    const created_by_uid = await currentUid(this.app, params);
    let styleProfileDbRec: IStyleProfileDb | undefined = undefined;
    let parsedFramework = JSON.parse(framework);


    // Await test design creation for client
    if (!test_design_id){
      // fetch style profile & version only needed on first publish
      if(parsedFramework.styleProfile){
        //? QC4 duration 0.038 sec / 0.000014 sec
        styleProfileDbRec = await this.app
        .service('db/read/style-profiles')
        .db()
        .where('slug', parsedFramework.styleProfile) // doing this so it only retrieves the newest active version of the style profile could maybe look into building default or seperate versions for formatting 
        .orderBy('created_on_date', 'desc')
        .first();
      }
      if(!styleProfileDbRec || !styleProfileDbRec.config) throw new Error(`STYLE_PROFILE_MISSING: framework does not have associated style profile for item set id: ${source_item_set_id}.`) 
      const testDesignRecord = await this.app
        .service('db/write/test-designs')
        .create({
          created_by_uid,
          name,
          framework,
          source_item_set_id,
          author_group_id: null,
          is_publishing_in_progress: 1,
          last_stage_completed: STAGES.START,
          full_output: fullOutput,
          form_cache,
          style_profile_id: styleProfileDbRec?.id
        })
      test_design_id = testDesignRecord.id;
      const testDesignFormsPath = `style_profile/pregen/EQAO/test_designs/${test_design_id}`;
      // upload style-profile
      if(styleProfileDbRec && test_design_id){
        const { slug } = styleProfileDbRec
        const styleProfileSlug = slug?.split('.')?.[0] || slug;
        const styleProfilePath = `${testDesignFormsPath}/${styleProfileSlug}-v${(new Date()).valueOf()}.json`;
        await storeInS3(styleProfilePath, <string>(styleProfileDbRec.config));
        await this.app.service('db/write/test-designs').patch(test_design_id, { profile_path: styleProfilePath });
      } 
    }

    // Run publishing process asynchronously
    this.publishTestDesign(test_design_id, data, params);
    return [{success: true}];
  }

  async publishTestDesign(test_design_id: number | undefined, data: any, params: any) {
    if(!test_design_id) {
      throw new Errors.BadRequest('MISSING_TEST_DESIGN_ID')
    }

    let fullOutput: string = STAGES.START;
    const {form_cache_list, form_cache_info} = data;

    const formInfo = await axios.get(form_cache_info, {})
    if (!formInfo.data) {
      throw new Errors.BadRequest('INVALID_FORM_INFO_URL');
    }
    const { framework } = formInfo.data
    const parsedFramework = JSON.parse(framework ?? '{}');
    const {isResourcePageOnly} = parsedFramework
    try {

      const tqrQuestionsUpdatedSet: Set<number> = new Set();

      for (let i = 0; i < form_cache_list.length; i++) {
        const form_cache = form_cache_list[i]
        const currFractionPublishing = `${i+1}/${form_cache_list.length}`

        // Get form cache from S3
        const formData = await axios.get(form_cache, {});
        if(!formData.data) {
          throw new Errors.BadRequest('INVALID_FORM_CACHE_URL');
        }
        const { forms } = formData.data;
        const created_by_uid = await currentUid(this.app, params);
        let source_tf_id = data.source_tf_id;
        fullOutput = await this.updateStage(test_design_id, STAGES.ONE_ONE, fullOutput, currFractionPublishing)
  
        const formIdMap = await this.storePublishedForms(test_design_id, source_tf_id, forms, created_by_uid)
        fullOutput = await this.updateStage(test_design_id, STAGES.ONE_TWO, fullOutput, currFractionPublishing)

        await this.updateTestQuestionRegister(test_design_id, formIdMap, tqrQuestionsUpdatedSet); 
        fullOutput = await this.updateStage(test_design_id, STAGES.ONE_THREE, fullOutput, currFractionPublishing)

        await this.storeSimilaritySlugs(framework, formIdMap, created_by_uid);
        fullOutput = await this.updateStage(test_design_id, STAGES.ONE_FOUR, fullOutput, currFractionPublishing)
  
      }

      if(!isResourcePageOnly){
        await this.updateTestFormModuleItems(test_design_id, params); // derivative of TQR?
        fullOutput = await this.updateStage(test_design_id, STAGES.TWO, fullOutput)
        await this.updateTestFormModuleStages(test_design_id) // derivative of TQR?
        fullOutput = await this.updateStage(test_design_id, STAGES.THREE, fullOutput)
      } else {
        fullOutput = await this.updateStage(test_design_id, STAGES.TWO_SKIPPED, fullOutput)
        fullOutput = await this.updateStage(test_design_id, STAGES.THREE_SKIPPED, fullOutput)
      }
      


    } catch (err: any) {
      logger.error(err);
      await this.logError(test_design_id, fullOutput, err.message || "(other error)");
      return; // Exit the function
    }

    await this.finishPublishing(test_design_id, fullOutput);

  }

  getLog(stage: string, currFractionPublishing?: string, ) {
    const currDate = new Date();
    const log =  `${currFractionPublishing ? "Fraction" + currFractionPublishing + " - " : ""} stage: ${stage} - COMPLETED AT ${currDate.toString()}`
    return log;
  }

  async logError(test_design_id: number, fullOutput: string, message: string) {
    fullOutput = fullOutput + '\n' + message;
    await this.app
          .service('db/write/test-designs')
          .patch(test_design_id, {
            last_updated_on: dbDateNow(this.app),
            full_output: fullOutput,
            is_error: 1,
            is_publishing_in_progress: 0,
          })
  }

  async updateStage(test_design_id: number, stage: STAGES, fullOutput: string, currFractionPublishing?: string, ) {
    fullOutput = fullOutput + '\n' + this.getLog(stage, currFractionPublishing);
    await this.app
          .service('db/write/test-designs')
          .patch(test_design_id, {
            curr_fraction_publishing: currFractionPublishing || null,
            last_stage_completed: stage,
            last_updated_on: dbDateNow(this.app),
            full_output: fullOutput
          })
    return fullOutput;
  }

  async finishPublishing(test_design_id: number, fullOutput: string) {
    const currDate = new Date();
    fullOutput = fullOutput + '\n' + `${STAGES.FINAL} - COMPLETED AT ${currDate.toString()}`;
    await this.app
          .service('db/write/test-designs')
          .patch(test_design_id, {
            is_publishing_in_progress: 0,
            last_stage_completed: STAGES.FINAL,
            last_updated_on: dbDateNow(this.app),
            published_on: dbDateNow(this.app),
            full_output: fullOutput,
          })
  }

  async storeSimilaritySlugs(framework: string, formIdMap: Map<number, any>, created_by_uid: number) {
    const parsedFramework = JSON.parse(framework);
    const {testlets} = parsedFramework;
    if(!testlets || testlets.length <= 0) {
      return;
    }

    const recordsToInsert: {test_form_id: number, created_by_uid: number,similarity_tag:string}[] = []

    for (const [test_form_id, formObj] of formIdMap){
      const formTestlets: number[] = formObj.testletIds;
      const filteredTestlets = testlets.filter((testlet: {id: number, similaritySlug?: string}) => {
        return formTestlets?.includes(testlet.id) && testlet.similaritySlug && testlet.similaritySlug.length > 0
      })

      if (!filteredTestlets || !filteredTestlets.length) {
        continue;
      }

      filteredTestlets.forEach((testlet: any) => {
        const newRecord = {
          test_form_id,
          created_by_uid,
          similarity_tag: testlet.similaritySlug,
        }
        recordsToInsert.push(newRecord)
      })

    }

    if (!recordsToInsert.length) return;
    // Insert all records at once
    const knex:Knex = this.app.get('knexClientWrite');
    await knex('historic_form_tags').insert(recordsToInsert)
    .catch((err) => {
      throw new Errors.GeneralError('FAILED_TO_INSERT_HISTORIC_FORM_TAGS');
    });
  }

  async storePublishedForms(test_design_id:number, source_tf_id:number | string, forms:any[], created_by_uid: number){
    const formConfigs:any[] = forms.map(form => {
      const formObj = JSON.parse(form);
      return {
        formObj,
        form
      }
    });
    const formIdMap: Map<number,any> = new Map();

    // Do in chunks to avoid db overload
    for (let testformIteratorChunk of _.chunk(formConfigs, 50)) {
      await Promise.all(testformIteratorChunk.map(async (formConfig:any) => {
        const {form, formObj} = formConfig;
        const {lang, sourceFormId, foreignFormCode} = formObj;
        const newTestFormRecord = await this.app
          .service('db/write/test-forms')
          .create({
            test_design_id,
            created_by_uid,
            source_tf_id: sourceFormId || source_tf_id || null, // only defined for pre-assembled panels
            foreign_form_code: foreignFormCode || '', // used for panel mapping
            lang,
          })
        const testFormId = newTestFormRecord.id;
        formIdMap.set(testFormId, formObj);
        const timestamp = (new Date()).valueOf();
        const file_path = 'test_forms/pregen/8/'+testFormId+'/'+testFormId+'-'+timestamp+'.json';
        await storeInS3(file_path, form);
        await this.app
          .service('db/write/test-forms')
          .patch( testFormId, {file_path});
        return newTestFormRecord.id
      }))
    }

    return formIdMap

  }

  async updateTestQuestionRegister(test_design_id: number, formIdMap: Map<number,any>, tqrQuestionsUpdatedSet: Set<number>){
    await this.app
      .service('public/test-question-register/tqr-publish')
      .populateTestQuestionRegisterEQAO(+<number>test_design_id, formIdMap, tqrQuestionsUpdatedSet);
  }
  async updateTestFormModuleItems(test_design_id:number, params:any /* todo: phase this out */ ){
    const knex = this.app.service('db/write/test-form-module-items').knex;
    const newData = { test_design_id: "" + test_design_id }
    await this.app
      .service('public/support/test-form-module-items')
      .create( newData, params);
  }

  async updateTestFormModuleStages(test_design_id:number){
    const knex = this.app.service('db/write/test-form-module-items').knex;
    await knex('test_form_module_stages')
      .update({
        is_revoked: 1, revoked_on: dbDateNow(this.app)
      })
      .where({
        is_revoked: 0, 
        test_design_id: test_design_id
      });
      const test_form_module_stages = await dbRawRead(this.app, [test_design_id], `
          select test_design_id, test_panel_id, module_id as module_num, section_num as stage_num, count(0) num_items
            from test_form_module_items tfmi
          where tfmi.test_design_id = ?
            and tfmi.is_revoked = 0
        group by test_design_id, test_panel_id, module_id, section_num 
      ;`)

      const recordsToInsert = test_form_module_stages.map(test_form_module_stage => {
        return {
          test_design_id,
          test_panel_id: test_form_module_stage.test_panel_id,
          module_num: test_form_module_stage.module_num,
          stage_num: test_form_module_stage.stage_num,
          num_items: test_form_module_stage.num_items,
        }
      })

      // Insert all records at once
      await knex('test_form_module_stages').insert(recordsToInsert)
      .catch((err) => {
        throw new Errors.GeneralError('FAILED_TO_INSERT_TEST_FORM_MODULE_STAGES');
      })
  }

  async processFormTQR(test_design_id:number, lang:string, formObj:any, recentlyCreatedTQR:Map<number, boolean>){
    // extract list of questions
    const qIds:number[] = [];
    Object.keys(formObj.questionDb).map((qId:string | number) => qIds.push(+qId));
    // check which questions are already indicated in the tqr
    const existingRecords = await dbRawRead(this.app, [test_design_id, lang, qIds], `
      select tqr.question_id, tqr.id 
      from test_question_register tqr 
      where tqr.test_design_id = ?
      and tqr.lang = ?
      and tqr.question_id in (?)
    `);
    const tqrRef = arrToMap(existingRecords, 'question_id');
    const qIdsToInsert:number[] = [];
    qIds.forEach(qId => {
      if (!tqrRef.get(qId) && !recentlyCreatedTQR.get(qId)){
        qIdsToInsert.push(qId);
      }
    });
    if (qIdsToInsert.length === 0){
      return;
    }
    const paramMapRules:{item_bank_code:string, tqr_col:string, options:any}[] = await dbRawRead(this.app, [], `
      select item_bank_code
           , tqr_col
           , options
      from test_question_register_param_map tqrpm
    ;`);
    paramMapRules.forEach(paramMapRule => {
      if (paramMapRule.options){
        paramMapRule.options = JSON.parse(paramMapRule.options);
      }
      else {
        paramMapRule.options = {};
      }
    })

    const questionConfigs = await dbRawRead(this.app, [qIdsToInsert], `
      select tq.id question_id
           , tq.question_label question_label
           , tq.config 
      from test_questions tq 
      where tq.id in (?)
    `);
    await Promise.all(
      questionConfigs.map(async (record:any) => {
        const {question_id, question_label, config} = record;
        const questionConfig = JSON.parse(config);
        const meta = questionConfig.meta || {};
        const tqrPayload:any = {
          question_id, 
          question_label,
          test_design_id, 
          lang,
        }
        paramMapRules.forEach(paramMapRule => {
          let val = meta[paramMapRule.item_bank_code] || null;
          // to do: can handle more complicated rules with options
          tqrPayload[paramMapRule.tqr_col] = val;
        });
        await this.app.service('db/write/test-question-register').create(tqrPayload);
        recentlyCreatedTQR.set(question_id, true);
      })
    );
  }

  async update (id: NullableId, data: Data , params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch(id: NullableId, data: Partial<Data>, params?: Params): Promise<any> {
    throw new Errors.MethodNotAllowed()
  }
  
  async getTestDesignOutput(test_design_id:number){
    return (await dbRawReadSingle(this.app, [test_design_id],
      `
      select full_output from test_designs where id = (?);
      `
    ))?.full_output
  }

  async remove (id: NullableId, params?: Params): Promise<any> {
    if(!id){
      throw new Errors.BadRequest('MISSING_TEST_DESIGN_ID');
    }
    
    await dbRawWrite(this.app, [id], `
        UPDATE test_designs 
        SET is_revoked = 1
        WHERE id = ?
    `);

    const testForms = await dbRawRead(this.app, {id},`
        SELECT id
        FROM test_forms
        WHERE test_design_id = :id
      `);
    
    if(testForms?.length){
      dbRawWrite(this.app, [testForms.map(tf => tf.id)], `
        UPDATE test_forms 
        SET is_revoked = 1
        WHERE id in (?) 
      `)
    }

    return `Revoked ${id}`
  }
}
