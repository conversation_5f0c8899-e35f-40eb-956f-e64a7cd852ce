import { Id, NullableId, Paginated, Query, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from "../../../../util/uid";
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbEscapeString, dbEscapeNum } from './../../../../util/db-raw';
import { ChangeLogType } from '../question-change-log/types';
// import {ChangeLogType} from './../question-change-log/types'

export enum GraphicReqStatus {
  TO_DO = "TO_DO",
  IN_PROGRESS = "IN_PROGRESS",
  PENDING = "PENDING",
  COMPLETED = "COMPLETED"
}
interface Data {
  test_question_id?:number,
  lang?:string,
  created_by_uid?:number,
  title?:string,
  is_revision?:number,
  is_approved_high_contrast?:number,
  is_signed_off?:number,
  assigned_uid?:number,
  status?: GraphicReqStatus
}


interface ServiceOptions {}

export class QuestionGraphicRequests implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /** Given a list of question IDs, return how many pending graphic requests each has in each language */
  async getPendingCountByQIds(questionIds:number[]){
    if (!questionIds.length) return {};
    const pendingCountMap:any = {};
    const pendingRequests = <any[]>await this.app.service('db/write/test-question-graphic-requests').find({
      query: {
        $select: ['lang', 'test_question_id'],
        test_question_id: { $in: questionIds},
        is_deleted: { $ne: 1 },
        is_signed_off: { $ne: 1 }
      },
      paginate: false
    });

    pendingRequests.forEach(req => {
      const {lang, test_question_id} = req;
      if (!pendingCountMap[test_question_id]) pendingCountMap[test_question_id] = {}
      const reqLangs = (lang === 'both') ? ['en', 'fr'] : [lang]
      reqLangs.forEach(lang => {
        const currentCount = pendingCountMap[test_question_id][lang] || 0
        pendingCountMap[test_question_id][lang] = currentCount + 1
      })
    })

    return pendingCountMap;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {

    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    const queryParams = <Query>(<Params>params).query;
    let $limit = parseInt(queryParams.$limit) || 100;
    let $skip = parseInt(queryParams.$skip) || 0;

    const {
      req_id,
      set_group_ids,
      assigned_name,
      created_by_name,
      status,
      is_signed_off,
      set_name,
      question_label
    } = queryParams;

    const data =  await dbRawRead(this.app, { uid, req_id, set_group_ids, assigned_name, created_by_name, status, is_signed_off, question_label, set_name }, `
    select 
      SQL_CALC_FOUND_ROWS 
      req.*
      , q.question_label
      , s.id as set_id
      , s.group_id as set_group_id
      , s.single_group_id as set_single_group_id
      , s.name as set_name
      , CONCAT(u_assigned.first_name, ' ', u_assigned.last_name) as assigned_name
      , CONCAT(u_created.first_name, ' ', u_created.last_name) as created_by_name
      from 
      test_question_graphic_requests req
      join test_questions q 
        on req.test_question_id = q.id
      join temp_question_set s 
        on q.question_set_id = s.id
      join question_set_by_uid qsbu 
          on qsbu.id = s.id 
      left join users u_assigned 
        on u_assigned.id = req.assigned_uid
      left join users u_created 
        on u_created.id = req.created_by_uid
      where 
      qsbu.uid = :uid
      and req.is_deleted != 1
      and q.is_archived != 1
      and s.is_archived != 1
      ${req_id ? `and req.id like ${await dbEscapeString(req_id, true)}` : '' }
      ${set_name ? `and s.name like ${await dbEscapeString(set_name, true)}` : '' }
      ${question_label ? `and q.question_label like ${await dbEscapeString(question_label, true)}` : '' }
      ${is_signed_off ? `and req.is_signed_off = ${await dbEscapeString(is_signed_off)}` : '' }
      ${status?.length ? `and req.status in (${await dbEscapeString(status)})` : '' }
      ${set_group_ids?.length ? `and s.group_id in (${await dbEscapeString(set_group_ids)})` : '' }
      ${created_by_name ? `and CONCAT(u_created.first_name, ' ', u_created.last_name) like (${await dbEscapeString(created_by_name, true)})` : '' }
      ${assigned_name ? `and CONCAT(u_assigned.first_name, ' ', u_assigned.last_name) like (${await dbEscapeString(assigned_name, true)})` : '' }
      order by req.created_on desc
    limit ${await dbEscapeNum($skip)}, ${await dbEscapeNum($limit)}
      ;
    `);

    const total = await dbRawRead(this.app, [], 'SELECT FOUND_ROWS()');

    return <any>{
      data,
      total: total[0]['FOUND_ROWS()']
    }

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (test_question_id: Id, params?: Params): Promise<any> {
    if(!params || !params.query) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {lang} = params.query

    // Find graphic requests for a given question and language
    const requests = <any[]>await this.app.service('db/write/test-question-graphic-requests').find({
      query: {
        test_question_id,
        lang: { $in: [lang, 'both'] },
        is_deleted: { $ne: 1 }
      },
      paginate: false
    });

    // Find and include full names of request authors
    const authorUids: Set<number> = new Set();
    requests.forEach(r => authorUids.add(r.created_by_uid))
    let authorNames:any[] = []
    if (requests.length) {
      authorNames = <any[]>await this.app.service('db/write/users').find({
        query: { id: { $in: [...authorUids] }},
        paginate: false
      });
    }
    requests.forEach(r => {
      const userName = authorNames.find(n => n.id === r.created_by_uid)
      r.created_by_name = userName.first_name + " " + userName.last_name
    })

    return requests;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params || !data) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {test_question_id, lang} = data;
    const newRequest = await this.app.service('db/write/test-question-graphic-requests').create({
      ...data,
      status: GraphicReqStatus.TO_DO, // Default first status is todo
      created_by_uid: await currentUid(this.app, params)
    });
    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_CREATION,
      log_data: {
        graphic_request_id: newRequest.id,
        is_revision: newRequest.is_revision
      }
    }, params)
    return newRequest;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params || !data) throw new Errors.BadRequest('ERR_MISSING_PARAMS');

    const {is_signed_off, is_approved_high_contrast, assigned_uid, status} = data;
    const patchedRequest = await this.app.service('db/write/test-question-graphic-requests').patch(id, 
      {is_signed_off, is_approved_high_contrast, assigned_uid, status}  
    );

    const {test_question_id, lang, is_revision} = patchedRequest;
    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_STATUS,
      log_data: {
        graphic_request_id: id,
        is_revision,
        // Only one of these will be true
        is_signed_off: is_signed_off ? true : undefined,
        is_remove_signed_off: is_signed_off == 0 ? true : undefined,
        is_approved_high_contrast: is_approved_high_contrast ? true : undefined,
        is_remove_approved_high_contrast: is_approved_high_contrast == 0 ? true : undefined,
        is_unassign : assigned_uid === null ? true : undefined,
        assigned_uid: assigned_uid ? assigned_uid : undefined,
        new_status: status ? status : undefined
      }
    }, params)

    return patchedRequest;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    const {test_question_id, lang, is_revision} = params.query;
    await this.app.service('db/write/test-question-graphic-requests').patch(id, {
      is_deleted: 1
    });

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.GRAPHIC_REQUEST_DELETION,
      log_data: {
        graphic_request_id: id,
        is_revision
      }
    }, params)

    return {}
  }
}
