// Initializes the `public/test-auth/suggestion-change-log` service on path `/public/test-auth/suggestion-change-log`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { QuestionChangeLog } from './question-change-log.class';
import hooks from './question-change-log.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/question-change-log': QuestionChangeLog & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/question-change-log', new QuestionChangeLog(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/question-change-log');

  service.hooks(hooks);
}
