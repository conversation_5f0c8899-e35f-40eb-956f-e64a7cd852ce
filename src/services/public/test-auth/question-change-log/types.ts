export enum ChangeLogType {
  ITEM_CREATION = "item_creation",
  REAL_EDIT = "real_edit",
  SUGGESTION_EDIT = "suggestion_edit",
  TRACKING_CHANGES_ENTER = "tracking_changes_enter",
  TRACKING_CHANGES_LEAVE = "tracking_changes_leave",
  STAGE_TRANSITION = "stage_transition",
  STAGE_STATUS = "stage_status",
  STAGE_ASSIGNMENT = "stage_assignment",
  GRAPHIC_REQUEST_CREATION = "graphic_request_creation",
  GRAPHIC_REQUEST_DELETION = "graphic_request_deletion",
  GRAPHIC_REQUEST_STATUS = "graphic_request_status",
}

export enum LogFilters {
  filter_suggestion = "filter_suggestion",
  filter_real = "filter_real",
  filter_tracking_changes = "filter_tracking_changes",
  filter_workflow_stage = "filter_workflow_stage",
  filter_graphic_request = "filter_graphic_request",
}

// Maps which log types to return for each filter
export const filterToLogType: { [key in LogFilters]: ChangeLogType[] } = {
 [LogFilters.filter_suggestion]: [ChangeLogType.SUGGESTION_EDIT],
 [LogFilters.filter_real]: [ChangeLogType.REAL_EDIT],
 [LogFilters.filter_tracking_changes]: [ChangeLogType.TRACKING_CHANGES_ENTER, ChangeLogType.TRACKING_CHANGES_LEAVE],
 [LogFilters.filter_workflow_stage]: [ChangeLogType.STAGE_ASSIGNMENT, ChangeLogType.STAGE_STATUS, ChangeLogType.STAGE_TRANSITION],
 [LogFilters.filter_graphic_request]: [ChangeLogType.GRAPHIC_REQUEST_CREATION, ChangeLogType.GRAPHIC_REQUEST_DELETION, ChangeLogType.GRAPHIC_REQUEST_STATUS]
}
