import { Id, NullableId, <PERSON><PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { Knex } from 'knex';
import {ChangeLogType, LogFilters,  filterToLogType} from './types'
import { dbRawRead, dbRawReadSingle } from './../../../../util/db-raw';

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class QuestionChangeLog implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }


  /**
   * Pull records from a table based on an ID list
   * @param targetTableName - db table (e.g. use cases: users, test_question_versions, test_question_suggestion_versions, auth_workflow_stages)
   * @param idList - list of record IDs
   * @param fieldList - which columns to pull
   * @returns an object with IDs as keys
   */
  async findDetails(targetTableName:string, idList:any[], fieldList:string[]){
    const db:Knex = this.app.get('knexClientRead');
    const records = await db(targetTableName)
    .whereIn('id', idList)
    .select('id', ...fieldList);
    const recordObj:any = {}
    records.forEach(r => {
      recordObj[(r.id).toString()] = r;
    })
    return recordObj
  }

  async find (params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS')
    const {test_question_id, lang, offset, limit, dateRange, is_review_log } = params.query
    const trackingReview = +is_review_log;
    // Extract filters for logs
    const {filter_suggestion, filter_real, filter_tracking_changes, filter_workflow_stage, filter_graphic_request} = params.query;
    const filterParams: {[key: string]: string} = {filter_suggestion, filter_real, filter_tracking_changes, filter_workflow_stage, filter_graphic_request};
    // Make a list of all log types to be selected
    let logTypesToInclude: ChangeLogType[] = [];
    for (const filterName in filterParams) {
      if(parseInt(filterParams[filterName])) logTypesToInclude = [...logTypesToInclude, ...filterToLogType[filterName as LogFilters]]
    }

    if(!logTypesToInclude.length){
      return {
        log:[],
        fullInfo: {},
        totalCount: 0
      }
    }

    // Convert start and/or end date to UTC format to account for time zones
    let startDateUTC;
    let endDateUTC;
    if (dateRange && !trackingReview ){
      if (dateRange.start){
        startDateUTC = new Date(dateRange.start).toISOString();
      }
      if (dateRange.end){
        endDateUTC = new Date(dateRange.end).toISOString();
      }
    }
    if(trackingReview){
      startDateUTC = (await this.getLatestStageTransitionByOrder(test_question_id, lang, 3)).created_on.toISOString();
      const dateMovedBackToEditor = (await this.getLatestStageTransitionByOrder(test_question_id, lang, 2))?.created_on?.toISOString();
      if(dateMovedBackToEditor && new Date(startDateUTC) < new Date(dateMovedBackToEditor)){ // If it got put back into edit stage ater review ignore nedit stage logs
        endDateUTC = dateMovedBackToEditor;
      }
    }

    const queryParams = { 
      test_question_id,
      langs: trackingReview ? [lang] : [lang, 'both'],
      logTypesToInclude,
      startDateUTC,
      endDateUTC,
      offset: +offset || undefined,
      limit: +limit || undefined
    }

    // Start with log records - general query
    const logGeneralQuerySubstring = `
      -- 0.098 sec / 0.000017 sec qc4
      from test_question_auth_change_log
      where test_question_id = :test_question_id
      and lang in (:langs)
      and log_type in (:logTypesToInclude)
      ${(startDateUTC) ? 'and created_on >= :startDateUTC' : ''}
      ${(endDateUTC) ? 'and created_on <= :endDateUTC' : ''}
      ${trackingReview ? 'and log_data like "%is_%_sugg%"' : ''}
    `

    // Run version of general query with offset and limit (for one page of log table on the UI)
    const logRecords = await dbRawRead(this.app, queryParams, `
      select *
      ${logGeneralQuerySubstring}
      order by created_on desc
      ${+limit ? 'limit :limit' : ''}
      ${+offset ? 'offset :offset' : ''}
      ;
    `)

    // Run version of general query to get the total record count
    const totalCountRes = await dbRawReadSingle(this.app, queryParams, `
      select count(id) as total
      ${logGeneralQuerySubstring}
      ;
    `)
    const totalCount = totalCountRes?.total || 0;

    // Log records store ID references, collect them into sets
    const uidSet = new Set();
    const suggVersionIdSet = new Set();
    const questionVersionIdSet = new Set();
    const stageIdSet = new Set();

    logRecords?.forEach((log:any) => {
      uidSet.add(log.created_by_uid)

      if (log.log_type == ChangeLogType.STAGE_ASSIGNMENT) {
        const { assigned_uids, prev_assigned_uids } = JSON.parse(log.log_data);
        assigned_uids.concat(prev_assigned_uids).forEach((uid: string) => uidSet.add(uid));
      }
      else if (log.log_type == ChangeLogType.SUGGESTION_EDIT) {
        const { suggestion_version_id, prev_suggestion_version_id } = JSON.parse(log.log_data);
        if (suggestion_version_id) suggVersionIdSet.add(suggestion_version_id)
        if (prev_suggestion_version_id) suggVersionIdSet.add(prev_suggestion_version_id)
      }
      else if (log.log_type == ChangeLogType.REAL_EDIT) {
        const { test_question_version_id, prev_test_question_version_id } = JSON.parse(log.log_data);
        if (test_question_version_id) questionVersionIdSet.add(test_question_version_id)
        if (prev_test_question_version_id) questionVersionIdSet.add(prev_test_question_version_id)
      }
      else if (log.log_type == ChangeLogType.GRAPHIC_REQUEST_STATUS) {
        const { new_assigned_uid } = JSON.parse(log.log_data);
        if (new_assigned_uid) uidSet.add(new_assigned_uid)
      }

      if ([ChangeLogType.STAGE_ASSIGNMENT, ChangeLogType.STAGE_STATUS, ChangeLogType.STAGE_TRANSITION].includes(log.log_type)) {
        const { stage_id } = JSON.parse(log.log_data);
        if (stage_id) stageIdSet.add(stage_id)
      }
    })

    // Find required data from other tables by IDs
    let userInfo;
    let suggVersionInfo;
    let questionVersionInfo;
    let stageInfo;
    if (uidSet.size) userInfo = await this.findDetails('users', [...uidSet], ['first_name', 'last_name', 'contact_email'])
    if (suggVersionIdSet.size) suggVersionInfo = await this.findDetails('test_question_suggestion_versions', [...suggVersionIdSet], ['config'])
    if (questionVersionIdSet.size) questionVersionInfo = await this.findDetails('test_question_versions', [...questionVersionIdSet], ['config'])
    if (stageIdSet.size) stageInfo = await this.findDetails('auth_workflow_stages', [...stageIdSet], ['stage_order'])

    return {
      log: logRecords,
      fullInfo: {
        userInfo,
        suggVersionInfo,
        questionVersionInfo,
        stageInfo
      },
      totalCount
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Some logs record a change from one version to another (of the question or suggestion), find what was the last logged version to include it in the new log
   * @param test_question_id 
   * @param lang 
   * @param searchedLogTypes - which log types to consider in log history
   * @param versionProp - Value to return from `log_data` object, e.g. `test_question_version_id`, `suggestion_version_id`
   * @returns 
   */
  async findPrevVersionInLog (test_question_id:number, lang:string, searchedLogTypes: ChangeLogType[], versionProp: string) {

    const queryParams = {test_question_id, langs: [lang, 'both'], searchedLogTypes}
    const prevRealRecords = await dbRawReadSingle(this.app, queryParams, `
      select log_data
      from test_question_auth_change_log
      where test_question_id = :test_question_id
      and lang in (:langs)
      and log_type in (:searchedLogTypes)
      order by created_on desc;
    `);

    if (prevRealRecords) {
      const logData = JSON.parse(prevRealRecords.log_data)
      return logData?.[versionProp]
    }
  }

  /**
   * Function used to retrieve the latest log for a given stage transtion  (i.e what was the most recent transition to a given stage)
   * @param question_id 
   * @param lang 
   * @param stage_order 
   * @returns 
   */
  async getLatestStageTransitionByOrder(question_id:number, lang:string, stage_order:number){
    const stage_id = await this.app.service('public/test-auth/question-workflow-stages').getStageIdByOrder(stage_order);
    return await dbRawReadSingle(this.app, {question_id, stage_id, lang}, 
      `
      -- 0.098 sec / 0.000028 sec QC4
      SELECT * 
      FROM test_question_auth_change_log 
      WHERE test_question_id = :question_id 
        AND log_type = 'stage_transition'
        AND log_data = '{"stage_id"::stage_id}' 
        AND lang = :lang
      ORDER BY created_on DESC;
      `
    )
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) throw new Errors.BadRequest('MISSING_PARAMS');

    const {log_data, log_type, lang} = data;

    // When recording a save of question or suggestion, find the previous version and add it to the data to be saved
    if (log_type === ChangeLogType.REAL_EDIT) {
      const prevVersionId = await this.findPrevVersionInLog(data.test_question_id, lang, [ChangeLogType.ITEM_CREATION, ChangeLogType.REAL_EDIT], 'test_question_version_id')
      if (prevVersionId) log_data.prev_test_question_version_id = prevVersionId
    }
    else if (log_type === ChangeLogType.SUGGESTION_EDIT) {
      const prevVersionId = await this.findPrevVersionInLog(data.test_question_id, lang, [ChangeLogType.TRACKING_CHANGES_ENTER, ChangeLogType.SUGGESTION_EDIT], 'suggestion_version_id')
      if (prevVersionId) log_data.prev_suggestion_version_id = prevVersionId
    }

    const current_uid = await currentUid(this.app, params);
    return this.app
    .service('db/write/test-question-auth-change-log')
    .create({
      ...data,
      log_data: JSON.stringify(data.log_data),
      created_by_uid: current_uid,
    });

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
