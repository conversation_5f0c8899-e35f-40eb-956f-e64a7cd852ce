import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Knex } from 'knex';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import {ChangeLogType} from './../question-change-log/types'
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  [key: string]: any,
}

enum WORKFLOW_STAGE {
  CREATION = 1,
  EDIT = 2,
  ASSESSMENT_REVIEW = 3,
  FINAL_REVIEW = 4,
  APPROVED = 5,
}

interface ServiceOptions {}

export class QuestionWorkflowStages implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async getStageIdByOrder(stage_order:number){
    const stageRecords = <any[]>await this.app
    .service('db/read/auth-workflow-stages')
    .find({query: {stage_order}, paginate: false});
    return stageRecords[0]?.id;
  }

  /** Return full info about the current stage the item is in */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (test_question_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !params.query.lang) throw new Errors.BadRequest("MISSING_PARAMS_REQ");
    const {lang} = params.query;

    // Find and return the current stage data of the item (or none)
    const stageRecord = await dbRawReadSingle(this.app, {lang, test_question_id}, `
      select status.*, stage.stage_order
      from test_question_workflow_status as status
      join auth_workflow_stages as stage on stage.id = status.stage_id
      where status.lang = :lang
      and status.test_question_id = :test_question_id
    `)

    if (!stageRecord) return {}

    // If a stage exists, find any users assigned to it
    const assignedRecords = await dbRawRead(this.app, { 
      stage_id: stageRecord.stage_id,
      lang, 
      test_question_id
    }, `
      select a.uid
      from test_question_workflow_assignments as a
      where a.test_question_id = :test_question_id
      and a.lang = :lang
      and a.stage_id = :stage_id
      and is_revoked = 0;
    `);
    const assignedUids = assignedRecords.map(r => r.uid);

    // Search the log to count how many times the question in lang entered the edit stage
    const enterEditRecordCount = await dbRawReadSingle(this.app, {
      test_question_id,
      lang,
      edit_stage_log_data: `{"stage_id":${WORKFLOW_STAGE.EDIT}}`,
      log_type: ChangeLogType.STAGE_TRANSITION,
    }, `
      select count(id) as total
      from test_question_auth_change_log as log
      where test_question_id = :test_question_id
      and lang = :lang
      and log_type = :log_type
      and log_data = :edit_stage_log_data
    `);

    const enterReviewRecordCount = await dbRawReadSingle(this.app, {
      test_question_id,
      lang,
      edit_stage_log_data: `{"stage_id":${WORKFLOW_STAGE.ASSESSMENT_REVIEW}}`,
      log_type: ChangeLogType.STAGE_TRANSITION,
    }, `
      select count(id) as total
      from test_question_auth_change_log as log
      where test_question_id = :test_question_id
      and lang = :lang
      and log_type = :log_type
      and log_data = :edit_stage_log_data
    `);
    const numEnterEdit = enterEditRecordCount.total
    const numEnterReview = enterReviewRecordCount.total

    return {...stageRecord, assignedUids, numEnterEdit, numEnterReview}
  }

  /** To move the item into a new stage */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {

    if (!params || !params.query) throw new Errors.BadRequest("MISSING_PARAMS_REQ");

    const {test_question_id, lang} = params.query
    const {stage_order} = data

    //Find any existing records and remove them - should only store the one current stage
    const stageStatusRecords = await dbRawRead(this.app, {test_question_id, lang}, `
      select id 
      from test_question_workflow_status
      where test_question_id = :test_question_id
      and lang = :lang;
    `)
    await Promise.all(stageStatusRecords.map(r => this.app.service('db/write/test-question-workflow-status').remove(r.id)));

    // Find the ID of the stage
    const stage_id = await this.getStageIdByOrder(stage_order);


    // Create the stage record
    const stageRecord = await this.app
    .service('db/write/test-question-workflow-status')
    .create({
      test_question_id,
      lang,
      stage_id, 
    })

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.STAGE_TRANSITION,
      log_data: {
        stage_id,
      }
    }, params)
    
    // Find any users assigned to the stage (if returning to the stage, the previous assignments are still stored)
    const assignedRecords = await dbRawRead(this.app, {
      test_question_id,
      lang,
      stage_id: stageRecord.stage_id
    }, `
      select uid 
      from test_question_workflow_assignments
      where test_question_id = :test_question_id
      and lang = :lang
      and stage_id = :stage_id
      and is_revoked = 0;
    `);
    const assignedUids = assignedRecords.map(r => r.uid)
    
    return {...stageRecord, assignedUids}

  }



  /** Used for multi-question asssignment of stage to users */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest("MISSING_PARAMS_REQ");
    const {lang, stage_order, remove_assignees} = params.query
    const {test_question_ids, assigned_uids} = data;
    const stage_id = await this.getStageIdByOrder(stage_order);
    let questionAssignments;
    if(+remove_assignees){
      questionAssignments = test_question_ids.map((id:number) =>{
        return this.removeUsersAssignmentFromQuestion(id, lang, stage_id, params)
      })
    } else {
      questionAssignments = test_question_ids.map((id: number) => {
        return this.assignQuestionToUsers(id, lang, assigned_uids, stage_id, params)
      });
    }
    return Promise.all(questionAssignments)
  }

  /** Within an existing stage, update assignments or status settings */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest("MISSING_PARAMS_REQ");

    const test_question_id = id;
    const {lang, stage_order} = params.query
    const {assigned_uids, is_signed_off, is_done_editing, is_changes_required, is_edit_review} = data;
    const stage_id = await this.getStageIdByOrder(stage_order);

    // If called to update assignments
    if (test_question_id && assigned_uids && Array.isArray(assigned_uids)) {

      await this.assignQuestionToUsers(+test_question_id, lang, assigned_uids, stage_id, params)
      
    }
    // Otherwise update the stage status settings  
    else {
      //Find existing record and update with changes (only one will be passed at a time)
      await this.app.service('db/write/test-question-workflow-status')
      .db()
      .where({ test_question_id, lang, stage_id })
      .update({
        is_signed_off,
        is_done_editing,
        is_edit_review,
        is_changes_required
      });

      // Make a record in the log
      this.app.service('public/test-auth/question-change-log').create({
        test_question_id,
        lang,
        log_type: ChangeLogType.STAGE_STATUS,
        log_data: {
          stage_id,
          // Only one of these will be true
          is_signed_off: is_signed_off ? true : undefined,
          is_remove_signed_off: is_signed_off == 0 ? true : undefined,
          is_done_editing: is_done_editing ? true : undefined,
          is_remove_done_editing: is_done_editing == 0 ? true : undefined,
          is_edit_review: is_edit_review ? true : undefined,
          is_remove_edit_review: is_edit_review == 0 ? true : undefined,
          is_changes_required: is_changes_required ? true : undefined,
          is_remove_changes_required: is_changes_required == 0 ? true : undefined,
        }
      }, params)      
    }

    return {};
  }

/**
 * Assign/reassign the question to one or more users
 * @param test_question_id - ID of the question
 * @param lang - "en" or "fr" for which version fo the question to assign
 * @param assigned_uids - List of UIDs of new assignees
 * @param stage_id - ID of the stage to be assigned
 * @param params - Params from the initial request
 */
  async assignQuestionToUsers(test_question_id: number, lang: string, assigned_uids: number[], stage_id:number, params: Params){
    const current_uid = await currentUid(this.app, params);
    // Find which users are already assigned
    const existingAssignmentRecords = <any[]>await this.app
    .service('db/read/test-question-workflow-assignments')
    .find({query: {test_question_id, lang, stage_id }, paginate: false});
    const existingAssignedUids = existingAssignmentRecords.map(record => record.uid)

    // What assignments exist and need to be unrevoked
    const assignmentsToUnrevoke = existingAssignmentRecords.filter((r)=> r.is_revoked && assigned_uids.includes(r.uid));
    const uidsToUnrevoke = assignmentsToUnrevoke.map(r => r.uid);

    // What users need to be newly assigned
    const uidsToAssign = assigned_uids.filter(uid => !existingAssignedUids.includes(uid) && !uidsToUnrevoke.includes(uid))
    // What existing assignments need to be removed
    const uidsToUnassign = existingAssignedUids.filter(uid => !assigned_uids.includes(uid) && !uidsToUnrevoke.includes(uid))
    const assignmentRecordsToRevoke = existingAssignmentRecords.filter(record => uidsToUnassign.includes(record.uid) && !record.is_revoked)

    // Execute assignment creation and removal
    const promises: Promise<any>[] = [];
    assignmentRecordsToRevoke.forEach(r => {
      promises.push(this.app.service('db/write/test-question-workflow-assignments').patch(r.id, { 
        is_revoked: 1, 
        revoked_on: dbDateNow(this.app)
      }))
    })
    assignmentsToUnrevoke.forEach(r => {
      promises.push(this.app.service('db/write/test-question-workflow-assignments').patch(r.id, { 
        is_revoked: 0, 
        revoked_on: null
      }))
    })
    uidsToAssign.forEach(uid => {
      promises.push(
        this.app.service('db/write/test-question-workflow-assignments')
        .create({
          test_question_id, lang, stage_id,
          uid,
          created_by_uid: current_uid,
        })
      )
    })
    await Promise.all(promises);

    // Make a record in the log
    this.app.service('public/test-auth/question-change-log').create({
      test_question_id,
      lang,
      log_type: ChangeLogType.STAGE_ASSIGNMENT,
      log_data: {
        stage_id,
        assigned_uids,
        prev_assigned_uids: existingAssignedUids,
      }
    }, params)
  }

/**
 * Remove all assigned user from the given question and stage
 * @param test_question_id - ID of the question
 * @param lang - "en" or "fr" for which version fo the question to assign
 * @param params - Params from the initial request
 */
  async removeUsersAssignmentFromQuestion(test_question_id: number, lang: string, stage_id:number, params: Params){
    const existingAssignmentRecords = <any[]>await this.app
      .service('db/read/test-question-workflow-assignments')
      .find({query: {test_question_id, lang, stage_id }, paginate: false});
    const existingAssignedUids = existingAssignmentRecords.map(record => record.uid);
    if(existingAssignmentRecords.length){
        await dbRawWrite(this.app, [dbDateNow(this.app), existingAssignmentRecords.map(record => record.id)], `
          -- 0.121 sec Mirror
          UPDATE test_question_workflow_assignments
          SET 
          is_revoked = 1,
          revoked_on = ?
          WHERE id in (?)
        `);
        this.app.service('public/test-auth/question-change-log').create({
          test_question_id,
          lang,
          log_type: ChangeLogType.STAGE_ASSIGNMENT,
          log_data: {
            stage_id,
            assigned_uids: [],
            prev_assigned_uids: existingAssignedUids,
          }
        }, params)
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  /** From a list of question IDs, find their stage orders, assignees of the current stages, and details of assignees */
  async getStageInfoByQIds(questionIds:number[]){
    if (!questionIds.length) return {};
    const stageInfoMap:any = {};

    // Find any current stage records of the given question IDs
    const db:Knex = this.app.get('knexClientRead');
    const stageRecords = await dbRawRead(this.app, {questionIds}, `
      select status.test_question_id
      , status.lang
      , status.stage_id
      , status.is_signed_off
      , status.is_done_editing
      , status.is_changes_required
      , stage.stage_order
      from test_question_workflow_status as status
      left join auth_workflow_stages as stage
        on stage.id = status.stage_id
      where status.test_question_id in (:questionIds);
    `);
    // Search the log to count how many times the questions entered the edit stage in each lang
    const enterEditRecords = await dbRawRead(this.app, {
      questionIds,
      log_type: ChangeLogType.STAGE_TRANSITION,
      log_data: `{"stage_id":${WORKFLOW_STAGE.EDIT}}`
    }, `
      select test_question_id
      , lang
      , count(*) as num_enter_edit
      from test_question_auth_change_log as log
      where log.test_question_id in (:questionIds)
      and log_type = :log_type
      and log_data = :log_data
      group by test_question_id, lang;
    `);

    // Rearrange into an object by question IDs and languages
    stageRecords.forEach(stageRecord => {
      const {lang, test_question_id, stage_order, is_signed_off, is_done_editing, is_changes_required} = stageRecord;
      if (!stageInfoMap[test_question_id]) stageInfoMap[test_question_id] = {}
      // Number of times entered edit stage - if not counted by logs, then 0
      const numEnterEdit = enterEditRecords.find(r => r.test_question_id == test_question_id && r.lang == lang)?.num_enter_edit || 0
      stageInfoMap[test_question_id][lang] = {
        order: stage_order,
        numEnterEdit,
        // Mutually exclusive is_done_editing OR is_changes_required required for the Item in Edit stage, other stages use is_signed_off
        isStageWorkCompleted: !!is_signed_off || (!!is_done_editing || !!is_changes_required)
      }
    })

    const assignmentRecords:any[] = [];

    // Slower than creating a string but that opens up to sql injection as the previous method wasn't working
    for(let record of stageRecords){
      const {test_question_id, lang, stage_id} = record;
      const assignment = await dbRawRead(this.app, {test_question_id, lang, stage_id}, `
        -- 0.111 sec / 0.000032 sec on QC6
        select a.test_question_id, a.lang, a.uid
        from test_question_workflow_assignments as a
        where 
          a.test_question_id = :test_question_id 
          and a.lang = :lang 
          and a.stage_id = :stage_id
          and is_revoked = 0
      `);
      assignmentRecords.push(...assignment)
    }

    // Put list of assigned UIDs into the object
    assignmentRecords.forEach(assignmentRecord => {
      const {test_question_id, lang, uid} = assignmentRecord;
      if (!stageInfoMap[test_question_id][lang].assigneeUids) stageInfoMap[test_question_id][lang].assigneeUids = []
      stageInfoMap[test_question_id][lang].assigneeUids.push(uid)
    })

    // Find the details of any assigned users
    const assignmentUids: Set<number> = new Set();
    assignmentRecords.forEach(a => assignmentUids.add(a.uid))
    let stageUserDetails:any[] = []
    if (assignmentRecords.length) {
      stageUserDetails = await dbRawRead(this.app, {assignmentUids: Array.from(assignmentUids)}, `
        select u.id as uid, u.first_name, u.last_name, u.contact_email
        from users as u
        where u.id in (:assignmentUids)
      `);
    }
    // Rearrange user details into an object by UIDs
    const userDetailMap:any = {};
    stageUserDetails.forEach(u => {
      const {contact_email, first_name, last_name, uid} = u;
      userDetailMap[uid] = {uid, contact_email, last_name, first_name}
    })

    return {stageInfoMap, userDetailMap};
  }
  /**
  * Function that to maps questions to their assigned user for a specific workflow stage
  * @param questionIds question being mapped
  * @param stageId workflow stage being addressed
  * @returns {assignedRecordMap, userDetailMap} A map of question and their assigned users, and a map of the users and their details
  */
  async getAssignedStageInfoByQIds(questionIds:number[], stageId:number){
    if (!questionIds.length) return {};
    const assignedRecords:any[] = await dbRawRead(this.app, {questionIds, stageId}, `
      -- 0.105 sec / 0.000022 sec in QC6
      select a.test_question_id, a.lang, a.uid
      from test_question_workflow_assignments as a
      where 
        a.test_question_id in (:questionIds)
        and a.stage_id = :stageId
        and is_revoked = 0
    `);
    const assignedRecordMap:{[test_question_id:number]: {[lang:string] : number[]}} = {};
    const assignmentUids: Set<number> = new Set();
    assignedRecords?.forEach(record =>{
      const {test_question_id, lang, uid} = record;
      if(!assignedRecordMap[test_question_id]) assignedRecordMap[test_question_id] = {};
      if(!assignedRecordMap[test_question_id][lang]) assignedRecordMap[test_question_id][lang] = [];
      assignedRecordMap[test_question_id][lang].push(uid);
      assignmentUids.add(uid);
    });
    const userDetailMap:any = {};
    if(assignmentUids.size){
      const stageUserDetails = await dbRawRead(this.app, { assignmentUids: Array.from(assignmentUids) }, `
      -- 0.106 sec / 0.000013 sec QC6  
      select u.id as uid, u.first_name, u.last_name, u.contact_email
        from users as u
        where u.id in (:assignmentUids)
      `);
  
      stageUserDetails.forEach(u => {
        const {contact_email, first_name, last_name, uid} = u;
        userDetailMap[uid] = {uid, contact_email, last_name, first_name}
      })
    }
    return {assignedRecordMap, userDetailMap};
  }
}
