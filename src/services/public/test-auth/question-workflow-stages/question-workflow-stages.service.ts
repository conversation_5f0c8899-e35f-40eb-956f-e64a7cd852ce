// Initializes the `public/test-auth/question-workflow-stages` service on path `/public/test-auth/question-workflow-stages`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { QuestionWorkflowStages } from './question-workflow-stages.class';
import hooks from './question-workflow-stages.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-auth/question-workflow-stages': QuestionWorkflowStages & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-auth/question-workflow-stages', new QuestionWorkflowStages(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-auth/question-workflow-stages');

  service.hooks(hooks);
}
