import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { AxiosInstance } from 'axios';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';
import { Application } from '../../../../../declarations';
import { currentUid } from '../../../../../util/uid';
import { dbDateNow } from '../../../../../util/db-dates';
import logger from '../../../../../logger';
interface Data {
  [key:string]:any
}

interface ServiceOptions {}

interface IQrContents {
  oen?: number,
  sasn?: string,
  question_slugs: string[],
  uid: number,
  questions?: any[]
}

export interface IResQrData {
  [uid: string]: IQrContents
}

export const SSSlugMapToSessionTitle: any = {
  lang_session_a: 'SESSION_A',
  lang_session_b: 'SESSION_B',
  lang_session_c: 'SESSION_C',
}

export class QrcodeContents implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = app.get('eqaoScanningService');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }


  async getStudentSubmittedSessions(params:Params, testSessionId:Id, schl_class_group_id:Id, school_class_id:Id, fromSchoolAdminUpload = false, isOverrideFlaggedOnly: boolean = false){
    const scanClassInfo = <any> await this.app.service('public/educator/class-scan-info').getClassScanInfo(school_class_id, {...params, query: {
        schl_class_group_id,
        fromSchoolAdminUpload,
        testSessionId
      }
    });
    //A list of sessions for which scans apply, like ["lang_session_a", "lang_session_b", "lang_session_c"]
    const scanSessionSlugs:string[] = scanClassInfo.testSlugs
    
    let allSessionData
    if(fromSchoolAdminUpload){
      const created_by_uid = undefined // no need to pass in created_by_uid since its not used in the "ensureSessionInit"
      const isForced = false  // mean student need to be in an active class
      const loadFromRedis = false //load from db directly and not from redis due to the session maybe closed and can't be loaded into redis

      //ensureSessionInit return same thing as getTestSession but it can load from db diretly when setting "loadFromRedis" to "false"
      allSessionData = <any> await this.app.service('public/educator/session').ensureSessionInit(testSessionId, +school_class_id, created_by_uid, isForced, loadFromRedis)
    }else{
      allSessionData = <any> await this.app.service('public/educator/session').getTestSession(testSessionId, {...params,
        query: {
          school_class_id,
          school_class_group_id:schl_class_group_id
        }
      });
    }

    const studentSubmittedSubsessions: { [key: string]: any } = {}
    // go over allSessionData.subSessions.studentStates[].subSessions. Find ones that are submitted and find a scan slug by .tass_id 
    // scan slug found by scanClassInfo.studentScanInfoMap[student_uid].scanningQues[] by matching tass_id
    // then, add to studentSubmittedSubsessions[student_uid].submittedSubsessionSlugs
    for(const uid in allSessionData.subSessions.studentStates) {
      const studentState = allSessionData.subSessions.studentStates[uid]
      if (!studentSubmittedSubsessions[uid]){
        studentSubmittedSubsessions[uid] = {uid, submittedSubsessionSlugs: [], noOverrideSessionSlugs: []}
      }
      const studentScanInfoMap = scanClassInfo.studentScanInfoMap[uid]
      if (!studentScanInfoMap) continue;

      for (let subSession of studentState.subSessions) {
        if (!subSession.is_submitted) continue;

        const tass_id = subSession.tass_id;
        const sessionKey = Object.keys(studentScanInfoMap.scanningQues).find((sessionKey: any) => studentScanInfoMap.scanningQues[sessionKey]?.tassId == tass_id)
        if(sessionKey){
          studentSubmittedSubsessions[uid].submittedSubsessionSlugs.push(sessionKey)
          // If the upload doesn't allow to override non-flagged existing uploads, then 
          if (isOverrideFlaggedOnly){
            const hasScan = studentScanInfoMap.scanningQues[sessionKey].hasTasr
            const canOverrideScan = studentScanInfoMap.scanningQues[sessionKey].isNeedsReupload
            if (hasScan && !canOverrideScan){
              studentSubmittedSubsessions[uid].noOverrideSessionSlugs.push(sessionKey)
            }
          }
        }
      }
    }
    /*
    Only scan-applicable subsessions submitted by students
    Final format looks like... (students with nothing completed are excluded)
    {"1925902":{"uid":"1925902","submittedSubsessionSlugs":["SESSION_A"]}}
     */
    return studentSubmittedSubsessions;
  }

  /**
   * Pass the file intended for the single scan upload through the scanning service to determine the student UID and session slug of the scan
   * @returns Output data of the scanning service endpoint
   */
  async checkSingleScan(data: Data){
    const {scanFileUrl, uid, sessionSlug} = <any> data;
    
    return this.eqaoScanningService.post(`/split-and-scan-single`, { singleScanFile: scanFileUrl })
    .then(res => {
      const qrData = res.data
      // Compare QR code output against intended - return relevant error
      if (!Object.keys(qrData).length){
        return Promise.reject(new Errors.BadRequest("NO_QR_DETECTED"));
      }
      else if(!qrData[uid]){
        const errData = {uids: Object.keys(qrData)}
        return Promise.reject(new Errors.BadRequest("STUDENT_MISMATCH", errData));
      } else if (qrData[uid].question_slug !== sessionSlug){
        const errData = {qrSessionSlug: qrData[uid].question_slug}
        return Promise.reject(new Errors.BadRequest("STUDENT_MATCH_SESSION_MISMATCH", errData));
      } else {
        return { isMatch: true }
      }
    })
    .catch(err => {
      if (err.response?.data == "ERR_TOO_MANY_SCANS"){
        throw new Errors.BadRequest("ERR_TOO_MANY_SCANS")
      }
      else throw err;
    })
  }

  /**
   * Attempts to find a cross-matched student UID when the original scan UID is invalid.
   * This handles cases where duplicate student records exist with the same StudentOEN.
   * 
   * @param studentScanData - The original scan data from the scanning service
   * @param studentSubmittedSubsessions - Map of valid student UIDs with their submitted sessions
   * @returns Direct replacement object for studentQrData[scanIndex] or null if no match
   */
  async findCrossMatchedStudentUid(studentScanData: any, studentSubmittedSubsessions: any, isSasn: boolean): Promise<any | null> {
    const invalidStudentUid = Object.keys(studentScanData)[0];
    const originalScanData = studentScanData[invalidStudentUid];

    const oenKey = isSasn ? 'SASN' : 'StudentOEN';
    
    // Find all UIDs with the same StudentOEN as the invalid UID, excluding the invalid UID itself
    const matchingOenRecords = await dbRawRead(this.app, [invalidStudentUid, oenKey], `
      SELECT DISTINCT um_oen_crossmatch.uid 
      FROM user_metas um_oen
      JOIN user_metas um_oen_crossmatch
        ON um_oen.value = um_oen_crossmatch.value 
       AND um_oen.uid != um_oen_crossmatch.uid
       AND um_oen_crossmatch.key = um_oen.key
       AND um_oen_crossmatch.key_namespace = um_oen.key_namespace
      WHERE um_oen.uid = ? 
        AND um_oen.key_namespace = 'eqao_sdc' 
        AND um_oen.key = ?
    `).catch(() => []);

    // Check each matching UID for validation
    for (const record of matchingOenRecords) {
      const crossMatchedUid = record.uid;
      
      // Check if cross-matched UID exists in the session
      if (studentSubmittedSubsessions[crossMatchedUid]) {
        const questionSlug = originalScanData.question_slug;
        const submittedSlugs = studentSubmittedSubsessions[crossMatchedUid].submittedSubsessionSlugs || [];
        const noOverrideSlugs = studentSubmittedSubsessions[crossMatchedUid].noOverrideSessionSlugs || [];

        // Calculate validation flags to behave the same as the scanning service
        const is_session_invalid = !submittedSlugs.includes(questionSlug);
        const is_override_invalid = noOverrideSlugs.includes(questionSlug);

        logger.info('SCAN_OEN_CROSSMATCH', { 
          invalidStudentUid, 
          crossMatchedUid,
          is_session_invalid,
          is_override_invalid
        });
        
        return {
          [crossMatchedUid]: {
            ...originalScanData,
            uid: crossMatchedUid,
          },
          is_student_invalid: false,
          is_session_invalid,
          is_override_invalid
        };
      }
    }

    return null;
  }

  /**
   * Pass file through QR scanning service
   * Return data on the pages
   */
  async processBulkScan (params: Params, data:Data) {

    const {bulkFileUrl, is_override_flagged_only, isSasn, testSessionId, schl_class_group_id, school_class_id, studentScanInfoMap} = <any> data;

    const isSasnBool = parseInt(isSasn) === 1;

    //Obtain list of sessions that students by UID have submitted
    //Pass this to QR service so that all other pages are ignored and not uploaded as files
    const studentSubmittedSubsessions =  await this.getStudentSubmittedSessions(params, testSessionId, schl_class_group_id, school_class_id, false, is_override_flagged_only);
    
    // validate and read qr code contents scanned
    const scanApiData = {
      bulkFile: bulkFileUrl,
      isSasn: isSasnBool,
      validSlugsByStudent: studentSubmittedSubsessions,
    }

    const res = await this.eqaoScanningService.post(`/split-and-scan`, scanApiData);
    const studentQrData = res.data;

    const resQrData: IResQrData = {};
    const num_scans_with_errors = []
    const num_scans_invalid = []
    const num_scans_override = []
    const num_input_pages = studentQrData.length

    for (let [scanIndex, student] of studentQrData.entries()) {
      // If the student is invalid, try to find a cross-matched student UID by StudentOEN
      if(studentQrData[scanIndex].is_student_invalid) {
        const crossMatchedResult = await this.findCrossMatchedStudentUid(student, studentSubmittedSubsessions, isSasn);

        if(crossMatchedResult) {
          studentQrData[scanIndex] = crossMatchedResult;
        }
      }
      
      student = studentQrData[scanIndex];
      const studentKeys = Object.keys(student);
      const {is_student_invalid, is_session_invalid, is_override_invalid} = studentQrData[scanIndex];

      // Detect invalid QR scans as determined in scanning service
      if (is_student_invalid || is_session_invalid || is_override_invalid) {
        num_scans_invalid.push({
          num: scanIndex+1,
          is_student_invalid,
          is_session_invalid,
          is_override_invalid
        })
      }
      // Otherwise it will not have is_student_invalid or is_session_invalid as keys and all keys will be student uids
      else if (studentKeys.length > 0) {
        const studentKey = studentKeys[0];
        if (!(Object.keys(resQrData).includes(studentKey))) {
          resQrData[studentKey] = {uid: student[studentKey].uid, question_slugs: [], questions: []};
          const oen = (await dbRawRead(this.app, [studentKey], `
            select value as oen from user_metas um where um.uid = ? and um.key = 'StudentOEN'
          `))[0]?.oen;
          if (isSasn) {
            resQrData[studentKey].sasn = oen
          } else {
            resQrData[studentKey].oen = oen;
          }
          resQrData[studentKey].uid = student[studentKey].uid;
        }
        resQrData[studentKey].question_slugs.push(student[studentKey].question_slug);
        resQrData[studentKey].questions?.push({ slug: student[studentKey].question_slug, filePath: student[studentKey].filePath, filePathCropped: student[studentKey].filePathCropped});
      
        // Detect overwriting existing scans
        // @ts-ignore
        const studentData = studentScanInfoMap[student[studentKey].uid].scanningQues
        const sessionQuestion = studentData[student[studentKey].question_slug];
        const quesId = Object.keys(sessionQuestion)[0];
        if (sessionQuestion[quesId].responses.length > 0) {
          num_scans_override.push(scanIndex+1)
        }
      } 
      // If no keys, the QR could not be processed
      else {
        num_scans_with_errors.push(scanIndex+1)
      }
    }

    return {
      scanData: resQrData,
      num_scans_override,
      num_scans_with_errors,
      num_scans_invalid,
      num_input_pages
    };

  }

  /** With QR scanning, either check that a single scan being uploaded belongs to the target student/session, or split and scan a bulk upload file */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const { isSingleScanCheck } = params.query
    if (+isSingleScanCheck){
      return this.checkSingleScan(data)
    } else {
      return await this.processBulkScan(params, data)
    }
  
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<any> {
    throw new Errors.NotImplemented();
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
