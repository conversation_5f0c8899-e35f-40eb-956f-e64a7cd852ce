import { Id, NullableId, Paginated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import axios, { AxiosInstance } from 'axios';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import { Knex } from 'knex';
import logger from '../../../../../logger';
import { NotificationCentre} from "../../../notifications/data/types";
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import moment from 'moment-timezone';
import {Format} from './../../../test-ctrl/schools/student-attempt-responses/student-attempt-responses.class'
const Mustache = require('mustache');

// If user did not last long-poll within these seconds, exclude them from the queue
const MAX_SEC_UPLOADER_LAST_CHECK = 60 * 2 // 2 mins

interface Data {}

interface ServiceOptions {}

export enum SCAN_UPLOAD_TYPE {
  INVIG_BULK_UPLOAD = "INVIG_BULK_UPLOAD",
  INVIG_INDIVIDUAL_UPLOAD = "INVIG_INDIVIDUAL_UPLOAD",
  ADMIN_SCAN_RECONCILIATION = "ADMIN_SCAN_RECONCILIATION"
}

export interface IResponseSheetRes {
  [uid: number]: {
    base64: string,
    croppedImgUrl: string,
    filePath: string,
    filePathCropped: string,
    qrcodes: any
  }
}


export enum BULK_SCAN_STATUS {
  IN_QUEUE = 'IN_QUEUE', 
  IN_PROGRESS = 'IN_PROGRESS', 
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

export interface IScanConfig {
  filePath: string
}

export interface IUploadParams {
  test_attempt_id: number,
  test_question_id: number,
  isBulk: boolean,
  params: any,
  fullScanConfig?: IScanConfig,
  croppedScanConfig?: IScanConfig,
  sessionSlug?: string,
  scanFileUrl?: any,
  isForceCrop?: boolean,
  upload_type?: SCAN_UPLOAD_TYPE
}

export class UploadResponseSheets implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = this.app.get('eqaoScanningService');
  }

  /** Return the upload history for the target session. 
   * Also, if the latest upload is waitlisted, start it now if allowed, otherwise return its updated number in the waitlist queue. */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    if (!params || !params.query){
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');

    }
    const { test_session_id, school_class_id, schl_class_group_id, isSasn } = params.query

    // Find test window id
    const db = this.app.get('knexClientRead')
    const tsRecord = await db('test_sessions as ts').where('ts.id', test_session_id).select('ts.test_window_id').first();
    const { test_window_id } = tsRecord;

    // Pull the test session's upload records
    const INVIG_BULK_UPLOAD_HISTORY = `
      select
      bulk.id
      , bulk.created_on
      , bulk.created_by_uid
      , bulk.started_on
      , bulk.completed_on
      , bulk.bulk_file_path
      , bulk.status
      , bulk.num_input_pages
      , bulk.num_scans_uploaded
      , bulk.scan_data
      , bulk.is_override_flagged_only
      , u.contact_email as uploader_email
      , CONCAT(u.first_name, ' ', u.last_name) as uploader_name
      from 
      class_bulk_scan_upload as bulk
      join users as u
        on u.id = bulk.created_by_uid
      where test_window_id = :test_window_id and test_session_id = :test_session_id
      order by bulk.created_on desc
    `

    const bulkUploadRecords = await dbRawRead(this.app, {test_session_id, test_window_id}, INVIG_BULK_UPLOAD_HISTORY)

    // Process data to be returned
    const expireSeconds = 24 * 60 * 60 // 1 day
    bulkUploadRecords.forEach((r:any) => {
      r.bulk_file_url = generateS3DownloadUrl(r.bulk_file_path, expireSeconds)
      r.bulk_file_name = r.bulk_file_path.split('/').pop()
      r.scan_data = JSON.parse(r.scan_data)
    })

    // If the latest one is waiting in queue (there could be no other ones created after it waits), check if it can run now and if so, run it
    if(bulkUploadRecords.length && bulkUploadRecords[0].status == BULK_SCAN_STATUS.IN_QUEUE){
      const waitingRecord = bulkUploadRecords[0]
      const { canProcessStart, numInQueue } = await this.checkNewOrWaitingProcessStartPermission(waitingRecord.id);
      if (canProcessStart){
        // Update history data to be returned
        waitingRecord.started_on = new Date().toISOString();
        waitingRecord.status = BULK_SCAN_STATUS.IN_PROGRESS;
        // Start running it async
        (async () => {
          this.runBulkUpload(params, waitingRecord.bulk_file_path, waitingRecord.id, test_session_id, schl_class_group_id, school_class_id, !!waitingRecord.is_override_flagged_only, +isSasn, waitingRecord.created_by_uid, true)
        })()

      // If not starting, update that the user checked it and return with latest # in queue
      } else {
        waitingRecord.numInQueue = numInQueue;
        await db('class_bulk_scan_upload').where('id', waitingRecord.id)
        .update({
          client_queue_last_check_on: dbDateNow(this.app),
        });
      }

    }

    return bulkUploadRecords;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async retrieveSplitPdfResponses(bulkFileUrl: string, studentScanInfo: any, testSessionId: number) {
    const testSessionRecord = await dbRawRead(this.app, [testSessionId],
      `SELECT * from test_sessions ts
      where ts.id = ?
      `
    );
    const eqaoScanningService: AxiosInstance = this.app.get('eqaoScanningService');
    const res = await axios.post(`/split-and-upload`, { bulkFileUrl: bulkFileUrl, test_window_id: testSessionRecord[0].test_window_id, test_session_id: testSessionId });
    const respSheets: IResponseSheetRes[] = res.data;
    const scanInfo = JSON.parse(studentScanInfo);

    const classUids = Object.keys(scanInfo).map(uid => +uid);

    const resRespSheetConfig: any = {};

    let sessionsNotSubmitted = false;

    await Promise.all(respSheets.map( async (resp: IResponseSheetRes) => {
      const uid: number = +(Object.keys(resp)[0]);
      if (!classUids.includes(uid)) {
        resRespSheetConfig[uid] = null;
        return;
      }
      const studentRespInfo: any = {...resp[uid]};
      const qrContents = studentRespInfo['qrcodes'][0].split('-');
      const questionSlug = qrContents[1];
      const sessionInfo = scanInfo[uid]['sessionInfo'][questionSlug];
      const quesId = Object.keys(sessionInfo)[0];
      const questionInfo = sessionInfo[quesId];
      const ssIdToCheck = sessionInfo.tassId;
      if (!sessionsNotSubmitted) {
        const isSubmitted = await this.checkIfSubsessionSubmitted(ssIdToCheck);
        if (!isSubmitted) {
          sessionsNotSubmitted = true;
          return;
        }
      }
      if (questionInfo.is_test_session_submitted && questionInfo.isConfirmed) return;
      const sessionSlugsQuesToRetrieve = [];
      sessionSlugsQuesToRetrieve.push(questionSlug);
      const {scanningTestQues} = await this.app.service('public/educator/class-scan-info')
        .getTestFormScanningQuestions(scanInfo[uid].test_form_id, scanInfo[uid].file_path, sessionSlugsQuesToRetrieve);
      const testAttempt = await dbRawRead(this.app, [uid, testSessionId],
        `SELECT ta.id
        from test_attempts ta
        where ta.uid = ?
        and ta.test_session_id = ?
        and ta.twtdar_order = 0
        `
      )
      studentRespInfo['questionId'] = scanningTestQues[0]['question_id'];
      studentRespInfo['test_attempt_id'] = testAttempt[0].id;

      if (!(Object.keys(resRespSheetConfig).includes(`${uid}`))) {
        resRespSheetConfig[uid] = {};
      }
      delete studentRespInfo.qrcodes;
      resRespSheetConfig[uid][questionSlug] = {...studentRespInfo};
    }));
    return {resRespSheetConfig, sessionsNotSubmitted};
  }

  async submitTaqrForUploads(uploadParams: IUploadParams): Promise<any> {
    const { test_attempt_id, test_question_id, isBulk, params, sessionSlug, scanFileUrl, fullScanConfig, croppedScanConfig, isSasn, upload_type } = <any> uploadParams;

    const taqrIdRec = await this.app.service('db/read/test-attempt-question-responses').db()
    .where('test_attempt_id', test_attempt_id)
    .where('test_question_id', test_question_id)
    .whereNot('is_invalid', 1)
    // .pluck('id'); //Purposefully ignoring is_invalid to avoid issues with scans being tied to an online response that becomes invalidated.

    let taqr_id: any;
    let ri_common_id_resolved = null;
    const uid = await currentUid(this.app, params);
    // If TAQR does not exist (when student doesn't reach the question and their session is submitted by teacher or autosubmitted), create TAQR first
    if(!taqrIdRec?.length) {
      const newTaqrRecordData = await this.app.service('public/test-ctrl/schools/student-attempt-responses').createTaqrFromScratch(test_attempt_id, null, test_question_id, undefined, null, Format.SCAN)
      const taqrRec = await this.app.service('db/write/test-attempt-question-responses').create(newTaqrRecordData);
      taqr_id = taqrRec.id;
    } 
    // If TAQR exists, ensure that its fields reflect getting a new scanned response
    else {
      taqr_id = taqrIdRec[0].id;
      let responseRawString = taqrIdRec[0].response_raw
      let responseRaw;
      if (responseRawString){
        responseRaw = JSON.parse(responseRawString)
      }
      else {
        const newTaqrRecordData = await this.app.service('public/test-ctrl/schools/student-attempt-responses').createTaqrFromScratch(test_attempt_id, null, test_question_id, undefined, null, Format.SCAN)
        responseRaw = JSON.parse(newTaqrRecordData.response_raw)
      }
      const entryId = responseRaw.__meta.entryOrder[0] // Assume scannable question has only one respondable element, which it always should
      // Since a scan will exist for the TAQR, ensure that isResponded (and isFilled) are true for consistency and to avoid it being re-flagged as NR in future NR re-calculations 
      responseRaw[entryId].isResponded = true;
      responseRaw[entryId].isFilled = true;
      responseRaw[entryId].isPaperFormat = true;
      await this.app
        .service('db/write/test-attempt-question-responses')
        .db()
        .where('test_attempt_id', test_attempt_id)
        .where('test_question_id', test_question_id)
        .where('id', taqr_id)
        .update({
          response_raw: JSON.stringify(responseRaw),
          is_confirmed: 0, // New scan requires confirmation
          is_nr: 0, // Since a scan is being added, ensure it's not NR
          is_scan_needs_reupload: 0,
          is_scan_needs_reupload_ri_common_id: null
        });

        // If there was previously a reported issue for needing to replace the scan, auto-resolve it
        if (taqrIdRec[0].is_scan_needs_reupload_ri_common_id){
          ri_common_id_resolved = taqrIdRec[0].is_scan_needs_reupload_ri_common_id;
          this.app.service('public/educator/response-sheet-needs-reupload').resolveScanReuploadReportedIssue(ri_common_id_resolved, uid, 'Scan reuploaded')
        }

    }

    const testTakerDetails = await dbRawRead(this.app, [test_attempt_id],
      `SELECT ts.id as test_session_id,
      ts.test_window_id as test_window_id,
      um.value as oen
      FROM test_sessions ts
      JOIN test_attempts ta ON ta.test_session_id = ts.id
      JOIN user_metas um on ta.uid = um.uid and um.key = ${isSasn ? `'SASN'` : `'StudentOEN'`}
      WHERE ta.id = ?;
    `);
    const test_window_id = testTakerDetails[0].test_window_id;
    const test_session_id = testTakerDetails[0].test_session_id;
    const oen = testTakerDetails[0].oen;
    let oenHash;

    let resUncroppedScanPath;
    let resCroppedScanPath;

    if (!isBulk) {
      const response = await this.eqaoScanningService.post(`/generate-cropped-image`, {scanFileUrl, test_window_id, test_session_id, oen, session_slug: sessionSlug});
      if (response.data === 'ERR_TOO_MANY_SCANS') {
        throw new Errors.BadRequest(response.data);
      }
      resUncroppedScanPath = response.data.uncroppedFilePath;
      resCroppedScanPath = response.data.croppedFilePath;
      // retrieving hashed oen from scanning service upload
      oenHash = resUncroppedScanPath.split('/')[4].split('-')[0];
    } else {
      resUncroppedScanPath = fullScanConfig.filePath;
      resCroppedScanPath = croppedScanConfig.filePath;
      oenHash = resUncroppedScanPath.split('/')[4].split('-')[0];
    }

    // discard all previous scans under this taqr id, then create new scan response
    await dbRawWrite(this.app, [taqr_id],
      `UPDATE test_attempt_scan_responses tasr
      set tasr.is_discarded = 1
      where tasr.taqr_id = ?`
    )
    
    const newTasrRec = await this.app.service('db/write/test-attempt-scan-responses').create({
      taqr_id,
      scan: resCroppedScanPath,
      full_scan: resUncroppedScanPath,
      uploaded_by_uid: uid,
      is_allow_review_bypass: isBulk,
      is_individual_upload: isBulk ? 0 : 1,
      upload_type,
      ri_common_id_resolved
    });

    const uncroppedFile = await generateS3DownloadUrl(resUncroppedScanPath, 600);
    const croppedFile = await generateS3DownloadUrl(resCroppedScanPath, 600);

    return {
      taqr_id,
      new_tasr_id: newTasrRec.id,
      scan_path: resCroppedScanPath,
      full_scan_path: resUncroppedScanPath,
      test_attempt_id,
      test_question_id,
      sessionSlug,
      uncroppedFile,
      croppedFile
    }
  }

/**
 * Check if there is capacity for a (new or existing waitlisted) bulk process to start. If not, also return its place in the queue.
 * Used to limit load on scanning service.
 * @param waitingProcessId - pass the ID of a waiting process to check if it can run now. If nothing passed, it checks if a new process that hasn't been created yet could run.
 * @returns `canProcessStart` true or false if it can start now, `numInQueue` - if it cannot start, what is its position in the queue
 */
  async checkNewOrWaitingProcessStartPermission(waitingProcessId?: number) : Promise<{canProcessStart: boolean, numInQueue: number|null}> {

    // Get all running records, and all records that are in queue (if client hasn't checked them recently, ignore them from queue)
    // If comparing against a waiting candidate record, only get those in queue before it (created earlier)
    const targetRunningAndQueueRecords = await dbRawRead(this.app, {inProgress: BULK_SCAN_STATUS.IN_PROGRESS, inQueue: BULK_SCAN_STATUS.IN_QUEUE, MAX_SEC_UPLOADER_LAST_CHECK, waitingProcessId}, `
    select id, status from class_bulk_scan_upload bulk
    where status = :inProgress  or (
      status = :inQueue
      and client_queue_last_check_on >= DATE_SUB(NOW(), INTERVAL :MAX_SEC_UPLOADER_LAST_CHECK SECOND)
      ${waitingProcessId ? `and bulk.id < :waitingProcessId` : '' }
    );`)

    const numRunningRecords = targetRunningAndQueueRecords.filter(r => r.status == BULK_SCAN_STATUS.IN_PROGRESS).length
    const numQueueRecordsBeforeTarget = targetRunningAndQueueRecords.filter(r => r.status == BULK_SCAN_STATUS.IN_QUEUE).length

    const maxAllowedRunning = await getSysConstNumeric(this.app, 'BULK_CLASS_SCAN_UPLOAD_MAX_RUNNING', true)

    // This is allowed to run if there's space for at least one more process to run, and this one is the first one in the queue
    const canProcessStart = numRunningRecords < +maxAllowedRunning && numQueueRecordsBeforeTarget == 0;

    const numInQueue = canProcessStart ? null : numQueueRecordsBeforeTarget + 1

    return {
      canProcessStart,
      numInQueue
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { isBulk } = <any> data;
    const uid = await currentUid(this.app, params);

    if (isBulk) {

      const { bulk_file_path, test_session_id, schl_class_id, schl_class_group_id, isSasn, is_override_flagged_only } = <any>data;
      
      const db = this.app.get('knexClientRead')

      // Find test window id
      const tsRecord = await db('test_sessions as ts').where('ts.id', test_session_id).select('ts.test_window_id').first();
      const { test_window_id } = tsRecord;

      // Check if can start the process now or if it should be waitlisted
      const { canProcessStart, numInQueue } = await this.checkNewOrWaitingProcessStartPermission();

      // Create the upload record
      const newBulkRecordData = {
        created_by_uid: uid, 
        test_window_id,
        test_session_id,
        bulk_file_path,
        is_override_flagged_only,
        started_on: canProcessStart ? dbDateNow(this.app) : undefined,
        client_queue_last_check_on: canProcessStart ? undefined : dbDateNow(this.app),
        status: canProcessStart ? BULK_SCAN_STATUS.IN_PROGRESS : BULK_SCAN_STATUS.IN_QUEUE,
      }
      const knex: Knex = this.app.get('knexClientWrite');
      const [bulkUploadRecordId] = await knex('class_bulk_scan_upload').insert(newBulkRecordData);

      // If allowed to start now, run the upload async
      if (canProcessStart){
        (async () => {
          this.runBulkUpload(params, bulk_file_path, bulkUploadRecordId, test_session_id, schl_class_group_id, schl_class_id, !!is_override_flagged_only, isSasn, uid);
        })()
      }

      return { id: bulkUploadRecordId, status: newBulkRecordData.status, numInQueue }
    } else {
        const {scanFileUrl, scanFilePath, test_attempt_id, test_question_id, tassId, sessionSlug} = <any>data;
        const isForceCrop = (<any>data)?.isForceCrop || false;
        const isSubmitted = await this.checkIfSubsessionSubmitted(tassId);
        if (!isSubmitted) {
          throw new Errors.BadRequest('ERR_SESS_NOT_SUBMITTED');
        } else {
          try {
            const uploadParams: IUploadParams = {scanFileUrl, test_attempt_id, test_question_id, isBulk: false, params, sessionSlug: sessionSlug, isForceCrop, upload_type: SCAN_UPLOAD_TYPE.INVIG_INDIVIDUAL_UPLOAD}
            const submittedUpload = await this.submitTaqrForUploads(uploadParams);

            // Check if the upload resolves a scan required flag
            const {srrtaqr_id, scan_response_require_id, scan_response_open_school_id} = await this.getScanRequireInfoByTaqrId(submittedUpload.taqr_id)
            if (srrtaqr_id){
              await this.app.service('public/school-admin/upload-scan').resolveScanRequires(uid, srrtaqr_id, scan_response_require_id, scan_response_open_school_id)
              //create a new record to scan-response-uploads
              const newScanResponseUploadData = {
                created_by_uid: uid,
                scan_response_require_id,
                s3_file_link: scanFilePath,
                upload_type: SCAN_UPLOAD_TYPE.INVIG_INDIVIDUAL_UPLOAD,
                is_revoked:0,
              }
              await this.app.service('db/write/scan-response-uploads').create(newScanResponseUploadData);
            }
            return submittedUpload
          } catch (e: any) {
            throw new Errors.BadRequest(e.message);
          }
        }
    }
  }

  async checkIfSubsessionSubmitted(tassId: number) {
    const tassRecord = await this.app
          .service('db/read/test-attempt-sub-sessions')
          .db()
          .where('id', tassId);
    const isSubmitted = tassRecord[0].is_submitted ? true : false
    return isSubmitted;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async runBulkUpload(params: Params, bulkFilePath: string, bulkUploadRecordId: number, testSessionId: number, schl_class_group_id: number, school_class_id: number, is_override_flagged_only: boolean, isSasn: number, uploader_uid: number, doUpdateRecordFirst: boolean = false){
    const knex:Knex = this.app.get('knexClientWrite')
    try {

      // Update to running status first if required
      if (doUpdateRecordFirst){
        await knex('class_bulk_scan_upload').where('id', bulkUploadRecordId)
        .update({
          started_on: dbDateNow(this.app),
          status: BULK_SCAN_STATUS.IN_PROGRESS
        });
      }

      const query = { schl_class_group_id }
      const studentScanInfo =  await this.app.service('public/educator/class-scan-info').getClassScanInfo(school_class_id, {...params, query});
      // @ts-ignore
      const { studentScanInfoMap } = studentScanInfo;

      const expireSeconds = 24 * 60 * 60 // 1 day
      // Pass bulk file through scanner 
      const data = {
        bulkFileUrl: generateS3DownloadUrl(bulkFilePath, expireSeconds),
        testSessionId,
        schl_class_group_id,
        school_class_id,
        is_override_flagged_only,
        studentScanInfoMap,
        isSasn
      }

      // Pass the file through the scanning service to read QR codes, and process returned data
      const processedQrData = await this.app.service('public/educator/resp-sheet-upload/qrcode-contents').processBulkScan(params, data)

      const { num_input_pages } = processedQrData
      // Update the upload records with result of QR scanning
      await knex('class_bulk_scan_upload').where('id', bulkUploadRecordId)
      .update({
        num_input_pages,
        scan_data: JSON.stringify(processedQrData || {})
      });

      // Rearrange and create records for TAQRs
      const uploadedScanData = [];
      const srrIdSet = new Set()
      let num_scans_uploaded = 0;
      for(let uid in processedQrData?.scanData) {
        const studentInfo = processedQrData.scanData[uid];
        // @ts-ignore
        for(let question of studentInfo.questions) {
          const fullScanConfig = {
            filePath: question.filePath
          }
          const croppedScanConfig = {
            filePath: question.filePathCropped
          }
          // @ts-ignore
          const {scanningTestQues} = await this.app.service('public/educator/class-scan-info').getTestFormScanningQuestions(studentScanInfoMap[uid].test_form_id, studentScanInfoMap[uid].file_path, [question.slug]);
          const testAttempt = await dbRawRead(this.app, [uid, testSessionId],
            `SELECT ta.id
            from test_attempts ta
            where ta.uid = ?
              and ta.test_session_id = ?
              and ta.twtdar_order = 0
              and ta.is_invalid != 1
            `
          )
  
          const test_attempt_id = testAttempt[0].id;
          const test_question_id = scanningTestQues[0]['question_id'];
  
          const uploadParams = {
            fullScanConfig,
            croppedScanConfig,
            test_attempt_id,
            test_question_id,
            isBulk: true,
            isSasn: !studentInfo.oen,
            sessionSlug: question.slug,
            params,
            upload_type: SCAN_UPLOAD_TYPE.INVIG_BULK_UPLOAD
          }
          // Create TAQR records
          const { taqr_id, new_tasr_id, scan_path, full_scan_path } = await this.submitTaqrForUploads(uploadParams);

          // Check if the upload resolves a scan required flag
          const {srrtaqr_id, scan_response_require_id, scan_response_open_school_id} = await this.getScanRequireInfoByTaqrId(taqr_id)
          if (srrtaqr_id){
            srrIdSet.add(scan_response_require_id)
            await this.app.service('public/school-admin/upload-scan').resolveScanRequires(uploader_uid, srrtaqr_id, scan_response_require_id, scan_response_open_school_id)
          }
          
          // To save in the db bulk record which scans were actually uploaded for convenience
          const uploadData = {
            taqr_id, new_tasr_id, scan_path, full_scan_path, test_attempt_id, uid, test_question_id, session_slug: question.slug,
          }
          uploadedScanData.push(uploadData)
          num_scans_uploaded++;
        }
      }

      const scanResponseRequireIds = [...srrIdSet]
      for (let scan_response_require_id of scanResponseRequireIds){
        //create a new record to scan-response-uploads
        const newScanResponseUploadData = {
          created_by_uid: uploader_uid,
          scan_response_require_id,
          s3_file_link: bulkFilePath,
          upload_type: SCAN_UPLOAD_TYPE.INVIG_BULK_UPLOAD,
          is_revoked:0,
        }
        await this.app.service('db/write/scan-response-uploads').create(newScanResponseUploadData);
      }

      // Flag as completed
      await knex('class_bulk_scan_upload').where('id', bulkUploadRecordId)
      .update({
        num_scans_uploaded,
        status: BULK_SCAN_STATUS.COMPLETED,
        uploaded_scan_data: JSON.stringify(uploadedScanData),
        completed_on: dbDateNow(this.app)
      });

      // Send notification of completion to user
      this.sendScanNotif(uploader_uid, school_class_id, bulkUploadRecordId);
    } catch (err) {
      logger.error('ERR_CLASS_BULK_UPLOAD', { bulkUploadRecordId, err })
      await knex('class_bulk_scan_upload').where('id', bulkUploadRecordId)
      .update({
        status: BULK_SCAN_STATUS.ERROR
      });
      // Send notification of error to user
      this.sendScanNotif(uploader_uid, school_class_id, bulkUploadRecordId, true);
    }


  }

  /** Send the user a notification that their bulk scan process either completed or failed due to technical error */
  async sendScanNotif(uid: number, school_class_id: number, bulkUploadRecordId:number, isError = false){

    // Get data about school, class, and bulk upload record
    const db = this.app.get('knexClientRead')

    const sdchoolRecord = await dbRawReadSingle(this.app, {school_class_id}, `
      select sc.schl_group_id, schl.lang as schl_lang, sc.name as sc_name
      from school_classes as sc
      join schools as schl
        on sc.schl_group_id = schl.group_id
      where sc.id = :school_class_id
    `)


    const bulkRecord = await dbRawReadSingle(this.app, {bulkUploadRecordId}, `
      select created_on from class_bulk_scan_upload as bulk where id = :bulkUploadRecordId
    `)

    const { schl_group_id, schl_lang, sc_name } = sdchoolRecord;


    const urRecord = await dbRawReadSingle(this.app, {uid, schl_group_id}, `
      select id
      from user_roles ur
      where is_revoked != 1
      and role_type in ('schl_admin', 'schl_teacher')
      and uid = :uid
      and group_id = :schl_group_id
    `)

    if (!urRecord) return; // Should not happen

    const recipient: Partial<NotificationCentre.IUserMessageRecipient> = {
      recipient_uid: uid,
      recipient_group_id: schl_group_id,
      recipient_role_id: urRecord.id
    }

    // Get error or completion template, in french or english depending on school
    const subjSlug = isError ? 'educ_bulk_scan_notif_err_subj' : 'educ_bulk_scan_notif_subj'
    const bodySlug = isError ? 'educ_bulk_scan_notif_err_body' : 'educ_bulk_scan_notif_body'
    const subjTemplate = await this.app.service('public/translation').getOneBySlug(subjSlug, schl_lang);
    const bodyTemplate = await this.app.service('public/translation').getOneBySlug(bodySlug, schl_lang);

    // Convert UTC date to EST, in en/fr
    const dateFormat = await this.app.service('public/translation').getOneBySlug("datefmt_timestamp", schl_lang);
    const formattedDate = moment(bulkRecord.created_on).tz('America/New_York').format(dateFormat);

    // Fill the data into the tempalte
    const templateParams = {
      SCHOOL_CLASS_NAME: sc_name,
      UPLOAD_CREATED_ON: formattedDate,
    }
    const subjectMarkdown = Mustache.render(subjTemplate, templateParams);
    const bodyMarkdown = Mustache.render(bodyTemplate, templateParams);

    const notification: Partial<NotificationCentre.IUserMessage> = {
      subject: subjectMarkdown,
      message: bodyMarkdown,
      sender_uid: 0,
      is_auto: 1,
      channel_id: 2
    }

    // Send notification
    return this.app.service('public/notifications/ntf-ctrl/message').sendNotification(notification, [recipient])
    .catch(() => {
      throw new Errors.BadRequest("ERROR SENDING NOTIFICATION")
    });


  }

  /** Return relevant info if there's a flagged missing scan for the TAQR */
  async getScanRequireInfoByTaqrId(taqr_id: number){
    const scanRequireInfo = await dbRawReadSingle(this.app, {taqr_id}, `
      -- < 0.1 sec
      SELECT srrtaqr.id as srrtaqr_id
      , srrtaqr.scan_response_require_id
      , srr.scan_response_open_school_id
      from
      scan_response_require_taqr srrtaqr
      join scan_response_require srr
        on srr.id = srrtaqr.scan_response_require_id
      where srrtaqr.taqr_id = :taqr_id
        and srrtaqr.is_revoked = 0
        and srrtaqr.is_resolved = 0
    ;`)

    return {
      srrtaqr_id: scanRequireInfo?.srrtaqr_id,
      scan_response_require_id: scanRequireInfo?.scan_response_require_id, 
      scan_response_open_school_id: scanRequireInfo?.scan_response_open_school_id
    }
  }

}
