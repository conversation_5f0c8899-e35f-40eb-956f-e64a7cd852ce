import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbBulkInsert, dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';
import { TW_EXCEPTION_STUDENT_STATUS } from '../g9-reports/g9-reports.class'
import { sleep } from '../../../../util/timeout';
import _ from 'lodash';

interface Data {}

interface ServiceOptions {}

export class G9ReportsPdf implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data> | {}> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const { test_window_id, class_group_id } = (<any>params).query; // clientdomain can be empty when called by pysch pipline, 
                                                                                   // test_window_id, class_group_id can be empty
    
    //Get reportReadyAttempts(including attempt took in other school as guest students)

    const test_window = await this.getTwInfo(test_window_id, class_group_id)
    const isEndOfWindow = test_window.is_end_window_isr

    let checkGuestStudent = false;
    const notGuestReportReadyAttempts = await this.getReportReadyAttempts(test_window_id, class_group_id, checkGuestStudent, isEndOfWindow);
    checkGuestStudent = true;
    const guestReportReadyAttempts = await this.getReportReadyAttempts(test_window_id, class_group_id, checkGuestStudent, isEndOfWindow)

    let notFullyParticipateReadyReport = []
    if (isEndOfWindow) { // Include not fully participating students for report
      notFullyParticipateReadyReport = await this.getNFPStudentReadyForReport(test_window_id, class_group_id)
      
    }
    const reportReadyAttempts = notGuestReportReadyAttempts.concat(guestReportReadyAttempts, notFullyParticipateReadyReport)
                                                                         
    return { test_window_id, class_group_id, reportReadyAttempts }
  }

  /**
   * Get the attempts that need an ISR (Attempt that request for one but not yet get one)
   * If test window id is included, only get attempt in that test window
   * If class group id is included, only get attempt in that class
   * @param test_window_id(optional could be undefine) 
   * @param class_group_id(optional could be undefine)
   * @returns list of attempt that need ISR
   */
  async getReportReadyAttempts(test_window_id:any, class_group_id:any, checkGuestStudent:boolean, endOfWindow:boolean = false){
    const filtered_tw_exception_student_status = [TW_EXCEPTION_STUDENT_STATUS.PENDED,TW_EXCEPTION_STUDENT_STATUS.WITHHOLD]                                                                               
    //generate school class group ids
    const queryParams = [filtered_tw_exception_student_status]
    if( test_window_id ){
      queryParams.push(test_window_id)
    }
    if( class_group_id ){
      queryParams.push(class_group_id)
    }
    if( endOfWindow ){
      filtered_tw_exception_student_status.pop() // We want report for withheld student at the end of window
    }

    const reportReadyAttempts = await dbRawRead(this.app, queryParams, `
        -- took 828 ms on mirror db
        select distinct 
              ts.test_window_id    
            , sc.group_id
            , asrg.id as asrg_id
            , asrg.test_attempt_id
            , asrg.student_uid
          from auto_student_report_generations asrg
          join test_attempts ta on ta.id = asrg.test_attempt_id
          join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
          ${checkGuestStudent?
            `
              join school_classes sc_invi on sc_invi.id = scts.school_class_id
              join school_classes_guest scg on scg.invig_sc_group_id = sc_invi.group_id
              join school_classes sc on sc.group_id = scg.guest_sc_group_id
              join user_roles ur on ur.group_id = sc.group_id and ur.uid = ta.uid -- make sure student registered in the class 
            `:
            `
              join school_classes sc on sc.id = scts.school_class_id
              join user_roles ur on ur.group_id = sc.group_id and ur.uid = ta.uid -- make sure student registered in the class it took the test(this will remove guest students) 
            `
          }
          join student_reports sr on sr.uid = asrg.student_uid and sr.attempt_id = asrg.test_attempt_id and sr.is_revoked!= 1 and sr.is_pending != 1 
            ${ !endOfWindow ? `and sr.is_withheld != 1 and sr.is_data_insufficient != 1 and sr.is_absent != 1` : '' }
          join test_sessions ts on ts.id = scts.test_session_id
    left join tw_exceptions_students twes on twes.test_window_id = ts.test_window_id and twes.uid = asrg.student_uid and twes.is_revoked = 0 and twes.category in (?)
        where asrg.student_report_generated_on is not null 
          and asrg.student_pdf_generated_on is null 
          and asrg.is_revoked = 0
          and twes.id is null -- only generate student's report pdf that are not pended/withhold in tw_exceptions_students
          ${test_window_id ? 'and ts.test_window_id = ?':''}
          ${class_group_id ? 'and sc.group_id = ?':''}
    ;`)

    return reportReadyAttempts
  }

  async getNFPStudentReadyForReport (test_window_id:any, class_group_id:any){
    const tw_exception_pended = TW_EXCEPTION_STUDENT_STATUS.PENDED
    const reportReadyNFPStudents = await dbRawRead(this.app, {tw_exception_pended, test_window_id, class_group_id}, `
      -- Benchmark: 0.031 sec, cost: 18.82
      SELECT ansrg.test_window_id
           , ansrg.schl_class_group_id AS group_id
           , ansrg.id AS ansrg_id
           , ansrg.student_uid
        FROM auto_nfp_student_report_generations ansrg
   LEFT JOIN tw_exceptions_students twes ON twes.test_window_id = ansrg.test_window_id AND twes.uid = ansrg.student_uid AND twes.is_revoked = 0 AND twes.category = (:tw_exception_pended)
       WHERE ansrg.is_revoked = 0
         AND ansrg.student_pdf_generated_on is null
      ${test_window_id ? 'AND ansrg.test_window_id = (:test_window_id)' : ''}
      ${class_group_id ? 'AND ansrg.schl_class_group_id = (:class_group_id)' : ''}
    ;`)
    return reportReadyNFPStudents
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * This endpoint is used by data exporter client and API, and educator g9 report client with secret user parameter
   * @param data 
   * @param params 
   * @returns 
   */
  async create (data: Data, params?: Params): Promise<Data> {
    // this is the function for pipline to call back when it finish the report generations

    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const created_by_uid = await currentUid(this.app, params);
    const fromPyschPipline = true

    return this.generateG9IsrPdfByChunk(params, created_by_uid, fromPyschPipline)
  }

  /**
   * Get all asrg and ansrg ready to have pdf report
   * Generate G9 ISR PDF by chunk of 5
   * @param params 
   * @returns 
   */
  async generateG9IsrPdfByChunk (params:any, created_by_uid:number, fromPyschPipline:boolean = false, fromIssueReviewer:boolean = false) { 

    let { clientDomain, test_window_id, class_group_id} = (<any>params).query; // clientdomain can be empty when called by pysch pipline, 
                                                                               // Either test_window_id or class_group_id is required

    // Get end of window flag to generate withheld and not fully participate students reports
    const test_window = await this.getTwInfo(test_window_id, class_group_id)
    const isEndOfWindow = test_window.is_end_window_isr

    //Get reprotReadyAttempts(including attempt took in other school as guest students)
    let checkGuestStudent = false;
    const notGuestReprotReadyAttempts = await this.getReportReadyAttempts(test_window_id, class_group_id, checkGuestStudent, isEndOfWindow );
    checkGuestStudent = true;
    const guestReprotReadyAttempts = await this.getReportReadyAttempts(test_window_id, class_group_id, checkGuestStudent, isEndOfWindow )
    
    let notFullyParticipateReadyReport = []
    if (isEndOfWindow) { // Include not fully participated students for report
      notFullyParticipateReadyReport = await this.getNFPStudentReadyForReport(test_window_id, class_group_id)
      
    }
    const reportReadyAttempts = notGuestReprotReadyAttempts.concat(guestReprotReadyAttempts, notFullyParticipateReadyReport)

    const newReportSchoolClassGroupIds:any[] = []
    reportReadyAttempts.forEach(reportReadyAttempt =>{
      if(!newReportSchoolClassGroupIds.find(id => id === reportReadyAttempt.group_id)){
        newReportSchoolClassGroupIds.push(reportReadyAttempt.group_id)
      }
    })

    //Cut the newReportSchoolClassGroupIds into chuncks with maximun of 5 in each chuck.
    //All the PDF generate service at a maximun of 5 call at each time.
    //Wait til all 5 call are finished than do another 5 call.
    const CHUNK_SIZE = 5;
    new Promise(async (resolve, reject) => {
      for(let newReportSchoolClassGroupIdsChunck of _.chunk(newReportSchoolClassGroupIds, CHUNK_SIZE)){
        await Promise.all(newReportSchoolClassGroupIdsChunck.map(async newReportSchoolClassGroupId => {
          const { long_generate_reports } = await this.app.service('public/educator/g9-reports').updateReportGenerateStatus(created_by_uid, newReportSchoolClassGroupId)
          const is_regenerate = long_generate_reports.length > 0
          await this.app.service('public/educator/g9-reports').generateReportPDF(created_by_uid, newReportSchoolClassGroupId, is_regenerate, clientDomain, fromPyschPipline, test_window.isr_version, fromIssueReviewer, isEndOfWindow )
        }))
      }  
    })
    return []
  }

  /**
   * This function create a fake student_reports record for the attempt that need an ISR in the class, so no export/pipeline process is needed
   * This is a hidden function only avaiable for secrete user for testing purpose.
   * @param params {schl_class_group_id} The class group id for create the fake student report
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    //internal use to simulate what should be done by psych pipline, This can be comment out when the pipeline is ready
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const {
      schl_class_group_id,
      clientDomain
    } = params.query;

    //Get newAttempts(including attempt took in other school as guest students)
    let checkGuestStudent = false;
    const notGuestNewAttempts = await this.getNewAttempts(schl_class_group_id, checkGuestStudent);
    checkGuestStudent = true;
    const guestNewAttempts = await this.getNewAttempts(schl_class_group_id, checkGuestStudent)
    const newAttempts = notGuestNewAttempts.concat(guestNewAttempts)

    const newAttemptIds:any[] = newAttempts.map( (newAttempt:any) => {return newAttempt.test_attempt_id });
    const asrgIds:any[] = newAttempts.map( (newAttempt:any) => {return newAttempt.asrg_id  });

    //Get newAttempts(including attempt took in other school as guest students)
    let newStudentReportData = []
    if(newAttemptIds.length){
      let checkGuestStudent = false;
      const notGuestNewStudentReportData = await this.getNewStudentData(newAttemptIds, schl_class_group_id, checkGuestStudent);
      checkGuestStudent = true;
      const guestNewStudentReportData = await this.getNewStudentData(newAttemptIds, schl_class_group_id, checkGuestStudent)
      newStudentReportData = notGuestNewStudentReportData.concat(guestNewStudentReportData)
    }
    
    // create fake score for all student ISR
    newStudentReportData.map( data =>{
      data.dot_score = ""+Math.floor(Math.random() * 5)+"."+Math.floor(Math.random() * 10)
    })

    //insert into student_reports
    if (newStudentReportData.length){
      await dbBulkInsert(this.app, 'student_reports', newStudentReportData, 'id')
    }

    //update student_report_generated_on value
    if(asrgIds.length){
      await dbRawWrite(this.app, [asrgIds], `
        update auto_student_report_generations set student_report_generated_on = now() where id in (?)
      ;`)        
    }

    return []
  }

  /**This function is for internal use only to create fake student report for G9
   * It will return the asrg record for the class including student took the test in other class as guest student
  */
  async getNewAttempts(schl_class_group_id:number, checkGuestStudent:boolean){
    const newAttempts = await dbRawRead(this.app, {schl_class_group_id}, `
      -- 819 ms on mirror db
      select distinct 
            asrg.test_attempt_id
          , asrg.id as asrg_id
        from auto_student_report_generations asrg
        join test_attempts ta on ta.id = asrg.test_attempt_id
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        ${checkGuestStudent?
          `
            join school_classes sc_invi on sc_invi.id = scts.school_class_id
            join school_classes_guest scg on scg.invig_sc_group_id = sc_invi.group_id
            join school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.group_id = :schl_class_group_id
          `:
          `
            join school_classes sc on sc.id = scts.school_class_id and sc.group_id = :schl_class_group_id
          `
        }
      where asrg.student_report_generated_on is null  
        and asrg.is_revoked = 0
    ;`)
    return newAttempts
  }

  /**This function is for internal use only to create fake student report for G9
   * It will return fake student report data for the class including student took the test in other class as guest student
  */
  async getNewStudentData(newAttemptIds:any[], schl_class_group_id:number, checkGuestStudent:boolean){
    const newStudentReportData = await dbRawRead(this.app, {newAttemptIds, schl_class_group_id}, `
      -- 593ms on mirror db  
      select distinct 
            ta.uid
          , ta.id as attempt_id
          , "" as data_raw
          , twtdar.test_window_id
          , tw.academic_year as school_year
          , schl.foreign_id as school_mident
          , sd.foreign_id as board_mident
          , um.value as student_oen
          , schl.lang as lang
        from test_attempts ta
        join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        ${checkGuestStudent?
          `
            join school_classes sc_invi on sc_invi.id = scts.school_class_id
            join school_classes_guest scg on scg.invig_sc_group_id = sc_invi.group_id
            join school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.group_id = :schl_class_group_id
          `:
          `
            join school_classes sc on sc.id = scts.school_class_id and sc.group_id = :schl_class_group_id
          `
        }
        join schools schl on schl.group_id = sc.schl_group_id
        join school_districts sd on sd.group_id = sc.schl_dist_group_id
        join user_metas um on um.uid = ta.uid and um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN'
        join test_windows tw on tw.id = twtdar.test_window_id
      where ta.id in (:newAttemptIds)
      group by ta.id
    ;`)
    return newStudentReportData
  }

  /**
   * Get tw.is_end_window_isr by test window id or school class group id 
   * @param test_window_id 
   * @param class_group_id 
   * @returns 
   */
  async getTwInfo(test_window_id: Id, class_group_id: any) {
    let test_window
    if (test_window_id) {
      test_window = await this.app.service('db/read/test-windows').get(test_window_id)
    } else if(class_group_id) {
      test_window = await dbRawReadSingle(this.app, {class_group_id}, `
        -- Benchmark 0.031, cost 1.7
        SELECT tw.is_end_window_isr
             , tw.isr_version
          FROM test_windows tw
          JOIN school_semesters ss ON ss.test_window_id = tw.id
          JOIN school_classes sc ON sc.semester_id = ss.id
         WHERE sc.group_id = (:class_group_id)
      ;`)
    }
    if (!test_window) {
      throw new Errors.BadRequest('INVALID_TEST_WINDOW')
    }
    return test_window
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
