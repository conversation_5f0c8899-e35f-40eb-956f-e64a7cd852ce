import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { generateAccessCode } from '../../../../util/secret-codes';
import { currentUid } from '../../../../util/uid';
import { dbDateOffsetHours, dbDateNow, dbDateSetDateTime } from '../../../../util/db-dates';
import { Knex } from 'knex';
import { EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { techReadinessFlags, techReadiPrimaryGoMode, techReadiJuniorGoMode, techReadiGoMode, techReadiOSSLTGoMode, techReadinessFlagsRequired } from '../../../../constants/g9-constants';
import { AssessmentType, getTestControllerGroupId } from '../../bc-admin-coordinator/test-window/assessment-type';
import moment from 'moment';
import logger from '../../../../logger';
import { randArrEntry } from '../../../../util/random';
import { Redis } from 'ioredis';
import { ensureSessionInfoToRedis } from '../../../../redis/inits/educator-session-init'
import { REDIS_ASSESSMENT_DATA_EXPIRE, KClassStuCredOEN, KClassStuCredSASN, KClassSchoolUserRoles, KSessionLoadedInit, checkRedisKey } from '../../../../redis/redis'
import { getRedisSchoolClass } from '../../../../redis/table-key-operations/school-class'
import { getRedisClassUserRolesFromSchoolClassId, getRedisSchoolUserRolesFromSchoolClassId, createRedisUserRole } from '../../../../redis/table-key-operations/user-role'
import { getRedisUserMetaLang, getRedisUserMetaOEN, getRedisUserMetaSASN } from '../../../../redis/table-key-operations/user-meta'
import { RedisKeyTestSession, isTestSessionPaused, getRedisTestSession, getRedisTestSessions, patchTestSession, patchRedisTestSession } from '../../../../redis/table-key-operations/test-session'
import { getClassGuestClasses } from '../../../../redis/relation-key-operations//class-guest-classes'
import { getRedisUser } from '../../../../redis/table-key-operations/user'
import { getRedisTestAttemptsFromSchoolClassId, getRedisTestAttemptsFromTestSessionId, patchRedisTestAttempt, patchTestAttempt } from '../../../../redis/table-key-operations/test-attempt'
import { getRedisTestSessionSubSessions } from '../../../../redis/table-key-operations/test-session-sub-session'
import { getRedisTestAttemptSubSessionsFromSchoolClassId, patchTestAttemptSubSession } from '../../../../redis/table-key-operations/test-attempt-sub-session'
import { getTestWindowTwtdars } from '../../../../redis/relation-key-operations/test-window-twtdars';
import { getRedisTestWindowTDAllocRules } from '../../../../redis/table-key-operations/test-window-td-alloc-rule';
import { getTestWindowAssessmentComponents } from '../../../../redis/relation-key-operations/test-window-assessment-components';
import { getRedisAssessmentComponentes } from '../../../../redis/table-key-operations/assessment-component';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { addSessionAttemptsInit } from '../../../../redis/unique-key-operations/session-attempts-init';
import { addSessionLoadedInit } from '../../../../redis/unique-key-operations/session-loaded-init';
import _ from 'lodash';


const DEFAULT_TEST_WINDOW = 6; // temp
const STANDARD_TIMEZONE = 'America/Toronto';

interface Data {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  isScheduled: boolean,
  isRemovePrev?: boolean;
  openAssessments: any[]
}
interface ISession {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  isScheduled: boolean,
  isRemovePrev?: boolean;
  is_fi?: boolean;
}

export type ISessionTime = string[];
interface ServiceOptions { }

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  get SCHOOL_ADMIN_SESSION() {
    return this.app.service('public/school-admin/session');
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    // list of active sessions for a class
    const { school_class_group_id,school_class_id,slug } = (<any>params).query;
    // long polling
    if ( params && slug ) {
      const activeSubSessions = await this.getActiveSubSessions(school_class_id);
      const completedSubSessions = await this.getSubmittedSubSessions(school_class_id);
      const sctsRecords =  <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { school_class_id,slug }, paginate: false });
      let subSessions = [];
      if(sctsRecords.length > 0){
        const sctsRecord = sctsRecords[0];
        subSessions =  <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: {test_session_id:sctsRecord.test_session_id }, paginate: false });
      }

      return <any>{
        activeSubSessions,
        completedSubSessions,
        subSessions
      }
    }
    throw new Errors.BadRequest();
  }

  private async getSchoolGroupByClassroomId(school_class_id:number){
    const schoolClassRecord = <any>await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    return schoolClassRecord.schl_group_id;
  }

  async create(data: Data, params?: Params): Promise<Data> {
    const { school_class_group_id }: { school_class_group_id:number } = (<any>params).query;
    if (params) {
      const created_by_uid = await currentUid(this.app, params); // should be currentUid
      const newSession = await this.createSessionForEducator(created_by_uid, <any> data, school_class_group_id);
      // return this.createSessionBc(created_by_uid, data, AssessmentType.GRAD);
      
      // Scheduled by educator
      // If the session is happening soon enough, students in the class may have approved alt requests that should be auto-operational sent
      // It will log any errors in db so don't await
      if (newSession.slug?.includes('OPERATIONAL') && newSession.school_class_id && newSession.date_time_start && newSession.test_window_id){
        const isClassOpSendRequired = await this.app.service('public/alt-version-ctrl/alt-version-requests').isClassOpSendRequired(+newSession.school_class_id, newSession.date_time_start, +newSession.test_window_id)
        if (isClassOpSendRequired) {
          this.app.service('public/alt-version-ctrl/alt-version-requests').handleOpAutoSend({school_class_id: newSession.school_class_id})
        }
      }
      return newSession
    }
    return <any>{};
  }

  /**
   * @deprecated
   * @important access codes with 4 chars have only 25,600 possible combinations
   * which causes issues with duplicate codes and db load
   */
  private async generateSessionAccessCode(schl_group_id:number){
    throw new Errors.BadRequest('DEPRECATED');
    let access_code:string = '';
    for (let i = 0; i < 10; i++) {
      access_code = generateAccessCode(8);
      const previousCodeMatches = <Paginated<any>>await this.app
        .service('db/write/test-sessions')
        .find({
          query: {
            access_code,
            schl_group_id,
            is_cancelled: 0,
            is_closed: 0,
          }
        });
      if (previousCodeMatches.total === 0) {
        break;
      }
    }
    return access_code;
  }

  async getActiveTestWindowForClass(school_class_id: Id, test_ctrl_group_id?:number){
    logger.silly({ school_class_id, test_ctrl_group_id })
      // const testWindowRecords = await dbRawRead(this.app, [school_class_id, test_ctrl_group_id], `
      //   select tw.id, tw.date_start, tw.date_end, tw.is_active
      //   from school_classes sc
      //   join school_semesters ss on ss.id = sc.semester_id
      //   join test_windows tw on tw.id = ss.test_window_id
      //   where sc.id = ?
      //     and tw.test_ctrl_group_id = ?
      //     and tw.is_active = 1
      // ;`)
      const schoolClassRecord = <any>await this.app
        .service('db/read/school-classes')
        .get(school_class_id);

      const semester_id = schoolClassRecord.semester_id
      //get the class's semester
      const semesterRecord = <any>await this.app
        .service('db/read/school-semesters')
        .get(semester_id);

      const test_window_id = semesterRecord.test_window_id;

      const testWindowRecord =  await this.app
        .service('db/read/test-windows')
        .get(test_window_id);

      if (!testWindowRecord){
        throw new Errors.GeneralError('NO_ACTIVE_WINDOW')
      }
      return testWindowRecord.id;
  }

  /**
   * Get test window for given semester ID, joins TW table to ensure that test window exists
   * @param semester_id - semester id to get the active test window for
   * @throws NO_ACTIVE_WINDOW - if no test window is found
   * @returns test window id
   */
  async getActiveTestWindowForSemester(semester_id: number) {
    const knex = this.app.service('db/read/test-windows').knex
    const testWindowRecord = await knex.from('test_windows as tw')
      .select('tw.id') 
      .join('school_semesters as ss', 'ss.test_window_id', 'tw.id')
      .where('ss.id', semester_id)
      .limit(1).first()

    if (!testWindowRecord) {
      throw new Errors.GeneralError('NO_ACTIVE_WINDOW');
    }
    return testWindowRecord.id;
  }

  /**
   * load from redis to make sure all student in the class have associate school user_roles
   * if not create one in db and add to redis.
   * @param school_class_id
   * @param created_by_uid
   */
  async ensureSchoolUserRoles(school_class_id: number, created_by_uid:any, loadFromRedis = false){
    let missingStudentSchoolRecords:any[] = []
    let redis_schl_group_id:any;

    if(loadFromRedis){
      //load from redis to make sure student have school user roles
      const schoolClass = await getRedisSchoolClass(this.app, school_class_id)

      //get class user roles
      const classUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, school_class_id)
      const classStudentUserRoles = classUserRoles.filter(classUserRole => classUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)

      //get school user roles of the class
      redis_schl_group_id = schoolClass.schl_group_id
      const redis_schoolUserRoles = await getRedisSchoolUserRolesFromSchoolClassId(this.app, school_class_id)
      const schoolStudentUserRoles = redis_schoolUserRoles.filter( schoolUserRole => schoolUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)

      //check every student class user roles and make sure student have a school user roles as well
      missingStudentSchoolRecords = classStudentUserRoles.filter( (classStudentUserRole:any) => {
        const theSchoolStudentUserRole =  schoolStudentUserRoles.find((schoolStudentUserRole:any) => +schoolStudentUserRole.uid === +classStudentUserRole.uid)
        if(!theSchoolStudentUserRole) return classStudentUserRole
      })
    }else {
      // loading from DB
      missingStudentSchoolRecords = await dbRawRead(this.app, [school_class_id], `
          select ur.*
               , sc.schl_group_id
            from user_roles ur
            join school_classes sc on sc.group_id = ur.group_id and sc.id = ?
       left join user_roles ur2 on ur2.uid = ur.uid and ur2.group_id = sc.schl_group_id and ur2.is_revoked != 1
           where ur.group_id = sc.group_id and ur.role_type = 'schl_student' and ur.is_revoked != 1 and ur2.id is null
      ;`);
    }

    await Promise.all( missingStudentSchoolRecords.map( async record =>{
      let schl_grp_id = loadFromRedis?redis_schl_group_id:record.schl_group_id
      const new_user_role = await this.app
          .service('db/write/user-roles')
          .create({
            role_type: DBD_U_ROLE_TYPES.schl_student,
            uid: record.uid,
            group_id: schl_grp_id,
            created_on: dbDateNow(this.app),
            created_by_uid: created_by_uid
          });
      //store new created user_roles into redis        
      if(loadFromRedis){
        //store into redis    
        const redisPayload = {
          id: new_user_role.id,
          role_type: new_user_role.role_type,
          uid:new_user_role.uid,
          group_id: new_user_role.group_id,
        }
        await createRedisUserRole(this.app, redisPayload, school_class_id, KClassSchoolUserRoles(school_class_id))
      }
    }))
  }

  async ensureStudentLang(school_class_group_id: number, schl_group_id: number) {

    const key_namespace = 'eqao_dyn'
    const key = 'Lang'

    const studentLangRecords = await dbRawRead(this.app, [key_namespace, key, school_class_group_id], `
      select ur.uid, um.id, um.\`value\`
      from user_roles ur
      left join user_metas um
        on um.uid = ur.uid
       and um.key_namespace = ?
       and um.\`key\` = ?
      where ur.group_id = ?
        and ur.role_type = 'schl_student'
        and ur.is_revoked != 1
        and um.\`value\` is null
      group by ur.uid
    ;`);

    if (studentLangRecords.length) {
      const schoolRecord = <Paginated<any>>await this.app
        .service('db/read/schools')
        .find({
          query: {
            group_id: schl_group_id
          }
        })
      const school = schoolRecord.data[0];
      let value = school.lang || 'en';
      value = value.toLowerCase()
      for (let i = 0; i < studentLangRecords.length; i++) {
        const record = studentLangRecords[i];
        const uid = record.uid;
        await this.app
          .service('db/write/user-metas')
          .create({
            uid,
            key_namespace,
            key,
            value
          });
      }
    }
    //ensure guest class lang
    const guestStudentLangRecords = await dbRawRead(this.app, [key_namespace, key, school_class_group_id], `
          select ur.uid, um.id, um.value, gscl.lang
            from school_classes sc
            join school_classes_guest scg on scg.invig_sc_group_id = sc.group_id and scg.is_revoked != 1
            join school_classes sc2 on sc2.group_id = scg.guest_sc_group_id
            join schools gscl on gscl.group_id = sc2.schl_group_id
            join user_roles ur on ur.group_id = scg.guest_sc_group_id and ur.role_type = 'schl_student' and ur.is_revoked != 1
       left join user_metas um
              on um.uid = ur.uid
             and um.key_namespace = ?
             and um.key = ?
           where sc.group_id = ?
             and sc.is_active = 1
             and um.value is null
        group by ur.uid
    ;`);

    await Promise.all(guestStudentLangRecords.map(async record =>{
      await this.app
        .service('db/write/user-metas')
        .create({
          uid:record.uid,
          key_namespace,
          key,
          value:record.lang
        });
    }))

  }

  private async closeExistingSessions(school_class_id:number){
    const existingSessions = await dbRawRead(this.app, [school_class_id], `
      select ts.id
      from school_class_test_sessions scts
      join test_sessions ts on ts.id = scts.test_session_id
      where ts.is_closed = 0
        and ts.is_cancelled = 0
        and scts.school_class_id = ?
    ;`);
    const testSessionsToClose = existingSessions.map(r => r.id);
    if (testSessionsToClose.length) {
      await dbRawWrite(this.app, [testSessionsToClose], `
        UPDATE test_sessions
        SET is_closed = 1,
        closed_on = NOW()
        WHERE id IN (?)
      ;`);
    }
  }


  async validateTechnicalReadinessStatus(schl_group_id: number,slug:string) {
    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props: any[], query: string) => {
      const res = await db.raw(query, props);
      return <any[]>res[0];
    }
    let isGoMode = techReadiGoMode;
    if(slug === "PRIMARY_OPERATIONAL"){
      isGoMode = techReadiPrimaryGoMode;
    }
    if(slug === "JUNIOR_OPERATIONAL"){
      isGoMode = techReadiJuniorGoMode;
    }
    if(slug === "G9_OPERATIONAL"){
      isGoMode = techReadiGoMode;
    }
    if(slug === "OSSLT_OPERATIONAL"){
      isGoMode = techReadiOSSLTGoMode
    }
    const techReadiRecords = await dbRawRead(this.app, [isGoMode, schl_group_id], `
      select *
      from (
        select uid, group_id, value
        from user_group_checklist ugc
        where slug = ?
          and group_id = ?
          and value != 0
        group by uid, group_id
      ) ugc
    ;`);
    const goMode = techReadiRecords[0]
    if (!goMode|| goMode.value === 0) {
      throw new Errors.Forbidden('TECH_READI_PENDING')
    }
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    const { school_class_group_id,school_class_id,slug } = (<any>params).query;
    const {openAssessments} = data
    const otherOpenSessionId = id
    if(!params || !otherOpenSessionId){
      throw new Errors.BadRequest();
    } 

    const otherOpenSessionRecord = openAssessments.find(assessment => assessment.test_session_id == otherOpenSessionId)
    const currOpenSessionRecord = openAssessments.find(assessment => assessment.test_session_id != otherOpenSessionId)
    const uid = await currentUid(this.app, params);
    
    await this.updateIsPaused(id, data, !otherOpenSessionRecord.is_paused, uid)
    if(currOpenSessionRecord.is_paused){
      await this.updateIsPaused(currOpenSessionRecord.test_session_id, data, !currOpenSessionRecord.is_paused, uid)
    }

    const otherAttemptRecords = await getRedisTestAttemptsFromTestSessionId(this.app, otherOpenSessionRecord.test_session_id)
    const payload: any = {};
    payload.active_sub_session_id = null

     // Patch 5 attempts at at time to minimize the database connection load.
     for(let taRecordsChunk of _.chunk(otherAttemptRecords, 5)){
      await Promise.all(taRecordsChunk.map(async (ta) => {
        await patchTestAttempt(this.app, ta.id, payload)
      }));
    }
    return data
  }
  public async getActiveSubSessions(school_class_id: number, loadFromRedis = false) {
    let active_sub_sessions:any = []
    if(loadFromRedis){
      //get all the test attempts
      const test_attempts = await getRedisTestAttemptsFromSchoolClassId(this.app, school_class_id)
      
      // filter the test attempt with active subsession
      const activeSubsessionTestAttempts = test_attempts.filter(ta => ta.active_sub_session_id)

      // get all distinct test sesession ids in activeSubsessionTestAttempts
      const distinctTestSessionIds:any[] = [...new Set(activeSubsessionTestAttempts.map(test_attempt => test_attempt.test_session_id))]

      // fetch test sessions in distinctTestSessionIds from redis
      const test_sessions = await getRedisTestSessions(this.app, distinctTestSessionIds)

      // filter test session with is_closed = 0
      const notClosedTestSessions = test_sessions.filter(ts => +ts.is_closed === 0)
      const distinctNotClosedTestSessionIds:any[] = [...new Set(notClosedTestSessions.map(ts => ts.id))]

      //filter test attempts in not_closed test session
      const notClosedActiveSubSessionTestAttempts  = activeSubsessionTestAttempts.filter(activeTa => distinctNotClosedTestSessionIds.includes(activeTa.test_session_id))

      // get all distinct Test Session Sub Session ids in activeSubsessionTestAttempts's active subsession
      const distinctActiveTestSessionSubSessionIds:any[] = [...new Set(notClosedActiveSubSessionTestAttempts.map(test_attempt => test_attempt.active_sub_session_id))]

      //fetch Test Session Sub Sessions in distinctActiveTestSessionSubSessionIds from redis
      const test_session_sub_sessions = await getRedisTestSessionSubSessions(this.app, distinctActiveTestSessionSubSessionIds)

      //put the result togather and push to active_sub_sessions
      notClosedActiveSubSessionTestAttempts.map( activeSubsessionTestAttempt => {
        const test_session = test_sessions.find(ts => ts.id == activeSubsessionTestAttempt.test_session_id)
        const test_session_sub_session = test_session_sub_sessions.find(tsss => tsss.id == activeSubsessionTestAttempt.active_sub_session_id)
        const active_sub_session = {
          test_session_id: activeSubsessionTestAttempt.test_session_id,
          student_uid: activeSubsessionTestAttempt.uid,
          twtdar_order: activeSubsessionTestAttempt.twtdar_order,
          active_sub_session_id: activeSubsessionTestAttempt.active_sub_session_id,
          school_class_id: school_class_id,
          session_type: test_session?.slug,
          current_session: test_session_sub_session?.caption,
          sub_session_slug: test_session_sub_session?.slug,
          sub_session_order: test_session_sub_session?.order,
        }
        active_sub_sessions.push(active_sub_session)
      })
    }else{
      active_sub_sessions = await dbRawRead(this.app, [school_class_id], `
       select ts.id as test_session_id
            , ta.uid as student_uid
            , ta.twtdar_order
            , ta.active_sub_session_id
            , scts.school_class_id
            , scts.slug as session_type
            , tsss.caption as current_session
            , tsss.slug as sub_session_slug
            , tsss.order as sub_session_order
         from mpt_dev.test_sessions ts
         join mpt_dev.test_attempts ta on ta.test_session_id = ts.id
         join mpt_dev.school_class_test_sessions scts on scts.test_session_id = ts.id
         join mpt_dev.test_session_sub_sessions tsss on tsss.id = ta.active_sub_session_id
        where ts.is_closed = 0
          and ts.is_cancelled = 0
          and scts.school_class_id = ?
          and ta.is_invalid != 1
          and ta.active_sub_session_id IS NOT NULL
      ;`);
    }

    return active_sub_sessions;
  }


  public async getCompletedSubSessions(school_class_id: number, isSecure: boolean = true, loadFromRedis = false) {
    let completed_sub_sessions:any[] = []
    if(loadFromRedis) {
      //get all the test attempts sub session in school class
      const test_attempt_sub_sessions = await getRedisTestAttemptSubSessionsFromSchoolClassId(this.app, school_class_id)
      
      //filter the test attempts sub session with submitted 
      const submitted_test_attempt_sub_sessions = test_attempt_sub_sessions.filter(tass => tass.is_submitted == 1)
      
      //get distinct test session ids in submitted_test_attempt_sub_sessions
      const distinctTestSessionIds:any[] = [...new Set(submitted_test_attempt_sub_sessions.map(submited_tass => submited_tass.test_session_id))]

      //get test sessions in distinctTestSessionIds
      const test_sessions = await getRedisTestSessions(this.app, distinctTestSessionIds)

      //get all distinct Test Session Sub Session ids in submitted_test_attempt_sub_sessions's sub session id
      const distinctTestSessionSubSessionIds:any[] = [...new Set(submitted_test_attempt_sub_sessions.map(submit_tass => submit_tass.sub_session_id))]

      //fetch Test Session Sub Sessions in distinctTestSessionSubSessionIds from redis
      const test_session_sub_sessions = await getRedisTestSessionSubSessions(this.app, distinctTestSessionSubSessionIds)

      //fetch class
      const school_class = await getRedisSchoolClass(this.app, school_class_id);

      //fetch test window allocation rules
      const test_window_id = school_class.test_window_id
      const testWindowTwtdarIds =  test_window_id ? await getTestWindowTwtdars(this.app, +test_window_id) : []
      const twtdars = await getRedisTestWindowTDAllocRules(this.app, testWindowTwtdarIds)
      
      submitted_test_attempt_sub_sessions.forEach(test_attempt_sub_session=> {
        const test_session = test_sessions.find(ts => ts.id == test_attempt_sub_session.test_session_id )
        const isSecureNum = isSecure? 1:0
        const filterTwtdar = twtdars.find( (twtdar:any) => test_session?.slug == twtdar.type_slug &&  twtdar.is_secured ==isSecureNum )
        if(!filterTwtdar) return
        const test_session_sub_session = test_session_sub_sessions.find (tsss => tsss.id == test_attempt_sub_session.sub_session_id)
        const completed_sub_session = {
          is_submitted: test_attempt_sub_session.is_submitted,
          student_uid: test_attempt_sub_session.uid,
          session_type: test_session?.slug,
          test_session_id: test_attempt_sub_session.test_session_id,
          sub_session_caption: test_session_sub_session?.caption,
          sub_session_slug: test_session_sub_session?.slug,
          sub_session_order: test_session_sub_session?.order,
        }
        completed_sub_sessions.push(completed_sub_session)
      })
    } else {
      completed_sub_sessions = await dbRawRead(this.app, [school_class_id, isSecure ? 1 : 0], `
        SELECT tass.is_submitted
             , tass.uid as student_uid
             , scts.slug as session_type
             , tass.test_session_id
             , tsss.caption as sub_session_caption
             , tsss.slug as sub_session_slug
             , tsss.order as sub_session_order
          FROM mpt_dev.school_class_test_sessions scts
          join mpt_dev.test_attempt_sub_sessions tass on tass.test_session_id = scts.test_session_id
          join mpt_dev.test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
          join mpt_dev.test_attempts ta on ta.id = tass.test_attempt_id and ta.is_invalid = 0
         where scts.school_class_id = ?
           and tass.is_submitted = 1
           and tass.is_invalid != 1
           and EXISTS (SELECT * FROM test_window_td_alloc_rules twtar WHERE twtar.type_slug = scts.slug and twtar.is_secured = ?)
      group by test_session_id, sub_session_caption, student_uid
      ;`);
    }
    return completed_sub_sessions;
  }

  public async getSubmittedSubSessions(school_class_id: number, isSecure: boolean = true) {  
    const schlStudentRole = DBD_U_ROLE_TYPES.schl_student
    const completed_sub_sessions = await dbRawRead(this.app, [schlStudentRole, isSecure ? 1 : 0, school_class_id], `
         SELECT tass.is_submitted
              , tass.uid as student_uid
              , scts.slug as session_type
              , tass.test_session_id
              , tsss.caption as sub_session_caption
              , tsss.slug as sub_session_slug
              , tsss.order as sub_session_order
           FROM school_classes sc 
           join user_roles ur on ur.group_id = sc.group_id and ur.is_revoked = 0 and ur.role_type = ?
           join school_semesters ss on ss.id = sc.semester_id
           join test_windows tw on tw.id = ss.test_window_id
           join test_sessions ts on ts.test_window_id = tw.id
           join school_class_test_sessions scts on  scts.test_session_id = ts.id
           join test_session_sub_sessions tsss on tsss.test_session_id = ts.id
           join test_attempt_sub_sessions tass on tass.sub_session_id = tsss.id and tass.is_submitted = 1 and tass.is_invalid = 0
           join test_attempts ta on ta.id = tass.test_attempt_id and ta.uid = ur.uid and ta.is_invalid = 0
     right join mpt_dev.test_window_td_alloc_rules twdtar on twdtar.type_slug = scts.slug and twdtar.is_secured = ?
           where sc.id = ?
        group by test_session_id, sub_session_caption, student_uid
    ;`);
    return completed_sub_sessions;
  }

  // for pausing
  async get(test_session_id: Id, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest();
    }
    return this.getTestSession(test_session_id, params)
  }

  async getTestSession(test_session_id: Id, params: Params){
    const { school_class_id, fromInit } = (<any>params).query;
    logger.silly({ school_class_id });

    // long polling

    // return this.getSessionInfo(id, school_class_id);
    // const testSession = await this.app.service('db/read/test-sessions').get(id);
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);

    // If client is init the UI ( teacher first time go in invigilatoer view), load data from db to redis
    // Since this end point is used for teacher heartbeat as well, only loaded into redis when the teacher/invigilator open the invigilator page.
    // if fromInit is undefined or null, it means that client is outdated and we need to force-initialize the cache every time
    const isOutdatedClient = fromInit === undefined || fromInit === null;
    const isForcedRedisLoad = isOutdatedClient && (await getSysConstNumeric(this.app, 'ALLOW_FORCE_REDIS_LOAD_ON_INIT'));
    const isSessionLoadedIntoRedisPrev = await checkRedisKey(this.app, KSessionLoadedInit(test_session_id));

    // Throw error on already closed or canceled session
    await this.checkSessionClosedOrCanceled(test_session_id, isSessionLoadedIntoRedisPrev)

    const loadIntoRedis = parseInt(fromInit) === 1 || isForcedRedisLoad || !isSessionLoadedIntoRedisPrev;
    if(loadIntoRedis){
      logger.info(`Session::ensureSessionInfoToRedis`, {test_session_id, school_class_id})
      const loadGuestClassToRedis = true; // only when teacher click into session we load the guest class info, otherwise don't load it to prevent concurrent issue
      await ensureSessionInfoToRedis(this.app, +school_class_id, loadGuestClassToRedis);
    }

    //add SessionAttemptsInit key to redis
    await addSessionAttemptsInit(this.app, test_session_id)

    const loadFromRedis = true
    await this.ensureSchoolUserRoles(school_class_id, userInfo.uid, loadFromRedis)
    const isForced = undefined
    const sessionInitResult = await this.ensureSessionInit(test_session_id, school_class_id, userInfo.uid, isForced, loadFromRedis) // function create test attempt
    
    // Pre-load the student login credential and return data into redis.
    // After the test attempt is generated, load the login credential into redis.
    if(loadIntoRedis){
      await this.ensureStudentCredToRedis(school_class_id);

      // After everything is successfully loaded, load SessionLoadedInit key to redis
      await addSessionLoadedInit(this.app, test_session_id)
    }

    return sessionInitResult
  }

  /**",
   * Pre-load studnets login cred into redis when teacher go into invigilation view
   * @param school_class_id 
   */
  async ensureStudentCredToRedis(school_class_id:number){
    //get class data, load from redis to make sure student have school user roles
    const theClass = await getRedisSchoolClass(this.app, school_class_id)
    const class_access_code:any = theClass.access_code

    // Get student cred info from redis 
    const classStudents = await this.fetchStudentCred(school_class_id)

    // Get guest students cred info from redis
    const get_guest_class_student = true;
    const classGuestStudents = await this.fetchStudentCred(school_class_id, get_guest_class_student)

    //Add guest student into student (make sure no duplicate uid)
    classGuestStudents.forEach( classGuestStudent => {
      const haveStudent = classStudents.find( classStudent => +classStudent.uid === +classGuestStudent.uid)
      if(!haveStudent){
        classStudents.push(classGuestStudent)
      }
    })

    // Add assistive_tech and linear to classStudents data
    await Promise.all( classStudents.map( async classStudent => {
      const asmt_slug = undefined
      const loadFromRedis = true
      classStudent.assistive_tech = await this.app.service('public/student/session').verifyAssistedTechAccomodation(classStudent.uid, classStudent.sch_class_group_type, asmt_slug, loadFromRedis);
      classStudent.linear = await this.app.service('public/student/session').getLinear(classStudent.uid, classStudent.sch_class_group_type, loadFromRedis);
    }))

    // Generate classStudentCredOEN and classStudentCredSASN data for this class
    const classStudentCredOEN: any = {}
    const classStudentCredSASN: any = {}
    classStudents.forEach(classStudent =>{
      const stu_oen = classStudent.oen 
      const stu_sasn = classStudent.sasn
      delete classStudent.oen
      delete classStudent.sasn
      if(stu_oen && stu_oen.trim().length && stu_oen != '#' && stu_oen != '000000000'){
        classStudent.studentOenOrSasn = stu_oen
        classStudent.isSasnLogin = 0
        const classStudentJsonString = JSON.stringify(classStudent)
        classStudentCredOEN[stu_oen] = classStudentJsonString
      }
      if(stu_sasn  && stu_sasn.trim().length){
        classStudent.studentOenOrSasn = stu_sasn
        classStudent.isSasnLogin = 1
        const classStudentJsonString = JSON.stringify(classStudent)
        classStudentCredSASN[stu_sasn] = classStudentJsonString
      }
    })                                 
    
    // Store student credition info to redis
    const redis: Redis = this.app.get('redis');
    if(Object.keys(classStudentCredOEN).length){
      const redis_key = KClassStuCredOEN(class_access_code)
      await redis.pipeline()
       .hset(redis_key, classStudentCredOEN)
       .expire(redis_key, REDIS_ASSESSMENT_DATA_EXPIRE)
       .exec();
    }
    if(Object.keys(classStudentCredSASN).length){
      const redis_key = KClassStuCredSASN(class_access_code)
      await redis.pipeline()
       .hset(redis_key, classStudentCredSASN)
       .expire(redis_key, REDIS_ASSESSMENT_DATA_EXPIRE)
       .exec();
    }
  }

  /**",
   * fetch the student login data from the redis
   * @param school_class_id
   * @param get_guest_class_student get the class students or guest class students ( true is guest students,  false is class students) 
   * @return arrays of student login data.
   */
  async fetchStudentCred (school_class_id:number, get_guest_class_student: boolean = false){
    //load from redis
    const schoolClass = await getRedisSchoolClass(this.app, school_class_id)
    const classStudentUIDs:any[] = [];

    if(get_guest_class_student){
      const guestClassIds = await getClassGuestClasses(this.app, school_class_id) 
      await Promise.all(guestClassIds.map(async (guestClassId:any) => {
        const guestClassUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, guestClassId)
        const guestClassStudentUserRoles = guestClassUserRoles.filter(classUserRole => classUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
        guestClassStudentUserRoles.forEach(classStudentUserRole => {
          classStudentUIDs.push(classStudentUserRole.uid)
        });
      }))
    }else{
      const classUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, school_class_id)
      const classStudentUserRoles = classUserRoles.filter(classUserRole => classUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
      classStudentUserRoles.forEach(classStudentUserRole => {
        classStudentUIDs.push(classStudentUserRole.uid)
      });
    }

    let classStudents:any[] = [];
    await Promise.all(classStudentUIDs.map(async classStudentUID =>{
      const user = await getRedisUser(this.app, classStudentUID)
      const userMetaLang = await getRedisUserMetaLang(this.app, classStudentUID)
      const userMetaOEN = await getRedisUserMetaOEN(this.app, classStudentUID)
      const userMetaSASN = await getRedisUserMetaSASN(this.app, classStudentUID)
      const classStudent = {
        ...user,
        uid: user.id,
        accountType: user.account_type,
        sch_class_group_id: schoolClass.group_id,
        sch_class_id: schoolClass.id,
        sch_class_group_type: schoolClass.group_type,
        lang: userMetaLang||'',
        oen: userMetaOEN||'',
        sasn: userMetaSASN||'', 
      }
      classStudents.push(classStudent)
    }))
    
    return classStudents
  }

  async ensureSessionInit(test_session_id:Id, school_class_id:number, created_by_uid?:number, isForced?:boolean, loadFromRedis = false){
    let testSession:any= {};
    if(loadFromRedis) {
      const redisTestSession: Partial<RedisKeyTestSession> = await getRedisTestSession(this.app, test_session_id);
      testSession = redisTestSession
    } else {
      const db = this.app.get('knexClientRead');
      testSession = (await db('test_sessions as ts')
        .join('school_class_test_sessions as scts', 'scts.test_session_id', 'ts.id')
        .where('ts.id', test_session_id)
        .select('ts.*', 'scts.slug'))[0]
    }
    const activeSubSessions = await this.getActiveSubSessions( school_class_id, loadFromRedis );
    const isSecure = true;
    const completedSubSessions = await this.getCompletedSubSessions(school_class_id, isSecure, loadFromRedis);
    //const schl_group_id = await this.getSchoolGroupByClassroomId( school_class_id );
    // TODO this is untested but should be enabled soon - zsw 20220529
    // await this.ensureStudentLang(school_class_group_id, schl_group_id);
    const subSessions = await this.app.service('public/educator/session-sub').getSubSessions(test_session_id, isForced, loadFromRedis);

    const is_no_td_order = await this.getIsNoTdOrder(test_session_id, loadFromRedis);
    const is_paused = await isTestSessionPaused(this.app, test_session_id);
    return <any>{
      is_paused,
      paused_by_user: await this.getPausedByUserName(testSession.paused_by_uid),
      id: testSession.id,
      date_time_start: (testSession.date_time_start),
      activeSubSessions,
      completedSubSessions,
      subSessions,
      is_no_td_order,
      asmt_slug: testSession.slug,
      auto_close_after: testSession.auto_close_after
    }
  }

  /**
   * Returns first initial + last name of user that paused session
   */
 async getPausedByUserName(uid:number) {
  const pausedByUser = await getRedisUser(this.app, uid)
  const pausedByUserFirstNameInitial = pausedByUser?.first_name?.[0] + '.'
  return pausedByUser.id ? pausedByUserFirstNameInitial + " " + pausedByUser.last_name : null
 }

  /**
   * Patch test session to set is_paused
   */
  async patch(id: NullableId, data: Data, params?: Params): Promise<any> {
    const { school_class_group_id } = (<any>params).query;
    if(!params){
      throw new Errors.BadRequest();
    }
    const { is_paused } = <any>data;
    const uid = await currentUid(this.app, params);
    return  await this.updateIsPaused(id, data, is_paused, uid)
  }

  /**
   * Handles updating test_sessions.is_paused and test_sessions.paused_by_uid in redis and db
   */
  async updateIsPaused(testSessionId: NullableId, data: Data, isPaused: boolean, uid: number) {
    if (testSessionId !== null) {
      
      const payload: any = {};
      if (isPaused !== undefined) {
        payload.is_paused = isPaused ? 1 : 0;
        payload.paused_by_uid = isPaused ? uid : null
      }
      await patchTestSession(this.app, testSessionId, payload);
      return {
        pausedByUser: await this.getPausedByUserName(payload.paused_by_uid)
      }
    } else {
      // Handle the case when testSessionId is null
      throw new Errors.BadRequest('ERR_NO_ID');
    }
  }

  // async generateStudentReportsForTestSession(test_session_id: number, created_by_uid: number) {
  //   const testAttempts = await dbRawRead(this.app, [test_session_id],
  //   ` SELECT ta.id
  //     FROM test_attempts ta
  //     JOIN test_sessions ts ON ta.test_session_id = ts.id
  //     JOIN test_forms tf ON tf.id = ta.test_form_id
  //     JOIN test_window_td_alloc_rules twtdar ON ts.test_window_id = twtdar.test_window_id
  //     AND tf.test_design_id = twtdar.test_design_id
  //     WHERE ta.test_session_id = ?
  //     AND twtdar.generate_report = 1
  //   `);
  //   for (let testAttempt of testAttempts){
  //     this.app.service('public/educator/student-report')
  //       .generateReportByAttemptId(
  //         testAttempt.id,
  //         created_by_uid,
  //         true
  //       );
  //   }
  // }

  // #2021-11-16-unclear-conflict
  async closeAttemptAndSubmitSubsessions(test_session_id:number){

    //get all the test attempt subsession in the test session
    const activeStudentsTestAttemptSubsession = await dbRawRead(this.app, [test_session_id],
      `SELECT * FROM mpt_dev.test_attempt_sub_sessions
        where test_session_id= ?
          and is_invalid != 1
    `);

    // submit test attempt sub session if student have touch it
    activeStudentsTestAttemptSubsession.forEach( async attemptsubsession =>{
      if(attemptsubsession.started_on != null || attemptsubsession.last_touch_on != null){
        if(attemptsubsession.is_submitted != 1){
          patchTestAttemptSubSession(this.app, attemptsubsession.id, {is_submitted: 1})
        }
      }
    })

    //get all the test attempt
    const activetestAttempts = await dbRawRead(this.app, [test_session_id],
      `SELECT * FROM mpt_dev.test_attempts
        where test_session_id= ?
          and is_invalid != 1
    ;`)

    const activetestAttemptIds = activetestAttempts.map(activetestAttempt => {return activetestAttempt.id})
    const allTestAttemptSubsessions = await dbRawRead(this.app, {activetestAttemptIds},`
      SELECT tass.*
           , tsss.order as tsss_order
        FROM mpt_dev.test_attempt_sub_sessions tass
        join mpt_dev.test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
       where tass.test_attempt_id in (:activetestAttemptIds)
    ;`);
     
    const submitTestAttemptIds:number[] = []
    const noSubmitTestAttemptIds:number[] = []
    const twtdarOrderZeroAttemptIds:number[] = []
    const closed_on = new Date().toISOString()
    activetestAttempts.forEach( async attempt =>{
      const testAttemptSubsessions = allTestAttemptSubsessions.filter(allTestAttemptSubsession => +allTestAttemptSubsession.test_attempt_id === +attempt.id)
      let lastTestAttemptSubsession:any;
      testAttemptSubsessions.forEach( tass =>{
        if(!lastTestAttemptSubsession || lastTestAttemptSubsession.tsss_order < tass.tsss_order ){
          lastTestAttemptSubsession = tass
        }
      })

      // 1. submit test attempt if student touch it and last test attemptsubsession is submitted
      // 2. only close the test attempt if the student have never touch it
      // 3. do nothing if the student have touch the test but does not touch the last sub session
      if((attempt.started_on != null || attempt.last_touch_on != null) && lastTestAttemptSubsession && lastTestAttemptSubsession.is_submitted == 1){
        submitTestAttemptIds.push( attempt.id)
      }else if (attempt.started_on == null && attempt.last_touch_on == null) {
        noSubmitTestAttemptIds.push(attempt.id)
      }

      // mark the tasr (scanning) records that were uploaded as test_session_submitted
      if (attempt.twtdar_order === 0) {
        twtdarOrderZeroAttemptIds.push(attempt.id)
      }
    })

    //bulk update submitTestAttempts and redis data
    if(submitTestAttemptIds.length){
      await dbRawWrite(this.app, [closed_on, test_session_id, submitTestAttemptIds], `
        UPDATE test_attempts
        SET is_submitted = 1
          , is_closed = 1
          , closed_on = ?
          , submitted_test_session_id = ?
        WHERE id IN (?)
      ;`);
    }
    for(let submitTestAttemptIdsChunk of _.chunk(submitTestAttemptIds,5)){
      await Promise.all(submitTestAttemptIdsChunk.map(async submitTestAttemptId =>{
        const payload = {
          is_submitted: 1,
          is_closed: 1,
          closed_on,
          submitted_test_session_id: test_session_id
        }
        patchRedisTestAttempt(this.app, submitTestAttemptId, payload)
      }))    
    }

    //bulk update noSubmitTestAttemptIds and redis data
    if(noSubmitTestAttemptIds.length){
      await dbRawWrite(this.app, [closed_on, noSubmitTestAttemptIds], `
        UPDATE test_attempts
        SET is_closed = 1
          , closed_on = ?
        WHERE id IN (?)
      ;`);
    }
    for(let noSubmitTestAttemptIdsChunk of _.chunk(noSubmitTestAttemptIds,5)){
      await Promise.all(noSubmitTestAttemptIdsChunk.map(async noSubmitTestAttemptId =>{
        const payload = {
          is_closed: 1,
          closed_on
        }
        patchRedisTestAttempt(this.app, noSubmitTestAttemptId, payload)
      }))    
    }
    
    //bulk load tasrRecords
    const tasrRecords = await dbRawRead(this.app, {twtdarOrderZeroAttemptIds},`
      SELECT tasr.* 
        from test_attempt_scan_responses tasr
        join test_attempt_question_responses taqr on tasr.taqr_id = taqr.id
       where taqr.test_attempt_id in (:twtdarOrderZeroAttemptIds)
         and tasr.is_discarded != 1
    ;`);
    //bulk update tasrRecords
    const tarsIds = tasrRecords.map(tasrRecord => tasrRecord.id)
    if(tarsIds.length){
      await dbRawWrite(this.app, [closed_on, tarsIds], `
        UPDATE test_attempt_scan_responses
           SET is_test_session_submitted = 1
         WHERE id IN (?)
      ;`);
    }
  }
  // end of #2021-11-16-unclear-conflict

  async remove(id: NullableId, params?: Params): Promise<Data> {
    const { school_class_group_id,is_session_completed } = (<any>params).query;
    if (id && params) {
      this.closeAttemptAndSubmitSubsessions(<number>id);
      const uid = await currentUid(this.app, params);
      await this.app
        .service('db/write/test-sessions')
        .patch(id, {
          is_closed: 1,
          closed_on: dbDateNow(this.app),
        });
      
        //update redis test session is_closed to 1
      await patchRedisTestSession(this.app, +id, {is_closed:1})

      if(is_session_completed){
        // NO LONGER AUTO-GENERATING REPORTS!
        // const scTestSessions:{slug:string, school_class_id:number}[] = await this.app
        //   .service('db/read/school-class-test-sessions')
        //   .db()
        //   .where('test_session_id', id);
        // const scTestSession = scTestSessions[0];
        // this.generateStudentReportsForTestSession(<number>id, uid);
        // disabling auto
        // try {
        //   this.app
        //     .service('public/educator/report-results')
        //     .ensureStudentReports(
        //       scTestSession.school_class_id,
        //       scTestSession.slug,
        //       uid
        //     )
        // }
        // catch(e){
        //   logger.info(e as any);
        // }
      }
      return <any>{ id }
    }
    throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
  }

  async getIsNoTdOrder(test_session_id: Id, loadFromRedis = false) {
    let isNoTdOrderRes:any[] = []
    if(loadFromRedis){
      //get test session 
      const testSession = await getRedisTestSession(this.app, test_session_id)

      //get all test windows assessment component
      const test_window_id = testSession.test_window_id
      const TestWindowAssessmentComponentIds = test_window_id?await getTestWindowAssessmentComponents(this.app, test_window_id):[]
      const assessmentComponents = await getRedisAssessmentComponentes(this.app, TestWindowAssessmentComponentIds)

      //filter assessment component with test session slug
      const filteredAssessmentComponenets = assessmentComponents.filter((assessmentComponent:any) => assessmentComponent.assessment_code === testSession.slug)

      //generate isNoTdOrderRes
      filteredAssessmentComponenets.forEach( (filteredAssessmentComponenet:any) =>{
        const isNoTdOrde = {
          is_no_td_order: filteredAssessmentComponenet.is_no_td_order,
        }
        isNoTdOrderRes.push(isNoTdOrde)  
      }) 

    }else{
      isNoTdOrderRes = await dbRawRead(this.app, [test_session_id], `
        SELECT ac.is_no_td_order 
          FROM assessment_components ac
          JOIN school_class_test_sessions scts ON ac.assessment_code = scts.slug
          JOIN test_sessions ts ON scts.test_session_id = ts.id
         WHERE ts.id = ?
           AND ac.test_window_id = ts.test_window_id
        `)
    }

    if(isNoTdOrderRes?.length) {
      return isNoTdOrderRes[0].is_no_td_order;
    }
    return undefined;
  }



  async createSessionForEducator(created_by_uid: number, data: ISession, school_class_group_id: number, isForced:boolean=false) {

    //const test_ctrl_group_id = 5403; // hard coded to G9 for now
    const delivery_format = EDeliveryOption.SCHOOL;
    const { school_class_id, slug, caption, isRemovePrev, isScheduled, is_fi} = data;

    //block sandbox school from schedule operational test
    await this.blockSandBoxSchoolOperTest(school_class_id, slug);

    //validate all scheduled_time in data
    data.scheduled_time?.forEach(scheduled_time =>{
      // only validate if scheduled_time is defined
      // New session with only session B need to be schedule (Due to all student finished A already)
      // In this case  data.scheduled_time[0] will be undefined/null and no need to check if its valid or not.
      if(scheduled_time){
        this.SCHOOL_ADMIN_SESSION.validateScheduleTime(scheduled_time);
      }
    })
    
    const classes_sessions = await dbRawRead(this.app, [school_class_id, slug], `
      select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, ts.access_code
      from school_class_test_sessions scts
      join test_sessions ts on ts.id = scts.test_session_id
      where scts.school_class_id = ?
        and ts.is_cancelled = 0
        and ts.is_closed = 0
        and scts.slug = ?
      ;`);

    if (classes_sessions.length > 0 && !isForced) {
      if (slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE' || slug === 'G9_SAMPLE' || slug === 'OSSLT_SAMPLE') {
        throw new Errors.BadRequest('ONGOING_SAMPLE_ASSESSMENT');
      } else if (slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_OPERATIONAL') {
        throw new Errors.BadRequest('ONGOING_LIVE_ASSESSMENT');
      }
    }

    if(data.scheduled_time?.length >= 2 && !data.scheduled_time[0]) {
      data.scheduled_time[0] = data.scheduled_time[1];
    // if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
    //   if(data.scheduled_time?.length >= 2 && !data.scheduled_time[0]) {
    //     data.scheduled_time[0] = data.scheduled_time[1];
    //   }
    }
    // check to see if this is a scheduled session

    let date_time_start
    if (isScheduled) {
      if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
        // validate if test session scheduled time is within tw start and end time for operational
        if(this.SCHOOL_ADMIN_SESSION.mustValidateScheduledTimeAssessments(slug)) {
          await this.SCHOOL_ADMIN_SESSION.checkSessionStartTime(slug, data.scheduled_time, school_class_id); 
        }
      
        const scheduledStandardTimeMoment = moment.tz(data.scheduled_time[0] || data.scheduled_time[1], STANDARD_TIMEZONE);
        const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
        const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
        date_time_start = dbDateSetDateTime(this.app, 0 ,scheduledUTCTime)
      }
      if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL'){
        let startTimeMoment;
        let scheduledTimes: string[] = [];
        // if Language scheduled only
        if(data.scheduled_time[0] && data.scheduled_time[1] && !data.scheduled_time[2] && !data.scheduled_time[3]){
          scheduledTimes = [this.SCHOOL_ADMIN_SESSION.addHMtoPJScheduleTime(data.scheduled_time[0]), this.SCHOOL_ADMIN_SESSION.addHMtoPJScheduleTime(data.scheduled_time[1])];
        }
        // if Math scheduled only
        if(!data.scheduled_time[0] && !data.scheduled_time[1] && data.scheduled_time[2] && data.scheduled_time[3]){
          scheduledTimes = [this.SCHOOL_ADMIN_SESSION.addHMtoPJScheduleTime(data.scheduled_time[2]), this.SCHOOL_ADMIN_SESSION.addHMtoPJScheduleTime(data.scheduled_time[3])];
        }
        // if Language and Math both scheduled
        if(data.scheduled_time[0] && data.scheduled_time[1] && data.scheduled_time[2] && data.scheduled_time[3]){
          scheduledTimes = data.scheduled_time.map(time => this.SCHOOL_ADMIN_SESSION.addHMtoPJScheduleTime(time));
        }

        if(this.SCHOOL_ADMIN_SESSION.mustValidateScheduledTimeAssessments(slug)) {
          await this.SCHOOL_ADMIN_SESSION.checkSessionStartTime(slug, scheduledTimes, school_class_id); 
        }

        if(data.scheduled_time[0] && data.scheduled_time[2]) {
          startTimeMoment = (data.scheduled_time[0] <= data.scheduled_time[2]) ? data.scheduled_time[0] : data.scheduled_time[2];
        }
        if(data.scheduled_time[0] && !data.scheduled_time[2]) {
          startTimeMoment = data.scheduled_time[0];
        }
        if(!data.scheduled_time[0] && data.scheduled_time[2]) {
          startTimeMoment = data.scheduled_time[2];
        }
        const scheduledStandardStartTimeMoment = moment.tz(startTimeMoment, STANDARD_TIMEZONE);
        const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
        const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm');
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      }
    }
    else {
      date_time_start = dbDateNow(this.app);
    }

    const schl_group_id = await this.getSchoolGroupByClassroomId(school_class_id);
    await this.ensureSchoolUserRoles(+school_class_id, created_by_uid)
    let test_window_id = await this.getActiveTestWindowForClass(school_class_id); // default for now
    
    this.app.service('public/school-admin/student').checkStudentInfoLock(test_window_id);

    // TODO this is untested but should be enabled soon - zsw 20220529
    await this.ensureStudentLang(school_class_group_id, schl_group_id);

    const test_window_td_alloc_rules = await dbRawRead(this.app, [test_window_id, slug], `
     select *
      from test_window_td_alloc_rules
      where test_window_id = ?
        and type_slug = ?
    ;`);

    let isAnySecured = false;
    test_window_td_alloc_rules.forEach(rule => {
      if (rule.is_secured){
        isAnySecured = true;
      }
    })
    if (isAnySecured && !isForced) {
      await this.validateTechnicalReadinessStatus(schl_group_id,slug);
    }

    const testSessionGroup = await this.app
      .service('db/write/u-groups')
      .create({
        group_type: DBD_U_GROUP_TYPES.mpt_test_session,
        created_by_uid,
      });
    const test_session_group_id = testSessionGroup.id;

    const access_code = ''; // Test session acces code is deprecated
    const testSession = await this.app
      .service('db/write/test-sessions')
      .create({
        test_session_group_id,
        test_window_id,
        schl_group_id,
        delivery_format,
        date_time_start,
        access_code,
        is_access_code_enabled: false
      })
    const test_session_id = testSession.id;
    await this.app
      .service('db/write/school-class-test-sessions')
      .create({
        school_class_id,
        test_session_id,
        slug,
        caption,
      });
     let subSessionRecords
     if(isScheduled){
      subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, data.scheduled_time)
     }
     else{
      subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined, is_fi)
     }

    await this.ensureSchool_class_common_forms(test_session_id, created_by_uid);

    return <any>{
      ...subSessionRecords,
      test_session_id,
      date_time_start: testSession.date_time_start,
      school_class_id,
      slug,
      caption,
      access_code,
      test_window_id
    };
  }

  async createSessionForSchool(created_by_uid: number, data: ISession, assessmentType: AssessmentType, testWindowId?: number) {
    const test_ctrl_group_id = getTestControllerGroupId(assessmentType); // hard coded to G9 for now
      const delivery_format = EDeliveryOption.SCHOOL;
      const { school_class_id, slug, caption, isRemovePrev, isScheduled } = data;

      // check to see if this is a scheduled session
      let date_time_start
      if (isScheduled) {
        date_time_start = dbDateSetDateTime(this.app, 4 ,data.scheduled_time[0] || data.scheduled_time[1])
      }
      else {
        date_time_start = dbDateNow(this.app);
      }

      const schl_group_id = await this.getSchoolGroupByClassroomId(school_class_id);
      // await this.ensureStudentLang(schl_group_id); // todo: this can be long running!
      let test_window_id;
      if (testWindowId) {
        test_window_id = testWindowId;
      } else {
        const activeTestWindows = await this.app.service('public/bc-admin-coordinator/test-window').findCurrentTestWindows(assessmentType);
        if (activeTestWindows.length === 0) throw new Error('No active test windows.');
        test_window_id = activeTestWindows[0].id;
      }

      // let test_window_id = await this.getActiveTestWindowForClass(school_class_id, test_ctrl_group_id); // default for now
      logger.silly('creating test session for test window %s', test_window_id)
      const test_window_td_alloc_rules = await dbRawRead(this.app, [test_window_id, slug], `
       select twtdar.*, ac.is_no_td_order
        from test_window_td_alloc_rules twtdar
        left join assessment_components ac
        ON ac.assessment_code = twtdar.type_slug
        AND ac.test_window_id = twtdar.test_window_id
        where test_window_id = ?
          and type_slug = ?
      ;`);

      // const is_no_td_order = !!test_window_td_alloc_rules[0].is_no_td_order;  //may need this
      let isAnySecured = false;
      test_window_td_alloc_rules.forEach(rule => {
        if (rule.is_secured){
          isAnySecured = true;
        }
      })
      if (isAnySecured) {
        await this.validateTechnicalReadinessStatus(schl_group_id,slug);
      }

      const testSessionGroup = await this.app
        .service('db/write/u-groups')
        .create({
          group_type: DBD_U_GROUP_TYPES.mpt_test_session,
          created_by_uid,
        });
      const test_session_group_id = testSessionGroup.id;

      const access_code = ''; // Test session acces code is deprecated
      const testSession = await this.app
        .service('db/write/test-sessions')
        .create({
          test_session_group_id,
          test_window_id,
          schl_group_id,
          delivery_format,
          date_time_start,
          access_code,
          is_access_code_enabled: false
        })
      const test_session_id = testSession.id;
      logger.silly('created test session id %s', test_session_id);
      await this.app
        .service('db/write/school-class-test-sessions')
        .create({
          school_class_id,
          test_session_id,
          slug,
          caption,
        });
       let subSessionRecords
       if(isScheduled){
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, data.scheduled_time)
       }
       else{
        logger.silly('init subsession');
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined)
       }

      return <any>{
        ...subSessionRecords,
        test_session_id,
        date_time_start: testSession.date_time_start,
        school_class_id,
        slug,
        caption,
        access_code
      };
  }

  async getSessionInfo(id: Id, school_class_id: number, isSecure: boolean = true) {
    const testSession = await this.app.service('db/read/test-sessions').get(id);
    const activeSubSessions = await this.getActiveSubSessions(school_class_id);
    const completedSubSessions = await this.getCompletedSubSessions(school_class_id, isSecure);
    const schl_group_id = await this.getSchoolGroupByClassroomId(school_class_id);
    // await this.ensureStudentLang(schl_group_id);
    const subSessions = await this.app.service('public/educator/session-sub').getSubSessions(id);
    const is_paused = await isTestSessionPaused(this.app, id);
    
    return <any>{
      is_paused,
      id:testSession.id,
      date_time_start: (testSession.date_time_start),
      activeSubSessions,
      completedSubSessions,
      subSessions,
    }
  }

  async ensureSchool_class_common_forms(test_session_id:any, created_by_uid:any){
    const testSession = await this.app.service('db/read/test-sessions').get(test_session_id);
    const schoolClassTestSession = await dbRawRead(this.app, [testSession.id], `
      select *
        from school_class_test_sessions scts
       where scts.test_session_id = ?
      ;`);
    const schoolClassID = schoolClassTestSession[0].school_class_id;
    const test_window_td_alloc_rules = await dbRawRead(this.app, [testSession.test_window_id], `
      select *
        from test_window_td_alloc_rules twtdar
       where twtdar.test_window_id = ?
         and twtdar.is_classroom_common_form = 1
      ;`);

    test_window_td_alloc_rules.forEach( async twtdar => {
      const school_class_common_form = await dbRawRead(this.app, [schoolClassID ,twtdar.id], `
        select *
          from school_class_common_forms sccf
         where sccf.school_class_id = ?
           and sccf.twtdar_id = ?
           and sccf.is_revoked != 1
        ;`);

      if(school_class_common_form.length === 0){
        const testFormRefs = <Paginated<any>> await this.app
          .service('db/read/test-forms')
          .find({
            query: {
              $select: ['id', 'lang'],
              test_design_id: twtdar.test_design_id,
              is_revoked: 0,
              $limit: 1000,
            }
          });

        const testFormSelection = randArrEntry(testFormRefs.data);

        await this.app
        .service('db/write/school-class-common-forms')
        .create({
          school_class_id:schoolClassID,
          twtdar_id: twtdar.id,
          test_form_id:testFormSelection.id,
          created_by_uid: created_by_uid,
          is_revoked:0
        });
      }
    })
  }
/**
 * Check if the test session has already been closed or canceled
 * This is to prevent multi-tabs user sending heartbeat request from an inactive test session
 * @param test_session_id 
 * @param isSessionLoadedIntoRedisPrev
 */
  async checkSessionClosedOrCanceled(test_session_id: Id, isSessionLoadedIntoRedisPrev: boolean) {
    const istestSessionInRedis = await getRedisTestSession(this.app, test_session_id)
    const isSessionInRedisClosed = istestSessionInRedis?.is_closed

    if ( isSessionInRedisClosed !== 0 || !isSessionLoadedIntoRedisPrev ){
      let testSession:any= {};
      const db = this.app.get('knexClientRead'); // this db check won't be heavy since it only checks when there is no session record in redis(invigilator first time open tab) or when the session was just closed.
      testSession = (await db('test_sessions as ts')
        .where('ts.id', test_session_id)
        .select('ts.is_closed', 'ts.is_cancelled'))[0]
      
      if ( testSession.is_closed === 1 || testSession.is_cancelled === 1 ){
        throw new Errors.BadRequest('TEST_SESSION_IS_NOT_ACTIVE')
      }
    }
  }

  /**
   * Block sandbox school from scheduling operational class
   * @param school_class_id 
   */
  async blockSandBoxSchoolOperTest(school_class_id: number, slug:string){
    const classInfo = await dbRawReadReporting(this.app, {school_class_id}, `
      -- 78 ms
      select schl.is_sandbox
        from school_classes sc
        join schools schl on schl.group_id = sc.schl_group_id
       where sc.id = :school_class_id
    ;`)
    const isSandboxSchl = !!classInfo[0]?.is_sandbox
    if(isSandboxSchl && slug.includes("OPERATIONAL")){
      throw new Errors.BadRequest("SANDBOX_SCHL_NOT_ALLOW_OPERATIONAL_TEST")
    }
  }
}
