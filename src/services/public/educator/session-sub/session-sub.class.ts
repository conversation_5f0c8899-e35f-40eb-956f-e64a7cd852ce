import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { getPropVals } from '../../../../util/param-sanitization';
import { dbBulkInsert, dbRawRead, dbRawReadReporting, dbRawWrite, dbRawWriteMulti } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { dbDateSetDateTime, dbDateNow } from '../../../../util/db-dates';
import moment from 'moment';
import { Knex } from 'knex';
import absenceReportService from '../../test-admin/invigilation/absence-report/absence-report.service';
import logger from '../../../../logger';
import Redis from 'ioredis';
import _ from 'lodash';
import { currentUid } from '../../../../util/uid';
import { KAttemptPos, KSubmData, KTestSession, KTestSessionInit, TESTSESSION_INIT_REDLOCK_TIMEOUT, checkRedisKey, getRedisLock } from '../../../../redis/redis';
import { getRedisTestAttemptSubSessionsFromTestSessionId, patchRedisTestAttemptSubSession } from '../../../../redis/table-key-operations/test-attempt-sub-session'
import { getRedisTestAttemptsFromTestSessionId, patchRedisTestAttempt, getRedisTestAttemptsFromSchoolClassId, isTestAttemptPaused } from '../../../../redis/table-key-operations/test-attempt'
import { getRedisTestSession, getRedisTestSessions, patchTestSession } from '../../../../redis/table-key-operations/test-session';
import { RedisKeyTestSessionSubSession, createRedisTestSessionSubSession, getRedisTestSessionSubSession, getRedisTestSessionSubSessions, getRedisTestSessionSubSessionsFromTestSessiontId, patchTestSessionSubSession } from '../../../../redis/table-key-operations/test-session-sub-session';
import { getRedisClassUserRolesFromSchoolClassId } from '../../../../redis/table-key-operations/user-role';
import { getClassGuestClasses } from '../../../../redis/relation-key-operations/class-guest-classes';
import { getRedisTestWindowTDAllocRules } from '../../../../redis/table-key-operations/test-window-td-alloc-rule';
import { getTestWindowTwtdars } from '../../../../redis/relation-key-operations/test-window-twtdars';
import { getTestSessionTestAttemptUnsubmissions } from '../../../../redis/relation-key-operations/test-session-test-attempt-unsubmissions';
import { getRedisTestAttemptUnsubmissiones } from '../../../../redis/table-key-operations/test-attempt-unsubmission';
import { ensureSessionInfoToRedis } from '../../../../redis/inits/educator-session-init';
import { getTestSessionTATrasferRequireUids } from '../../../../redis/relation-key-operations/test-session-ta-trans-req-uids';
import { randArrEntry } from '../../../../util/random';

const DEFAULT_SESSION_DURATION_HOURS = 4;
const STANDARD_TIMEZONE = 'America/Toronto';

interface Data { }

interface ISubSessionPartial{
  session_rescheduled:boolean,
  test_session_id:string,
  date_time_start:string,
  date_time_end?:string,
  pjSessionStartDate?:any,
  pjSessionEndDate?:any,
  asmtSlug:string,
}
export interface TestDesignSimilarityTag {
  test_design_id: number;
  similarity_tag: string|undefined;
}

interface ServiceOptions { }

export class SessionSub implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }



  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  get SCHOOL_ADMIN_SESSION() {
    return this.app.service('public/school-admin/session');
  }

  stringToMomentUTC(date: string) {
    return moment.tz(date, STANDARD_TIMEZONE).utc();
  }

  /**
   * This end point is used for update session/subsession start/end time
   * @param sub_session_id 
   * @param data 
   * @param params 
   * @returns 
   */
  async update(sub_session_id: NullableId, data: ISubSessionPartial, params?: Params): Promise<Data> {
    if (!sub_session_id) {
      throw new Errors.BadRequest('ERR_MISSING_ID');
    }

    if (!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const { school_class_id } = params.query

    const {
      test_session_id,
      session_rescheduled,
      pjSessionStartDate,
      pjSessionEndDate,
      asmtSlug
    } = data;

    let sessionSchedule;

    //validate date_time_start in data
    if(data?.date_time_start){
      this.SCHOOL_ADMIN_SESSION.validateScheduleTime(data?.date_time_start);
    }

    // data.date_time_start and data.date_time_end for G9 and OSSLT
    const scheduledG9OSSLTStartUTCTime = this.stringToMomentUTC(data.date_time_start);

    // pjSessionStartDate and pjSessionEndDate for PJ
    const scheduledPJStartUTCTime = this.stringToMomentUTC(pjSessionStartDate);
    const scheduledPJEndUTCTime = this.stringToMomentUTC(pjSessionEndDate);

    //pj subsession start/end Date
    const scheduledPJSubSessionStartUTCTime = this.stringToMomentUTC(data.date_time_start);
    const scheduledPJSubSessionEndUTCTime = data.date_time_end ? this.stringToMomentUTC(data.date_time_end) : undefined

    //check if the start end time is within test window start end time
    const test_windows = await this.getData([test_session_id] , `
        select tw.*
          from test_sessions ts
          join test_windows tw on tw.id = ts.test_window_id
         where ts.id = ?
    ;`);
    if(test_windows.length ==  0){
      throw new Errors.BadRequest("TEST_WINDOW_NOT_FOUND");
    }

    const test_window = test_windows[0]
    const twStartDateUTC = this.stringToMomentUTC(test_window.date_start);
    const twEndDateUTC = this.stringToMomentUTC(test_window.date_end);

    // only operational assessment need to be check scheduled time within test window
    if (this.SCHOOL_ADMIN_SESSION.mustValidateScheduledTimeAssessments(asmtSlug)) {
      const slugs = ['G9_OPERATIONAL', 'OSSLT_OPERATIONAL'];
      // Validate only Start Date Time for G9 and OSSLT
      if (slugs.indexOf(asmtSlug) > -1) {
        if (scheduledG9OSSLTStartUTCTime < twStartDateUTC || scheduledG9OSSLTStartUTCTime > twEndDateUTC)
          throw new Errors.BadRequest("MSG_ADMIN_WINDOW_WARNING");

      } else { // Validate Start and End Date for PJ
        const pj = ['PRIMARY_OPERATIONAL', 'JUNIOR_OPERATIONAL'];
        if (pj.indexOf(asmtSlug) > -1) {
          if (pjSessionStartDate < twStartDateUTC || pjSessionStartDate > twEndDateUTC
              || pjSessionEndDate < twStartDateUTC || pjSessionEndDate > twEndDateUTC) {
            throw new Errors.BadRequest("MSG_ADMIN_WINDOW_WARNING");
          }
        } else {
          if (scheduledPJStartUTCTime < twStartDateUTC || scheduledPJStartUTCTime > twEndDateUTC
            || scheduledPJEndUTCTime < twStartDateUTC || scheduledPJEndUTCTime > twEndDateUTC) {
            throw new Errors.BadRequest("MSG_ADMIN_WINDOW_WARNING");
          }
        }
      }
    }

    let session_date_time_start, session_date_time_end;
    let subsession_date_time_start, subsession_date_time_end;

    // for G9 and OSSLT, only start time needed, date_time_start is subsession start time (and session start time if this subsession is first subsession which session_rescheduled will be true )
    session_date_time_start = scheduledG9OSSLTStartUTCTime.toISOString();
    subsession_date_time_start = scheduledG9OSSLTStartUTCTime.toISOString(); 

    // for PJ, both start time and end time needed
    const pjSlugs = ['PRIMARY_SAMPLE', 'PRIMARY_OPERATIONAL', 'JUNIOR_SAMPLE', 'JUNIOR_OPERATIONAL'];
    if(pjSlugs.indexOf(asmtSlug) > -1){
      session_date_time_start = scheduledPJStartUTCTime.toISOString();
      session_date_time_end = scheduledPJEndUTCTime.toISOString();
      subsession_date_time_start = scheduledPJSubSessionStartUTCTime.toISOString();
      subsession_date_time_end = scheduledPJSubSessionEndUTCTime?.toISOString();
    }

    if(session_rescheduled){
      patchTestSession(this.app, +test_session_id, {date_time_start:session_date_time_start, date_time_end: session_date_time_end})
    }

    await patchTestSessionSubSession(this.app, sub_session_id, {datetime_start: subsession_date_time_start, datetime_end: subsession_date_time_end})
    const subsessionRecord = await getRedisTestSessionSubSession(this.app, sub_session_id)

    // Reschedule by educator
    // If the session is happening soon enough, students in the class may have approved alt requests that should be auto-operational sent
    // It will log any errors in db so don't await
    if (session_rescheduled && asmtSlug.includes('OPERATIONAL') && school_class_id && session_date_time_start && test_window.id){
      const isClassOpSendRequired = await this.app.service('public/alt-version-ctrl/alt-version-requests').isClassOpSendRequired(+school_class_id, session_date_time_start, test_window.id)
      if (isClassOpSendRequired) {
        this.app.service('public/alt-version-ctrl/alt-version-requests').handleOpAutoSend({school_class_id})
      }
    }

    return {
      sessionSchedule,
      subsessionRecord,
    }
  }

  async patch(id: NullableId, data: { subSessionId: number, twtdarOrder: number, classId: number, isClosing: boolean, studentUids: number[], isPJ:boolean}, params?: Params,): Promise<Data> {
    return this.patchSession(id, data, params);
  }

  // async patchSession(id: NullableId, data: { subSessionId: number, twtdarOrder: number, classId: number, isClosing: boolean, studentUids: number[] }, params?: Params): Promise<Data> {
  async patchSession(id: NullableId, data: any, params?: Params): Promise<Data> {
    const { school_class_group_id } = (<any>params).query;
    const test_session_id:any = id;
    let {
      subSessionId,
      isClosing,
      studentUids,
      classId,
      isSubmitting
    } = data;
    const changed: any[] = [];
    const errors: any[] = [];

    //read from redis
    const redis_test_attempt_sub_sessions = await getRedisTestAttemptSubSessionsFromTestSessionId(this.app, +test_session_id)
    const testAttempts = redis_test_attempt_sub_sessions.filter((r_tass:any) => studentUids.includes(+r_tass.uid) && +r_tass.sub_session_id === +subSessionId);

    //not fethcing from db anymore
    // const testAttempts = <any[]>await this.app
    // .service('db/read/test-attempt-sub-sessions')
    // .find({
    //   query: {
    //     test_session_id,
    //     uid: { $in: studentUids },
    //     sub_session_id: subSessionId,
    //     is_invalid: {$ne: 1}
    //     // twtdar_order: twtdarOrder
    //   },
    //   paginate: false,
    // });

    let attemptsToClose:any[] = [];

    if(!isClosing) { //can only have one session active at a time.
      const redis_test_attempts = await getRedisTestAttemptsFromTestSessionId(this.app, +test_session_id)
      attemptsToClose = redis_test_attempts.filter((r_ta:any) => studentUids.includes(+r_ta.uid) && +r_ta.active_sub_session_id);

      //not fethcing from db anymore
      // attemptsToClose = <any[]>await this.app
      // .service('db/read/test-attempts')
      // .find({
      //   query: {
      //     test_session_id,
      //     uid: { $in: studentUids },
      //     active_sub_session_id: {$ne: null}
      //   },
      //   paginate: false,
      // });
    }

    const testAttemptIds = testAttempts.map((entry:any) => entry.test_attempt_id);
    const testAttemptIdsToClose = isClosing ? testAttemptIds : attemptsToClose.map((entry:any) => entry.id);
    let lastLockByUid = null;
    if(params){
      lastLockByUid = await currentUid(this.app, params);
    }
    if (testAttemptIds.length > 0) {
      if (testAttemptIdsToClose.length) {
        await dbRawWrite(this.app, [testAttemptIdsToClose], `
          UPDATE test_attempts
          SET active_sub_session_id = NULL
          WHERE id IN (?)
        ;`);
        //update redis data
        await Promise.all( testAttemptIdsToClose.map( async (testAttemptToClose:any) => {
          await patchRedisTestAttempt(this.app, +testAttemptToClose, {active_sub_session_id:null})
        }))

        await dbRawWrite(this.app, [lastLockByUid, testAttemptIdsToClose, subSessionId], `
          UPDATE test_attempt_sub_sessions
          SET last_locked_on = NOW()
            , last_locked_by_uid = ?
          WHERE test_attempt_id IN (?)
            AND sub_session_id = ?
            AND is_invalid != 1
          ;`);
        // NO need to update redis for now (these two field is not loaded into redis so not updating now.)

        if (isSubmitting) {
          await dbRawWrite(this.app, [testAttemptIds, subSessionId], `
            UPDATE test_attempt_sub_sessions
            SET is_submitted = 1
            WHERE test_attempt_id IN (?)
              AND sub_session_id = ?
              AND is_invalid != 1
          ;`);
          //update redis data
          const submit_tasss = redis_test_attempt_sub_sessions.filter((r_tass:any) => testAttemptIds.includes(r_tass.test_attempt_id) && (+r_tass.sub_session_id === +subSessionId))
          await Promise.all( submit_tasss.map( async (submit_tass:any) => {
            await patchRedisTestAttemptSubSession(this.app, +submit_tass.id, {is_submitted:1})
          }))
        }
      }

      if(!isClosing) {
        if (await this.checkActiveSubSessions(studentUids, classId)) {
          throw new Errors.Conflict("OTHER_SESSION_ACTIVE");
        }
        await dbRawWrite(this.app, [subSessionId, testAttemptIds], `
          UPDATE test_attempts
          SET active_sub_session_id = ?
          WHERE id IN (?)
        ;`);
        //update redis data
        await Promise.all( testAttemptIds.map( async (testAttemptId:any) => {
          await patchRedisTestAttempt(this.app, +testAttemptId, {active_sub_session_id: subSessionId})
        }))
      }
    }
    return { changed, errors };
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
  private async checkActiveSubSessions(studentUids: number[], classId: number) {
    //read from redis
    const redis_test_attempts = await getRedisTestAttemptsFromSchoolClassId(this.app, +classId)
    const active_session_attempts = redis_test_attempts.filter((r_ta:any) => studentUids.includes(+r_ta.uid) && +r_ta.active_sub_session_id);

    // get all distinct test sesession ids in active_session_attempts
    const distinctTestSessionIds:any[] = [...new Set(active_session_attempts.map(test_attempt => test_attempt.test_session_id))]

    // fetch test sessions in distinctTestSessionIds from redis
    const test_sessions = await getRedisTestSessions(this.app, distinctTestSessionIds)

    // filter test session with is_closed = 0
    const notClosedTestSessions = test_sessions.filter(ts => +ts.is_closed === 0)
    const distinctNotClosedTestSessionIds:any[] = [...new Set(notClosedTestSessions.map(ts => ts.id))]

    //filter test attempts in not_closed test session
    const notClosedActiveSubSessionTestAttempts  = active_session_attempts.filter(activeTa => distinctNotClosedTestSessionIds.includes(activeTa.test_session_id))

    //not fethcing from db anymore
  //   const active_sessions = await this.getData([classId, studentUids], `
  //    select ts.id, ta.active_sub_session_id
  //      from test_sessions ts
  //      join test_attempts ta on ta.test_session_id = ts.id
  //      join school_class_test_sessions scts on scts.test_session_id = ts.id and scts.school_class_id = ?
  //     where ts.is_closed = 0
  //       and ts.is_cancelled = 0
  //       and ta.uid IN (?)
  //       and ta.active_sub_session_id IS NOT NULL;
  // ;`);
    return (notClosedActiveSubSessionTestAttempts.length > 0);
  }
  private async checkCompletedSubSessions(studentUids: number[], subSessionId: number, classId: number) {
    const active_sub_session = await this.app.service('db/read/test-session-sub-sessions').get(subSessionId);
    const session_slug = active_sub_session.slug;
    const completed_sessions = await this.getData([classId,studentUids], `
    SELECT tass.is_submitted, scts.slug as session_type, tass.test_session_id,tsss.caption as sub_session_caption,tsss.slug as sub_session_slug
		 FROM mpt_dev.school_class_test_sessions scts
		 join mpt_dev.test_attempt_sub_sessions tass on tass.test_session_id = scts.test_session_id
     join mpt_dev.test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
     where scts.school_class_id = ?
        and tass.uid in (?)
        and tass.is_submitted = 1
        AND tass.is_invalid != 1
  ;`);
    const is_session_completed = completed_sessions.filter(session => { return session.sub_session_slug === session_slug })
    return (is_session_completed.length > 0);
  }

  private async getData(props: any[], query: string) {
    const db = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]>res[0];
  }

  async getStudentsInSessionClass(school_class_id: number, isForced?:boolean, loadFromRedis = false) {
    // sqlMessage:"Unknown column 'schl_student' in 'on clause'"

    let studentRecords:any[] = []
    if(loadFromRedis) {
      const schoolClassUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, school_class_id)
      const schoolClassStudentUserRoles = schoolClassUserRoles.filter(schoolClassUserRole => schoolClassUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
      const distinctStudentUids = [...new Set(schoolClassStudentUserRoles.map(userRole => userRole.uid))]
      distinctStudentUids.forEach(distinctStudentUid => {
        const studentRecord = {
          uid: distinctStudentUid
        }
        studentRecords.push(studentRecord)
      })
    }else{
      studentRecords = <any[]>await dbRawRead(this.app, [school_class_id], `
      select ur.uid
        from school_classes sc
        join user_roles ur on ur.group_id = sc.group_id
         and ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}'
         and ur.is_revoked = 0
       where sc.id = ?
       ${ isForced ? '' : `and sc.is_active = 1` }
      ;`);
    }

    let guestClassStudentRecord:any[] = []
    if(loadFromRedis){
      const guestClassIds = await getClassGuestClasses(this.app, school_class_id)
      await Promise.all(guestClassIds.map(async (guestClassId:any) => {
        const guestClassUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, guestClassId)
        const guestClassStudentUserRoles = guestClassUserRoles.filter(classUserRole => classUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
        const distinctGuestStudentUids = [...new Set(guestClassStudentUserRoles.map(userRole => userRole.uid))]
        distinctGuestStudentUids.forEach(distinctGuestStudentUid => {
          const studentRecord = {
            uid: distinctGuestStudentUid
          }
          guestClassStudentRecord.push(studentRecord)
        })
      }))
    }else{
      guestClassStudentRecord = <any[]>await dbRawRead(this.app, [school_class_id], `
        select ur.uid
          from school_classes isc
          join school_classes_guest scg on scg.invig_sc_group_id = isc.group_id and scg.is_revoked != 1
          join school_classes sc on sc.group_id = scg.guest_sc_group_id ${ isForced ? '' : `and sc.is_active = 1` }
          join user_roles ur on ur.group_id = sc.group_id and ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}' and ur.is_revoked = 0
         where isc.id = ?
      ;`);
    }
    const returnRecords = studentRecords.concat(guestClassStudentRecord)
    return returnRecords.map(r => r.uid);
  }

  unlockStudentSubSessions() {

  }

  // ensures intialization
  async getSubSessions(test_session_id: Id, isForced?:boolean, loadFromRedis = false) {
    if(!test_session_id) {
      throw new Errors.BadRequest();
    }
    let sctsRecords:any[] = []
    if(loadFromRedis){
      const test_session = await getRedisTestSession(this.app, test_session_id);
      const sctsRecord = {
        school_class_id: test_session.school_class_id,
        slug: test_session.slug,
      }
      sctsRecords.push(sctsRecord)
    }else{
      sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    }
    const sctsRecord = sctsRecords[0];
    const school_class_id = sctsRecord.school_class_id;
    let subSessionRecords = await this.getSubSessionRecords(test_session_id, loadFromRedis);
    const subSessionOrderIndex = new Map();
    subSessionRecords = _.orderBy(subSessionRecords, 'order').map((entry: any) => Object({
      id: entry.id,
      order: entry.order,
      slug: entry.slug,
      caption: entry.caption,
      date_time_start:entry.datetime_start,
      duration_hours:entry.duration_hours,
      twtdar_order: entry.twtdar_order,
      is_last: entry.is_last
    }))

    //Get the slug from one of the subsession records
    subSessionRecords.forEach((subSessionRecord, i) => {
      subSessionOrderIndex.set(subSessionRecord.id, i)
    })
    // test takers
    const studentUids = await this.getStudentsInSessionClass(school_class_id, isForced, loadFromRedis);
    let studentRecords;
    if(studentUids.length>0) {
      // only uid is being used so no need to interact with database here.
      studentRecords = studentUids.map(studentUid => { return { uid:studentUid } })
      //   studentRecords = <any[]>await dbRawRead(this.app, [test_session_id, studentUids], `
      //    select ta.id as test_attempt_id, u.id as uid, tass.id as tass_id
      //    from users u
      //    left join test_attempts ta
      //      on ta.uid = u.id
      //     and ta.test_session_id = ?
      //    left join test_attempt_sub_sessions tass
      //      on tass.test_attempt_id = ta.id
      //      and tass.is_invalid != 1
      //    where u.id IN (?)
      //    group by u.id
      //  ;`);
    }

    // check if this test session have attempt that are not initialized
    const IsSessionStuAttInitPending = await this.IsSessionStuAttInitPending(+test_session_id, loadFromRedis)

    // for operational test check if any student have started ta in other test session
    let IsSessionStuAttNeedMoving = false
    if(sctsRecord.slug.includes("OPERATIONAL")){
      IsSessionStuAttNeedMoving = await this.IsSessionStuAttNeedMoving(+test_session_id, loadFromRedis)
    }

    if (studentRecords && (IsSessionStuAttInitPending || IsSessionStuAttNeedMoving)) { //
      logger.info('getSubSessions:initSession', { test_session_id, IsSessionStuAttInitPending, IsSessionStuAttNeedMoving, loadFromRedis });
      //check if the test attempts in this test session is initializing, throw error if it is.
      let lock: any
      try{
        lock = await getRedisLock(this.app, [KTestSessionInit(test_session_id)], TESTSESSION_INIT_REDLOCK_TIMEOUT);
      } catch (error){
        if(lock){
          await lock.release();
        }
        throw new Errors.Conflict("SESSION_INITIALIZING"); // Conflict (http error 409)
      }

      const lastTestAttemptsTestSessionIds:number[] = []
      const uidToTestAttemptPromise = new Map<number, Map<number, Promise<any>>>();

      const getCreateTestAttemptPromise = (uid: number, twtdar_order: number) => {
        const twtdarOrderToTestAttemptPromise = uidToTestAttemptPromise.get(uid);
        if (twtdarOrderToTestAttemptPromise) {
          return twtdarOrderToTestAttemptPromise.get(twtdar_order);
        }
      }

      const setCreatedTestAttemptPromise = (uid: number, twtdar_order: number, taCreatePromise: Promise<any>) => {
        let twtdarOrderToTestAttempt = uidToTestAttemptPromise.get(uid);
        if (!twtdarOrderToTestAttempt) {
          twtdarOrderToTestAttempt = new Map<number, Promise<any>>();
        }
        twtdarOrderToTestAttempt.set(twtdar_order, taCreatePromise);
        uidToTestAttemptPromise.set(uid, twtdarOrderToTestAttempt);
      }

      const sessionRecord = (await dbRawRead(this.app, [test_session_id], `
        select test_window_id
        from test_sessions
        where id = ?
      ;`))[0];
      const test_window_id = sessionRecord.test_window_id;
      const pre_loaded_test_designs = await dbRawReadReporting(this.app, {test_window_id}, `
        select td.*
          from test_window_td_alloc_rules twtdar
          join test_designs td on td.id = twtdar.test_design_id
         where twtdar.test_window_id = :test_window_id
      ;`);
      let maxtwtdar_order = 0
      subSessionRecords.forEach( (sr:any) => {if (sr.twtdar_order > maxtwtdar_order) maxtwtdar_order = sr.twtdar_order })

      //get class's similarity_slugs for each test_design
      const student_uids = studentRecords.map(studentRecord => {return studentRecord.uid})
      const testSessionTDSimilarityTags = await this.getTestSessionTDSimilarityTags(test_session_id, student_uids)

      let studentRecordInitError:any;
      try {
        // initialize 5 student's attempts at at time to minimize the database connection load.
        let newTestAttempts:any[] = [];
        for(let studentRecordsChunk of _.chunk(studentRecords, 5)){
          await Promise.all(studentRecordsChunk.map(async studentRecord => {
            for(let twtdar_order = 0; twtdar_order < maxtwtdar_order+1; twtdar_order++){
              const subSessionRecord = subSessionRecords.find(sr => sr.twtdar_order == twtdar_order);
              const uid = studentRecord.uid;
              const slug = sctsRecord.slug;
              const previousAttemptsRecords = <any[]>await dbRawRead(this.app, [test_window_id, uid, twtdar_order, slug], `
                select ta.uid
                     , ta.id
                     , ta.twtdar_order
                     , ta.test_session_id
                     , scts.slug
                     , ta.started_on
                from test_attempts ta
                join school_class_test_sessions scts
                  on scts.test_session_id = ta.test_session_id
                join test_sessions ts
                  on scts.test_session_id = ts.id
                  and ts.test_window_id = ?
                join test_windows tw
                  on tw.id = ts.test_window_id
                 and tw.date_end > now()
                where ta.uid = ?
                  and ta.twtdar_order = ?
                  and ta.is_invalid != 1
                  and scts.slug = ?
                  and ta.started_on is not null -- only need to laod ta that has not started
                order by ta.created_on
              ;`);

              const currentAttemptsRecords = <any[]>await dbRawRead(this.app, [test_session_id, uid, twtdar_order], `
                select ta.*
                  from test_attempts ta
                 where ta.test_session_id  = ?
                   and ta.uid = ?
                   and ta.is_invalid != 1
                   and ta.twtdar_order = ?
              ;`);

              const currentTestAttemptIds =  currentAttemptsRecords.map(attempt => attempt.id)

              let currentAttemptsSubSessionRecords = [];
              if(currentTestAttemptIds.length > 0){
                currentAttemptsSubSessionRecords = <any[]>await dbRawRead(this.app, [currentTestAttemptIds], `
                  select tass.*
                    from test_attempt_sub_sessions tass
                   where tass.test_attempt_id in (?)
                     and tass.is_invalid != 1
                ;`);
              }

              let lastTestAttempt;
              let previousAttemptId
              let isPreviousAttempt = false;
              let test_attempt_id;

              if ((slug === 'PRIMARY_OPERATIONAL'||slug === 'JUNIOR_OPERATIONAL'||slug === 'G9_OPERATIONAL' || slug === 'OSSLT_OPERATIONAL') && previousAttemptsRecords.length > 0){
                let taqrRecord :any[] = []
                for(let prev_test_attempt_i= 0; prev_test_attempt_i<previousAttemptsRecords.length; prev_test_attempt_i++){
                  const this_lastTestAttempt = previousAttemptsRecords[prev_test_attempt_i]
                  const this_previousAttemptId = this_lastTestAttempt.id;
                  const this_taqrRecord = <any[]>await dbRawRead(this.app, [this_previousAttemptId], `
                    select * from test_attempt_question_responses where test_attempt_id = ?
                  ;`)
                  //make lastTestAttempt[0] as default attempt to move  and change it if we found attempt with more tqars
                  if(!lastTestAttempt || (this_taqrRecord.length > taqrRecord.length)){
                    lastTestAttempt = this_lastTestAttempt
                    previousAttemptId = this_previousAttemptId
                    taqrRecord = this_taqrRecord
                  }
                }

                //pull lastTestAttempt to current test session if its started and in different test session started, even with no tqars (since taqrs might still be in redis not databse)
                if(lastTestAttempt && +lastTestAttempt.test_session_id != +test_session_id && lastTestAttempt.started_on){
                  //revoke the current test_attempt
                  logger.info('getSubSessions:initSession.negateTestAttempt', {
                    test_session_id,
                    uid,
                    twtdar_order,
                    ta_ids: currentAttemptsRecords.map(attempt => attempt.id),
                    tass_ids: currentAttemptsSubSessionRecords.map(tass => tass.id)
                  });
                  await Promise.all(currentAttemptsRecords.map(async ta =>{
                    await this.app
                      .service('db/write/test-attempts')
                      .patch(ta.id,{
                        uid: 0-(+ta.uid),
                        test_session_id: 0 - (ta.test_session_id),
                        is_invalid: 1
                      });
                  }))
                  //revoke the current test_attempt_subsession
                  await Promise.all(currentAttemptsSubSessionRecords.map(async tass =>{
                    await this.app
                      .service('db/write/test-attempt-sub-sessions')
                      .patch(tass.id,{
                        uid: 0-(+tass.uid),
                        test_session_id: 0 - (tass.test_session_id),
                        is_invalid : 1
                      });
                  }))

                  const testSessionSubSessions = <any[]>await this.app
                    .service('db/read/test-session-sub-sessions')
                    .find({
                      query: {
                        test_session_id,
                      },
                      paginate: false,
                    });

                  const previousAttemptsSubSessionRecords = <any[]>await dbRawRead(this.app, [previousAttemptId], `
                    select tass.*, tsss.slug
                      from test_attempt_sub_sessions tass
                      join test_session_sub_sessions tsss
                        on tsss.id = tass.sub_session_id
                     where test_attempt_id  = ?
                     and tass.is_invalid != 1
                  ;`);

                  const testAttempts = <any[]>await this.app
                    .service('db/write/test-attempts')
                    .patch(lastTestAttempt.id,{test_session_id, active_sub_session_id: null}); // explicitly setting active_sub_session_id to null when transferring the stduent attempt

                  if(!lastTestAttemptsTestSessionIds.includes(+lastTestAttempt.test_session_id)){
                    lastTestAttemptsTestSessionIds.push(+lastTestAttempt.test_session_id)
                  }

                  for(let record of previousAttemptsSubSessionRecords)
                  {
                    const testSessionSubSession = testSessionSubSessions.find( subSession =>subSession.slug == record.slug )
                    const sub_session_id = testSessionSubSession.id;
                    await this.app.service('db/write/test-attempt-sub-sessions')
                      .patch(record.id,{test_session_id, sub_session_id});
                  }

                  test_attempt_id = previousAttemptId;
                  isPreviousAttempt = true;
                }
              }

              //test atttempt id should be link only if there is a corrent SubSession
              if(!test_attempt_id && currentTestAttemptIds.length > 0 && currentAttemptsSubSessionRecords.length > 0){
                const currentAttemptsSubSessionRecord = currentAttemptsSubSessionRecords.find( tass => tass.sub_session_id == subSessionRecord.id)
                if(currentAttemptsSubSessionRecord){
                  test_attempt_id = currentTestAttemptIds[0]
                }
              }
              //test_attempt_id = studentRecord.test_attempt_id;

              let attempt;
              if(!this.app.get('context').isBCED) {
                if (!test_attempt_id && !isPreviousAttempt) {
                  logger.info('getSubSessions:initSession:createTestAttempt', { test_session_id, school_class_id, uid, twtdar_order })

                  const taCreatePromise = getCreateTestAttemptPromise(uid, twtdar_order);
                  if (taCreatePromise) {
                    attempt = await taCreatePromise;
                    test_attempt_id = attempt.id;
                  } else {
                    const from_session_init = true
                    const taCreatePromise = await this.app
                      .service('public/student/session')
                      .createAttemptByTeacher(uid, <number>test_session_id, testSessionTDSimilarityTags, twtdar_order, from_session_init, pre_loaded_test_designs);
                    setCreatedTestAttemptPromise(uid, twtdar_order, taCreatePromise);
                    attempt = await taCreatePromise;
                    newTestAttempts.push(attempt)
                  }

                  //check if there's more than 1 test attempts get created by other concurrent API call
                  const existingTestAttempts = <any[]>await dbRawWrite(this.app, [test_session_id, uid, twtdar_order], `
                       select ta.*
                         from test_attempts ta
                        where ta.test_session_id = ?
                          and ta.uid = ?
                          and ta.twtdar_order = ?
                          and ta.is_invalid = 0
                     order by ta.id asc
                  ;`);

                  //unlink the extra test attempts, just keep existingTestAttempts[0]
                  if(existingTestAttempts.length > 1){
                    for (let ta_i = 1; ta_i<existingTestAttempts.length; ta_i++ ){
                      const test_attempt_id = existingTestAttempts[ta_i].id
                      const new_uid = 0 - (+uid) // -uid
                      const new_test_session_id = 0 - (+test_session_id) // -test_session_id
                      await this.app.service('db/write/test-attempts').patch(test_attempt_id, {
                        uid: new_uid,
                        test_session_id: new_test_session_id,
                        is_invalid : 1
                      });

                      const  existingAttemptsSubSessionRecords = <any[]>await dbRawWrite(this.app, [test_attempt_id], `
                          select tass.*
                            from test_attempt_sub_sessions tass
                          where tass.test_attempt_id = ?
                            and tass.is_invalid != 1
                        ;`);

                      await Promise.all(existingAttemptsSubSessionRecords.map(async etassr =>{
                        await this.app
                          .service('db/write/test-attempt-sub-sessions')
                          .patch(etassr.id,{
                            uid: 0-(+etassr.uid),
                            test_session_id: 0 - (etassr.test_session_id),
                            is_invalid : 1
                          });
                      }))
                    }
                  }
                  // If there alreayd exist a test attempt, (other thread created one already),
                  // filter out record with same uid and twtdar order from newTestAttempts
                  if(existingTestAttempts.length > 0){
                    //remove uid attempt in newTestAttempts if it has one
                    newTestAttempts = newTestAttempts.filter((newTestAttempt:any) => +newTestAttempt.data.uid !== +uid && +newTestAttempt.data.twtdar_order !== +twtdar_order)
                  }
                }
              }
            }
          }));
          lock = await lock.extend(TESTSESSION_INIT_REDLOCK_TIMEOUT)
        }

        //bulk create test attempt and test attempt sub sessions
        const knex:Knex = this.app.get('knexClientWrite');
        let firstCreatedTAId
        if(newTestAttempts.length){
          const newTestAttemptsData = newTestAttempts.map(newTestAttempt => newTestAttempt.data)
          const testAttemptIds = await knex('test_attempts').insert(newTestAttemptsData) // return id of the first row that was created. The ids for the other rows are guaranteed to be consecutive.
          firstCreatedTAId = testAttemptIds[0]
        }

        //generate associate test attempt sub session data
        let newTestAttemptSubSessions:any[] = [];
        if(firstCreatedTAId){
          for(const [index, newTestAttempt] of newTestAttempts.entries()){
            const newAttemptID =  firstCreatedTAId + index  // The ids for the other rows are guaranteed to be consecutive
            const twdarOrderSubSessionRecords = subSessionRecords.filter( (ssr:any) => +ssr.twtdar_order === +newTestAttempt.data.twtdar_order )
            for(let subSessionRecord of twdarOrderSubSessionRecords){
              newTestAttemptSubSessions.push({
                test_session_id:newTestAttempt.data.test_session_id,
                sub_session_id: subSessionRecord.id,
                test_attempt_id: newAttemptID,
                uid:newTestAttempt.data.uid,
                num_responses: 0,
                sections_allowed: this.getSectionsAllowed(newTestAttempt.subsession_meta, subSessionRecord.slug)|| null
              })
            }
          }
        }

        //bulk create test attempt sub session
        if(newTestAttemptSubSessions.length){
          await knex('test_attempt_sub_sessions').insert(newTestAttemptSubSessions)
        }
      }
      catch (e){
        if(lock){
          await lock.release();
        }
        studentRecordInitError = e;
      }

      if (studentRecordInitError){
        throw studentRecordInitError;
      }

      //if new test attempts is created/moved to the class, need to reload data into redis
      if(loadFromRedis){
        const loadGuestClassToRedis = true; // Only host class call this function so the guest class need to be loaded
        logger.info(`getSubSessions:ensureSessionInfoToRedis.onNewTestAttempt`, {test_session_id, school_class_id})
        await ensureSessionInfoToRedis(this.app, school_class_id, loadGuestClassToRedis)

        //if test attempts is pulled from other test session, that class redis data need to update as well.
        if(IsSessionStuAttNeedMoving && lastTestAttemptsTestSessionIds.length){
          //check if any lastTestAttemptsTestSessionIds are in redis
          const redisLastTestAttemptsTestSessionIds:number[] = []
          await Promise.all( lastTestAttemptsTestSessionIds.map( async lastTestAttemptsTestSessionId =>{
            const sessionInRedis = await checkRedisKey(this.app, KTestSession(lastTestAttemptsTestSessionId))
            if(sessionInRedis){
              redisLastTestAttemptsTestSessionIds.push(+lastTestAttemptsTestSessionId)
            }
          }))

          //get test session from redis
          const redisLastTestAttemptsTestSessions = await getRedisTestSessions(this.app, redisLastTestAttemptsTestSessionIds)

          //get test session class ids
          const redisLastTestAttemptsClassIds = [... new Set(redisLastTestAttemptsTestSessions.map(ts => ts.school_class_id))]
                                                .filter(classId => classId)

          //reload data into redis for all affecting classes
          await Promise.all(redisLastTestAttemptsClassIds.map(async classId=>{
            logger.info(`getSubSessions:ensureSessionInfoToRedis.onNewTestAttempt.affectedClass`, {test_session_id, school_class_id: classId})
            await ensureSessionInfoToRedis(this.app, +classId, loadGuestClassToRedis)
          }))
        }
      }

      if(lock){
        await lock.release();
      }
    }

    const db:Knex = await this.app.get('knexClientRead');
    const dbR:Knex = await this.app.get('knexClientReadReporting');

    let studentAttemptRecords:any[] = []
    if(loadFromRedis){
      const test_attempts = await getRedisTestAttemptsFromTestSessionId(this.app, test_session_id)
      const test_attempt_unsubmissionIds = await getTestSessionTestAttemptUnsubmissions(this.app, test_session_id)
      const test_attempt_unsubmissions = await getRedisTestAttemptUnsubmissiones(this.app, test_attempt_unsubmissionIds)
      studentUids.forEach(studentUid =>{
        const student_tau = test_attempt_unsubmissions.find((tau:any) => +tau.student_uid === +studentUid && tau.is_approved === 0 && tau.is_pending === 1)
        const student_test_attempts = test_attempts.filter(ta => +ta.uid === +studentUid)
        student_test_attempts.forEach(student_test_attempt => {
          const studentAttemptRecord = {
            ...student_test_attempt,
            is_unsubmit_pending: student_tau? student_tau.is_pending : null,
          }
          studentAttemptRecords.push(studentAttemptRecord)
        })
      })
    }else{
      //tf.test_design_id is not being used anymore, no need to load it
      studentAttemptRecords = <any[]>await dbR('test_attempts as ta')
      //.join('test_forms as tf', 'tf.id', 'ta.test_form_id')
        .leftJoin('test_attempt_unsubmissions as tau',
        {
          'tau.test_session_id': 'ta.test_session_id',
          'tau.student_uid': 'ta.uid',
          'tau.is_approved': 0,
          'tau.is_revoked': 0,
          'tau.is_pending': 1
        })
        .whereIn('ta.uid', studentUids)
        .where('ta.test_session_id', test_session_id)
        .whereNot('is_invalid', 1)
        //.select('ta.*', 'tf.test_design_id', 'tau.is_pending as is_unsubmit_pending')
        .select('ta.*',  'tau.is_pending as is_unsubmit_pending')
    }

    let studentSubSessionRecords: any = [];
    if(studentUids.length>0){
      if(loadFromRedis){
        // get test session test attempts
        const testAttemptSubSessions = await getRedisTestAttemptSubSessionsFromTestSessionId(this.app, test_session_id)

        //get test attempts associate test session sub sessions
        const sub_session_ids = [... new Set(testAttemptSubSessions.map((testAttemptSubSession => testAttemptSubSession.sub_session_id)))]
        const testSessionSubSessions = await getRedisTestSessionSubSessions(this.app, sub_session_ids)

        //for each studentUid's test attempts craete a studentSubSessionRecord.
        studentUids.forEach(studentUid =>{
          const student_tasses = testAttemptSubSessions.filter(tass => +tass.uid === +studentUid)
          student_tasses.forEach(tass => {
            const tsss = testSessionSubSessions.find((tsss:any) => +tsss.id === +tass.sub_session_id)
            if(tsss){
              const studentSubSessionRecord = {
                ...tass,
                subsession_slug: tsss.slug,
                sections_allowed: tsss.sections_allowed
              }
              studentSubSessionRecords.push(studentSubSessionRecord)
            }
          })
        })
      }else{
        studentSubSessionRecords = await dbRawRead(this.app, [studentUids, test_session_id],
          `SELECT tass.*, tsss.slug as subsession_slug, tsss.sections_allowed from test_attempt_sub_sessions tass
          join test_session_sub_sessions tsss on tass.sub_session_id = tsss.id
          where uid in (?)
          and tass.test_session_id = ?
          and is_invalid != 1;
        `)
      }
    }
    const studentStates: { [key: string]: any } = {};
    // const studentSubSessionStates:any[] = [];
    const ensureStudentState = (uid: number) => {
      let studentState = studentStates[uid];
      if (!studentState) {
        studentState = {
          uid,
          subSessions: []
        }
        studentStates[uid] = studentState;
      }
      return studentState;
    }
    studentAttemptRecords.forEach(async attempt => {
      let studentState = ensureStudentState(attempt.uid);
      if (studentState.active_sub_session_id !== null && studentState.active_sub_session_id !== undefined) {
        return;  //We already have the attempt corresponding to the active subsession for this student
      }
      studentState.attempt_id = attempt.id;
      studentState.last_touch_on = attempt.last_touch_on;
      studentState.active_sub_session_id = attempt.active_sub_session_id;
      studentState.section_index = attempt.section_index;
      studentState.question_index = attempt.question_index;
      studentState.question_caption = attempt.question_caption;
      studentState.is_submitted = attempt.is_submitted;
      studentState.is_paused = (await isTestAttemptPaused(this.app, attempt.id));
      studentState.is_ta_unsubmit_pending = !!attempt.is_unsubmit_pending;
    })

    studentSubSessionRecords.forEach((entry: any) => {
      let studentState = ensureStudentState(entry.uid)
      const sessionIndex = subSessionOrderIndex.get(entry.sub_session_id)
      let debugSubsessions = getPropVals(entry, [
        'is_submitted',
        'num_responses',
        'started_on',
        'last_locked_on',
        'last_touch_on',
        'subtracted_time',
        'sections_allowed',
        'test_attempt_id'
      ]);

      if(debugSubsessions.sections_allowed) {
        debugSubsessions.sections_allowed = JSON.parse(debugSubsessions.sections_allowed);
      }
      debugSubsessions.tass_id = entry.id;
      debugSubsessions.subsession_slug = entry.subsession_slug;
      debugSubsessions.test_attempt_id = entry.test_attempt_id;
      studentState.subSessions[sessionIndex] = debugSubsessions;
    });

    const redis:Redis = this.app.get('redis'); // Redis is now enforced
    if (studentAttemptRecords && studentAttemptRecords.length) {
      const attemptPoss = await Promise.all(studentAttemptRecords.map(async (attempt) => {
        return redis.get(KAttemptPos(attempt.id));
      }));
      const cachedAttemptPos = _(attemptPoss)
        .compact()
        .map((ap:string) => JSON.parse(ap))
        .keyBy('test_attempt_id')
        .value();

      const cachedSubmData:Record<string, string>[] = [];
      for (const s of studentAttemptRecords) {
        cachedSubmData.push(await redis.hgetall(KSubmData(s.id)));
      }
      const submDataChunk: any[] = _(cachedSubmData)
        .map((r) => Object.values(r))
        .flatten()
        .map((r)=> JSON.parse(r))
        .compact()
        .value();

      let mostRecentTAQRs: any;
      if(submDataChunk && submDataChunk.length > 0) {
        mostRecentTAQRs = _(submDataChunk)
          .groupBy('tass_id')
          .map((tassIdGroup:any[]) => (_.sortBy(tassIdGroup, 'timestamp').pop()))
          .keyBy('tass_id')
          .value();
      }

      _.forEach(studentStates, (studentState) => {
        const formatTime = (t: number) => moment(t).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');

        if(mostRecentTAQRs) {
          for(let subSession of studentState.subSessions) {
            if(subSession) {
              const mostRecentTAQR = mostRecentTAQRs[subSession.tass_id];
              if(mostRecentTAQR) {
                subSession.last_touch_on = formatTime(mostRecentTAQR.timestamp);
              }
            }
          }
        }

        const cached = cachedAttemptPos[studentState.attempt_id];
        if (!cached) {
          return;
        }
        const last_touch_on = formatTime(cached.last_touch_on);
        studentState.last_touch_on = last_touch_on
        studentState.section_index = cached.section_index;
        studentState.question_index = cached.question_index;
        studentState.question_caption = cached.question_caption;
        studentState.module_id = cached.module_id;
      });
    }
    // await Promise.all(
    //   studentAttemptRecords.map( attempt => {
    //     return this.app.service('public/student/session').loadNewStudentTestDesign(attempt.uid, attempt.test_session_id, attempt.twtdar_order)
    //     .then( (testDesign) => {
    //         if( (attempt.test_form_linear != null && attempt.test_form_linear != testDesign.test_form_linear) && testDesign.test_design_id !== attempt.test_design_id) {
    //           let studentState = ensureStudentState(attempt.uid);
    //           studentState.hasOldTestForm = true;
    //           studentState.canUpdateTestForm = !attempt.is_submitted && !attempt.last_touch_on && !attempt.started_on
    //         }
    //       }
    //     );
    //   })
    // )

    // MERGE CONFLICT (incoming)
    // await Promise.all(
    //   studentAttemptRecords.map( attempt => {
    //     return this.app.service('public/student/session').loadNewStudentTestDesign(attempt.uid, attempt.test_session_id, attempt.twtdar_order)
    //     .then( (testDesign) => {
    //         if( (attempt.test_form_linear != null && attempt.test_form_linear != testDesign.test_form_linear) && testDesign.test_design_id !== attempt.test_design_id) {
    //           let studentState = ensureStudentState(attempt.uid);
    //           studentState.hasOldTestForm = true;
    //           studentState.canUpdateTestForm = !attempt.is_submitted && !attempt.last_touch_on && !attempt.started_on
    //         }
    //       }
    //     );
    //   })
    // )

    return { studentStates, subSessionRecords };
  }


  /**
   * Get test session's similarity_tags for each test_design in test_session
   * If there already exist similarity tags used by students, return those tags. (1 for each test desing)
   * If no existed similarity tags used by students,  random select one from historic_form_tags table
   * Possible similarity tag is null/undefined
   * @param test_session_id test session id
   * @param student_uids student uids in test session
   * @returns list of test_design ids associate with its similarity tags
   */
  public async getTestSessionTDSimilarityTags(test_session_id:Id, student_uids: number[]):Promise<TestDesignSimilarityTag[]>{
    const test_session_info = await dbRawRead(this.app, {test_session_id}, `
        select distinct
               (CASE
                 WHEN scts.slug IS NULL
                 THEN qts.slug
                 ELSE scts.slug END) AS slug
             , ts.test_window_id
          from test_sessions ts
          LEFT join school_class_test_sessions scts on scts.test_session_id = ts.id
          LEFT JOIN questionnaire_test_sessions qts on qts.test_session_id = ts.id
         where ts.id = :test_session_id
    ;`)
    const type_slug = test_session_info[0].slug
    const test_window_id = test_session_info[0].test_window_id

    // Get test design ids that fit in type_slug and test_window_id
    const twtdar_test_designs = await dbRawRead(this.app, {type_slug, test_window_id}, `
        select distinct
               twtdar.test_design_id
          from test_window_td_alloc_rules twtdar
         where twtdar.test_window_id = :test_window_id
           and twtdar.type_slug = :type_slug
    ;`)
    const test_design_similarity_tags:TestDesignSimilarityTag[] = [];

    for (let twtdar_test_design_chunks of _.chunk(twtdar_test_designs, 5)){
      await Promise.all(twtdar_test_design_chunks.map(async twtdar_test_design => {
        const test_design_similarity_tag:TestDesignSimilarityTag = {
          test_design_id : twtdar_test_design.test_design_id,
          similarity_tag: undefined //set test design's similarity_tag undefined by default
        }
        test_design_similarity_tags.push(test_design_similarity_tag)
        // Get all test forms and its similarity tag used by students in student_uids for test design (test form similarity tag can be null)
        // If more than 1 test form is found,  select the fist test form's similarity tag used by the smallest uid
        const test_design_id = twtdar_test_design.test_design_id
        const usedTestDesignSimilarityTags = await dbRawRead(this.app, {test_design_id, student_uids}, `
            select distinct
                   hft.similarity_tag
              from test_forms tf
              join test_attempts ta on ta.test_form_id = tf.id and ta.uid in (:student_uids) and ta.is_invalid = 0
         left join historic_form_tags hft on hft.test_form_id = tf.id and hft.is_revoked = 0
             where tf.is_revoked = 0
               and tf.test_design_id = :test_design_id
          order by uid asc
             limit 1
        ;`)

        if(usedTestDesignSimilarityTags.length){ //if usedTestDesignSimilarityTags found
          // assign similarity_tag currently used by smallest uid student(if exist) in student_uids to test design
          test_design_similarity_tag.similarity_tag = usedTestDesignSimilarityTags[0].similarity_tag || undefined
        }else{ // when no similarity tag currently used by students to test design,
          // Get all test forms and it's similarity tag for test design (test form similarity tag can be null)
          const testDesignSimilarityTags = await dbRawRead(this.app, {test_design_id}, `
               select tf.id as test_form_id
                    , hft.similarity_tag
                 from test_forms tf
            left join historic_form_tags hft on hft.test_form_id = tf.id and hft.is_revoked = 0
                where tf.is_revoked = 0
                  and tf.test_design_id = :test_design_id
          ;`)
          if(testDesignSimilarityTags.length){
            const testFormselection = randArrEntry(testDesignSimilarityTags);
            test_design_similarity_tag.similarity_tag = testFormselection.similarity_tag || undefined
          }
        }
      }))
    }
    return test_design_similarity_tags
  }


 getSectionsAllowed(subsession_meta:any, slug:string) {
  let subsession_meta_obj;
  let sections_allowed;
  if(subsession_meta) {
    subsession_meta_obj = JSON.parse(subsession_meta);
    sections_allowed = subsession_meta_obj[slug].sections;
    if(sections_allowed) {
      sections_allowed = JSON.stringify(sections_allowed);
    }
  }
  return sections_allowed;
 }

 //get test takers with missing test attempt
 private async IsSessionStuAttInitPending(test_session_id: number, loadFromRedis = true){
  let records:any[] = []
  let guest_records = []
  if(loadFromRedis){
    //get class id
    const test_session = await getRedisTestSession(this.app, test_session_id)
    const school_class_id = test_session.school_class_id

    // check current class students
    records = await this.getRedisClassMissingTestAttempts(school_class_id, test_session_id)

    //Need to take guest class student into account
    const guestClassIds = await getClassGuestClasses(this.app, school_class_id)
    await Promise.all(guestClassIds.map(async (guestClassId:any) => {
      const guest_class_records = await this.getRedisClassMissingTestAttempts(guestClassId, test_session_id)
      guest_class_records.forEach(record => guest_records.push(record))
    }))
  }else {
    records = await dbRawReadReporting(this.app, {test_session_id}, `
        select * from (
          select ur.uid, twtar.order, ta.id ta_id
            from school_class_test_sessions scts
            join school_classes sc on sc.id = scts.school_class_id
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student' and ur.is_revoked = 0
            join school_semesters ss on ss.id = sc.semester_id
            join test_window_td_alloc_rules twtar on twtar.test_window_id = ss.test_window_id and twtar.type_slug = scts.slug
      left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.twtdar_order = twtar.order and ta.uid = ur.uid -- this is where we expect it to miss
          where scts.test_session_id = :test_session_id
        group by ur.uid, twtar.order
      ) t
      where ta_id is null
    `)
    //Need to take guest class student into account
    guest_records = await dbRawReadReporting(this.app, {test_session_id}, `
        select * from (
          select ur.uid, twtar.order, ta.id ta_id
            from school_class_test_sessions scts
            join school_classes isc on isc.id = scts.school_class_id
            join school_classes_guest scg on scg.invig_sc_group_id = isc.group_id and scg.is_revoked != 1
            join school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.is_active = 1
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student' and ur.is_revoked = 0
            join school_semesters ss on ss.id = sc.semester_id
            join test_window_td_alloc_rules twtar on twtar.test_window_id = ss.test_window_id and twtar.type_slug = scts.slug
      left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.twtdar_order = twtar.order and ta.uid = ur.uid -- this is where we expect it to miss
          where scts.test_session_id = :test_session_id
        group by ur.uid, twtar.order
      ) t
      where ta_id is null
   `)
  }


  return records.length > 0 || guest_records.length > 0
 }

 private async getRedisClassMissingTestAttempts (school_class_id:number, test_session_id: number) {
  const records:any[] = []

  //get class id from redis
  const test_session = await getRedisTestSession(this.app, test_session_id)

  //Load all class uids from redis
  const schoolClassUserRoles = await getRedisClassUserRolesFromSchoolClassId(this.app, school_class_id)
  const schoolClassStudentUserRoles = schoolClassUserRoles.filter(schoolClassUserRole => schoolClassUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
  const distinctStudentUids = [...new Set(schoolClassStudentUserRoles.map(userRole => userRole.uid))]

  //Load all test session's test attempts from redis
  const TestSessionTestAttempts = await getRedisTestAttemptsFromTestSessionId(this.app, test_session_id)

  // get dstinct twtdar orders for this test sessions
  const twtdarIds = await getTestWindowTwtdars(this.app, test_session.test_window_id)
  const twtdars = await getRedisTestWindowTDAllocRules(this.app, twtdarIds)
  const testSessiontwtdars = twtdars.filter(twtdar => twtdar.type_slug === test_session.slug)
  const twtdarOrders = [... new Set(testSessiontwtdars.map(testSessiontwtdar => testSessiontwtdar.order))]

  //For each student find the associate test attempts, if its missing add to records
  distinctStudentUids.forEach((distinctStudentUid:any) => {
    twtdarOrders.forEach(twtdarOrder =>{
      const theStudentAttempt = TestSessionTestAttempts.find(ta => +ta.uid === +distinctStudentUid && +ta.twtdar_order === +twtdarOrder)
      if(!theStudentAttempt){
        const missingTAStudent = {
          uid: distinctStudentUid,
          order: twtdarOrder,
          ta_id: null,
        }
        records.push(missingTAStudent)
      }
    })
  })
  return records
}

 //get student that have started ta in other test session
 private async IsSessionStuAttNeedMoving(test_session_id: number, loadFromRedis = false){
  let records:any[] = []
  if(loadFromRedis){
    const testSessionTATrasferRequireUids = await getTestSessionTATrasferRequireUids(this.app, test_session_id)
    testSessionTATrasferRequireUids.forEach(uid => {
      records.push({uid})
    })
  }else{
    records = await dbRawReadReporting(this.app, {test_session_id}, `
      select distinct
             ur.uid
        from test_sessions ts
        join school_class_test_sessions scts on scts.test_session_id = ts.id
        join school_classes sc on sc.id = scts.school_class_id
        join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student' and ur.is_revoked = 0
        join test_windows tw on tw.id = ts.test_window_id
        join test_sessions ts2 on ts2.test_window_id = tw.id and ts2.id != ts.id -- all test sessions under this test window, except current one
        join test_attempts ta on ta.test_session_id = ts2.id and ta.started_on is not null and ta.uid = ur.uid and ta.is_invalid = 0 -- all test attempts that have started, under this test window, and uid in studentUids
        join school_class_test_sessions scts2 on scts2.test_session_id = ta.test_session_id and scts2.slug = scts.slug -- make sure the ta we pulled have same scts slug as the ts's scts slug
       where ts.id = :test_session_id
    ;`)
  }
  return records.length > 0
 }

 async getSubSessionRecords(test_session_id: Id, loadFromRedis = false){
    // session as a whole
    let subSessionRecords :any[] = []
    if(loadFromRedis){
      subSessionRecords = await getRedisTestSessionSubSessionsFromTestSessiontId(this.app, test_session_id)
    }else{
      subSessionRecords = <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: { test_session_id }, paginate: false });
    }

    // Sometime test session sub session record was not loaded into redis when the above check is running.
    // ticket https://bubo.vretta.com/vea/project-management/vretta-project-notes/eqao/admin/-/issues/2383
    // Double check database to make sure subsession record really need to be reinitilized
    if(loadFromRedis && subSessionRecords.length === 0){
      subSessionRecords = <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: { test_session_id }, paginate: false });
    }

    if (subSessionRecords.length === 0) {
      const scheduled_time = undefined // default value
      const is_fi = false //default value
      subSessionRecords = await this.initSubSessionRecords(test_session_id, scheduled_time, is_fi, loadFromRedis);
    }
    return subSessionRecords
 }

  async initSubSessionRecords(test_session_id: Id, scheduled_time?:string[], is_fi=false, loadFromRedis = false){
    // determine who many sessions based on school session slug
    let sctsRecords:any[] = []
    if(loadFromRedis){
     const test_session = await getRedisTestSession(this.app, test_session_id);
     const sctsRecord = {
       school_class_id: test_session.school_class_id,
       slug: test_session.slug,
     }
     sctsRecords.push(sctsRecord)
    }else{
     sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    }
    const sctsRecord = sctsRecords[0];
    const slug = sctsRecord.slug as string;
    let subSessionDefs: any[] = [];

      //Note: all subsessions with the same twtdar_order must be defined consecutively.
      if (slug === 'G9_SAMPLE') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1], twtdar_order: 0 },
        ];
      } else if (slug === 'OSSLT_SAMPLE') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1,2,3], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [4,5,6,7], twtdar_order: 0 },
        ];
      } else if (slug === 'OSSLT_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1,2,3], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [4,5,6,7], twtdar_order: 0 },
          { slug: 'session_q', caption: 'Q', sections: [0], twtdar_order: 1 }
        ];
      } else if (slug === 'G9_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [2,3], twtdar_order: 0 },
          { slug: 'session_q', caption: 'Q', sections: [0], twtdar_order: 1 }
        ];
      } else if (slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE') {
        subSessionDefs = [
          { slug: 'lang_session_a', caption: 'A', twtdar_order: 0},
          { slug: 'lang_session_b', caption: 'B', twtdar_order: 0},
          { slug: 'math_stage_1', caption: '1', twtdar_order: 1},
          { slug: 'math_stage_2', caption: '2', twtdar_order: 1}
        ]
      } else if (slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'lang_session_a', caption: 'A', twtdar_order: 0},
          { slug: 'lang_session_b', caption: 'B', twtdar_order: 0},
          { slug: 'lang_session_c', caption: 'C', twtdar_order: 0},
          { slug: 'lang_session_d', caption: 'D', twtdar_order: 0},
          { slug: 'math_stage_1', caption: '1', twtdar_order: 1},
          { slug: 'math_stage_2', caption: '2', twtdar_order: 1},
          { slug: 'math_stage_3', caption: '3', twtdar_order: 1},
          { slug: 'math_stage_4', caption: '4', twtdar_order: 1},
          { slug: 'session_q', caption: 'Q', twtdar_order: 2}
        ]
      } else if (slug.startsWith('GRAD')) {
        const testSessionRecord = await this.app.service('db/read/test-sessions').get(test_session_id);
        const testWindowId = testSessionRecord.test_window_id;
        const allocRules = <any[]>await this.app.service('db/read/test-window-td-alloc-rules').find({
          query: {
            $select: ['test_design_id'],
            type_slug: slug,
            test_window_id: testWindowId,
            $limit: 1,
          },
          paginate: false,
        });
        const testDesignId = allocRules[0].test_design_id;
        const testDesign = await this.app.service('db/read/test-designs').get(testDesignId);
        const framework = JSON.parse(testDesign.framework);
        logger.silly({ frameworkPartitions: framework.partitions });
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: Array.from(Array(framework.partitions.length).keys()), twtdar_order: 0},
        ];
      } else {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0], twtdar_order: 0 },
        ];
      }

      subSessionDefs.forEach((def, index) => {
        let nextDef = undefined;
        if (index + 1 < subSessionDefs.length) {
          nextDef = subSessionDefs[index + 1];
        }
        if (!nextDef || nextDef.twtdar_order !== def.twtdar_order) {
          def["is_last"] = 1;
        } else {
          def["is_last"] = 0;
        }
      });

      let twtdar_order: number;
      const tsssToCreate = subSessionDefs.map((subSessionDef, order) => {
        twtdar_order = subSessionDef.twtdar_order;

        let datetime_start, datetime_end;
        if ((slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') && scheduled_time && (order < scheduled_time.length)){
          const scheduledStandardTimeMoment = moment.tz(scheduled_time[order], STANDARD_TIMEZONE);
          const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
          const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
          if(scheduledUTCTime && scheduledUTCTime !== 'Invalid date'){
            datetime_start = scheduledUTCTime;
          }  
        }
        else if((slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && scheduled_time) {
          let scheduledStartStandardTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE); // for PJ Sample if order == 0 or 1; for PJ Operational if order == 0 or 1 or 2 or 3
          let scheduledEndStandardTimeMoment = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);   // for PJ Sample if order == 0 or 1; for PJ Operational if order == 0 or 1 or 2 or 3
          if((slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE') && (order == 2 || order == 3)) {
            scheduledStartStandardTimeMoment = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
            scheduledEndStandardTimeMoment = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
          }
          if((slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && (order == 4 || order == 5 || order == 6 || order == 7)) {
            scheduledStartStandardTimeMoment = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
            scheduledEndStandardTimeMoment = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
          }
          const scheduledStartUTCTimeMoment = scheduledStartStandardTimeMoment.utc();
          const scheduledEndUTCTimeMoment = scheduledEndStandardTimeMoment.utc();
          const scheduledStartUTCTime = scheduledStartUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
          const scheduledEndUTCTime = scheduledEndUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
          if(scheduledStartUTCTime && scheduledStartUTCTime !== 'Invalid date'){
            datetime_start = scheduledStartUTCTime // dbDateSetDateTime(this.app, 0, scheduledStartUTCTime);
          }
          if(scheduledEndUTCTime && scheduledEndUTCTime !== 'Invalid date'){
            datetime_end = scheduledEndUTCTime // dbDateSetDateTime(this.app, 0, scheduledEndUTCTime);
          }
          if((slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && order == 8) {
            datetime_start = moment.utc().format('YYYY-MM-DDTHH:mm');
            datetime_end = null;
          }
        }
        else if(is_fi && slug === 'PRIMARY_SAMPLE') {
          if(order == 0 || order == 1) {
            datetime_start = null;
          }
          if(order == 2 || order == 3) {
            datetime_start = moment.utc().format('YYYY-MM-DDTHH:mm');
          }
        }
        else{
          datetime_start = moment.utc().format('YYYY-MM-DDTHH:mm');
        }
        let duration_hours;
        if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
          duration_hours = DEFAULT_SESSION_DURATION_HOURS;
        }
        if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
          duration_hours = null;
        }

        const newTsss = {
          id: null,
          test_session_id,
          order,
          datetime_start,
          duration_hours,
          caption: subSessionDef.caption,
          slug: subSessionDef.slug,
          sections_allowed: JSON.stringify(subSessionDef.sections), //Note: deprecated, but keeping this temporarily for a transition period until it is safe to remove
          twtdar_order,
          is_last: subSessionDef.is_last,
          datetime_end,
        }
        return newTsss;
      });
    await dbBulkInsert(this.app, 'test_session_sub_sessions', tsssToCreate, 'id')
    if(loadFromRedis){
      for (const newTsssDbRecord of tsssToCreate as any[]) {
        const redisObject: RedisKeyTestSessionSubSession = {
          id: parseInt(newTsssDbRecord.id, 10),
          test_session_id: newTsssDbRecord.test_session_id,
          caption: newTsssDbRecord.caption,
          slug: newTsssDbRecord.slug,
          order: newTsssDbRecord.order,
          sections_allowed: newTsssDbRecord.sections_allowed,
          twtdar_order: newTsssDbRecord.twtdar_order,
          is_last: newTsssDbRecord.is_last,
          datetime_start: newTsssDbRecord.datetime_start,
          duration_hours: newTsssDbRecord.duration_hours,
          datetime_end: newTsssDbRecord.datetime_end,
        }
        await createRedisTestSessionSubSession(this.app, redisObject)
      }
    }
    return tsssToCreate;
  }

  async getTestSubSessions(test_session_id: Id) {
    // determine who many sessions based on school session slug
    const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    const sctsRecord = sctsRecords[0];
    const school_class_id = sctsRecord.school_class_id;
    const slug = sctsRecord.slug;
    // session as a whole
    let subSessionRecords = <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: { test_session_id }, paginate: false });
    if (subSessionRecords.length === 0) {
      let subSessionDefs: any[] = [];

      subSessionRecords = await Promise.all(subSessionDefs.map((subSessionDef, order) => {
        const twtdar_order = subSessionDef.twtdar_order;

        return this.app.service('db/write/test-session-sub-sessions').create({
          test_session_id,
          order,
          caption: subSessionDef.caption,
          slug: subSessionDef.slug,
          twtdar_order,
          is_last: subSessionDef.is_last
        });
      }));
    }
    const subSessionOrderIndex = new Map();
    subSessionRecords = _.orderBy(subSessionRecords, 'order').map((entry: any) => Object({
      id: entry.id,
      order: entry.order,
      slug: entry.slug,
      caption: entry.caption,
      twtdar_order: entry.twtdar_order,
      is_last: entry.is_last,
      datetime_start: entry.datetime_start,
      datetime_end: entry.datetime_end,
    }))
    subSessionRecords.forEach((subSessionRecord, i) => {
      subSessionOrderIndex.set(subSessionRecord.id, i)
    })
    // test takers
    const studentUids = await this.getStudentsInSessionClass(school_class_id);

    let studentRecords;
    if(studentUids.length>0){
     studentRecords = <any[]>await dbRawRead(this.app, [test_session_id, studentUids], `
      select ta.id as test_attempt_id, u.id as uid, tass.id as tass_id
      from users u
      left join test_attempts ta
        on ta.uid = u.id
       and ta.test_session_id = ?
      left join test_attempt_sub_sessions tass
        on tass.test_attempt_id = ta.id
        and tass.is_invalid != 1
      where u.id IN (?)
      group by u.id
    ;`);
    }
    const query = { uid: { $in: studentUids }, test_session_id, is_invalid: {$ne: 1} }
    const studentAttemptRecords = <any[]>await this.app.service('db/read/test-attempts').find({ query, paginate: false });
    const studentSubSessionRecords = <any[]>await this.app.service('db/read/test-attempt-sub-sessions').find({ query, paginate: false });
    const studentStates: { [key: string]: any } = {};
    // const studentSubSessionStates:any[] = [];
    const ensureStudentState = (uid: number) => {
      let studentState = studentStates[uid];
      if (!studentState) {
        studentState = {
          uid,
          subSessions: []
        }
        studentStates[uid] = studentState;
      }
      return studentState;
    }
    studentAttemptRecords.forEach(attempt => {
      let studentState = ensureStudentState(attempt.uid);
      if (studentState.active_sub_session_id !== null && studentState.active_sub_session_id !== undefined) {
        return;  //We already have the attempt corresponding to the active subsession for this student
      }
      studentState.attempt_id = attempt.id;
      studentState.last_touch_on = attempt.last_touch_on;
      studentState.active_sub_session_id = attempt.active_sub_session_id;
      studentState.section_index = attempt.section_index;
      studentState.question_index = attempt.question_index;
      studentState.is_submitted = attempt.is_submitted;
      studentState.submitted_test_session_id = attempt.submitted_test_session_id;
    })
    studentSubSessionRecords.forEach(entry => {
      let studentState = ensureStudentState(entry.uid)
      const sessionIndex = subSessionOrderIndex.get(entry.sub_session_id)
      let debugSubsessions = getPropVals(entry, [
        'is_submitted',
        'num_responses',
        'started_on',
        'last_locked_on',
        'last_touch_on',
        'subtracted_time',
      ]);
      studentState.subSessions[sessionIndex] = debugSubsessions;
    });

    return { studentStates, subSessionRecords };
  }
}
