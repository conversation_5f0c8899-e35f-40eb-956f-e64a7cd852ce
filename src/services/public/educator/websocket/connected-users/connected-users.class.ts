import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Redis } from 'ioredis';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import AWS from 'aws-sdk';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import { KClassConnections, KUserConnections, KWebsocketConnection, REDIS_ASSESSMENT_DATA_EXPIRE } from '../../../../../redis/redis';
import { IWebsocketConfig } from '../../../../../types/app-types';
import logger from '../../../../../logger';
import { convertObjectsToArrayOfArraysWithHeader } from '../../../../../util/param-sanitization';

interface Data {}

interface ServiceOptions {}

const AWS_CONFIG = {
  accessKeyId: '********************',
  secretAccessKey: 'Czd414/T9Agn0+d4P2xo1Z6ejQxNdaURBdf6WmAF',
};

export interface ISocketConnection {
  connection_id: string;
  uid: string;
  class_id: string;
  api_host: string;
  role_type: WS_ROLE_TYPE;
  created_on?: number;
  source_ip?: string,
}

export enum WS_ROLE_TYPE {
  STUDENT = 'student',
  EDUCATOR = 'educator'
}

export class ConnectedUsers implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  private apigwManagementApi: AWS.ApiGatewayManagementApi;
  private wsSecretPass: string;
  private websocketUrl: string; // Public WS URL, used by Client
  private websocketEndpoint: string; // AWS WS URL, used by API
  private wsGracePeriod: number;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;

    const wsConfig: IWebsocketConfig = this.app.get('websockets');
    this.websocketUrl = wsConfig.invigilation.url;
    this.websocketEndpoint = wsConfig.invigilation.endpoint || wsConfig.invigilation.url;
    this.wsSecretPass = wsConfig.invigilation.secretPass;
    this.wsGracePeriod = wsConfig.invigilation.gracePeriod;

    this.apigwManagementApi = new AWS.ApiGatewayManagementApi({
      apiVersion: '2018-11-29',
      endpoint: this.websocketEndpoint,
      region: wsConfig.invigilation.region,
      ...AWS_CONFIG,
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data | Paginated<Data>> {
    return {
      websocketUrl: this.websocketUrl,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(class_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !class_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS_AND_ID');
    }
    const { secretPass, userType, apiHost } = <any>params.query;

    if (!secretPass || secretPass !== this.wsSecretPass) {
      throw new Errors.Forbidden('ERR_REQUIRES_PASS');
    }

    const connectedUsers = await this.getConnectedUsers(class_id, userType, apiHost);

    return connectedUsers;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (!params || !data) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS_BODY');
    }

    const { connectionId, uid, classId, userType, apiHost, secretPass, source_ip } = <any>data;

    if (!secretPass || secretPass !== this.wsSecretPass) {
      throw new Errors.Forbidden('ERR_REQUIRES_PASS');
    }

    if (!connectionId || !uid || !classId || !userType || !apiHost) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    if (userType === WS_ROLE_TYPE.STUDENT) {
      if(this.app.get('context').isEQAO) {
        const ENABLE_WS_MULTITAB_BLOCK = await getSysConstNumeric(this.app, 'ENABLE_WS_MULTITAB_BLOCK')

        if(ENABLE_WS_MULTITAB_BLOCK) {
          this.forceLogout(uid, classId, apiHost, source_ip)
        }
      }

      await this.revokeStaleConnections(uid, classId);
    }
    const requestHeaders = Object.assign({}, params?.headers);
    const amznTraceId = requestHeaders['x-amzn-trace-id'];
    return this.addConnectionID(connectionId, uid, classId, userType, apiHost, source_ip, amznTraceId);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(class_id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!class_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    this.pingOnlineStudents(class_id)
      .then(() => {
        this.updateConnectedTeachers(class_id)
      })

    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  public async sendToConnectionId(messageData: any, userConnectionRec: ISocketConnection, updateTeachers = true) {
    return this.apigwManagementApi
        .postToConnection({ 
          ConnectionId: userConnectionRec.connection_id, 
          Data: JSON.stringify(messageData) 
        })
        .promise()
        .catch(async (e: any) => {
          if (e.statusCode === 410) {
            try {
              logger.info('WS_STALE_CONNECTION', { eventType: messageData.eventType, connection: userConnectionRec })
              await this.deleteConnectionID(userConnectionRec.connection_id);
              this.apigwManagementApi.deleteConnection({ ConnectionId: userConnectionRec.connection_id })
                .promise()
                .catch((e: any) => {
                  logger.info('WS_ALREADY_DELETED', { eventType: messageData.eventType, connection: userConnectionRec })
                });

              if (updateTeachers && userConnectionRec.role_type === WS_ROLE_TYPE.STUDENT) {
                await this.updateConnectedTeachers(userConnectionRec.class_id);
              }
              return;
            } catch (err:any) {
              throw new Error(`error sending to connection caused by: ${err.message}\n${err.stack}\n ------The above causes:-----`)
            }
          } else {
            logger.error('WS_POST_TO_CONNECTION_ERR', { 
              error: e.message, 
              messageData, 
              userConnectionRec,
              updateTeachers 
            })
            throw new Error(`WS_POST_TO_CONNECTION_ERR: error sending to connection caused by: ${e.message}\n${e.stack}\n ------The above causes:-----`);
          }
        });
  }

  private async pingOnlineStudents(class_id: Id) {
    const studentsOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT);

    const pingStudentPromise = studentsOnline.map((student) => {
      return this.sendToConnectionId(
        {
          eventType: 'ping',
        },
        student, false
      );
    });

    return Promise.all(pingStudentPromise);
  }

  /**
   * Update all connected teachers with the list of currently connected students
   */
  private async updateConnectedTeachers(class_id: Id) {
    const studentsOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT);

    const studentsOnlineDataInArrays = convertObjectsToArrayOfArraysWithHeader(studentsOnline);

    return this.sendToAllTeachers(class_id, {
      eventType: 'updateConnectedStudents',
      connectedStudents: studentsOnlineDataInArrays,
    });
  }

  /**
   * Sends a message to all teachers in a given class
   * @param class_id class **group** ID
   * @returns single Promise for all sends
   */
  public async sendToAllTeachers(class_id: Id, messageData: any) {
    const teachersOnline = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.EDUCATOR);

    const messageTeachersPromises = teachersOnline.map((teacher) => this.sendToConnectionId(messageData, teacher, false));

    return Promise.all(messageTeachersPromises);
  }

  /**
   * Refresh pages of all connected teachers to indicate the changes to student data
   */
  public async refreshTeacherPages(class_group_id: Id) {
    return this.sendToAllTeachers(class_group_id, {
      eventType: 'refreshPage',
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    if(!id || !params?.query) {
      throw new Errors.BadRequest('ERR_PARAMS_MISSING')
    }

    const { userType, connectionId, classId } = params.query;

    if(!userType || !connectionId || !classId) {
      throw new Errors.BadRequest('ERR_PARAMS_MISSING')
    }

    // revoke student's socket given the id of the realtime_class_users table
    await this.deleteConnectionID(connectionId);

    if (userType === WS_ROLE_TYPE.STUDENT) { // send the updated students online list to teachers
      this.updateConnectedTeachers(classId);
    }
    return { id };
  }

  /**
   * Creates a new connection record
   * Redis:
   *  - TTL is equal to REDIS_TTL (1 day)
   *    - Sets TTL on :connection_id
   *    - Sets/Renews TTL for the class array :class_id:role_type
   *  - Stores the connection record as a stringified JSON object
   *  - Stores all connected students/educators (for a class) in a set
   * @note no SQL DB fallback
   */
  private async addConnectionID(connection_id: string, uid: string, class_id: string, role_type: WS_ROLE_TYPE, api_host: string, source_ip: string, amznTraceId: string) {
    const newConnection: ISocketConnection = {
      connection_id,
      uid,
      class_id,
      api_host,
      role_type,
      source_ip
    }
    const redis: Redis = this.app.get('redis');
    // Set first un-hashed key
    await redis.set(KWebsocketConnection(connection_id), JSON.stringify({
      ...newConnection,
      created_on: Date.now(),
    }), 'EX', REDIS_ASSESSMENT_DATA_EXPIRE)

    // Set relational keys using sharding
    try {
      await redis.pipeline()
      .sadd(KClassConnections(class_id, role_type), connection_id)
      .expire(KClassConnections(class_id, role_type), REDIS_ASSESSMENT_DATA_EXPIRE)
      .sadd(KUserConnections(uid, class_id), connection_id)
      .expire(KUserConnections(uid, class_id), REDIS_ASSESSMENT_DATA_EXPIRE)
      .exec();
    } catch (e) {
      console.error(
        'redis-pipeline-error',
        {
          redisKeys: [
            KClassConnections(class_id, role_type),
            KClassConnections(class_id, role_type),
            KUserConnections(uid, class_id),
            KUserConnections(uid, class_id)
          ],
          amznTraceId
        },
        e
      )
      throw e;
    }


    return newConnection;
  }

  /**
   * Delete a connection ID from the redis
   */
  private async deleteConnectionID(connectionId: string) {
    const redis: Redis = this.app.get('redis');

    const connectionRec = await redis.get(KWebsocketConnection(connectionId));
    if (connectionRec) {
      const { uid, class_id } = JSON.parse(connectionRec);

      // Delete hashed keys in single pipeline first
      await redis.pipeline()
        .srem(KClassConnections(class_id, WS_ROLE_TYPE.STUDENT), connectionId)
        .srem(KClassConnections(class_id, WS_ROLE_TYPE.EDUCATOR), connectionId)
        .srem(KUserConnections(uid, class_id), connectionId)
        .exec();

      // Delete unhashed key last
      await redis.del(KWebsocketConnection(connectionId))
    }
  }

  /**
   * Find and delete all previous connections for a given user and class
   * Needed to clean up stale connections
   */
  private async revokeStaleConnections(uid: Id, class_id: Id) {
    const redis: Redis = this.app.get('redis');

    const staleConnectionIds = await redis.smembers(KUserConnections(uid, class_id));
    if(staleConnectionIds.length === 0) {
      return;
    }

    const userConnections = await Promise.all(staleConnectionIds.map(async (connectionId) => redis.get(KWebsocketConnection(connectionId))))
    const staleConnections = userConnections
      .filter((connection) => connection !== null)
      .map((connection) => JSON.parse(connection!))
      .filter((connection: ISocketConnection) => {
        if(!connection.created_on) {
          return true;
        }
        return Date.now() - connection.created_on > this.wsGracePeriod;
      }) as ISocketConnection[];

    if (staleConnections.length) {
      const staleConnectionPromises = staleConnections.map((connection) => {
        return this.deleteConnectionID(connection.connection_id);
      });
      await Promise.all(staleConnectionPromises);
    }
  }

  /**
   * Force logout any existing connections for a given user and class
   */
  async forceLogout(uid: Id, class_id: Id, apiHost: string, source_ip = '') {
    const SINGLE_IP_MULTITAB_BLOCK = await getSysConstNumeric(this.app, 'SINGLE_IP_MULTITAB_BLOCK').catch(() => true)

    const connectedUsers = await this.getConnectedUsers(class_id, WS_ROLE_TYPE.STUDENT, apiHost);
    const oldConnections = connectedUsers.filter((user) => user.uid == uid);

    if (oldConnections.length) {
      const oldConnectionPromises = oldConnections.map((connection) => {
        // Do not disconnect if it's the same IP address
        if(SINGLE_IP_MULTITAB_BLOCK && source_ip?.length && connection?.source_ip?.length && connection.source_ip == source_ip) {
          return undefined;
        }

        return this.sendToConnectionId({eventType: 'forceLogout'}, connection, false);
      });
      await Promise.all(oldConnectionPromises);
    }
  }

  /**
   * Get list of connected users for a given class and role
   * @returns {ISocketConnection[]} - list of connected users
   */
  public async getConnectedUsers(class_id: Id, role_type: WS_ROLE_TYPE, api_host?: string): Promise<ISocketConnection[]> {
    const redis: Redis = this.app.get('redis');

    const onlineUsers = await redis.smembers(KClassConnections(class_id, role_type));
    if(!onlineUsers.length) {
      return [];
    }

    // const onlineUserConnections = await redis.mget(onlineUsers);
    // Get connections sequentially as they are not hashed in the same shard
    const onlineUserConnections = await Promise.all(onlineUsers.map(async (connectionId) => redis.get(KWebsocketConnection(connectionId))));

    return onlineUserConnections
      .filter((connection) => connection !== null)
      .map((connection) => JSON.parse(connection!))
  }

}
