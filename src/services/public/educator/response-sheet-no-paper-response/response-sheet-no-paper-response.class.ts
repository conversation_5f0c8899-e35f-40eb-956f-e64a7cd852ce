import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ResponseSheetNoPaperResponse implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {testQuestionId, isNoPaperResponse, testSessionId} = <any> data;
    const testAttemptId = id;

    const taqrRecord = await dbRawRead(this.app, [testAttemptId, testQuestionId, testSessionId], 
      ` Select taqr.* from test_attempt_question_responses taqr
          join test_attempts ta on ta.id = taqr.test_attempt_id
          join test_sessions ts on ta.test_session_id = ts.id
         where taqr.test_attempt_id = ?
           and taqr.test_question_id = ?
           and ts.id = ?
           and taqr.is_invalid != 1
      ;`);

    if (taqrRecord.length === 0) {
      throw new Errors.BadRequest('TAQR_ID_NOT_FOUND');
    }

    await this.app
      .service('db/write/test-attempt-question-responses')
      .patch(taqrRecord[0].id, {
        is_no_paper_response: isNoPaperResponse
      });

    if (!!isNoPaperResponse){
      const {srrtaqr_id, scan_response_require_id, scan_response_open_school_id} = await this.app.service('public/educator/resp-sheet-upload/upload-response-sheets').getScanRequireInfoByTaqrId(taqrRecord[0].id)
      const uid = await currentUid(this.app, params);
      if (srrtaqr_id){
        await this.app.service('public/school-admin/upload-scan').resolveScanRequires(uid, srrtaqr_id, scan_response_require_id, scan_response_open_school_id)
      }
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }
}
