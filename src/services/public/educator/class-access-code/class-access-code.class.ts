import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { getSysConstString } from '../../../../util/sys-const-string';
import { generateAccessCode } from '../../../../util/secret-codes';
import { patchRedisSchoolClass } from '../../../../redis/table-key-operations/school-class';
import { dbRawRead } from '../../../../util/db-raw';
import { checkRedisKey, KSessionLoadedInit } from '../../../../redis/redis';

interface Data {}

interface ServiceOptions {}

export class ClassAccessCode implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Use this function to regenerate class access code
   * @param id class id
   * @param data 
   * @param params 
   */
  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    
    //check secrete user code
    const { secreteUserCode, school_class_id } = data;
    const secreteUserRecord = await getSysConstString(this.app, 'REGEN_CLASS_ACCESS_CODE')
    const isValidSecreteUser = secreteUserCode == secreteUserRecord
    if(!isValidSecreteUser){
      throw new Errors.BadRequest('INVALID_SECRETE_USER');
    }

    //update class accesscode
    let access_code = generateAccessCode(8);
    while((await this.app.service('public/school-admin/classes').validateUniqueClassAccessCode(access_code)).length > 0){
      access_code = generateAccessCode(8);
    }
    const updatePayload = {
      access_code
    }
    const response = await this.app
      .service('db/write/school-classes')
      .patch(id, updatePayload);

    //update data in redis 
    //get the test sessions of the class
    const class_test_sessions = await dbRawRead(this.app, {school_class_id}, `
      -- 78ms
      select distinct scts.test_session_id
        from school_class_test_sessions scts
       where scts.school_class_id = :school_class_id
    ;`);

    let haveClassSessionInRedis = false;
    for(let class_test_session of class_test_sessions){
      const isSessionLoadedIntoRedisPrev = await checkRedisKey(this.app, KSessionLoadedInit(class_test_session.test_session_id));
      if(isSessionLoadedIntoRedisPrev){
        haveClassSessionInRedis = true
        break
      }
    }

    if(haveClassSessionInRedis){
      //update redis class
      patchRedisSchoolClass(this.app, school_class_id, updatePayload)

      //update redis student login cred
      await this.app.service('public/educator/session').ensureStudentCredToRedis(school_class_id)
    }

    //return new access code
    return response
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
