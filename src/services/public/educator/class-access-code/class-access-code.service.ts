// Initializes the `public/educator/class-access-code` service on path `/public/educator/class-access-code`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ClassAccessCode } from './class-access-code.class';
import hooks from './class-access-code.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/class-access-code': ClassAccessCode & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/class-access-code', new ClassAccessCode(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/class-access-code');

  service.hooks(hooks);
}
