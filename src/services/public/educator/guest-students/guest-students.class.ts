import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadCountReporting, dbRawReadReporting } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class GuestStudents implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest();
    }

    const uid = await currentUid(this.app, params);

    if(!data.class_access_code){
      throw new Errors.BadRequest('CLASS_DOES_NOT_EXIST');
    }

    const guessClassAccessCode = this.senatizeAccessCode(data.class_access_code)
    const school_class_group_id = (<any>params).query.school_class_group_id

    //check if the guest class exist and is in the same test window
    const guestClass =  await dbRawRead(this.app, [guessClassAccessCode, school_class_group_id], `
      select gsc.group_id as guest_sc_group_id, isc.group_id as invig_sc_group_id
        from school_classes gsc
        join school_semesters gss on gss.id = gsc.semester_id
        join test_windows gtw on gtw.id = gss.test_window_id
        join school_semesters iss on iss.test_window_id = gtw.id
        join school_classes isc on isc.semester_id = iss.id
       where gsc.access_code = ?
         and isc.group_id = ?
    ;`);
    
    if(guestClass.length < 1){
      throw new Errors.BadRequest('CLASS_DOES_NOT_EXIST');
    }

    //check if the guest class is the host class ( teacher should not add the host class as guest class)
    if(+guestClass[0].invig_sc_group_id === +guestClass[0].guest_sc_group_id){
      throw new Errors.BadRequest('GUEST_CLASS_IS_HOST_CLASS');
    }

    //check if the guest class is in the same school
    const guestSchoolGroupId =  await dbRawRead(this.app, [guestClass[0].guest_sc_group_id], `
      select sc.group_id, sc.schl_group_id from school_classes sc where sc.group_id = ?
    ;`);

    const hostSchoolGroupId =  await dbRawRead(this.app, [guestClass[0].invig_sc_group_id], `
      select sc.group_id, sc.schl_group_id from school_classes sc where sc.group_id = ?
    ;`);

    if(guestSchoolGroupId[0].schl_group_id == hostSchoolGroupId [0].schl_group_id){
      throw new Errors.BadRequest('GUEST_CLASS_IS_SAME_SCHOOL');
    }

    //check if the guest class already exist
    const existguestClass =  await dbRawRead(this.app, [guestClass[0].invig_sc_group_id, guestClass[0].guest_sc_group_id], `
      select *
        from school_classes_guest scg
       where scg.invig_sc_group_id = ?
         and scg.guest_sc_group_id = ?
         and is_revoked != 1
    ;`);

    if(existguestClass.length > 0){
      throw new Errors.BadRequest('GUEST_CLASS_ALREADY_EXIST');
    }

    //add into school_classes_guest
    let guessClassRecord;
    if(guestClass.length > 0){
      const theGuessClass = guestClass[0]
      const invig_sc_group_id = theGuessClass.invig_sc_group_id
      const guest_sc_group_id = theGuessClass.guest_sc_group_id
      const guessClassEntry ={
        invig_sc_group_id,
        guest_sc_group_id,
        created_by_uid: uid
      }

      guessClassRecord = await this.app
      .service('db/write/school-classes-guest')
      .create(guessClassEntry);
    }
    const newGuestClassRecord = await dbRawRead(this.app, [guessClassRecord.id], `
      select sc.id, sc.group_id, sc.schl_group_id, sc.schl_dist_group_id, sc.name, sc.access_code, sc.notes, sc.group_type, sc.is_grouping, sc.semester_id, sc.is_placeholder, scg.id as scg_id, scg.invig_sc_group_id
        from school_classes_guest scg
        join school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.is_active = 1
      where scg.id = ?
    ;`);

    const newGuestClassInvigilateRecord = await dbRawRead(this.app, [guessClassRecord.id], `
      select scg.invig_sc_group_id as group_id, urt.uid, scg.id as scg_id, urs.first_name, urs.last_name
        from mpt_dev.school_classes_guest scg 
        join mpt_dev.school_classes sc on sc.group_id = scg.guest_sc_group_id and sc.is_active = 1
        join mpt_dev.user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_teacher' and urt.is_revoked != 1
        join mpt_dev.users urs on urt.uid = urs.id
        where scg.id = ?
    ;`);

    const  newGuestStudentRecords = await dbRawRead(this.app, [newGuestClassRecord[0].group_id], `
      select u.id as uid
            , u.first_name
            , u.last_name
            , um.value as eqao_student_gov_id
            , um2.value as eqao_gender
            , um3.value as eqao_is_g9
            , um4.value as eqao_is_g10
            , um5.value as eqao_is_g3
            , um6.value as eqao_is_g6m 
            , um7.value as lang
            , um8.value as teacher_notes
            from users u
            join user_roles ur on ur.uid = u.id and ur.is_revoked != 1 and ur.group_id  = ? and role_type ='schl_student'
            join user_metas um on um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN' and um.uid = ur.uid
        left join user_metas um2 on um2.key_namespace = 'eqao_sdc' and um2.key = 'Gender' and um2.uid = ur.uid
        left join user_metas um3 on um3.key_namespace = 'eqao_sdc' and um3.key = 'IS_G9' and um3.uid = ur.uid
        left join user_metas um4 on um4.key_namespace = 'eqao_sdc' and um4.key = 'IS_G10' and um4.uid = ur.uid
        left join user_metas um5 on um5.key_namespace = 'eqao_sdc' and um5.key = 'IS_G3' and um5.uid = ur.uid
        left join user_metas um6 on um6.key_namespace = 'eqao_sdc' and um6.key = 'IS_G6' and um6.uid = ur.uid
        left join user_metas um7 on um7.key_namespace = 'eqao_dyn' and um7.key = 'Lang'  and um7.uid = ur.uid
        left join user_metas um8 on um8.key_namespace = 'eqao_dyn' and um8.key = 'TeacherNotes'  and um8.uid = ur.uid
    ;`);
    const guessClassRecordID = guessClassRecord.id
    newGuestStudentRecords.forEach(ngsr =>{
      ngsr.id = -1;
      ngsr.eqao_g9_gender = ngsr.eqao_gender
      ngsr.displayName =  ngsr.first_name.trim() + " " + ngsr.last_name.trim()
      ngsr.is_guest = 1
      ngsr.scg_id = guessClassRecordID 
    })  
    return { class: newGuestClassRecord[0], students:newGuestStudentRecords, invigilate: newGuestClassInvigilateRecord[0]};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * This function is to remove the guest class relationship
   * @param id 
   * @param params 
   * @returns 
   */
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest();
    }

    if(!id){
      throw new Errors.BadRequest("GUEST_CLASS_RELATION_ID_NOT_FOUND");
    }
    const school_classes_guest_id = id
    
    // Get started guest student operational attempts count 
    const studentAttemptCount = await dbRawReadReporting(this.app, {school_classes_guest_id}, `
      -- took 172ms to run
      select count(distinct ta.id) as started_ta_counts 
        from school_classes_guest scg
        join school_classes sc_invi on sc_invi.group_id = scg.invig_sc_group_id
        join school_class_test_sessions scts on scts.school_class_id = sc_invi.id
        join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.started_on is not null
        join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.is_secured = 1
        join user_roles ur on ur.group_id = scg.guest_sc_group_id and ur.uid = ta.uid
        where scg.id = :school_classes_guest_id
    ;`);
    
    //Block invigilator from remove the guest class relationship and throw error if any started guest student attempt is found. 
    if(studentAttemptCount[0].started_ta_counts > 0){
      throw new Errors.BadRequest("GUEST_STUDENT_ATTEMPT_STARTED");
    }
    
    const uid = await currentUid(this.app, params);
    const patchRecord ={
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
      revoked_by_uid: uid
    } 
    const response = await this.app
      .service('db/write/school-classes-guest')
      .patch(id, patchRecord);
    return response;
  }

  senatizeAccessCode(accessCode:string){
    return accessCode.replace('-','')
  }
}
