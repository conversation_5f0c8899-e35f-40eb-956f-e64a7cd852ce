import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { Knex } from 'knex';
import { dbDateNow } from '../../../../util/db-dates';
import _ from 'lodash';

interface Data {}

interface ServiceOptions {}

export class PsychItemStats implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
     * Sanitize data to ensure "NA", "N/A", "nan", "" etc. are turned into nulls for number parameters
     * @param data - object
     */
  sanitizeData(data: any) {
    const numberFields = ['a', 'b', 'g', 'b1', 'b2', 'b3', 'b4', 'b5', 'b6', 'change_scale', 'diff', 'item_mean', 'pbis', 'cpbis', 'exposure'];
    Object.keys(data).forEach((key) => {
      const value = data[key];
      if (numberFields.includes(key) && (isNaN(value) || value === '')) {
        data[key] = null;
      }
    });
  }

  /**
     * Check if the value is empty
     * @param value
     * @returns boolean
     */
  isEmptyValue(value: any) {
    return value === undefined || value === null || value === '';
  }

  /**
     * Insert psych item stats record
     * @param data - object/array
     * @returns new record id (only first id is returned)
     */
  async insertRecord(data: any, trx?: Knex.Transaction) {
    const knex: Knex = this.app.get('knexClientWrite');
    const query = trx ? trx('psych_item_stats') : knex('psych_item_stats');
    const recordId = await query.insert(data);
    return recordId;
  }

  /**
     * Revoke psych item stats record
     * @param ids
     * @param uid
     */
  async revokeRecord(ids: Id[], uid: number, trx?: Knex.Transaction) {
    const knex: Knex = this.app.get('knexClientWrite');
    const query = trx ? trx('psych_item_stats') : knex('psych_item_stats');
    await query.whereIn('id', ids).update({
      is_revoked: 1,
      revoked_by_uid: uid,
      revoked_on: dbDateNow(this.app),
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const { test_window_id, question_id, scale_code } = params.query;

    if(!test_window_id && !question_id) {
      throw new Errors.BadRequest('ERR_MISSING_TW_OR_ITEM_ID');
    }

    const isNullTwId = test_window_id === 'null';

    return dbRawRead(this.app, { test_window_id: isNullTwId ? undefined : test_window_id, question_id, scale_code }, `
      -- took 0.14s on mirror
      select id
        , psych_run_id
        , test_window_id
        , question_id
        , question_window_id
        , a
        , b
        , g
        , b1
        , b2
        , b3
        , b4
        , b5
        , b6
        , calib_year
        , change_scale
        , diff
        , item_mean
        , pbis
        , cpbis
        , exposure
        , scale_code
        , item_version
        , is_prelim
        , created_by_uid
        , created_on
        , updated_by_uid
        , updated_on
      from psych_item_stats
      where is_revoked = 0 
      ${test_window_id ? `and test_window_id ${isNullTwId ? 'is null' : '= :test_window_id'}` : ''}
      ${question_id ? 'and question_id = :question_id' : ''}
      ${scale_code ? 'and scale_code = :scale_code' : ''}
      order by id desc
      ;
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return dbRawReadSingle(this.app, { id }, `
      WITH latest_test_window AS (
        SELECT MAX(test_window_id) AS max_tw
        FROM psych_item_stats
        WHERE is_revoked = 0 AND question_id = :id
      ),
      latest_psych_run AS (
        SELECT MAX(psych_run_id) AS max_pr
        FROM psych_item_stats
        WHERE is_revoked = 0 AND question_id = :id
          AND test_window_id = (SELECT max_tw FROM latest_test_window)
      )
      SELECT 
          id, psych_run_id, test_window_id, question_id, question_window_id,
          a, b, g, b1, b2, b3, b4, b5, b6,
          calib_year, change_scale, diff, item_mean, pbis, cpbis, exposure,
          scale_code, item_version, is_prelim,
          created_by_uid, created_on, updated_by_uid, updated_on
      FROM psych_item_stats
      WHERE is_revoked = 0 
        AND question_id = :id
        AND test_window_id = (SELECT max_tw FROM latest_test_window)
        AND psych_run_id = (SELECT max_pr FROM latest_psych_run)
      ORDER BY id DESC;
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params: Params): Promise<Data> {
    if (!data || _.isEmpty(data)) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }

    // The API payload accepts both single object and object array. Turns the data into array if it is an object
    if (!Array.isArray(data)) {
      data = [data]
    }

    // Check if there are existing records
    const knex: Knex = this.app.get('knexClientRead');
    const existingRecords = await knex('psych_item_stats')
      .where({ is_revoked: 0 })
      .andWhere((query) => {
        // Function to build field condition
        const fieldConditionBuilder = (builder: Knex.QueryBuilder, field: string, value: any) => {
          return this.isEmptyValue(value) ? builder.whereNull(field).orWhere(field, '') : builder.where(field, value);
        }
        // Iterate over the input data and build the condition
        data.forEach((record: any) => {
          const { test_window_id, question_id, scale_code, item_version } = record;
          query.orWhere((recordCondition) => {
            recordCondition.where({ test_window_id });
            recordCondition.where((fieldCondition) => fieldConditionBuilder(fieldCondition, 'question_id', question_id));
            recordCondition.where((fieldCondition) => fieldConditionBuilder(fieldCondition, 'scale_code', scale_code));
            recordCondition.where((fieldCondition) => fieldConditionBuilder(fieldCondition, 'item_version', item_version));
          });
        });
    });

    // Throw an error if there are existing records
    if (existingRecords.length > 0) {
      const existingRecordsDetails = JSON.stringify(existingRecords.map((record) => ({
        id: record.id,
        test_window_id: record.test_window_id,
        question_id: record.question_id,
        scale_code: record.scale_code,
        item_version: record.item_version
      })));
      throw new Errors.BadRequest(`There are ${existingRecords.length} records already exist in the database. Please check and try again: ${existingRecordsDetails}`);
    }

    // Sanitize data
    data.forEach((record: any) => {
      this.sanitizeData(record);
      delete record.id;
    });

    // Insert records into DB
    const created_by_uid = await currentUid(this.app, params);
    const created_on = dbDateNow(this.app);
    data = data.map((record: any) => ({ ...record, created_by_uid, created_on }));
    await this.insertRecord(data);
    return { success: true };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params: Params): Promise<any> {
    if (!data || _.isEmpty(data)) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }

    // The API payload accepts both single object and object array. Turns the data into array if it is an object
    if (!Array.isArray(data)) {
      data = [data]
    }

    // Get ids from the data
    const ids = data.map((record: any) => record.id);
    if (!ids.every((id: any) => id > 0)) {
      throw new Errors.BadRequest('ERR_INVALID_ID');
    }
    if (new Set(ids).size !== ids.length) {
      throw new Errors.BadRequest('ERR_DUPLICATE_ID');
    }

    // Get the existing records
    const knexRead: Knex = this.app.get('knexClientRead');
    const existingRecords = await knexRead('psych_item_stats')
      .where({ is_revoked: 0 })
      .whereIn('id', ids);
    const existingRecordMap = new Map(existingRecords.map(record => [record.id, record]));

    // Validate with the existing records
    data.forEach((record: any) => {
      const { id, test_window_id, question_id, scale_code, item_version } = record;
      const existingRecord = existingRecordMap.get(id);

      // Throw an error if the record does not exist in the database
      if (!existingRecord) {
        throw new Errors.BadRequest(`Record id: ${id} does not exist in the database`);
      }

      // Compare keys with the existing record
      const isMatch = (a: any, b: any) => a === b || (a != null && b != null && a.toString() === b.toString()) || (this.isEmptyValue(a) && this.isEmptyValue(b));
      const isKeyMatch = test_window_id === existingRecord.test_window_id
        && isMatch(question_id, existingRecord.question_id)
        && isMatch(scale_code, existingRecord.scale_code)
        && isMatch(item_version, existingRecord.item_version);

      // Throw an error if the keys do not match
      if (!isKeyMatch) {
        throw new Errors.BadRequest(`The keys of record id: ${id} do not match with the existing record`);
      }
    })

    // Sanitize data
    data.forEach((record: any) => {
      this.sanitizeData(record);
      delete record.id;
    });

    // Transcation
    const knexWrite: Knex = this.app.get('knexClientWrite');
    const trx = await knexWrite.transaction();

    try {
      // Revoke existing records
      const uid = await currentUid(this.app, params);
      await this.revokeRecord(ids, uid, trx);
  
      // Insert records into DB
      const created_on = dbDateNow(this.app);
      data = data.map((record: any) => ({ ...record, created_by_uid: uid, created_on }));
      await this.insertRecord(data, trx);
  
      // Commit the transaction
      await trx.commit();
  
      return { success: true };
    } catch (error) {
      // Rollback the transaction on error
      await trx.rollback();
      throw error;
    }
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params: Params): Promise<any> {
    if (!id) {
      throw new Errors.BadRequest('ERR_MISSING_ID');
    }
    const uid = await currentUid(this.app, params);
    await this.revokeRecord([id], uid);
    return { id };
  }
}
