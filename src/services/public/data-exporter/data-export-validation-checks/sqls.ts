const getExportSchema = (testWindowId: number, exportId: number) => `eqao_export_${exportId}_tw_${testWindowId}`;

const getExportSchemas = (testWindowId: number, refExportId: number, newExportId: number) => ({
  refExportSchema: getExportSchema(testWindowId, refExportId),
  newExportSchema: getExportSchema(testWindowId, newExportId),
});

export const getRemovedStudentCountSQL = (testWindowId: number, refExportId: number, newExportId: number) => {
  const { refExportSchema, newExportSchema } = getExportSchemas(testWindowId, refExportId, newExportId);
  return `
    select distinct
        old_export.uid
      , old_export.class_id
    from ${refExportSchema}.TestTakerRegistrationInfo old_export
    left join ${newExportSchema}.TestTakerRegistrationInfo new_export
      on new_export.uid = old_export.uid
    join school_classes sc
      on sc.id = old_export.class_id
    join user_roles ur
      on ur.uid = old_export.uid
      and ur.group_id = sc.group_id
    join data_export_jobs dej_ref
      on dej_ref.id = :refExportId
    join data_export_jobs dej_new
      on dej_new.id = :newExportId
    where new_export.uid is null 
      and ur.is_revoked = 1
      and ur.revoked_on > dej_ref.created_on
      and ur.revoked_on < dej_new.created_on
    group by old_export.uid
    ;
  `
};

export const getMissingTestAttemptSQL = (testWindowId: number, refExportId: number, newExportId: number) => {
  const { refExportSchema, newExportSchema } = getExportSchemas(testWindowId, refExportId, newExportId);
  return `
    select distinct
        ta.id
      , ta.uid
      , ta.test_session_id
      , ta.twtdar_order
      , ta.twtdar_id
      , ta.test_form_id
      , ta.created_on
      , ta.started_on
      , ta.last_touch_on
      , ta.is_closed
      , ta.closed_on
      , ta.is_invalid
      , ta.invalid_on
      , ta.invalid_by_uid
      , ta.invalid_reason
    from ${refExportSchema}.SubmittedTestAttempts old_export
    left join ${newExportSchema}.SubmittedTestAttempts new_export on new_export.id = old_export.id
    join test_attempts ta on ta.id = old_export.id
    where new_export.id is null
    ;
  `
};

export const getGuestStudentListSQL = () => {
  return `
    select distinct 
        ur.uid
      , schl.foreign_id as sch_mident
      , schl.name as sch_name
      , host_schl.foreign_id as host_mident
      , host_schl.name as host_name
    from school_classes_guest scg
    join school_classes sc on sc.group_id = scg.guest_sc_group_id
    join school_semesters ss on ss.id = sc.semester_id and ss.test_window_id = :testWindowId
    join test_windows tw on tw.id = ss.test_window_id
    join schools schl on schl.group_id = sc.schl_group_id and schl.is_sandbox = 0
    join school_districts sd on sd.group_id = sc.schl_dist_group_id and (sd.is_sample = 0 or tw.is_qa = 1)
    left join school_classes sc_invig on sc_invig.group_id = scg.invig_sc_group_id
    left join school_class_test_sessions scts on scts.school_class_id = sc_invig.id
    left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.is_invalid = 0
    join user_roles ur on ur.group_id = scg.guest_sc_group_id and ur.role_type = 'schl_student' and (ur.is_revoked = 0 or (ur.uid = ta.uid and ta.id is not null))
    join school_classes host_sc on host_sc.group_id = scg.invig_sc_group_id
    join schools host_schl on host_schl.group_id = host_sc.schl_group_id
    where scg.is_revoked = 0
    ;
  `
};