import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { Knex } from 'knex';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  testWindowId: number;
  questionId: string;
  score: number;
  scoreCategory: number;
}

interface QuestionData {
  score: number;
  scoreCategory: number;
}

interface ServiceOptions {}

export class PsychItemScoreCategories implements ServiceMethods<Data | QuestionData[]> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
     * Insert psych item score category record
     * @param testWindowId
     * @param questionId 
     * @param score 
     * @param scoreCategory
     * @param uid
     * @returns new record id
     */
  async insertRecord(testWindowId: number, questionId: string, score: number, scoreCategory: number, uid: number) {
    // create new record
    const record = {
      test_window_id: testWindowId,
      question_id: questionId,
      score: score,
      score_category: scoreCategory,
      created_on: dbDateNow(this.app),
      created_by_uid: uid,
    };

    // insert record into db
    const knex: Knex = this.app.get('knexClientWrite');
    const recordId = await knex('psych_item_score_categories').insert(record);
    return recordId;
  }

  /**
     * Revoke psych item score category record
     * @param id
     * @param uid
     */
  async revokeRecord(id: Id, uid: number) {
    const knex: Knex = this.app.get('knexClientWrite');
    await knex('psych_item_score_categories').where({ id }).update({
      is_revoked: 1,
      revoked_by_uid: uid,
      revoked_on: dbDateNow(this.app),
    });
  }

  /**
     * Update psych item score category record
     * @param id
     * @param questionId 
     * @param score 
     * @param scoreCategory
     * @param uid
     */
  async updateRecord(id: Id, questionId: string, score: number, scoreCategory: number, uid: number) {
    const knex: Knex = this.app.get('knexClientWrite');
    await knex('psych_item_score_categories').where({ id }).update({
      question_id: questionId,
      score: score,
      score_category: scoreCategory,
      updated_by_uid: uid,
      updated_on: dbDateNow(this.app),
    });
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query || !params.query.test_window_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const { test_window_id } = params.query;

    return dbRawRead(this.app, { test_window_id }, `
      select id
        , test_window_id
        , question_id
        , score
        , score_category
        , created_by_uid
        , created_on
        , updated_by_uid
        , updated_on
      from psych_item_score_categories
      where is_revoked = 0 and test_window_id = :test_window_id
      order by id desc
      ;
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<QuestionData[]> {
    if(!id){
      throw new Errors.BadRequest("ERR_MISSING_QUESTION_ID");
    }
    const data = await dbRawRead(this.app, { qId: id }, `
      SELECT
        score,
        score_category as scoreCategory
      FROM psych_item_score_categories
      WHERE is_revoked = 0
        AND question_id = :qId
        AND test_window_id = (
          SELECT MAX(test_window_id)
          FROM psych_item_score_categories
          WHERE question_id = :qId AND is_revoked = 0
        )
      ORDER BY id DESC;
    `);
    return data as QuestionData[]
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<any> {
    if (!data) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }
    const { testWindowId, questionId, score, scoreCategory } = data;
    const uid = await currentUid(this.app, params);
    const recordId = await this.insertRecord(testWindowId, questionId, score, scoreCategory, uid);
    return recordId;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params: Params): Promise<any> {
    if (!id || !data) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }
    const { questionId, score, scoreCategory } = data;
    const uid = await currentUid(this.app, params);
    await this.updateRecord(id, questionId, score, scoreCategory, uid);
    return { id };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params: Params): Promise<any> {
    if (!id) {
      throw new Errors.BadRequest('ERR_MISSING_ID');
    }
    const uid = await currentUid(this.app, params);
    await this.revokeRecord(id, uid);
    return { id };
  }
}
