import { NullableId, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import axios, { AxiosInstance } from 'axios';
import { dbDateNow } from '../../../../util/db-dates';
import { PDFDocument } from 'pdf-lib';
import { SCAN_UPLOAD_TYPE } from './../../educator/resp-sheet-upload/upload-response-sheets/upload-response-sheets.class'
import logger from '../../../../logger';

interface Data {}

interface ServiceOptions {}

export class UploadScan implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = this.app.get('eqaoScanningService');
  }

  schl_admin_upload_scan_folder = 'schl_admin_upload_scan/';

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const { s3_file_link } = params.query;

    if( !s3_file_link ){
      throw new Errors.BadRequest('NO_UPLOAD_FILE_FOUND');
    }

    return generateS3DownloadUrl( s3_file_link )
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: number, params?: Params): Promise<Data> {
    if (!params || ! params.query){
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const scan_response_require_id = +id

    if(!scan_response_require_id){
      throw new Errors.BadRequest('SCAN_RESPONSE_REQUIRE_ID_INVALID');
    }

    return this.fetchStuScanResponseUploadsInfo(scan_response_require_id);
  }

  async fetchStuScanResponseUploadsInfo(scan_response_require_id:number, ignoreScanResponseOpenSchoolRevoke = false){
    // fetch all related scan_response_uploads that have the same test attempt as the srr_origin
    const stuscanResponseUploadsInfo = await dbRawWrite(this.app, [scan_response_require_id], `
        select srr.id as srr_id
              , srr.test_attempt_id as test_attempt_id
              , sru.id as sru_id 
              , sru.created_on as upload_date
              , sru.comment
              , sru.s3_file_link
              , sru.upload_type
              , srr.is_resolved 
          from scan_response_require srr_origin
          join scan_response_open_schools sros on sros.id = srr_origin.scan_response_open_school_id
          join scan_response_require srr on srr.student_uid = srr_origin.student_uid and srr.test_attempt_id = srr_origin.test_attempt_id
          join scan_response_uploads sru on sru.scan_response_require_id = srr.id and sru.is_revoked = 0
        where srr_origin.id = ?
      order by sru.created_on desc  
    ;`)

    return stuscanResponseUploadsInfo
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params || ! params.query || !data){
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const { file_path, srr_id, comment } = data
    const { schl_group_id } = params.query

    const created_by_uid = await currentUid(this.app, params);

    //Check if the file size is greater than 3 pages
    const full_file_path = generateS3DownloadUrl( file_path )
    const max_page_size = 4
    const pdfPageSize = await this.getPDFPageSize(full_file_path)
    if( pdfPageSize >  max_page_size){
      throw new Errors.BadRequest('PDF_PAGE_SIZE_TOO_LARGE')
    }  

    //*****************Try to resolve scan using QR code****************************
    //Gather info needed for QR Code scan
    const studentInfo = await dbRawRead(this.app, {srr_id}, `
       SELECT srr.id
            , scts.test_session_id
            , scts.school_class_id
            , sc.group_id as school_class_group_id
            , schl.is_sasn_login
            , srr.student_uid
            , ta.id as test_attempt_id
        FROM scan_response_require srr
        join test_attempts ta on ta.id = srr.test_attempt_id
        join test_sessions ts on ts.id = ta.test_session_id
        join school_class_test_sessions scts on scts.test_session_id = ts.id
        join school_classes sc on sc.id = scts.school_class_id
        join schools schl on schl.group_id = sc.schl_group_id
        join scan_response_open_schools sros on sros.id = srr.scan_response_open_school_id and sros.is_revoked = 0
       where srr.id = :srr_id
         and srr.is_revoked = 0
    ;`) 

    if(!studentInfo.length){ // This shouldn't happen. But put in there just in case.
      throw new Errors.BadRequest('STUDENT_INFO_ERROR');
    }

    const testSessionId = studentInfo[0].test_session_id
    const schl_class_group_id = studentInfo[0].school_class_group_id
    const school_class_id = studentInfo[0].school_class_id
    const student_uid = studentInfo[0].student_uid
    const test_attempt_id = studentInfo[0].test_attempt_id
    const isSasnBool = !!studentInfo[0].is_sasn_login
    
    //add admin to the class as invigilator so he can pass the hook. 
    await this.app.service('public/school-admin/invigilate-class').patch( null, {class_id:school_class_id, schl_group_id}, params) 

    //Scan the QR code and get the related student uid and upload scan files data
    const fromSchoolAdminUpload = true;
    const studentSubmittedSubsessions = await this.app.service('public/educator/resp-sheet-upload/qrcode-contents').getStudentSubmittedSessions(params, testSessionId, schl_class_group_id, school_class_id, fromSchoolAdminUpload);
    
    // Get the scan response require TAQR subsession slugs before trying to process results
    const scanResponseRequireTQARSubsessionSlugs = await dbRawRead(this.app, {srr_id}, `
      SELECT distinct
             srrtaqr.id as srrtaqr_id
           , srrtaqr.scan_response_require_id
           , tsss.slug
           , taqr.test_question_id
           , srr.scan_response_open_school_id
        FROM scan_response_require_taqr srrtaqr
        join test_session_sub_sessions tsss on tsss.id = srrtaqr.tsss_id
        join test_attempt_question_responses taqr on taqr.id = srrtaqr.taqr_id
        join scan_response_require srr on srr.id = srrtaqr.scan_response_require_id
       where srrtaqr.scan_response_require_id = :srr_id
         and srrtaqr.is_revoked = 0
    ;`)
    
    // Set a timeout for the scanning service call (5 seconds)
    const SCAN_TIMEOUT_MS = 10_000;
    
    // Start the scanning service call - we'll use this same promise for both immediate and background processing
    const scanningPromise = this.eqaoScanningService.post(`/split-and-scan`, { 
      bulkFile: full_file_path, 
      isSasn: isSasnBool, 
      validSlugsByStudent: studentSubmittedSubsessions 
    });
    
    // Create a timeout promise
    let timeoutReached = false;
    const timeoutPromise = new Promise<null>((resolve) => {
      setTimeout(() => {
        timeoutReached = true;
        resolve(null);
      }, SCAN_TIMEOUT_MS);
    });
    
    // Race between scanning completion and timeout
    await Promise.race([
      timeoutPromise,
      scanningPromise.then(async (res: any) => {
        // Only process immediately if timeout hasn't been reached
        if (!timeoutReached) {
          const studentQrData = res.data.filter((data: any) => +(Object.keys(data)?.[0]) === +student_uid);
          await this.processScanResults(studentQrData, scanResponseRequireTQARSubsessionSlugs, test_attempt_id, isSasnBool, created_by_uid, params);
        }
      })
    ]);

    //create a new record to scan-response-uploads
    const newScanResponseUploadData = {
      created_by_uid,
      scan_response_require_id: srr_id,
      s3_file_link: file_path,
      upload_type: SCAN_UPLOAD_TYPE.ADMIN_SCAN_RECONCILIATION,
      comment,
      is_revoked:0,
    }

    await this.app.service('db/write/scan-response-uploads').create(newScanResponseUploadData);
    
    // If timeout was reached, continue with background processing and return early
    if (timeoutReached) {
      logger.info('UPLOAD_SCAN_TIMEOUT_EXCEEDED', {
        srr_id,
        student_uid,
        test_attempt_id,
        file_path,
      });
      
      // Continue processing in background without awaiting
      scanningPromise
        .then(async (res: any) => {
          const studentQrData = res.data.filter((data: any) => +(Object.keys(data)?.[0]) === +student_uid);
          await this.processScanResults(studentQrData, scanResponseRequireTQARSubsessionSlugs, test_attempt_id, isSasnBool, created_by_uid, params);
          logger.info('UPLOAD_SCAN_BACKGROUND_PROCESSING_COMPLETED_SUCCESSFULLY', {
            srr_id,
            student_uid,
            test_attempt_id,
            file_path,
          });
        })
        .catch((error) => {
          logger.error('UPLOAD_SCAN_BACKGROUND_PROCESSING_ERROR', {
            srr_id,
            student_uid,
            test_attempt_id,
            file_path,
            error: error.message,
          });
        });
      
      // Return a message indicating background processing
      const StuScanResponseUploadsInfo = await this.fetchStuScanResponseUploadsInfo(srr_id);
      const scan_response_require_tsss_slugs = await dbRawRead(this.app, {srr_id}, `
        select distinct
                tsss.slug
          from scan_response_require_taqr srrt
          join test_session_sub_sessions tsss on tsss.id = srrt.tsss_id
          where srrt.scan_response_require_id = :srr_id
            and srrt.is_resolved = 0
            and srrt.is_revoked = 0  
      ;`);
      
      return { 
        StuScanResponseUploadsInfo, 
        scan_response_require_tsss_slugs,
        processing_status: 'PROCESSING_IN_BACKGROUND'
      };
    }

    //fetch all student upload scans info and the session that still need upload scans
    const scan_response_require_tsss_slugs = await dbRawRead( this.app, {srr_id}, `
      select distinct
              tsss.slug
        from scan_response_require_taqr srrt
        join test_session_sub_sessions tsss on tsss.id = srrt.tsss_id
        where srrt.scan_response_require_id = :srr_id
          and srrt.is_resolved = 0
          and srrt.is_revoked = 0  
    ;`)
    const StuScanResponseUploadsInfo = await this.fetchStuScanResponseUploadsInfo(srr_id);
    return { StuScanResponseUploadsInfo, scan_response_require_tsss_slugs}
  }

  async getPDFPageSize(PDF_URL:string): Promise<number> {
    let numPages: number = 0;
  
    try {
      // Load the PDF file from its HTTP link
      const response = await axios.get(PDF_URL, { responseType: 'arraybuffer' });
      const pdfBytes = response.data;
  
      // Count the number of pages in the PDF file
      const pdfDoc = await PDFDocument.load(pdfBytes);
      numPages = pdfDoc.getPages().length;
    } catch (e) {
      throw new Errors.BadRequest('ERR_LOAD_PDF_FILE');
    }
  
    // Return true if the PDF file has no more than 3 pages
    return numPages;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  /** Resolve the missing scan record for the question, and for the student and school if that was the last scan missing */
  async resolveScanRequires(created_by_uid: number, srrtaqr_id: number, scan_response_require_id: number, scan_response_open_school_id: number){
    
    //resolve the scan_response_require_taqr record and resolve scan_response_require if all scan_response_require_taqr is resolved.
    await this.app.service('db/write/scan-response-require-taqr').patch(srrtaqr_id, {
      is_resolved: 1,
      resolved_on: dbDateNow(this.app),
      resolved_by: created_by_uid
    })
    const minIsResolve = await dbRawWrite(this.app, [ scan_response_require_id ], `
      SELECT min(is_resolved) as min_is_resolve
        FROM scan_response_require_taqr srrtaqr
        where srrtaqr.scan_response_require_id = ?
          and srrtaqr.is_revoked = 0
    ;`)

    if(+minIsResolve[0].min_is_resolve){
      await this.app.service('db/write/scan-response-require').patch(scan_response_require_id, {
        is_resolved: 1,
        resolved_on: dbDateNow(this.app),
        resolved_by: created_by_uid
      })
    }

    //close the school upload button if no  scans is required required for the school (all student's scan is resolve/revoked)
    const minIsResolveSchool = await dbRawWrite(this.app, [ scan_response_open_school_id ], `
        select min(srr.is_resolved+srr.is_revoked) as min_srr_revoked_resolve
          from scan_response_open_schools sros
          join scan_response_require srr on srr.scan_response_open_school_id = sros.id
          where sros.id = ?
      group by sros.id
    ;`)

    if(+minIsResolveSchool[0].min_srr_revoked_resolve){
      await this.app.service('db/write/scan-response-open-schools').patch(scan_response_open_school_id, {
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid: created_by_uid
      })
    }
    
  }

  /**
   * Process the scan results for a student
   */
  async processScanResults(
    studentQrData: any[], 
    scanResponseRequireTQARSubsessionSlugs: any[], 
    test_attempt_id: number, 
    isSasnBool: boolean,
    created_by_uid: number,
    params: Params
  ): Promise<void> {
    for(let student of studentQrData) {
      const studentKeys = Object.keys(student);
      if (studentKeys.length > 0) {
        const studentKey = studentKeys[0];
        const filePath = student[studentKey].filePath
        const filePathCropped = student[studentKey].filePathCropped
        const question_slug = student[studentKey].question_slug
        
        //replace the upload scan if that sub session is in scan_response_require_taqr
        const missingScanSubSession = scanResponseRequireTQARSubsessionSlugs.find((subsessionSlug: any) => subsessionSlug.slug.toLowerCase().includes(question_slug.toLowerCase()));
        if(missingScanSubSession){
          const test_question_id = missingScanSubSession.test_question_id 
          const fullScanConfig = { filePath }
          const croppedScanConfig = { filePath: filePathCropped }
          const uploadParams = { fullScanConfig, croppedScanConfig, test_attempt_id, test_question_id, isBulk: true, isSasn: isSasnBool,  sessionSlug: question_slug, upload_type: SCAN_UPLOAD_TYPE.ADMIN_SCAN_RECONCILIATION, params }
          await this.app.service('public/educator/resp-sheet-upload/upload-response-sheets').submitTaqrForUploads(uploadParams);
          await this.resolveScanRequires(created_by_uid, missingScanSubSession.srrtaqr_id, missingScanSubSession.scan_response_require_id,  missingScanSubSession.scan_response_open_school_id)
        }
      }
    }
  }
}
