import { MethodNotAllowed } from '@feathersjs/errors';
import { Id, NullableId, Pa<PERSON>ated, Params, ServiceMethods } from '@feathersjs/feathers';
import { DBD_U_ROLE_TYPES, DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { defaultDomain } from '../../../../constants/mail-constants';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { AccountType } from '../../../../types/account-types';
import { generateAccessCode } from '../../../../util/secret-codes';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from "../../../../util/db-raw";
import { dbDateNow } from '../../../../util/db-dates';
import logger from '../../../../logger';
import { convertTypes, TypeConvertSourceType, TypeConvertTargetType } from '../../../../util/type-convert';
import moment from 'moment';
import _ from 'lodash';
import { Knex } from 'knex';

interface Data {
}
// schl_group_id":1212,"schl_dist_group_id":1128,"name":"test55","foreign_id":"","educator_id

interface INewClassroom {
  schl_group_id: number,
  schl_dist_group_id: number,
  foreign_id?: string,
  semester_id: number,
  name: string,
  educator_id: number,
  course_type?: string,
  group_type?: string,
  is_grouping?: number,
  uuid?:any,
  is_fi?:number,
}
interface IClassroomRecordCore {
  id: number,
  group_id: number,
  schl_group_id: number,
  access_code: string,
}
interface IClassroom extends INewClassroom {
  id: number,
  notes?: string,
  is_active?: number,
  created_on?: string,
  is_fi?:number,
}
interface ServiceOptions { }

enum CLASS_CREATION_RESULT {
  SUCCESS = 'SUCCESS',
  BLANK_CLASS_CODE = 'BLANK_CLASS_CODE',
  DUPL_CLASS_CODE = 'DUPL_CLASS_CODE',
  Invalid_Term_Format = 'Invalid_Term_Format',
  NO_ACTIVE_WINDOW = 'NO_ACTIVE_WINDOW',
  ERR_STUDENT_INFO_LOCKED = 'ERR_STUDENT_INFO_LOCKED'
}

export class Classes implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const schl_group_id = params.query.schl_group_id;
    // G9 2024-2025 reporting spec change: All registered students should have a report. If a class report exist, show class to admin
    const g9ReportClasses =  await dbRawRead(this.app, [schl_group_id], `
      -- Benchmark: 0.045 sec
        select sc.id as sc_id
            , sc.group_id as sc_group_id 
            , sc.name as class_code
            , ss.name as semester_label
            , case 
                when u.id is not null then concat(u.first_name," ",u.last_name) 
                else '' 
              end as educator
            , SUM(CASE WHEN ur2.is_revoked = 0 THEN 1 ELSE 0 END) AS students
            , tw.id as tw_id
            , tw.date_start as tw_date_start
            , tw.date_end as tw_date_end
            , tw.is_allow_appeals as tw_is_allow_appeals
            , sta.allow_ISR
        from test_windows tw
        join school_semesters ss on ss.test_window_id =tw.id
        join school_classes sc on sc.semester_id = ss.id and sc.schl_group_id = ?
   left join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_teacher' and ur.is_revoked != 1
   left join users u on u.id = ur.uid 
        join user_roles ur2 on ur2.group_id = sc.group_id and ur2.role_type = 'schl_student'
   left join school_test_access sta on sta.test_window_id = tw.id and sta.school_group_id = sc.schl_group_id and sta.is_revoked != 1
        where tw.type_slug = 'EQAO_G9M'
      and ( tw.is_active = 1 or tw.show_inactive_tw_report = 1)
      group by sc.id   
    ;`)
    return g9ReportClasses
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: IClassroom[], params?: Params): Promise<Data> {
    let returnResult: any[] = [];
    
    for (let classroom of data){
      const classroomData = {
          schl_group_id: classroom.schl_group_id,
          schl_dist_group_id: classroom.schl_dist_group_id,
          foreign_id: '',
          semester_id: classroom.semester_id,
          group_type: classroom.course_type,
          is_grouping: classroom.is_grouping,
          name: classroom.name,
          educator_id: classroom.educator_id,
          is_fi: classroom.is_fi,
          uuid: classroom.uuid,
      }

      try {
        const testWindowId = await this.app.service('public/educator/session').getActiveTestWindowForSemester(classroom.semester_id);
        await this.app.service('public/school-admin/student').checkStudentInfoLock(testWindowId)

        const createClassRecordAndResultMsg = await this.createClassAndAssignTeacher(classroomData)

        returnResult.push(createClassRecordAndResultMsg)
      } catch (err:any) {
        const resultMessageRes:CLASS_CREATION_RESULT|string = err.message

        returnResult.push({...classroomData, resultMessageRes})
      }  
    }
    return returnResult;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: any, data: any, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest();
    }
    const { schl_group_id } = (<any>params).query;
    const schl_data = {
      name: data.name,
      semester_id: data.semester_id,
      is_fi:data.is_fi
    }
    const uid = await currentUid(this.app, params);

    const classInActiveTW =  await dbRawRead(this.app, {id}, `
      select sc.id
        from school_classes sc
        join school_semesters sm
          on sm.id = sc.semester_id
        join test_windows tw
          on tw.id =  sm.test_window_id
         and tw.date_end > now()
       where sc.id = :id
    ;`)

    if(classInActiveTW.length === 0){
      throw new Errors.BadRequest('CLASS_IN_INACTIVE_TW');
    }

    const classroom = <Paginated<any>>await this.app
      .service('db/read/school-classes')
      .find({
        query: {
          id: id
        }
      });

    if(classroom.data.length < 1){
      throw new Errors.BadRequest('CLASS_ROOM_NOT_FOUND');
    }

    const testWindowId = await this.app.service('public/educator/session').getActiveTestWindowForSemester(classroom.data[0].semester_id);
    await this.app.service('public/school-admin/student').checkStudentInfoLock(testWindowId);

    if( +classroom.data[0].is_fi == 1 && +schl_data.is_fi == 0){
      throw new Errors.BadRequest('INVALID_IS_FI');
    }

    this.validateUniqueClassCode ({
      semester_id: data.semester_id,
      name: data.name,
      schl_group_id: data.schl_group_id,
    }, classroom.data[0].id)


    if (classroom.total > 0) {
      const currentTeacherRecord = <Paginated<any>>await this.app
        .service('db/read/user-roles')
        .find({
          query: {
            role_type: DBD_U_ROLE_TYPES.schl_teacher,
            group_id: classroom.data[0].group_id,
            is_revoked: 0
          }
        });
      if (data.educator_id && data.educator_id != '') {
        if (currentTeacherRecord.total > 0) {
          if (currentTeacherRecord.data[0].uid != data.educator_id) {
            currentTeacherRecord.data.map(async (record: any) => {
              await this.app
                .service('db/write/user-roles')
                .patch(record.id, {
                  is_revoked: 1,
                  revoked_on: dbDateNow(this.app),
                  revoked_by_uid: uid
                })
            })
          }
        }
        if (currentTeacherRecord.total == 0 || currentTeacherRecord.data[0].uid != data.educator_id) {
          await this.app
            .service('db/write/user-roles')
            .create({
              role_type: DBD_U_ROLE_TYPES.schl_teacher,
              uid: data.educator_id,
              group_id: classroom.data[0].group_id,
              created_on: dbDateNow(this.app),
              created_by_uid:uid
            })
        }
      } else {
        currentTeacherRecord.data.map(async (record: any) => {
          await this.app
            .service('db/write/user-roles')
            .patch(record.id, {
              is_revoked: 1,
              revoked_on: dbDateNow(this.app),
              revoked_by_uid: uid
            })
        })
      }
    }
  
    await this.app
      .service('db/write/school-classes')
      .patch(id, schl_data)

    await this.updateClassStudentFrenchImm(classroom.data[0].id, uid)
    await this.updateClassStudentTermFormat(classroom.data[0].id, uid)

    return data
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * This method is to copy class from one test window to another test window
   * @param id 
   * @param data 
   * @param params 
   * @returns 
   */
  async patch(id: NullableId, data:any[], params?: Params): Promise<Data> {
    if(!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    const created_by_uid = await currentUid(this.app, params);

    const classes = data;
    //const newClasses:any[] = [];
    await Promise.all(classes.map(async theClass =>{
      const theClassDetail = <any>await this.app
        .service('db/read/school-classes').find({
          query: {
            id: theClass.id
          },
          paginate:false
        });

      const testWindowId = await this.app.service('public/educator/session').getActiveTestWindowForSemester(theClass.new_semester_id);
      const test_windows =  await dbRawRead(this.app, {testWindowId}, `select * from test_windows where id  = :testWindowId`)
      const key_namespace  =  this.app.service('public/school-admin/alternative-version-request').getMetaKeyNameSpace(test_windows[0].type_slug);
      await this.app.service('public/school-admin/student').checkStudentInfoLock(testWindowId);

      if(theClassDetail[0].is_placeholder === 1){
        return;
      }

      const classNameExistInDestinateTW =  await dbRawRead(this.app, [theClassDetail[0].name, theClassDetail[0].schl_group_id, theClass.new_semester_id ], `
        select sc.*
          from school_semesters ss
          join school_semesters ss2 on ss2.test_window_id = ss.test_window_id
          join school_classes sc on sc.semester_id = ss2.id and sc.name = ? and sc.is_active = 1 and sc.schl_group_id = ?
         where ss.id = ?
      ;`)

      if(classNameExistInDestinateTW.length > 0){
        return;
      }

      // if(theClass.id){
      //   await this.app
      //       .service('db/write/school-classes')
      //       .patch(theClass.id, {
      //         semester_id: theClass.new_semester_id,
      //     })
      // }

      //create new class
      const newClass = await this.createClassRecord({
        schl_group_id: theClassDetail[0].schl_group_id,
        schl_dist_group_id: theClassDetail[0].schl_dist_group_id, // should be determined based on the school group ID
        foreign_id: '',
        semester_id: theClass.new_semester_id,
        group_type: theClassDetail[0].group_type,
        is_grouping: theClassDetail[0].is_grouping,
        name: theClassDetail[0].name,
        educator_id:-1
      })

      theClass.newClassId = newClass.id
      theClass.newgroup_id = newClass.group_id
      theClass.newaccess_code = newClass.access_code

      //inactive old class
      const inactivedClass = await this.app
             .service('db/write/school-classes')
             .patch(theClass.id, {
               is_active: 0,
      })

      await this.createClassIsActiveUpdateRecord(created_by_uid, theClassDetail[0], inactivedClass.is_active);

      //put the old class teacher and students in new class
      const classUserRoles = <any>await this.app
        .service('db/read/user-roles').find({
          query: {
            group_id: theClassDetail[0].group_id,
            is_revoked: 0
          },
          paginate:false
        });

      await Promise.all(classUserRoles.map( async (cur:any) =>{
        await this.app
          .service('db/write/user-roles')
          .create({
            role_type: cur.role_type,
            uid: cur.uid,
            group_id: newClass.group_id,
            created_by_uid
        })
      }))

      //reset student's red flag
      const classStudentuserRoles = classUserRoles.filter((classUserRole:any) => classUserRole.role_type === DBD_U_ROLE_TYPES.schl_student)
      const classStudentUids =  classStudentuserRoles.map((classStudentuserRole:any) => {return +classStudentuserRole.uid})

      if(classStudentUids.length){
        await this.resetStudentsRedFlag(classStudentUids, key_namespace, created_by_uid)
        
        //re-validate student redflag
        await this.validateStudentByUidClassGrpId(classStudentUids, newClass.group_id, created_by_uid)
      }
    }))

    logger.info({
      created_by_uid,
      slug: 'CLASS_COPY',
      data: JSON.stringify(data)
    })

    return data
  }

  /**
   * Re-validate students in student_uids in class class_group_id
   * Need to make sure student_uids are in class group id
   * @param student_uids 
   * @param class_group_id 
   * @param created_by_uid 
   */
  async validateStudentByUidClassGrpId(student_uids:number[], class_group_id:number, created_by_uid:number ){
    const schoolClassEntries:any = await this.app.service('db/read/school-classes').find({query: {group_id: class_group_id}});
    const schoolClassEntry = schoolClassEntries.data[0]
    const testWindowId = await this.app.service('public/educator/session').getActiveTestWindowForClass(schoolClassEntry.id);
    const schl_group_id = schoolClassEntry.schl_group_id;
    const semester_id = schoolClassEntry.semester_id;
    const schoolEntries = <Paginated<any>> await this.app.service('db/read/schools').find({query: {group_id: schl_group_id}});
    const schoolEntry = schoolEntries.data[0];
    const classes_students = await dbRawRead(this.app, {class_group_id, student_uids }, `
      select ur.group_id
           , ur.uid
        from user_roles ur
       where ur.role_type = 'schl_student'
         and ur.is_revoked != 1
         and ur.group_id = :class_group_id
         and ur.uid in (:student_uids)
    ;`);
    const students = await dbRawRead(this.app, {student_uids}, `
      select u.id id
            , u.first_name first_name
            , u.last_name last_name
        from users u
        where u.id in (:student_uids)
    ;`);
    const students_meta = await dbRawRead(this.app, {student_uids}, `
      select um.uid
          , um.key_namespace
          , um.key
          , um.value
          , um.id
       from user_metas um
      where um.uid in (:student_uids)
    ;`);
    const semesters = await dbRawRead(this.app, {semester_id}, `
      select id
           , foreign_id
           , foreign_scope_id
           , name
           , test_window_id
        from school_semesters
        where id = :semester_id
    ;`);
    const test_windows = await dbRawRead(this.app, {testWindowId}, `
      select * from test_windows where id = :testWindowId
    ;`);
    console.log({
      schl_group_id, //sch_group_id
      classes_students, // classes_students
      classes:[ schoolClassEntry ], //classes 
      students, //students 
      students_meta, //students_meta
      created_by_uid, //created_by_uid
      IsSASNLogin : schoolEntry.is_sasn_login, //IsSASNLogin 
      isprivate: schoolEntry.is_private, //isprivate
      semesters,//school_semesters 
      lang : schoolEntry.lang, //lang 
      school_type : schoolEntry.school_type, //school_type 
      test_windows //test_windows:any
    })
    //re-validate student redflag
    await this.app
      .service('public/school-admin/school')
      .validateStudents( 
        schl_group_id, //sch_group_id
        classes_students, // classes_students
        [ schoolClassEntry ], //classes 
        students, //students 
        students_meta, //students_meta
        created_by_uid, //created_by_uid
        schoolEntry.is_sasn_login, //IsSASNLogin 
        schoolEntry.is_private, //isprivate
        semesters,//school_semesters 
        schoolEntry.lang, //lang 
        schoolEntry.school_type, //school_type 
        test_windows //test_windows:any
      );
  }

  /**
   * reset student red flag.
   * @param student_uids 
   * @param key_namespace 
   * @param created_by_uid 
   */
  async resetStudentsRedFlag(student_uids:number[], key_namespace:string, created_by_uid:number){
    const redFlagUserMetaRecords = await dbRawRead(this.app, {student_uids, key_namespace}, `
     select um.* 
       from user_metas um
      where um.key_namespace = :key_namespace
        and um.key = 'errMsg'
        and um.uid in (:student_uids)
    ;`)
    const knex: Knex = this.app.get('knexClientWrite');
    for(let redFlagUserMetaRecordChunk of _.chunk(redFlagUserMetaRecords) ){
      await Promise.all(redFlagUserMetaRecordChunk.map(async (redFlagUserMetaRecord:any) =>{
        await knex('user_metas')
        .update({ 
          key_namespace: redFlagUserMetaRecord.key_namespace+'_deleted',
          key: redFlagUserMetaRecord.key+ '-'+ moment().utc().format('YYYY-MM-DD HH:mm:ss'),
          updated_on: dbDateNow(this.app),
          updated_by_uid: created_by_uid,
         })
        .where({ 
          'id': redFlagUserMetaRecord.id
        })
      }))
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    const { schl_group_id, lang } = (<any>params).query;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = userInfo.uid;

    //0. check the class/group is not a place_holder and have no student in it
    const student_role_type = DBD_U_ROLE_TYPES.schl_student
    var classToRemoved = await dbRawReadSingle(this.app, [student_role_type, id], `
          -- 78ms with 752 student in class
           select sc.*
                , ss.test_window_id
                , count(distinct ur.id) as class_student_counts
             from school_classes sc
             join school_semesters ss on sc.semester_id = ss.id
        left join user_roles ur on ur.group_id = sc.group_id and role_type = ? and ur.is_revoked = 0   
            where sc.id = ?
          group by sc.id  
    ;`)

    if(classToRemoved.is_placeholder == 1){
      throw new Errors.BadRequest('PLACEHOLDER_CLASS');
    }

    //throw error if the class have student in it.
    if(+classToRemoved.class_student_counts > 0){
      throw new Errors.BadRequest('CLASS_HAVE_STUDENTS');
    }

    await this.app.service('public/school-admin/student').checkStudentInfoLock(classToRemoved.test_window_id);

    //1. Find place_holder class
    var placeholderClass = (await dbRawWrite(this.app, [classToRemoved.test_window_id, classToRemoved.schl_group_id], `
        select sc.*
          from school_classes sc
          join school_semesters ss
            on ss.id = sc.semester_id
           and ss.test_window_id = ?
         where sc.schl_group_id = ?
           and sc.is_active = 1
           and sc.is_placeholder = 1
    `))[0]

    if(placeholderClass == undefined){
      const group_type = classToRemoved.group_type;
      let nameSlug
      switch (group_type) {
        case 'EQAO_G3':
          nameSlug = 'lb_class_placeholder_primary'
        break;
        case 'EQAO_G6':
          nameSlug = 'lb_class_placeholder_junior'
        break;
        case 'EQAO_G9':
          nameSlug = 'lb_class_placeholder_g9'
        break;
        case 'EQAO_G10':
          nameSlug = 'lb_class_placeholder_osslt'
        break;
        default:
          nameSlug = 'lb_class_placeholder_primary'
        break;
      }
      await this.app.service('public/translation').getOneBySlug('subj_email_applicant_notif', lang);

      const name = await this.app.service('public/translation').getOneBySlug(nameSlug, lang);

      const classGroup = await this.app
      .service('db/write/u-groups')
      .create({
        group_type: DBD_U_GROUP_TYPES.school_class,
        description: name,
      });

      const group_id = classGroup.id;
      placeholderClass = await this.app
      .service('db/write/school-classes')
      .create({
        schl_group_id: classToRemoved.schl_group_id,
        schl_dist_group_id: classToRemoved.schl_dist_group_id,
        name: name,
        semester_id: classToRemoved.semester_id,
        is_grouping: classToRemoved.is_grouping,
        group_type: classToRemoved.group_type,
        created_by_uid: uid,
        created_on: dbDateNow(this.app),
        access_code: null,  //leave it to null so it can not create session
        group_id,
        is_active: 1,
        is_placeholder: 1,
      });
    }
    //2 revoke all students in the removed class and add them to the place_holder class
    var records = await dbRawRead(this.app, [classToRemoved.group_id], `
        select * from user_roles where  group_id = ? and is_revoked = 0 and role_type = 'schl_student';
    `)

    if (records.length > 0){
      await Promise.all(records.map(async (record: any) => (
        this.app
          .service('db/write/user-roles')
          .patch(record.id, {
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
        })
      )))

      await Promise.all(records.map(async (record: any) => (
        this.app
          .service('db/write/user-roles')
          .create({
            role_type: DBD_U_ROLE_TYPES.schl_student,
            uid: record.uid,
            group_id: placeholderClass.group_id
        })
      )))
    }

    //2.1 update student FrenchImm and Termformat to match placeholder class value
    const student_uids = Array.from(new Set(records.map(record => record.uid))); //get distinct list of student uids in the class
    for(let student_uid_chunks of _.chunk(student_uids, 5)){
      await Promise.all( student_uid_chunks.map(async student_uid =>{
        await this.updateStudentFrenchImm(placeholderClass.id, student_uid, uid) //uid is created_by_uid
        await this.updateStudentTermFormat(placeholderClass.id, student_uid, uid) //uid is created_by_uid
      }))
    }
    
    //3. set the class is_active:0
    const inactivedClass = await this.app
      .service('db/write/school-classes')
      .patch(id, {
        is_active: 0
      })
    
    if(uid) {
      await this.createClassIsActiveUpdateRecord(uid, classToRemoved, inactivedClass.is_active);
    }

    return <any>{ id }

  }

  private async createClassIsActiveUpdateRecord(uid: number, classDetail: any, is_active: number) {
    // Create a logger 
    logger.info({
      created_by_uid: uid,
      slug: 'SCHOOL_ADMIN_REMOVE_CLASS',
      data: JSON.stringify(classDetail)
    })

    // Insert inactive class record into db table
    let active_to_inactive = +classDetail.is_active === 1 && +is_active === 0 ? 1 : 0;
    let inactive_to_active = +classDetail.is_active === 0 && +is_active === 1 ? 1 : 0;

    await this.app
      .service('db/write/school-class-active-status-updates')
      .create({
        school_class_group_id: classDetail.group_id,
        active_to_inactive, 
        inactive_to_active,
        created_by_uid: uid,
        created_on: dbDateNow(this.app),
      });
  }

  private async ensureTeacherInSchool(uid: number, group_id: number) {
    const schoolTeacherRecord = <Paginated<any>>await this.app
      .service('db/read/user-roles')
      .find({
        query: {
          role_type: DBD_U_ROLE_TYPES.schl_teacher,
          uid,
          group_id,
          is_revoked: 0
        }
      });
    if (schoolTeacherRecord.total < 1) {
      const teacherRecord = await this.app
        .service('db/write/user-roles')
        .create({
          role_type: DBD_U_ROLE_TYPES.schl_teacher,
          uid,
          group_id,
        })
    }
  }

  private async assignTeacherToClass(uid: number, group_id: number, schl_group_id: number) {
    await this.ensureTeacherInSchool(uid, schl_group_id);
    const teacherRecord = await this.app
      .service('db/write/user-roles')
      .create({
        role_type: DBD_U_ROLE_TYPES.schl_teacher,
        uid,
        group_id,
      })
  }

  async validateUniqueAccessCode(classInfo: INewClassroom, access_code: string) {
    return await this.app
      .service('db/read/school-classes')
      .find({
        query: { access_code, schl_group_id: classInfo.schl_group_id, is_active: 1 },
        paginate: false
      })
  }

  async validateUniqueClassCode(config: {semester_id:number, name:string, schl_group_id:number}, class_id = -1) {
    // TODO: vea-api#539 Class code should only need to be unique within active test windows
    let {
      name,
      schl_group_id,
      semester_id,
    } = config;

    if (!name){
      throw new Errors.Conflict('BLANK_CLASS_CODE');
    }

    name = name.trim();

    const classesInSameSemesterTwWindow = await dbRawRead(this.app, [semester_id, name, schl_group_id], `
      select sc2.*
      from school_semesters ss1
      join test_windows tw1
        on tw1.id = ss1.test_window_id
        and ss1.id = ?
      join test_windows tw2
        on tw2.type_slug = tw1.type_slug
        and tw2.is_active = 1
      join school_semesters ss2
        on tw2.type_slug = tw1.type_slug
        and ss2.test_window_id = tw2.id
      join school_classes sc2
        on ss2.id = sc2.semester_id
        and sc2.name = ?
        and sc2.is_active = 1
        and sc2.schl_group_id = ?
    ;`);

    if (classesInSameSemesterTwWindow.length > 0 && classesInSameSemesterTwWindow[0].id != class_id) {
      throw new Errors.Conflict('DUPL_CLASS_CODE');
    }
  }

  async validateSemester(classInfo: INewClassroom) {
    if(classInfo.semester_id == -1){
      throw new Errors.Conflict('Invalid_Term_Format');
    }
  }

  async validateUniqueClassAccessCode(access_code: string) {
    return <any[]>await this.app
      .service('db/read/school-classes')
      .find({
        query: {access_code},
        paginate: false
      })
  }

  private async createClassRecord(classInfo: INewClassroom): Promise<IClassroomRecordCore> {
    let access_code = generateAccessCode(8);
    while((await this.validateUniqueClassAccessCode(access_code)).length > 0){
      access_code = generateAccessCode(8);
    }
    const schl_group_id = classInfo.schl_group_id;

    let successCreateUGroup = false;
    let classGroup;
    while(!successCreateUGroup){
      try{
        classGroup = await this.app
          .service('db/write/u-groups')
          .create({
            group_type: DBD_U_GROUP_TYPES.school_class,
            description: classInfo.name,
          });
        successCreateUGroup  = true;
      } catch(err){
        logger.error(err)
        if((<any>err).stack.includes("Lock wait timeout exceeded")){
          logger.error("Timeout while insert into u_group. group_type:"+DBD_U_GROUP_TYPES.school_class+" classInfo:"+classInfo)
        }
        else{
          throw err;
        }
      }
    }

    const group_id = classGroup.id;
    const classroom = await this.app
      .service('db/write/school-classes')
      .create({
        schl_group_id,
        schl_dist_group_id: classInfo.schl_dist_group_id,
        name: classInfo.name,
        semester_id: classInfo.semester_id,
        is_grouping: classInfo.is_grouping,
        group_type: classInfo.group_type,
        access_code,
        group_id,
        is_active: 1,
        is_fi: classInfo.is_fi
      });
    return {
      id: classroom.id,
      group_id,
      schl_group_id,
      access_code
    }
  }

  async findClassesByName(name: string, schl_group_id: number, schl_dist_group_id: number) {

    return <any[]>await this.app
      .service('db/read/school-classes')
      .find({
        query: { name, schl_group_id, schl_dist_group_id, is_active: 1 },
        paginate: false
      });
  }

  async findClassesById(id: number) {
    return await this.app.service('db/read/school-classes').get(id)
  }

  async ensureClassAndAssignTeacher(classInfo: IClassroom) {
    let classRecordSummary: IClassroomRecordCore;
    try {
      const classes = await this.findClassesById(classInfo.id)
      classRecordSummary = classes;
    }
    catch {
      classRecordSummary = await this.createClassRecord(classInfo);
    }
    await this.assignTeacherToClass(classInfo.educator_id, classRecordSummary.group_id, classInfo.schl_group_id);
    return {
      ...classInfo,
      ...classRecordSummary,
    }
  }

  async createClassAndAssignTeacher(classInfo: INewClassroom) {
    let classRecordSummary:any;
    let resultMessageRes:CLASS_CREATION_RESULT;
    try{
      await this.validateSemester(classInfo);
      await this.validateUniqueClassCode({
        semester_id: classInfo.semester_id,
        name: classInfo.name,
        schl_group_id: classInfo.schl_group_id,
      })
    }
    catch(e:any){
      resultMessageRes = e.message
      classRecordSummary = { id: -1,}
      return { ...classInfo, ...classRecordSummary, resultMessageRes }
    }
    classRecordSummary = await this.createClassRecord(classInfo);
    if (classInfo.educator_id && classInfo.educator_id!= -1) {
      await this.assignTeacherToClass(classInfo.educator_id, classRecordSummary.group_id, classInfo.schl_group_id);
    }
    return {
      ...classInfo,
      ...classRecordSummary,
      resultMessageRes: CLASS_CREATION_RESULT.SUCCESS
    }
  }

  async updateClassStudentFrenchImm(classroom_id:number, created_by_uid:any){
    const classroom = <Paginated<any>>await this.app
      .service('db/read/school-classes')
      .find({
        query: {
          id: classroom_id
        }
      });
    const studentsUid = await dbRawRead(this.app, [classroom.data[0].group_id], `
      select uid from user_roles ur where ur.is_revoked != 1 and role_type = 'schl_student' and ur.group_id = ?
    ;`);

    const student_uids = studentsUid.map(record => record.uid)

    await Promise.all(student_uids.map(async uid =>{
      this.updateStudentFrenchImm(classroom.data[0].id, uid, created_by_uid)
    }))
  }

  /**
  * Updates all students term format user meta in a class
  */
  async updateClassStudentTermFormat(classroom_id:number, created_by_uid:any){
    const classroom = <Paginated<any>>await this.app
      .service('db/read/school-classes')
      .find({
        query: {
          id: classroom_id
        }
      });
    const studentsUid = await dbRawRead(this.app, [classroom.data[0].group_id], `
      select uid from user_roles ur where ur.is_revoked != 1 and role_type = 'schl_student' and ur.group_id = ?
    ;`);

    const student_uids = studentsUid.map(record => record.uid)

    await Promise.all(student_uids.map(uid => this.updateStudentTermFormat(classroom.data[0].id, uid, created_by_uid)))
  }

  async updateStudentFrenchImm(ClassId:any, student_id: any, created_by_uid:any){
    const classroom = <Paginated<any>>await this.app
      .service('db/read/school-classes')
      .find({
        query: {
          id: ClassId
        }
      });

    if(classroom.data.length == 0) {
      return
    }
    
    const key_namespace = await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKeyNamespace,  classroom.data[0].group_type)
    const key =           await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKey,           classroom.data[0].group_type)

    if(!key_namespace || !key){
      return
    }

    const is_fi = !!classroom.data[0].is_fi
    const studentFIMeta = await dbRawRead(this.app, {student_id, key_namespace, key}, `
      select * from user_metas um where um.uid = :student_id and um.key_namespace = :key_namespace and um.key = :key
    ;`);

    if(studentFIMeta && studentFIMeta.length>0) {
      await this.app
        .service('db/write/user-metas')
        .patch(studentFIMeta[0].id, {
          value:is_fi?'1':'#',
          updated_on:dbDateNow(this.app),
          updated_by_uid: created_by_uid
        })
    } else {
      await this.app
        .service('db/write/user-metas')
        .create({
          uid:student_id,
          key_namespace,
          key,
          value:is_fi?'1':'#',
          created_on: dbDateNow(this.app),
          updated_on:dbDateNow(this.app),
          created_by_uid,
          updated_by_uid: created_by_uid
        })
    }
  }

    /**
  * Updates a student's term format user meta 
  */
  async updateStudentTermFormat(ClassId:any, student_id: any, created_by_uid:any){
    const classroom = <Paginated<any>>await this.app
      .service('db/read/school-classes')
      .find({
        query: {
          id: ClassId
        }
      });

    if(classroom.data.length == 0) {
      return
    }
    
    if(classroom.data[0].group_type !== 'EQAO_G9'){
      return
    }

    const key_namespace = 'eqao_sdc'
    const key = 'TermFormat'    

    if(!key_namespace || !key){
      return
    }

    const semester_foreign_id =  await dbRawRead(this.app, [classroom.data[0].semester_id], `
      select ss.foreign_id
      from school_semesters ss
      join school_classes sc on sc.semester_id = ss.id
      where ss.id = ?
      limit 1
    ;`)

    const term_format = semester_foreign_id[0].foreign_id
    const studentTermFormatMeta = await dbRawRead(this.app, {student_id, key_namespace, key}, `
      select * from user_metas um where um.uid = :student_id and um.key_namespace = :key_namespace and um.key = :key
    ;`);


    if(studentTermFormatMeta.length) {
      await this.app
      .service('db/write/user-metas')
      .patch(studentTermFormatMeta[0].id, {
        value:term_format,
        updated_on:dbDateNow(this.app),
        updated_by_uid: created_by_uid
      })
    }else {
      await this.app
        .service('db/write/user-metas')
        .create({
          uid:student_id,
          key_namespace,
          key,
          value:term_format,
          created_on: dbDateNow(this.app),
          updated_on:dbDateNow(this.app),
          created_by_uid,
          updated_by_uid: created_by_uid
        })
    }
  }
}
