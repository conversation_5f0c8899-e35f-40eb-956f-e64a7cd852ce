import { AlternativeAction } from '../../../payment-ctrl/alternative-payments/alternative-payments.class';

export const emailTemplateSlugs = [
  "payment_invoice_eqao_logo",
  "sa_purchase_invoice_eqao_info",
  "sa_lbl_date",
  "sa_lbl_purchase_invoice_number",
  "sa_lbl_purchase_invoice_transaction_num",
  "sa_lbl_purchase_invoice_num_of_sessions",
  "sa_lbl_purchase_invoice_school_name",
  "sa_lbl_purchase_invoice_assessment_name",
  "sa_lbl_purchase_invoice_admin_window",
  "sa_lbl_purchase_invoice_num_of_sessions_purchased",
  "sa_lbl_purchase_invoice_num_of_attempts",
  "sa_lbl_purchase_invoice_list_paid_students",
  "sa_lbl_purchase_invoice_payment_method",
  "sa_lbl_purchase_invoice_total_cost",
  "sa_lbl_purchase_invoice_student_name",
  "sa_lbl_purchase_invoice_student_oen",
  "sa_lbl_purchase_invoice_refund_policy",
  "sa_payment_stripe_overview_refund_policy",
  "sa_lbl_purchase_invoice_class_name",
  "sa_lbl_school_mident",
  "sa_lbl_refund_invoice_total_cost",
  "sa_lbl_refund_invoice_num_of_attempts",
  "lbl_original_invoice"
]


export const actionToTitleSlug = {
  [AlternativeAction.CONTACT_EQAO]: "sa_lbl_purchase_invoice_contact_eqao",
  [AlternativeAction.APPROVE]: "sa_lbl_purchase_invoice_approve",
  [AlternativeAction.REFUND]: "sa_lbl_purchase_invoice_refund",
  [AlternativeAction.CANCEL]: "sa_lbl_purchase_invoice_cancel",
  [AlternativeAction.PENDING]: "sa_lbl_purchase_invoice_pending",
  [AlternativeAction.EDIT_TRANSACTION]: ""
}

export const actionToDescriptionSlug = {
  [AlternativeAction.CONTACT_EQAO]: "sa_lbl_purchase_invoice_contact_eqao_desc",
  [AlternativeAction.APPROVE]: "sa_lbl_purchase_invoice_approve_desc",
  [AlternativeAction.REFUND]: "sa_lbl_purchase_invoice_refund_desc",
  [AlternativeAction.CANCEL]: "sa_lbl_purchase_invoice_cancel_desc",
  [AlternativeAction.PENDING]: "sa_lbl_purchase_invoice_pending_desc",
  [AlternativeAction.EDIT_TRANSACTION]: ""
}

export const metadataToHumanReadable = { 
  administrationWindow: "Administration Window", 
  assessmentName: "Assessment Name", 
  purchase_by_uid: "Purchased By (UID)",
  schl_group_id: "School Group ID", 
  schl_mident: "School Mident", 
  schoolName: "School Name", 
  sessionNames: "Class Name", 
  test_session_ids: "Test Session ID",
  test_session_purchase_intent_id: "Test Purchase Intent ID"
} as {[Identifier: string]: string}

export const humanreadableToMetadata = Object.entries(metadataToHumanReadable).reduce((acc, [key, value]) => ({...acc, [value]: key}), {}) as {[Identifier: string]: string}

export const QA_DOMAINS = [
  "http://localhost:4200", 
  "http://localhost:4401", 
  "https://d3h4m0g2lmrmq6.cloudfront.net",
  "https://qc4.mathproficiencytest.ca",
  "https://d3k4cvve5iw7mr.cloudfront.net",
  "https://qc7.vretta.com",
  "https://d24a8vriqswfok.cloudfront.net",
]

export const INTERNAL_INVOICE_BCC_EMAIL = '<EMAIL>'