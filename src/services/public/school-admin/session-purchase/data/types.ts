export interface StripePaymentMetadata {
  "Administration Window": string;
  "Assessment Name": string;
  "Purchased By (UID)": number;
  "School Group ID": number;
  "School Mident": number;
  "School Name": string;
  "Class Name": string;
  "Test Session ID": number;
  "Test Purchase Intent ID": number;
  "studentsInfo"?: string; // DEPRECATED
}

export interface StripeInternalMetadata {
  administrationWindow: string;
  assessmentName: string;
  purchase_by_uid: number;
  schl_group_id: number;
  schl_mident: number;
  schoolName: string;
  sessionNames: string;
  test_session_ids: number;
  test_session_purchase_intent_id: number;
  studentsInfo?: string | any; // DEPRECATED
}