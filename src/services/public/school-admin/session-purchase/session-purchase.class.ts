import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import StripeAP<PERSON> from 'stripe';
import { LZString } from '../../../../util/lzstring';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbRawReadCount, dbRawReadSingle } from '../../../../util/db-raw';
import { AlternativeAction } from '../../payment-ctrl/alternative-payments/alternative-payments.class';
import { StripeInternalMetadata, StripePaymentMetadata } from './data/types'
import { addAsmtPrefixToInvoice, PaymentStatus, PurchaseMethods } from '../../payment-ctrl/data/types';
import { emailTemplateSlugs, actionToTitleSlug, actionToDescriptionSlug, humanreadableToMetadata, QA_DOMAINS, INTERNAL_INVOICE_BCC_EMAIL } from './data/data'
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';


interface Data {
  [key: string]: any,
}

interface ServiceOptions { }

export class SessionPurchase implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  private convertPriceForStripe(amount: number): number | undefined {
    return Math.floor(amount * 100);
  }

  private generateRefundTable(slugs: any, data: any) {
    const {
      sa_lbl_refund_invoice_num_of_attempts,
      sa_lbl_purchase_invoice_payment_method,
      sa_lbl_purchase_invoice_transaction_num,
      sa_lbl_refund_invoice_total_cost,
      lbl_original_invoice
    } = slugs;
    const {refundStudentNum, refundTransactionNumber, item_purchase_method, totalRefund} = data;

    const refundTable = `
    <table style="text-align: left; border-collapse: collapse;min-width: 700px; max-width: 100vw;">
      <tbody>
        <tr>
          <th style="background-color: #d3d3d3; border: 1px solid #bbbbbb; padding: 2px 1rem;">
            ${sa_lbl_refund_invoice_num_of_attempts}
          </th>
          <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${refundStudentNum}</td>
        </tr>
        <tr>
          <th style="background-color: #d3d3d3; border: 1px solid #bbbbbb; padding: 2px 1rem;">
            ${sa_lbl_purchase_invoice_payment_method}
          </th>
          <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${item_purchase_method}</td>
        </tr>
        <tr>
          <th style="background-color: #d3d3d3; border: 1px solid #bbbbbb; padding: 2px 1rem;">
            ${sa_lbl_purchase_invoice_transaction_num}
          </th>
          <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${refundTransactionNumber || 'N/A'}</td>
        </tr>
        <tr>
          <th style="background-color: #d3d3d3; border: 1px solid #bbbbbb; padding: 2px 1rem;">
            ${sa_lbl_refund_invoice_total_cost}
          </th>
          <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">\$${totalRefund}</td>
        </tr>
      </tbody>
    </table>
    <br/>
    <h2 style="text-align: center">** ${ lbl_original_invoice } **</h2>
    `
    return refundTable;
  }

  /**
   * @param stripeMetadata - metadata from stripe payment in human readable format
   * @returns metadata in internal format
   */
  private stripeMetadataToInternalMetadata(stripeMetadata: StripePaymentMetadata) : StripeInternalMetadata {
    const metadata: any = {}
    for(const humanKey in stripeMetadata) {
      const internalKey = humanreadableToMetadata[humanKey];
      metadata[internalKey] = (stripeMetadata as any)[humanKey];
    }

    return metadata;
  }

  private async parseStripeEvent(stripeMetadata: StripePaymentMetadata): Promise<StripeInternalMetadata> {
    console.log('stripeMetadata', stripeMetadata);
    const metadata = this.stripeMetadataToInternalMetadata(stripeMetadata);

    // Read students_data from TSPI table and parse JSON
    const knex: Knex = this.app.get('knexClientWrite');
    const studentsDataJson = await knex('test_session_purchase_intents').select('students_data').where('id', metadata.test_session_purchase_intent_id).first();
    const studentsData: number[] = JSON.parse(studentsDataJson?.students_data || '[]');

    metadata.studentsInfo = studentsData;
    
    return metadata;
  }

  async sendInvoiceEmail(emailData: any) {
    const mailCore = this.app.service('mail/core');
    const lang = (await dbRawReadSingle(this.app, [emailData.schl_group_id], `select lang from schools s where group_id = ?;`)).lang

    const emailTemplate = await this.app.service('public/translation').getOneBySlug('sa_pay_invoice_template', lang)
    const parameterMapping = {} as any;
    for(let slug of emailTemplateSlugs) {
      parameterMapping[slug] = await this.app.service('public/translation').getOneBySlug(slug, lang)
    }

    const emailAddresses = [(await this.app.service('db/read/users').get(emailData.purchase_by_uid)).contact_email]
    let { contact_email } = emailData // Stripe email address
    if (contact_email)
      emailAddresses.push(contact_email)

    const payment_action = await this.app.service('public/translation').getOneBySlug(actionToTitleSlug[emailData.action as AlternativeAction], lang)
    let payment_action_description = await this.app.service('public/translation').getOneBySlug(actionToDescriptionSlug[emailData.action as AlternativeAction], lang)

    const item_purchase_method_slug = emailData.purchase_method_id === 1 ? 'sa_lbl_payment_overview_method_credit_card' : 'sa_lbl_payment_overview_method_alternative'
    const item_purchase_method = await this.app.service('public/translation').getOneBySlug(item_purchase_method_slug, lang)

    switch(emailData.action) {
      case AlternativeAction.PENDING:
        emailData.transaction_number = await this.app.service('public/translation').getOneBySlug('lbl_pending', lang);
        break;
      case AlternativeAction.REFUND:
        payment_action_description = this.generateRefundTable(parameterMapping, {...emailData, item_purchase_method});
        parameterMapping.sa_payment_stripe_overview_refund_policy = "";
        parameterMapping.sa_lbl_purchase_invoice_refund_policy = "";
        break;
    }
    if(!Array.isArray(emailData.sessionNames)) {
      emailData.sessionNames = [emailData.sessionNames];
    }
    let sessionNames = ``
    for (let className of emailData.sessionNames) {
      sessionNames += `<tr style="border: 1px solid #bbbbbb; padding: 2px 1rem;">
        <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${emailData.schoolName}</td>
        <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${emailData.schl_mident || "N/A"}</td>
        <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${className}</td>
        <td style="border: 1px solid #bbbbbb; padding: 2px 1rem;">${emailData.administrationWindow}</td>
      </tr>`
    }

    const bccAddresses = [INTERNAL_INVOICE_BCC_EMAIL]
    if (!emailData.is_qa) {
      const bccEqaoAddress = await this.app.service('public/translation').getOneBySlug('sa_pay_invoice_bcc_address', lang)
      bccAddresses.push(bccEqaoAddress)
    }

    if (this.app.get('isStressTest')){
      return;
    }

    await mailCore.sendEmails({
      whitelabel: 'eqao-api.vretta.com',
      emailAddresses,
      bccAddresses,
      subject: 'Invoice - EQAO Assessment sessions - #' + emailData.invoiceId,
      emailTemplate,
      parameterMapping: {
        ...parameterMapping,
        payment_action,
        payment_action_description,
        invoice_number: emailData.invoiceId,
        purchase_trans_num: emailData.transaction_number,
        item_schl_name: emailData.schoolName,
        item_assessment_name: emailData.assessmentName,
        item_admin_window: emailData.administrationWindow,
        item_class_name_length: 1,
        SESSIONS: sessionNames,
        CURRENT_TIMESTAMP: (new Date()).toString(),
        item_num_student_attempts_purchased: emailData.numOfStudents,
        item_total_amount: emailData.totalCost,
        item_purchase_method,
      }
    }, false);
  }

  async processPayment(isSuccessful: boolean, eventData: any, purchase_method_id = 1) {
    let stripe_id = undefined;
    let metaData: StripeInternalMetadata;

    if (purchase_method_id === 1) { // Stripe
      metaData = await this.parseStripeEvent(eventData.metadata);
      stripe_id = isSuccessful ? eventData.payment_intent : eventData.id
    } else { // Alternative
      metaData = eventData;
      isSuccessful = false
    }

    const {
      test_session_ids,
      sessionNames,
      schl_group_id,
      schl_mident,
      purchase_by_uid,
      schoolName,
      assessmentName,
      administrationWindow,
      // numOfStudents,
      // totalCost,
    } = metaData;

    const price_per_student = await this.getPricePerStudent(test_session_ids);

    // StudentsNotPaid is passed in stripe metadata
    let studentsNotPaid: any[] = [];
    if(purchase_method_id === PurchaseMethods.STRIPE) {
      studentsNotPaid = metaData.studentsInfo
    } else {
      studentsNotPaid = await this.getStudentsNotPaid([test_session_ids]);
    }
    const classGroupIds = [...new Set(studentsNotPaid.map((e: any) => e.class_group_id))];

    const studentsByClass: any = {};
    for (let i = 0; i < studentsNotPaid.length; i++) {
      studentsByClass[studentsNotPaid[i].class_group_id] = (studentsByClass[studentsNotPaid[i].class_group_id] || []);
      studentsByClass[studentsNotPaid[i].class_group_id].push(studentsNotPaid[i].uid);
    }

    for (let class_group_id of classGroupIds) {
      const num_student_attempts_purchased = studentsByClass[`${class_group_id}`].length;
      const test_session_id = Array.isArray(metaData.test_session_ids) ? metaData.test_session_ids[0] : metaData.test_session_ids;
      const test_session = await this.app.service('db/read/test-sessions').get(test_session_id);
      const purchased_total = Number(price_per_student) * studentsNotPaid.filter(stu => stu.class_group_id === class_group_id).length
      let alternative_status = PaymentStatus.PENDING;
      if (purchase_method_id === 1)
        alternative_status = isSuccessful ? PaymentStatus.APPROVED : PaymentStatus.DECLINED;

      const tspData = {
        test_window_id: test_session.test_window_id,
        schl_group_id,
        class_group_id,
        purchase_method_id, // Stripe by default
        purchase_by_uid,
        num_student_attempts_purchased,
        ordered_on: dbDateNow(this.app),
        stripe_id,
        is_approved: isSuccessful,
        approved_by_uid: isSuccessful ? purchase_by_uid : null,
        approved_on: isSuccessful ? dbDateNow(this.app) : null,
        alternative_status,
        purchased_total,
        is_qa: !eventData.livemode
      }
      await this.checkForDuplicatePayment(tspData);
      const testSessionPurchase = await this.app.service('db/write/test-session-purchases').create(tspData);
      await this.updateInvoiceId(testSessionPurchase);
      this.app.service('db/write/purchase-invoices-log').create({
        invoice_id: testSessionPurchase.id,
        is_approved: isSuccessful,
        payment_status_id: alternative_status,
      })

      for (let uid of studentsByClass[`${class_group_id}`]) {
        const sap = await this.app.service('db/write/student-attempt-purchases').create({
          ts_purchase_id: testSessionPurchase.id,
          payment_status: alternative_status,
          uid
        });
        this.app.service('db/write/purchased-attempts-log').create({
          purchased_attempt_id: sap.id,
          updated_on: dbDateNow(this.app),
        });
      }

      let action = isSuccessful ? AlternativeAction.APPROVE : AlternativeAction.CANCEL
      if(purchase_method_id === 2)
        action = AlternativeAction.PENDING

      const emailData = {
        contact_email: eventData.customer_details?.email || eventData.last_payment_error?.payment_method?.billing_details?.email,
        schl_group_id,
        schl_mident,
        purchase_by_uid,
        invoiceId: testSessionPurchase.invoice_id,
        transaction_number: stripe_id,
        schoolName,
        assessmentName,
        administrationWindow,
        sessionNames: sessionNames,
        numOfStudents: studentsNotPaid.length,
        totalCost: Number(price_per_student) * studentsNotPaid.length,
        purchase_method_id,
        action,
        is_qa: !eventData.livemode
      };
      await this.sendInvoiceEmail(emailData);
      return testSessionPurchase;
    }
  }

  private async updateInvoiceId(testSessionPurchase: any) {
    const testWindow = await this.app.service('db/read/test-windows').db()
                        .select('type_slug')
                        .where({ id: testSessionPurchase.test_window_id })
                        .first()
    const invoiceId = await addAsmtPrefixToInvoice(this.app, `${testSessionPurchase.ordered_on.getFullYear()}-${testSessionPurchase.id}`, testWindow.type_slug);
    return this.app.service('db/write/test-session-purchases').patch(testSessionPurchase.id, { invoice_id: invoiceId }).then(() => {
      testSessionPurchase.invoice_id = invoiceId;
    });
  }

  private async processStripePayment(data: Data): Promise<Data> {
    // from data, pull relevant information (sessions, number of test attempts) to calculate price needed to create stripe checkout instance
    const stripeAPI = this.app.get('stripe_api');

    const stripe = new StripeAPI(stripeAPI.secretKey, {
      apiVersion: stripeAPI.apiVersion,
    })

    const {
      domain,
      schl_group_id,
      schl_mident,
      lang,
      test_session_ids,
      purchase_by_uid,
      schoolName,
      assessmentName,
      administrationWindow,
      sessionNames,
    } = data;

    const price_per_student = await this.getPricePerStudent(test_session_ids[0]);
    const studentsNotPaid = await this.getStudentsNotPaid(test_session_ids);

    const test_session_id = test_session_ids[0]
    const test_window_ids = await dbRawRead(this.app, {test_session_id}, `
      -- took 78ms to run on qc4
      select test_window_id from test_sessions where id = :test_session_id
    ;`)
    const test_window_id = test_window_ids[0].test_window_id

    const knex: Knex = this.app.get('knexClientWrite');
    const tspi = await knex('test_session_purchase_intents').insert({
      test_window_id,
      students_data: JSON.stringify(studentsNotPaid),
      test_session_id,
      purchase_by_uid,
    }).returning('id').then((res: any) => res[0]);

    const redirectUrl = `${domain}#/${lang}/school-admin/sessions?school=${schl_group_id}`;
    const metadata = {
      "School Name": schoolName,
      "School Mident": schl_mident,
      "Class Name": sessionNames[0],
      "Administration Window": administrationWindow,
      "Test Session ID": test_session_ids[0],
      "Assessment Name": assessmentName,
      "Purchased By (UID)": purchase_by_uid,
      "School Group ID": schl_group_id,
      "Test Purchase Intent ID": tspi
      // studentsInfo: LZString.compressToUTF16(JSON.stringify(studentsNotPaid)) // Legacy: stored in TSPI table now
    };

    const session = await stripe.checkout.sessions.create({
      line_items: [
        {
          price_data: {
            currency: 'cad',
            product_data: {
              name: 'Private School Session Purchase',
            },
            unit_amount: this.convertPriceForStripe(Number(price_per_student)),
          },
          quantity: studentsNotPaid.length,
        }
      ],
      mode: 'payment',
      success_url: redirectUrl,
      cancel_url: redirectUrl,
      metadata,
      payment_intent_data: {metadata}
    });

    // Update TSPI with Stripe Payment Intent ID for future reference
    await knex('test_session_purchase_intents').where({id: tspi}).update({stripe_id: session.payment_intent});

    console.log('sessionInfo', session)
    return { sessionUrl: session.url };
  }

  private async processAlternativePayment(data: Data): Promise<Data> {
    data.purchased_total = data.totalPrice;

    const test_session_purchases = await this.processPayment(
      true,
      data,
      data.purchase_method_id
    )

    return test_session_purchases;
  }

  private async getPricePerStudent(testSessionId: number) {
    const { price_per_student } = await dbRawReadSingle(this.app, [testSessionId], `
      select ap.price_per_student
      from assessment_prices ap
      join test_sessions ts
        on ts.test_window_id = ap.test_window_id
      where ts.id = ? and ap.is_revoked = 0;
    `);

    return price_per_student;
  }

  /**
   * @important excludes students who already have a refunded purchase within a test window, of which they have not used it
   * @param test_session_ids - array of test session ids for payments, usually only one
   * @returns a list of students who are in need of payment for the test session and their class group id
   */
  private async getStudentsNotPaid(test_session_ids: any[]) {
    const studentsNotPaid = await dbRawRead(this.app, [test_session_ids], `
      select *
      from (
        select MAX(sap.id) sap_id, ur.uid, sc.group_id class_group_id
        from school_class_test_sessions scts
        join test_sessions ts
          on ts.id = scts.test_session_id
        join school_classes sc
          on sc.id = scts.school_class_id
        join user_roles ur
          on ur.group_id = sc.group_id
          and ur.is_revoked = 0
          and ur.role_type = 'schl_student'
        left join test_session_purchases tsp
          on tsp.test_window_id = ts.test_window_id
         and tsp.is_revoked != 1
         and tsp.is_approved = 1
        left join student_attempt_purchases sap
          on sap.ts_purchase_id = tsp.id
          and sap.uid = ur.uid
          and sap.is_revoked = 0
          and ((sap.is_refunded = 0 or sap.is_refunded is null) or (sap.is_refunded = 1 and sap.is_used = 0))
        where scts.test_session_id in (?)
        group by ur.uid
      ) t
      where t.sap_id is null;
    `);

    return studentsNotPaid;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    switch (data.purchase_method_id) {
      case 1:
        return this.processStripePayment(data);
      case 2:
        data.livemode = !QA_DOMAINS.includes(params?.headers?.origin);
        return this.processAlternativePayment(data);
      default:
        throw new Error('INVALID_PURCHASE_METHOD');
    }
  }

  private async checkForDuplicatePayment(data: any) {
    const query: any = {
      purchase_by_uid: data.purchase_by_uid,
      purchase_method_id: data.purchase_method_id,
      alternative_status: data.alternative_status,
      num_student_attempts_purchased: data.num_student_attempts_purchased,
      test_window_id: data.test_window_id,
      purchased_total: data.purchased_total,
      class_group_id: data.class_group_id,
    };
    if(data.purchase_method_id === 1) {
      query['stripe_id'] = data.stripe_id;
    }
    const purchase = await this.app.service('db/write/test-session-purchases').db()
      .select('id')
      .where(query)
      .whereNot({
        'is_revoked': 1
      })

    if(purchase.length > 0) {
      throw new Errors.TooManyRequests('DUPLICATE_PAYMENT');
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
