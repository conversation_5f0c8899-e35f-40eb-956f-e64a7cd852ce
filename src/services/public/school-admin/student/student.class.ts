import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadSingle, dbRawWrite, dbRawWriteMulti } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import recordingsService from '../../test-ctrl/schools/data-dl/responses/process-data/recordings/recordings.service';
import { renderDateTime } from '../../../../hooks/_util';
import moment from 'moment';
import { IUserMeta } from '../../auth/test-taker/test-taker.class';
import logger from '../../../../logger';
import { renderOperationalAssessmentSlug } from '../../test-ctrl/schools/summary/summary.class';
import { ALT_VERSION_REQUEST_STATUS } from '../../alt-version-ctrl/alt-version-requests/types'
import { convertTypes, TypeConvertSourceType, TypeConvertTargetType } from '../../../../util/type-convert';
import { getSysConstString } from '../../../../util/sys-const-string';

interface Data {}

interface ServiceOptions {}

export const QCheckInfoLock = 'CASE WHEN tw.reg_lock_on IS NOT NULL AND NOW() > tw.reg_lock_on THEN true ELSE false END';
export class Student implements ServiceMethods<Data> {

  static overlapVariables = ['FirstName','LastName','StudentType','StudentOEN','SASN','DateofBirth','Gender',
    'DateEnteredSchool', 'DateEnteredBoard', 'IndigenousType', 'FirstLanguage', 'EnrolledOntario',
    'OutOfProvinceResidence', 'StatusInCanada', 'Refugee', 'BornOutsideCanada', 'TimeInCanada','IS_G3','IS_G6','IS_G9','IS_G10'];

  static CLASSCODE_CHAR_LEN_RANGE = 20;
  static GROUPING_CHAR_LEN_RANGE = 25;

  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data[], params?: Params): Promise<Data> {
    const {schl_group_id, namespace, mode, test_windows_id} = (<any>params).query;
    if(!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    if(data.length > 60){
      throw new Errors.BadRequest('EQ_PARAMS_OVERLOAD');
    }
    
    await this.checkStudentInfoLock(test_windows_id)

    const created_by_uid = await currentUid(this.app, params);
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = userInfo.uid;
    let records: any[] = [] ;
    let newClassRecords: any[] = [] ;
    let classes_students: any[] = [];
    let classes: any[] = [];
    let students:any[] = [];
    let student_metas:any[]=[];
    let updateStudentsIds:any[]=[];
    let changeGrpStudents:any[] =[];
    const adminUID = await currentUid(this.app, params);
    const school = <any[]>await this.app
      .service('db/read/schools')
      .find({
        query: {
          group_id: schl_group_id
        },
        paginate: false
      });

    const is_sasn_login = school[0].is_sasn_login == 1;

    for (let i=0; i<data.length;i++){
      let currentData = data[i];
      let { sch_group_id, student, student_meta, school_class_id, uuid, school_class_name, alt_version_req } = <any> currentData;

      //const groupType:string = namespace === 'eqao_sdc' ? 'EQAO_G9' : 'EQAO_G10';
      let groupType:string = 'EQAO_G3'
      let typeSlug:string = 'EQAO_G3P'
      switch(namespace){
        case 'eqao_sdc_g3':
          groupType = 'EQAO_G3'
          typeSlug = 'EQAO_G3P'
        break;
        case 'eqao_sdc_g6':
          groupType = 'EQAO_G6'
          typeSlug = 'EQAO_G6J'
        break;
        case 'eqao_sdc':
          groupType = 'EQAO_G9'
          typeSlug = 'EQAO_G9M'
        break;
        case 'eqao_sdc_g10':
          groupType = 'EQAO_G10'
          typeSlug = 'EQAO_G10L'
        break;
        default:
          groupType = 'EQAO_G3'
          typeSlug = 'EQAO_G3P'
        break;
      }
      const isGrouping:number = groupType === 'EQAO_G10' ? 1 : 0;
      // const typeSlug_G3 = 'EQAO_G3P';
      // const typeSlug_G6 = 'EQAO_G6J';
      // const typeSlug_G9 = 'EQAO_G9M';
      // const typeSlug_G10 = 'EQAO_G10L';
      let semesterID:any;
      let semesterNum = student_meta.find((record:any) => {
        if (record.key == "TermFormat"){
          return record;
        }
        return null;
      })?.value;

      let isFi = '0'
      const fi_key_namespace = await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKeyNamespace,  groupType)
      const fi_key =           await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKey,           groupType)  
      if(fi_key_namespace && namespace == fi_key_namespace){
        isFi = student_meta.find((record:any) => {
          if (fi_key && record.key == fi_key && namespace == fi_key_namespace){
            return record;
          }
          return null;
        })?.value;
      }
      
      if(!semesterNum || Number.isNaN(parseInt(semesterNum))) {
        semesterNum = 1;
      }
      if(groupType === 'EQAO_G9' && semesterNum) {
        semesterID = await dbRawRead(this.app, [test_windows_id, +semesterNum],
          ` select ss.id
            from mpt_dev.school_semesters ss
            where ss.test_window_id = ?
              and ss.foreign_id= ?
              and ss.foreign_scope_id is null
          `);
      }
      if(groupType === 'EQAO_G3' || groupType === 'EQAO_G6'||groupType === 'EQAO_G10') {
        semesterID = await dbRawRead(this.app, [test_windows_id],
          ` select ss.id
            from mpt_dev.school_semesters ss
            where ss.test_window_id = ?
          `);
      }

      const semester_ID = semesterID ? semesterID[0].id : null;
      var a1:any = {}
      const classInfo = {
        schl_group_id: Number(sch_group_id),
        schl_dist_group_id: Number(school[0].schl_dist_group_id),
        foreign_id: '',
        semester_id: semester_ID,
        group_type: groupType,
        is_grouping: isGrouping,
        name: school_class_name,
        educator_id: a1.id,
        is_fi: isFi=='1'? 1:0,
        uuid: '',
      }

      // During Student import, if the class code does not already exist, create it
      if (!school_class_id && school_class_name) {
        // Check if the new class is already exist
        const classesInSameSemesterTwWindow = await dbRawRead(this.app, [semester_ID, school_class_name, schl_group_id], `
          select sc2.*
          from school_semesters ss1
          join test_windows tw1
            on tw1.id = ss1.test_window_id
            and ss1.id = ?
          join test_windows tw2
            on tw2.type_slug = tw1.type_slug
            and tw2.is_active = 1
          join school_semesters ss2
            on tw2.type_slug = tw1.type_slug
            and ss2.test_window_id = tw2.id
          join school_classes sc2
            on ss2.id = sc2.semester_id
            and sc2.name = ?
            and sc2.is_active = 1
            and sc2.schl_group_id = ?
        ;`);

        if (classesInSameSemesterTwWindow.length) {
          school_class_id = classesInSameSemesterTwWindow[0].id;
          (<any>currentData).school_class_id = school_class_id;
        } else {
          const config = { semester_id: semester_ID, name: school_class_name, schl_group_id: +sch_group_id }
          const classnameCharLenRange = groupType === 'EQAO_G10'? Student.GROUPING_CHAR_LEN_RANGE: Student.CLASSCODE_CHAR_LEN_RANGE;
          if(classInfo.name.length <= classnameCharLenRange){
            await this.app.service('public/school-admin/classes').validateUniqueClassCode(config);
            // Create new class record: (ONCE)
            const newClass = await this.app.service('public/school-admin/classes').createClassAndAssignTeacher(classInfo);
            newClassRecords.push(newClass);
            school_class_id = newClass.id;
            (<any>currentData).school_class_id = school_class_id;
          } else {
            const logData = {
              uid: '',
              user_entry: {},
              user_meta: [],
              oen:''
            };
            const validateResult = {
              ErrorMessages: groupType === 'EQAO_G10'? ['Grouping_CHAR_LEN_RANGE']: ['ClassCode_CHAR_LEN_RANGE'],
              warningMessages:[]
            }
            records.push({...logData, validateResult,uuid});
            continue;
          }
        }
      }

      //check if its a current student different course student
      //1 check if the OEN be used by old student
      //2 check if the student is register in different course (ie.An G9 student tried to create osslt record) and return true is this is the case.
      const uid = await this.studentStatusCheck(currentData, params)
      if(uid){
        const recordResult:any = await this.crossCourseCreate(uid, currentData, school[0].is_sasn_login, school[0].is_private, school[0].school_type, params);
        if(recordResult.ErrorMessages && recordResult.warningMessages) {
          const validateResult = recordResult
          const logData = {
            uid: uid,
            user_entry: {},
            user_meta: [],
            oen:''
          };
          records.push({...logData, validateResult, uuid});
        } else if(!recordResult.ErrorMessages && !recordResult.warningMessages && recordResult[0]){
          const { uid: id, user_entry, user_meta, oen, validateResult } = recordResult[0];
          const logData = {
            uid: id,
            user_entry: user_entry,
            user_meta: user_meta,
          };
          records.push({...logData, oen, validateResult, uuid});
        }
        continue;
      }

      //validate Data
      const validateData = await this.convertData(currentData, school[0].is_sasn_login, school[0].is_private, school[0].school_type, params)
      const schoolNameSpace = validateData.Namespace.toUpperCase()
      const validateResult:any = await this.app
        .service('private/schools/student/validate')
        .create(validateData);
      logger.silly({ validateResult });
      const onlyOENInvalid = validateResult.ErrorMessages.length == 1 && (validateResult.ErrorMessages[0].includes("StudentOEN_OEN_UNIQUE1"));
      

      // Error Messages for fail to fill out the mandatory fields for importing students
      const partialImportErrMsg =
              [ 'FirstName_NOT_NULL', 'FirstName_CHAR_LEN_RANGE',
                'LastName_NOT_NULL', 'LastName_CHAR_LEN_RANGE',
                'StudentOEN_NOT_NULL', 'StudentOEN_CHAR_LEN_RANGE', 'StudentOEN_CHAR_PATTERN', 'StudentOEN_VAL_RANGE',
                'Gender_NOT_NULL', 'Gender_CHAR_LEN_RANGE', 'Gender_VAL_RANGE',
                'ClassCode_NOT_NULL', 'ClassCode_CHAR_LEN_RANGE', 'ClassCode_VALIDE_CLASS', 'ClassCode_VALIDE_CLASS_2',
                'Grouping_NOT_NULL', 'Grouping_CHAR_LEN_RANGE', 'Grouping_VALIDE_GRPING', 'Grouping_VALIDE_GRPING_2',
                'Grade3_VAL_RANGE','Grade6_VAL_RANGE', 'SASN_NOT_NULL', 'SASN_UNIQUE'
              ];
      const partialInvalid = partialImportErrMsg.some(errMsg => validateResult.ErrorMessages.includes(errMsg));
      if(validateResult.ErrorMessages.length > 0 && ((!onlyOENInvalid && mode !== 'Import') || (partialInvalid && mode === 'Import'))){
        if(mode === 'Import' && validateResult.ErrorMessages.includes("StudentOEN_OEN_UNIQUE2")) {
          const index = validateResult.ErrorMessages.indexOf("StudentOEN_OEN_UNIQUE2");
          validateResult.ErrorMessages.splice(index, 1);
        }
        const logData = {
          uid: '',
          user_entry: {},
          user_meta: [],
          oen:''
        };

        records.push({...logData, validateResult,uuid});
        continue;
      }

      // Admins can update existing student information through import: allow ONLY duplicate OEN (non-sasn school)/SASN (sasn school) to go through for updating purpose
      let hasDuplicateOenOrSasn; 
      if(is_sasn_login) {
        hasDuplicateOenOrSasn = validateResult.ErrorMessages.includes("SASN_UNIQUE"); 
      } else {
        hasDuplicateOenOrSasn = validateResult.ErrorMessages.includes("StudentOEN_OEN_UNIQUE2"); 
      }
      // const hasDuplicateOEN = validateResult.ErrorMessages.includes("StudentOEN_OEN_UNIQUE2"); 

      if((hasDuplicateOenOrSasn) && mode === 'Import') {
        const schGroupId = Number(sch_group_id);
        const OENSASNKey = is_sasn_login ? 'SASN' : 'StudentOEN';
        const OENSASNValue = student_meta.find((sm:any) => sm.key === OENSASNKey).value;
        // const oen = student_meta.find((sm:any) => sm.key === 'StudentOEN').value;

        const record = <any>await dbRawRead(this.app, [groupType, OENSASNKey, OENSASNValue, schGroupId], 
          `
            select um.uid as uid
              , sc.group_id as class_group_id
              , sc.semester_id as class_semester_id
              , sc.id as class_id
              , sc.notes as class_notes
              , sc.group_type as class_group_type
              , sc.is_grouping as class_is_grouping
              , sc.access_code as class_access_code
              , sc.is_placeholder as class_is_placeholder
              , sc.schl_dist_group_id as school_dist_group_id
              , sc.name as class_name
              , ur.group_id as ur_group_id
              , u.id as user_id
              , u.first_name as first_name
              , u.last_name as last_name
              , tw.id AS tw_id
              , ur.id as ur_id
              from user_metas as um
              join user_roles ur
                on ur.uid = um.uid
                and ur.role_type = 'schl_student'
                and ur.is_revoked = 0
              join users u
                on u.id = um.uid
              join school_classes sc
                on sc.group_id = ur.group_id
                and sc.is_active = 1
                and sc.group_type = ?
              join school_semesters sm
                on sm.id = sc.semester_id
              join test_windows tw
                on tw.id = sm.test_window_id
                and tw.is_active = 1   
              where um.key = ?
                and um.key_namespace = 'eqao_sdc'
                and um.value = ?
                and sc.schl_group_id = ?
        `);

        const uid = record[0].uid;
        const brdMident = <any>await dbRawRead(this.app, [sch_group_id],
          `
           select sd.foreign_id
            from school_districts as sd
            join schools as sc
              on sd.group_id = sc.schl_dist_group_id
            where sc.group_id = ?
        `);
        const brd_mident = brdMident[0].foreign_id;
        if(record) {
          //Step 2: If the student is in old test window move them to new test window
          //Step 2.1 revoke student class user_role in the old test window

          // check if any student registered class is in old test window
          let inOldtestWindow = false
          await Promise.all(record.map( async (re:any) => {
            if(re.tw_id !== +test_windows_id){
              inOldtestWindow = true
            }
          }))

          // revoke all school class is any class is in old test window
          if(inOldtestWindow){
            await Promise.all(record.map( async (re:any) => {
              await this.app
              .service('db/write/user-roles')
              .patch(re.ur_id ,{
                is_revoked : 1,
                revoked_on : dbDateNow(this.app),
                revoked_by_uid : created_by_uid
              });
            }))
          }

          // Step 2.2 add student to new class
          if(inOldtestWindow){
            await this.createStudentSchoolClassRole(school_class_id, uid, <number> created_by_uid);

            changeGrpStudents.push({
              uid:uid,
              class_id : school_class_id
            })
          }
            
          // Step3: Insert into user_metas_imports
          const table_columns = [
                  'StudentOEN', 'SchMident', 'BrdMident', 'uid', 'StudentUID',
                  'AccAssistiveDevices', 'AccAssistiveTech', 'AccAudioResponse', 'AccAudioVersion', 'AccBraille', 'AccBreaks', 'AccOther', 'AccColoured',
                  'AccComputer', 'AccLargePrint', 'AccLPColoured', 'AccReadGraphic', 'AccReading', 'AccScribing', 'AccSign', 'AccVideotapeResponse',
                  'BornOutsideCanada', 'ClassCode', 'CreatedDate', 'DateEnteredBoard', 'DateEnteredSchool', 'DateofBirth', 'DateOfFTE', 'EligibilityStatus',
                  'EnrolledOntario', 'LanguageLearner', 'FirstLanguage', 'FirstName', 'FrenchImmersionOrExtended', 'Gender', 'Graduating', 'Grouping', 'Homeroom', 'IEP',
                  'IndigenousType', 'IPRCExceptionalities', 'LastName', 'LearningFormat', 'LevelofStudyLanguage', 'NonParticipationStatus',
                  'MathTeacherFirstName', 'MathTeacherLastName', 'OutOfProvinceResidence', 'PaperTest', 'ProjectCode', 'Refugee', 'SASN',
                  'TermFormat', 'SpecPermIEP', 'SpecPermMoved', 'SpecPermTemp', 'SpecProvBreaks', 'StatusInCanada', 'StudentType', 'TimeInCanada', 'IsImportUpdate',
                  'Grade','FrenchImmersion','ClassTeacherFirstName','ClassTeacherLastName','JrKindergarten','SrKindergarten','SpecEdNoExpectationMath',
                  'ExemptionRead','ExemptionWrite','ExemptionMath','AccSignRead','AccSignWrite','AccSignMath','AccBrailleRead','AccBrailleWrite','AccBrailleMath',
                  'AccAudioVersionRead','AccAudioVersionWrite','AccAudioVersionMath','AccAssistiveTechRead','AccAssistiveTechWrite','AccAssistiveTechMath',
                  'AccScribingRead','AccScribingWrite','AccScribingMath','AccAudioResponseRead','AccAudioResponseWrite','AccAudioResponseMath'
                ];

          let import_stu:any = {};
          table_columns.forEach(col => {
            if(col === 'FirstName') {
              import_stu.FirstName = student.first_name;
            }
            if(col === 'LastName') {
              import_stu.LastName = student.last_name;
            }
            // G3 G6 G9:
            if(isGrouping == 0 && col === 'ClassCode') {
              import_stu.ClassCode = school_class_name;
            }
            // OSSLT:
            if(isGrouping == 1 && col === 'Grouping') {
              import_stu.Grouping = school_class_name;
            }
            if(col === 'BrdMident') {
              import_stu.BrdMident = brd_mident;
            }
            if(col === 'uid') {
              import_stu.uid = uid;
            }
            if(col === 'IsImportUpdate') {
              import_stu.isImportUpdate = 1;
            }
            const meta = student_meta.find((sm:any) => sm.key == col);
            if(meta) {
              import_stu[col] = meta.value;
            }
          });

          await this.app
            .service('db/write/user-metas-imports')
            .create(import_stu);

          updateStudentsIds.push(import_stu.uid);
          
          //remove duplicate oen sasn error since these are used to check if student exist in system and update the existing student in the system in this if block,
          //not really an error
          const excludingErrMsgs = ["SASN_UNIQUE", "StudentOEN_OEN_UNIQUE2"]
          validateResult.ErrorMessages = validateResult.ErrorMessages.filter((errMsg:string) => !excludingErrMsgs.includes(errMsg)); 

          //reset student's red flag
          await this.app.service('public/school-admin/classes').resetStudentsRedFlag([uid], namespace, created_by_uid)

          //stored the new student redflag result
          await this.app.service('db/write/user-metas').create({
            uid, 
            key_namespace: namespace, 
            key:'errMsg', 
            value:JSON.stringify(validateResult.ErrorMessages),
            created_by_uid:created_by_uid, 
            updated_by_uid:created_by_uid
          });
        }

        await this.app
          .service('public/school-admin/student-asmt-info-signoff')
          .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);


        record.forEach((re:any) =>{
          if(!classes_students.find(classes_student => +classes_student.group_id === +re.ur_group_id && +classes_student.uid === +re.uid)){
            classes_students.push({
              group_id: re.ur_group_id,
              uid: re.uid
            });
          }  
          if(!classes.find((theClass:any) => +theClass.id === +re.class_id)){
            classes.push({
              id: re.class_id,
              group_id: re.class_group_id,
              schl_group_id: sch_group_id,
              schl_dist_group_id: re.school_dist_group_id,
              name: re.class_name,
              notes: re.class_notes,
              semester_id: re.class_semester_id,
              group_type: re.class_group_type,
              is_grouping: re.class_is_grouping,
              access_code: re.class_access_code,
              is_placeholder: re.class_is_placeholder
            });
          }  
        })

        //put student users record into students list so it can check if there yellow flag
        const student_record = {
          id: record[0].user_id,
          first_name: record[0].first_name,
          last_name: record[0].last_name
        }
        students.push(student_record);

        const studentmeta = <any>await dbRawRead(this.app, [uid],
          `
            select um.uid
                , um.key_namespace
                , um.key
                , um.value
                , um.id
            from user_metas um
            where um.uid = ?
          `);
        student_metas = student_metas.concat(studentmeta);

        continue;
      }

      if(validateResult.ErrorMessages.length > 0 && ((onlyOENInvalid && mode !== 'Import') || (!partialInvalid && mode === 'Import'))) {
        await this.app
        .service('public/school-admin/student-asmt-info-signoff')
        .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);
      }

      const newMeta = (onlyOENInvalid || !partialInvalid) ? {key_namespace:namespace, key:'errMsg', value:JSON.stringify(validateResult.ErrorMessages)} : {uid:student.id, key_namespace:namespace, key:'errMsg', value:JSON.stringify([])};
      student_meta.push(newMeta);
      validateResult.ErrorMessages = [];
      const user_entry = {
        account_type: 'student',
        created_by_uid: created_by_uid,
        first_name: student.first_name,
        last_name: student.last_name,
        is_claimed: 1,
        timezone: 'America/Vancouver',
      }
      const userRecord = await this.app
      .service('db/write/users')
      .create(user_entry);


      await this.app
          .service('db/write/users').patch(userRecord.id, {
            created_by_uid
          });


      const student_uid = userRecord.id;

      await this.createStudentSchoolRole(sch_group_id, student_uid, <number>  created_by_uid)
      if(school_class_id) {
        await this.createStudentSchoolClassRole(school_class_id, student_uid, <number> created_by_uid);
      }
      let oen;
      const meta_entries = [];
      for(let meta of student_meta) {

        // Primary & G9 FrenchImmersion should be set in createStudentSchoolClassRole function
        const isFrenchImmersion = namespace === fi_key_namespace && meta.key === fi_key
        if(isFrenchImmersion){
          continue;
        }

        const key = meta.key;
        const value = meta.value;
        if(key === 'StudentOEN'){
          oen = value;
        }

        let key_namespace = (namespace == 'eqao_sdc' || Student.overlapVariables.indexOf(key) > -1) ? 'eqao_sdc':namespace

        const meta_entry = {
          uid: student_uid,
          key_namespace: key_namespace,
          key: meta.key,
          value: meta.value,
          created_by_uid: created_by_uid,
          updated_by_uid: created_by_uid
        }

        if(meta.value !== undefined) {
          meta_entries.push(meta_entry);
        }
      }

      // make sure new created student have Lang user meta
      const LangMeta = {
        uid: student_uid,
        key_namespace: 'eqao_dyn',
        key: 'Lang',
        value: school[0].lang,
        created_by_uid: created_by_uid,
        updated_by_uid: created_by_uid
      }
      meta_entries.push(LangMeta);

      const createQuery = 'INSERT INTO user_metas (uid, key_namespace, `key`, `value`, created_by_uid, updated_by_uid) VALUES (?, ?, ?, ?, ?, ?)'
      await dbRawWriteMulti(this.app, createQuery, meta_entries, (entry) => {return [entry.uid, entry.key_namespace, entry.key, entry.value, created_by_uid, created_by_uid]})

      // await this.app.service('db/write/user-metas').create(meta_entries);

      const logData = {
        uid: student_uid,
        user_entry: user_entry,
        user_meta: meta_entries
      };

      this.app.service('public/log').create({slug: "SA_STUDENT_CREATE", data: logData}, params);

      await this.updateAltVersionRequest(student_uid, test_windows_id, alt_version_req, created_by_uid)

      records.push({...logData,oen,validateResult,uuid});

      //revoke current student-asmt-info-signoff when school admin create new student
      await this.app
          .service('public/school-admin/student-asmt-info-signoff')
          .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);
    }

    if (changeGrpStudents.length) {
      const group_type = classes[0].group_type
      await this.updateChangedGrpStudentsAndClasses(changeGrpStudents, classes_students, classes, group_type)
    }

    const school_semesters = <any>await dbRawRead(this.app, [test_windows_id], `
      select id, foreign_scope_id, foreign_id, name, test_window_id from school_semesters where test_window_id = ?;
    `);

    const test_windows = await dbRawRead(this.app, [test_windows_id], `
      select * from test_windows where id = ?;
    `);
  
    const SDC_conflicts = await this.app.service('public/school-admin/school').checkSDCConflict(schl_group_id, classes_students, classes, students, student_metas, adminUID , school[0].is_sasn_login, school[0].is_private, school_semesters, false, school[0].school_type, test_windows);
    const SDC_conflicts_g3 = SDC_conflicts.SDCconflicts_g3
    const SDC_conflicts_g6 = SDC_conflicts.SDCconflicts_g6
    const SDC_conflicts_g9 = SDC_conflicts.SDCconflicts_g9
    const SDC_conflicts_g10 = SDC_conflicts.SDCconflicts_g10
          
    return {studentRecords: records, newClassRecords: newClassRecords, SDC_conflicts_g3: SDC_conflicts_g3, SDC_conflicts_g6: SDC_conflicts_g6, SDC_conflicts_g9: SDC_conflicts_g9, SDC_conflicts_g10: SDC_conflicts_g10, updateStudentsIds, changeGrpStudents };
  }

  async crossCourseCreate(id: NullableId, data: Data, is_sasn_login:any, isPrivate:any, school_type:any, params?: Params): Promise<Data>{
    if(!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    const student_uid = id;
    const namespace  = (<any>params).query.namespace;
    let {student, student_meta, school_class_id, sch_group_id: schl_group_id} = <any> data;
    const responses: any[] = [];
    let logData = {};
    let oen;
    const meta_entries = [];
    const records: any[] = [];
    const created_by_uid = await currentUid(this.app, params);

    const validateData = await this.convertData(data, is_sasn_login, isPrivate, school_type, params, id)

    const validateResult:any = await this.app
    .service('private/schools/student/validate')
    .create(validateData);

    const onlyOENInvalid = validateResult.ErrorMessages.length == 1 && (validateResult.ErrorMessages[0].includes("StudentOEN_OEN_UNIQUE1"));

    if(validateResult.ErrorMessages.length > 0 && !onlyOENInvalid){
      return validateResult;
    }else if(validateResult.ErrorMessages.length === 1 && onlyOENInvalid) {
      (<any>data).student_meta.push({key_namespace: namespace, key: 'errMsg', value: JSON.stringify(["StudentOEN_OEN_UNIQUE1"])});
    }else{
      (<any>data).student_meta.push({key_namespace: namespace, key: 'errMsg', value: JSON.stringify([])});
    }
    (<any>data).student_meta.find( (record:any) => record.key =='IS_G9').value = '1';
    (<any>data).student_meta.find( (record:any) => record.key =='IS_G10').value = '1';

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const schoolNameSpace = validateData.Namespace.toUpperCase();
    let tw_type_slug = null;
    if(schoolNameSpace && schoolNameSpace === 'EQAO_G9') {
      tw_type_slug = 'EQAO_G9M'
    }
    if(schoolNameSpace && schoolNameSpace === 'EQAO_G10') {
      tw_type_slug = 'EQAO_G10L'
    }

    if(validateResult.ErrorMessages.length > 0 && onlyOENInvalid) {
      await this.app
        .service('public/school-admin/student-asmt-info-signoff')
        .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);
      /*
      const prevRecords = <Paginated<any>> await this.app
      .service('db/read/school-student-asmt-info-signoffs')
      .find({
        query: {
          schl_group_id,
          tw_type_slug,
          is_revoked: 0,
          $limit: 1 // only need to check that we are not creating duplicate
        }
      });
      if (prevRecords.total > 0){
        this.app
        .service('db/read/school-student-asmt-info-signoffs').patch(prevRecords.data[0].id, {
          is_revoked: 1,
          revoked_on: dbDateNow(this.app),
          revoked_by_uid: userInfo.uid,
          revoke_reason: `NEW_STUDENT: Class/Grouping \"${validateData.Grouping}\"`,
        });
      }
      */
    }

    validateResult.ErrorMessages = [];

    if (id && params) {
      const created_by_uid = await currentUid(this.app, params);

      const user_entry = {
        account_type: 'student',
        first_name: student.first_name,
        last_name: student.last_name,
        is_claimed: 1,
        timezone: 'America/Vancouver',
        created_by_uid: created_by_uid,
      }

      const patchRecord =  {
        first_name: student.first_name,
        last_name: student.last_name
      };
      const response = await this.app
        .service('db/write/users')
        .patch(id, patchRecord);
      responses.push(response);

      const group_type = namespace == 'eqao_sdc'?'EQAO_G9':'EQAO_G10'
      const classUserRoleRows = await dbRawRead(this.app, [schl_group_id, group_type, student_uid], `
        select urt.id, sc.id as school_class_id, sc.group_type
        from mpt_dev.school_classes sc
        join mpt_dev.user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student'
        where sc.schl_group_id = ?
          and sc.is_active = 1
          and sc.group_type = ?
          and urt.is_revoked = 0
          and urt.uid = ? ;
      `);
      let matchingRole:any = null;
      classUserRoleRows.forEach(row => {
        if (!matchingRole && +row.school_class_id === +school_class_id){
          matchingRole = row;
        }
      })

      if (!matchingRole && school_class_id){
        await this.createStudentSchoolClassRole(school_class_id, <number> student_uid, created_by_uid);
      }

      const keysToUpdate = [];
      const metasToUpdate = [];
      for(let meta of student_meta) {
        const key = meta.key;
        const value = meta.value;
        if(meta.value === undefined){
          continue;
        }
        if(key === 'StudentOEN'){
          oen = value;
        }
        const key_namespace = (namespace == 'eqao_sdc' || Student.overlapVariables.indexOf(key) > -1) ? 'eqao_sdc':'eqao_sdc_g10'

        const meta_entry = {
          uid: student_uid,
          key_namespace: key_namespace,
          key: meta.key,
          value: meta.value,
          created_by_uid: created_by_uid,
          updated_by_uid: created_by_uid,
        }

        if(meta.value !== undefined) {
          meta_entries.push(meta_entry);
        }
        keysToUpdate.push(meta.key);
        metasToUpdate.push(meta);
      }

      var SDC_KeyToUpdate = keysToUpdate;
      var SDC_G10_KeyToUpdate:any[] = [];
      if(namespace == 'eqao_sdc_g10'){
        SDC_KeyToUpdate = [];
        keysToUpdate.forEach(theKey => {
          if(Student.overlapVariables.indexOf(theKey) > -1){
            SDC_KeyToUpdate.push(theKey)
          }else{
            SDC_G10_KeyToUpdate.push(theKey)
          }
        })
      }

      const key_namespace = 'eqao_sdc'
      const key_namespace_g10 = 'eqao_sdc_g10'

      var oldRows = await this.app.service('db/read/user-metas')
      .db()
      .whereIn('key_namespace', [key_namespace, 'eqao_dyn'])
      .where('uid', id)
      .whereIn('key', SDC_KeyToUpdate)
      .select(['id', 'uid','key_namespace','value', 'key']);

      var oldRows_g10;
      if(namespace == 'eqao_sdc_g10'){
        oldRows_g10 = await this.app.service('db/read/user-metas')
        .db()
        .whereIn('key_namespace', [key_namespace_g10, 'eqao_dyn'])
        .where('uid', id)
        .whereIn('key', SDC_G10_KeyToUpdate)
        .select(['id', 'uid','key_namespace','value', 'key']);

        oldRows = oldRows.concat(oldRows_g10)
      }

      const oldRowKeyMap = new Map<string, any>();

      for(let oldRow of oldRows) {
        oldRowKeyMap.set(oldRow.key, oldRow);
      }

      const patchEntries: {id: number, key: string, value: any, valueBefore:any}[] = [];
      const createEntries: {key: string, value: any, key_namespace?:string, }[]= [];

      for(let meta of metasToUpdate) {
        const oldRow = oldRowKeyMap.get(meta.key);
        if(oldRow && oldRow.id !== undefined) {
          if(oldRow.value !== meta.value) {
            patchEntries.push({
              id: oldRow.id,
              key: meta.key, //for logging clarity
              value: meta.value,
              valueBefore: oldRow.value,
            })
          }
        } else {
          let key_namespace = namespace;
          if(Student.overlapVariables.indexOf(meta.key)>-1){
            key_namespace = 'eqao_sdc';
          }
          createEntries.push({
            key: meta.key,
            key_namespace,
            value: meta.value
          })
        }
      }

      const uid = await currentUid(this.app, params);

      const patchQuery = `UPDATE user_metas
      SET value = ?, updated_on = ?, updated_by_uid = ?
      WHERE id = ?`;

      const dateNow = dbDateNow(this.app);
      dbRawWriteMulti(this.app, patchQuery, patchEntries, (entry) => {return [entry.value, dateNow, uid, entry.id]} )

      const createQuery = 'INSERT INTO user_metas (uid, key_namespace, `key`, `value`, created_by_uid, updated_by_uid) VALUES (?, ?, ?, ?, ?, ?)'
      dbRawWriteMulti(this.app, createQuery, createEntries, (entry) => {return [id, entry.key_namespace || key_namespace, entry.key, entry.value, created_by_uid, created_by_uid]})
      //dbRawWriteMulti(this.app, createQuery, createEntries_g10, (entry) => {return [id, entry.key_namespace || key_namespace_g10, entry.key, entry.value, created_by_uid, created_by_uid]})

      logData = {
        uid: id,
        user_entry: user_entry,
        user_meta: meta_entries,
      };

      this.app.service('public/log').create({slug: "SA_STUDENT_EDIT", data: logData}, params);
      records.push({ ...logData, oen, validateResult });
    }

    //revoke current student-asmt-info-signoff when school admin create new student in crossnamespace
    await this.app
          .service('public/school-admin/student-asmt-info-signoff')
          .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);

    return records;
    // return validateResult;
    //return Promise.all(responses);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private async createStudentSchoolRole(sch_group_id:number, student_uid:number, created_by_uid:number){
    const sch_user_role_entry = {
      role_type: 'schl_student',
      uid: student_uid,
      group_id: sch_group_id,
      created_by_uid,
      is_revoked: 0
    }
    await this.app
      .service('db/write/user-roles')
      .create(sch_user_role_entry);
  }

  private async createStudentSchoolClassRole(school_class_id:number, student_uid:number, created_by_uid:number){
    const newSchoolClass = await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    if(newSchoolClass) {
      const newClassGroupId = newSchoolClass.group_id;
      const newClassCreateRecord = {
        uid: student_uid,
        group_id: newClassGroupId,
        role_type: 'schl_student',
        created_by_uid,
        is_revoked: 0 //ensure it is not revoked when updating to a new class
      }
      await this.app
        .service('db/write/user-roles')
        .create(newClassCreateRecord);
      await this.app.service('public/school-admin/classes').updateStudentFrenchImm(school_class_id, student_uid,created_by_uid);
      //not updating TermFormat here is because student moving to different termformat class is block by the business rule check TermFormat_2 with errMsg: 'TermFormat_VAL_1'
      //await this.app.service('public/school-admin/classes').updateStudentTermFormat(school_class_id, student_uid,created_by_uid);
      // reset student's red flag
      // No need to reset student red flag due to student is block from moving between test window by the teacher
    }
  }

  async fillMissingStudentPersonalOverlapUserMetas(student_uid:NullableId, student_meta:Partial<IUserMeta>[]){
    const missingPersonalMetas:string[] = [];
    const receivedUserMeta = new Map();
    let personalUserMetas = [];
    student_meta.forEach(userMetaRecord => {
      receivedUserMeta.set(userMetaRecord.key, true);
    })
    Student.overlapVariables.forEach(key => {
      if (!receivedUserMeta.get(key)){
        missingPersonalMetas.push(key)
      }
    })

    if(missingPersonalMetas.length != 0) {
      personalUserMetas = <any[]>await this.app
        .service('db/read/user-metas')
        .find({
          query: {
            $select: [
              'key',
              'key_namespace',
              'uid',
              'value',
            ],
            uid: student_uid,
            key_namespace: 'eqao_sdc',
            key: {
              $in: missingPersonalMetas
            }
          },
          paginate: false
        });
      personalUserMetas.forEach(userMetaRecord => {
        student_meta.push({
          key: userMetaRecord.key,
          key_namespace: userMetaRecord.key_namespace,
          uid: 0, // to align to what is coming from the UI
          value: userMetaRecord.value,
        })
      })
    }
  }

  /**
   * Fills in all of the existing user-metas that were not passed by client
   *  1. Pull all metas that are in the same namespace but were not passed as user_metas
   *  2. Mutate user_metas to fill in missing keys
   * @mutates user_metas
   */
  async fillMissingStudentMetaFromDB(key_namespace: string, uid: NullableId, user_metas: Partial<IUserMeta>[]) {
    if(!uid) return;
    const existingMetaKeys = user_metas
      // .filter(um => um.key_namespace == key_namespace) // not filtering as client namespace seems to be off
      .map(um => um.key)

    const missingMetas = <any[]>await this.app.service('db/read/user-metas')
      .find({
        query: {
          $select: [
            'key',
            'key_namespace',
            'uid',
            'value',
          ],
          uid,
          key_namespace,
          key: {
            $nin: existingMetaKeys
          }
        },
        paginate: false
      });

    missingMetas. forEach(userMetaRecord => user_metas.push({
      key_namespace: userMetaRecord.key_namespace,
      key: userMetaRecord.key,
      value: userMetaRecord.value,
      uid: 0, // to align to what is coming from the UI
    }))
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const { schl_group_id, lang, namespace, isSecreteUser, test_windows_id, isStudentClassChanged } = (<any>params).query;
    const student_uid = id;

    let {student, student_meta, school_class_id, sch_group_id, user_metas_import_disc_id, alt_version_req} = <any> data;
    const responses: any[] = [];

    await this.checkStudentInfoLock(test_windows_id)

    var validateResult:any;

    const secreteUserRecord = await getSysConstString(this.app, '12_HOUR_SUBM_OVERRIDE')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord
    //Check if secrete user is valid
    if(isSecreteUser && !isValidSecreteUser){
      throw new Errors.BadRequest("Invalid Secrete User")
    }

    await this.fillMissingStudentPersonalOverlapUserMetas(student_uid, student_meta);
    try {
      await this.fillMissingStudentMetaFromDB(namespace, student_uid, student_meta)
    }
    catch (e){
      console.log('ERROR in fillMissingStudentMetaFromDB')
    }
    

    const school = <any[]>await this.app
    .service('db/read/schools')
    .find({
      query: {
        group_id:schl_group_id
      },
      paginate: false
    });

    //data = this.modifyData(data)
    //student_meta = (<any>data).student_meta;
    const validateData = await this.convertData(data, school[0].is_sasn_login, school[0].is_private, school[0].school_type, params, student_uid)

    validateResult = await this.app
    .service('private/schools/student/validate')
    .create(validateData);

    const errMsg_meta = student_meta.find((record:any)=>(record.key_namespace ==='eqao_sdc' && record.key=='errMsg'))
    if(validateResult.ErrorMessages.includes("Grouping_VALIDE_GRPING_2")) {
      const index = validateResult.ErrorMessages.indexOf("Grouping_VALIDE_GRPING_2");
      validateResult.ErrorMessages.splice(index, 1);
    }
    if(errMsg_meta && validateResult.ErrorMessages.length > 0){
      if(user_metas_import_disc_id){//school board conflict update
        errMsg_meta.value=JSON.stringify(validateResult.ErrorMessages);
      }else{ //regular update
        return validateResult;
      }
    }else if (errMsg_meta){
      errMsg_meta.value = JSON.stringify([]);
    }

    if(user_metas_import_disc_id){//school board conflict update
      if(params){
        const uid = await currentUid(this.app, params);
        this.app.service('db/write/user-metas-import-disc').patch(user_metas_import_disc_id, {
          is_resolved: 1,
          resolved_on: dbDateNow(this.app),
          resolved_by: uid
        });
      }
    }


    if (id && params) {
      const created_by_uid = await currentUid(this.app, params);
      const patchRecord =  {
        first_name: student.first_name,
        last_name: student.last_name
      };
      const response = await this.app
        .service('db/write/users')
        .patch(id, patchRecord);
      responses.push(response);

      const group_type = namespace == 'eqao_sdc_g3'?'EQAO_G3':namespace == 'eqao_sdc_g6'?'EQAO_G6':namespace == 'eqao_sdc'?'EQAO_G9':'EQAO_G10'
      const classUserRoleRows = await dbRawRead(this.app, [sch_group_id, group_type, student_uid], `
        select urt.id, sc.id as school_class_id, sc.group_type
        from mpt_dev.school_classes sc
        join mpt_dev.user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student'
        where sc.schl_group_id = ?
          and sc.is_active = 1
          and sc.group_type = ?
          and urt.is_revoked = 0
          and urt.uid = ? ;
      `);
      //let oldUserRoleRow = classUserRoleRows[0];
      let matchingRole:any = null;
      classUserRoleRows.forEach(row => {
        if (!matchingRole && row.school_class_id === school_class_id){
          matchingRole = row;
        }
      })

      if (!matchingRole && school_class_id){
        await this.createStudentSchoolClassRole(school_class_id, <number> student_uid, created_by_uid);
      }
      await Promise.all(classUserRoleRows.map( async oldUserRoleRow => {
        if (!matchingRole || +oldUserRoleRow.id !== +matchingRole.id){
          await this.app
            .service('db/write/user-roles')
            .patch(oldUserRoleRow.id, {
              is_revoked: 1,
              revoked_on: dbDateNow(this.app),
              revoked_by_uid: created_by_uid,
            });
        }
      }))

      const fi_key_namespace = await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKeyNamespace,  group_type)
      const fi_key =           await convertTypes(this.app, TypeConvertSourceType.ClassGroupType, TypeConvertTargetType.FrenchImmersionSDCKey,           group_type)  
      const keysToUpdate = [];
      const metasToUpdate = [];
      for(let meta of student_meta) {
        const isFrenchImmersion = fi_key_namespace && namespace === fi_key_namespace && fi_key && meta.key === fi_key
        if(meta.value === undefined || isFrenchImmersion){
          continue;
        }
        keysToUpdate.push(meta.key);
        metasToUpdate.push(meta);
      }

      var SDC_KeyToUpdate = keysToUpdate;
      var SDC_Other_KeyToUpdate:any[] = [];
      if(namespace == 'eqao_sdc_g3'||namespace == 'eqao_sdc_g6'||namespace == 'eqao_sdc_g10'){
        SDC_KeyToUpdate = [];
        keysToUpdate.forEach(theKey => {
          if(Student.overlapVariables.indexOf(theKey) > -1){
            SDC_KeyToUpdate.push(theKey)
          }else{
            SDC_Other_KeyToUpdate.push(theKey)
          }
        })
      }

      const key_namespace = 'eqao_sdc'
      const key_namespace_other = namespace

      var oldRows = await this.app.service('db/read/user-metas')
      .db()
      .whereIn('key_namespace', [key_namespace, 'eqao_dyn'])
      .where('uid', id)
      .whereIn('key', SDC_KeyToUpdate)
      .select(['id', 'uid','key_namespace','value', 'key']);

      var oldRows_other;
      if(namespace == 'eqao_sdc_g3'||namespace == 'eqao_sdc_g6'||namespace == 'eqao_sdc_g10'){
        oldRows_other = await this.app.service('db/read/user-metas')
        .db()
        .whereIn('key_namespace', [key_namespace_other, 'eqao_dyn'])
        .where('uid', id)
        .whereIn('key', SDC_Other_KeyToUpdate)
        .select(['id', 'uid','key_namespace','value', 'key']);

        oldRows = oldRows.concat(oldRows_other)
      }



      const oldRowKeyMap = new Map<string, any>();

      for(let oldRow of oldRows) {
        oldRowKeyMap.set(oldRow.key, oldRow);
      }

      const patchEntries: {id: number, key: string, value: any, valueBefore:any}[] = [];
      const createEntries: {key: string, value: any, key_namespace?:string, }[]= [];
      const createEntries_other: {key: string, value: any, key_namespace?:string, }[]= [];

      for(let meta of metasToUpdate) {
        const oldRow = oldRowKeyMap.get(meta.key);
        if(oldRow && oldRow.id !== undefined) {
          if(oldRow.value !== meta.value) {
            patchEntries.push({
              id: oldRow.id,
              key: meta.key, //for logging clarity
              value: meta.value,
              valueBefore: oldRow.value,
            })
          }
        } else {
          if(namespace =='eqao_sdc'|| Student.overlapVariables.indexOf(meta.key) > -1){
            createEntries.push({
              key: meta.key,
              value: meta.value
            })
          }else{
            createEntries_other.push({
              key: meta.key,
              value: meta.value
            })
          }
        }
      }

      const uid = await currentUid(this.app, params);

      const patchQuery = `UPDATE user_metas
      SET value = ?, updated_on = ?, updated_by_uid = ?
      WHERE id = ?`;

      const dateNow = dbDateNow(this.app);
      dbRawWriteMulti(this.app, patchQuery, patchEntries, (entry) => {return [entry.value, dateNow, uid, entry.id]} )

      const createQuery = 'INSERT INTO user_metas (uid, key_namespace, `key`, `value`, created_by_uid, updated_by_uid) VALUES (?, ?, ?, ?, ?, ?)'
      dbRawWriteMulti(this.app, createQuery, createEntries, (entry) => {return [id, entry.key_namespace || key_namespace, entry.key, entry.value, created_by_uid, created_by_uid]})

      dbRawWriteMulti(this.app, createQuery, createEntries_other, (entry) => {return [id, entry.key_namespace || key_namespace_other, entry.key, entry.value, created_by_uid, created_by_uid]})

      const logData = {
        uid: id,
        first_name: student.first_name,
        last_name: student.last_name,
        user_meta: {
          patches: patchEntries,
          creates: createEntries
        }
      };

      await this.updateAltVersionRequest(student_uid, test_windows_id, alt_version_req, created_by_uid)

      // Moved classes by admin in the student info modal
      // If student moved classes, it may be that their assessment date is close enough to auto-send the assessment message
      // It will log any errors in db so don't await
      if (+isStudentClassChanged && student_uid && test_windows_id && school_class_id){
        const isStudentOpSendRequired = await this.app.service('public/alt-version-ctrl/alt-version-requests').isStudentOpSendRequired(+student_uid, +test_windows_id, +school_class_id)
        if (isStudentOpSendRequired){
          this.app.service('public/alt-version-ctrl/alt-version-requests').handleOpAutoSend({student_uid: +student_uid})
        }
      }

      //revoke current student-asmt-info-signoff when school admin edit student info
      await this.app
          .service('public/school-admin/student-asmt-info-signoff')
          .revokeSchStuAsmtSignoffByClassId(school_class_id, created_by_uid);

      this.app.service('public/log').create({slug: "SA_STUDENT_EDIT", data: logData}, params);
    }
    return validateResult;
    //return Promise.all(responses);
  }

  async updateAltVersionRequest(student_uid:any, test_windows_id:any, alt_version_req:any, created_by_uid:any){
    if(!student_uid || !test_windows_id ||!alt_version_req){
      return
    }

    if(alt_version_req.alt_version_requests_id){  //if there exist an alternative request, the request information is lock.
      return
    }

    //check if there exist old record
    const altVersionInformation = await this.getAltVersionInformation([student_uid], [test_windows_id])

    const newAltVersionInformation = {
      student_uid:             student_uid,
      test_window_id:          test_windows_id,
      created_by_uid:          created_by_uid,
      last_updated_on:         dbDateNow(this.app),
      last_updated_by_uid:     created_by_uid,
      braille_format:          alt_version_req.braille_format,
      pdf_format:              alt_version_req.pdf_format,
      audio_delivery_format:   alt_version_req.audio_delivery_format,
      audio_format:            alt_version_req.audio_format,
      asl_format:              alt_version_req.asl_format,
      reason:                  alt_version_req.reason,
      schl_contact:            alt_version_req.schl_contact,
      schl_email:              alt_version_req.schl_email,
      schl_phone:              alt_version_req.schl_phone,
      schl_street_number_name: alt_version_req.schl_street_number_name,
      schl_city:               alt_version_req.schl_city,
      schl_province:           alt_version_req.schl_province,
      schl_country:            alt_version_req.schl_country,
      schl_postal_code:        alt_version_req.schl_postal_code,
      schl_admin_phone:         alt_version_req.schl_admin_phone,
      tent_session_date:       alt_version_req.tent_session_date || null,
      is_iep_submit:           alt_version_req.is_iep_submit,
      is_spec_submit:          alt_version_req.is_spec_submit,
      is_ell_submit:           alt_version_req.is_ell_submit,
      is_fi_submit:            alt_version_req.is_fi_submit,
    }

    let altVersionInformationRecord;
    if(altVersionInformation && altVersionInformation.length === 1){ //if exist an altVersionInfon record update the record
      altVersionInformationRecord = await this.app
          .service('db/write/alt-version-information')
          .patch(altVersionInformation[0].id, newAltVersionInformation)
    }else if(!altVersionInformation || altVersionInformation.length === 0){ // if no exist an altVersionInfon record create the record
      if(+alt_version_req.braille_format !=0 || +alt_version_req.pdf_format !=0 || +alt_version_req.audio_delivery_format !=0 || +alt_version_req.audio_format != 0 || +alt_version_req.asl_format != 0){
        altVersionInformationRecord = await this.app.service('db/write/alt-version-information').create(newAltVersionInformation);
      }
    }else{ altVersionInformation.length > 1 // which shouldn't happen
      throw new Errors.GeneralError("DUPLICATE_ALT_VERSION_INFORMATION"); //this shouldn't happen
    }

    return altVersionInformationRecord
  }

  async getAltVersionInformation(student_uids:any, test_windows_ids:any){
    // check the alt_version_request_status table and find the status's id that want to be excluded from looking up altVersionInformation
    const excludeAltVersionReqStatusIds = [ALT_VERSION_REQUEST_STATUS.Canceled] // alt_version_request_status id 2: Canceled
    const altVersionInformation = await dbRawRead(this.app, [student_uids, test_windows_ids, excludeAltVersionReqStatusIds], `
          select distinct avi.* 
               , avr.id as alt_version_requests_id
               , avrs.status as alt_version_requests_status
               , avrs.id as alt_version_requests_status_id
               , avr.approved_on
            from alt_version_information avi
       left join alt_version_requests avr on avr.req_info_id = avi.id and avr.is_revoked = 0
       left join alt_version_request_status avrs on avrs.id = avr.alt_version_req_status_id
           where avi.student_uid in (?) 
             and avi.test_window_id in (?) 
             and avi.is_revoked = 0
             and (avr.id is null or avr.alt_version_req_status_id not in (?))
    ;`);
    return altVersionInformation
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
      if(!params || !params.query || !params.query.schl_group_id ||!params.query.studentClasses) {
        throw new Errors.BadRequest("REQ_PARAMS_MISS");
      }
      const studentClasses:any[] = JSON.parse(params.query.studentClasses);
      if(studentClasses.length == 0){
        return [];
      }
      const testWindowId = params.query.testWindowId;
      const studentUID = studentClasses[0].studentId;
      await this.checkStudentInfoLock(testWindowId)

      const typeSlugs = await dbRawRead(this.app, [testWindowId], `
        select type_slug 
          from test_windows tw
        where tw.id = ?
      ;`)
      const type_slug = typeSlugs[0].type_slug;
      const slug = renderOperationalAssessmentSlug(type_slug);

      const studentRecords = await dbRawRead(this.app, [studentUID, slug, testWindowId], `
        select ta.uid
             , um.value as studentOEN
             , u.first_name 
             , u.last_name
             , ta.test_session_id
             , ta.started_on
             , scts.slug
             , ts.test_window_id
          from test_attempts ta
          join user_metas um
            on um.uid = ta.uid
          join users u
            on u.id = ta.uid
          join school_class_test_sessions scts
            on scts.test_session_id = ta.test_session_id
          join test_sessions ts
            on ts.id = ta.test_session_id
          join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.is_sample= 0 
          where ta.uid in (?)
           and ta.started_on is not null
           and ta.is_invalid = 0
           and scts.slug = ?
           and ts.test_window_id = ?
           and um.key = 'StudentOEN'
           and um.key_namespace = 'eqao_sdc'
        ;`)

      if(studentRecords.length > 0) {
        return {studentRecords};
      }

      const schl_group_id = params.query.schl_group_id;
      const namespace = params.query.namespace;

      const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
      const uid = userInfo.uid;

      // meta_key = namespace == 'eqao_sdc'?'IS_G9':'IS_G10';
      let meta_key='';
      switch (namespace) {
        case 'eqao_sdc_g3':
          meta_key = 'IS_G3'
        break;
        case 'eqao_sdc_g6':
          meta_key = 'IS_G6'
        break;
        case 'eqao_sdc':
          meta_key = 'IS_G9'
        break;
        case 'eqao_sdc_g10':
          meta_key = 'IS_G10'
        break;
        default:
          meta_key = 'IS_G3'
        break;
      }

      //find user roles and user_metas that need to update.
      let revokeUserRoleIds:any[] = [];
      let updateUserMetasIds:any[] = [];

      await Promise.all(studentClasses.map(async (record:any) =>{
      //for (let record of studentClasses) {
        //find user role with classes
        const theClass = await dbRawRead(this.app, [record.classIds], `
          select group_id from school_classes where id = ?
        `)
        const classGroupId = theClass[0].group_id
        const classUserRoleIds = await dbRawRead(this.app, [ record.studentId, classGroupId], `
          select id from user_roles where uid = ? and group_id = ? and is_revoked = 0
        `)
        classUserRoleIds.forEach(r  => revokeUserRoleIds.push(r.id))

         //find user meta
        const userMeta = await dbRawRead(this.app, [meta_key, record.studentId], `
          select id from user_metas um where um.key_namespace = 'eqao_sdc' and um.key = ? and um.uid = ?;
        `)
        if(userMeta.length > 0){
          updateUserMetasIds.push(userMeta[0].id);
        }
        //find user role for school if needed
        const registeredClass = await dbRawRead(this.app, [schl_group_id, record.studentId], `
          select urt.id as id, sc.group_type as group_type
          from school_classes sc
          join user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student' and urt.is_revoked != 1
          where sc.schl_group_id = ? and urt.uid = ? and sc.is_active = 1 ;
        `);
        if(registeredClass && registeredClass.length == 1 && classUserRoleIds.indexOf(registeredClass[0].id)){
          const school_ur_rows = await this.app.service('db/read/user-roles').db()
          .where('uid', record.studentId)
          .where('group_id', schl_group_id)
          .where('role_type', 'schl_student')
          .whereNot('is_revoked', 1)
          .select('id');

          school_ur_rows.forEach( (r:any)  => revokeUserRoleIds.push(r.id))
        }
      }))

      // invalidate any unstarted test attempts
      const studentUIDs = [studentUID]
      await this.invalidTestAttempts(studentUIDs, testWindowId)

      //revoke class and school
      if(revokeUserRoleIds.length > 0){
        const res = await dbRawWrite(this.app, [uid, revokeUserRoleIds], `
          update user_roles set is_revoked = 1, revoked_on = now(), revoked_by_uid = ? where id in (?)
        `)
      }

      //update user metas
      if(updateUserMetasIds.length > 0){
        const res = await dbRawWrite(this.app, [meta_key, updateUserMetasIds], `
          update user_metas um set um.value ='#' where um.key_namespace = 'eqao_sdc' and um.key = ? and id in (?);
        `)
      }
      return [];
  }

  /**
   * Invalidates unstarted test attempts (used for revoked students or students who were moved to another TW)
   * @param studentUIDs: array of student uids
   * @param testWindowId
   * @see https://bubo.vretta.com/vea/project-management/vretta-project-notes/eqao/admin/-/issues/1442
   */
  async invalidTestAttempts(studentUIDs: any[], testWindowId: number){
    const attemptIds = await dbRawRead(this.app, [studentUIDs, testWindowId], `
      SELECT ta.id
      FROM test_attempts ta
      JOIN test_window_td_alloc_rules twtdar ON twtdar.id = ta.twtdar_id
      WHERE ta.is_invalid = 0
        AND ta.started_on IS NULL
        AND ta.uid IN (?)
        AND twtdar.test_window_id = ?
    `);

    if (attemptIds.length === 0) {
      return; // No attempts to invalidate
    }

    const attemptIdList = attemptIds.map((row: any) => row.id);
    
    logger.info('negateTestAttempt', { 
      studentUIDs, 
      testWindowId, 
      attemptIds: attemptIdList,
      count: attemptIdList.length 
    });

    await dbRawWrite(this.app, [attemptIdList], `
      UPDATE test_attempts 
      SET is_invalid = 1, 
          test_session_id = -test_session_id,
          uid = -uid
      WHERE id IN (?)
    `);

    await dbRawWrite(this.app, [attemptIdList], `
      UPDATE test_attempt_sub_sessions 
      SET is_invalid = 1,
          test_session_id = -test_session_id,
          uid = -uid
      WHERE test_attempt_id IN (?)
    `);
  }

  private async convertData(data: Data, is_sasn_login:any, isPrivate:any, school_type:string, params?: Params, id?:NullableId){
    const {schl_group_id, lang, namespace} = (<any>params).query;
    const student_uid = id;
    const {student, student_meta, school_class_id, sch_group_id} = <any> data;
    const responses: any[] = [];
    //***Convert student's data to business rules checking format*************
    var validateData:any = {};
    validateData['StudentID'] = student_uid;
    validateData['SchGroupID'] = schl_group_id;
    validateData['IsPrivate'] = String(isPrivate);
    validateData['Language'] = lang;
    validateData['FirstName'] = student.first_name;
    validateData['LastName'] = student.last_name;
    validateData['SchoolType'] = school_type;
    //The following keys is not fetching from user_meta
    //1. FirstName: should fetch from {table: users,  column: first_name} instead from user_meta key "FirstName"
    //2. LastName: should fetch from {table: users,  column: last_name} instead from user_meta key "LastName"
    const notCheckingKeys = ['FirstName', 'LastName'];  
    for(let i =0; i < student_meta.length; i++){
      if(notCheckingKeys.includes(student_meta[i].key)){
        continue
      }
      if(student_meta[i].value != undefined){
        validateData[student_meta[i].key] = student_meta[i].value+'';
      }
      //if(validateData[student_meta[i].key] == '#' || validateData[student_meta[i].key] == '0' ){ // 0 should not consider null and should be raised as a red flag
      if(validateData[student_meta[i].key] == '#' ){
          validateData[student_meta[i].key] = '';
      }
    }
    if(namespace == 'eqao_sdc_g3'){
      validateData['Namespace'] = 'eqao_g3';
    }
    if(namespace == 'eqao_sdc_g6'){
      validateData['Namespace'] = 'eqao_g6';
    }
    if(namespace == 'eqao_sdc'){
      validateData['Namespace'] = 'eqao_g9';
    }
    if(namespace == 'eqao_sdc_g10'){
      validateData['Namespace'] = 'eqao_g10';
    }

    // Grouping/ClassCode should fetch from school_classes table instead user_metas table
    delete validateData['Grouping']
    delete validateData['ClassCode']
    
    if(school_class_id && +school_class_id != -1 && validateData['Namespace'] =='eqao_g10' ){
      validateData['Grouping'] = (await dbRawReadSingle(this.app, [Number(sch_group_id),Number(school_class_id)], `
        select name from school_classes as sc where sc.schl_group_id = ? and sc.id  = ?`)).name;
        validateData["SchoolClassId"] = school_class_id  
      validateData['TestWindowEndDate'] = await this.getTWEndDateFromClassId(school_class_id)
    }
    if(school_class_id && +school_class_id != -1 && (validateData['Namespace'] =='eqao_g9'||validateData['Namespace'] =='eqao_g3'||validateData['Namespace'] =='eqao_g6')){
      // validateData['ClassCode'] = (await dbRawReadSingle(this.app, [Number(sch_group_id),Number(school_class_id)], `
      //   select name from school_classes as sc where sc.schl_group_id = ? and sc.id  = ?`)).name;
      const theClass = await dbRawReadSingle(this.app, [Number(sch_group_id),Number(school_class_id)], `
        select name,semester_id from school_classes as sc where sc.schl_group_id = ? and sc.id  = ?
      ;`)
      validateData['ClassCode'] = theClass.name
      if(validateData['Namespace'] =='eqao_g9'){
        validateData['ClassTermFormat'] = ''+ (await dbRawReadSingle(this.app, [theClass.semester_id], `
          select foreign_id from school_semesters where id = ?
        ;`)).foreign_id;
      }
      validateData["SchoolClassId"] = school_class_id  
      validateData['TestWindowEndDate'] = await this.getTWEndDateFromClassId(school_class_id)
    }



    validateData['IsSASNLogin'] = is_sasn_login;

    //*** End of convert student's data to business rules checking format ************************************************************/
    return validateData;
  }

   /**
   * get the test window end date in format YYYYMMDD with classId
   * @param classId
   * @throws 
   * @returns string test window end date in format YYYYMMDD
   */
  private async getTWEndDateFromClassId(classId: number){
    const test_window_date_end = await dbRawReadSingle(this.app, [classId], `
        select tw.date_end 
          from school_classes sc
          join school_semesters ss on ss.id = sc.semester_id
          join test_windows tw on tw.id = ss.test_window_id
         where sc.id = ? 
    ;`)

    return moment(test_window_date_end.date_end ).format("YYYYMMDD")
  }



  private async studentStatusCheck(data: Data, params?: Params, id?:NullableId){
    //1. check if the OEN been used by "old" student
    const {student, student_meta, school_class_id, sch_group_id} = <any> data;
    const oenMeta = student_meta.find( (record:any) => {
      if (record.key == "StudentOEN" && record.value){
        return record.value = record.value.trim()
      }
    })

    if(oenMeta === undefined||oenMeta.value =='000000000'){
      return undefined;
    }

    const studentOENMetas =  await dbRawRead(this.app, [
      oenMeta.value,
      sch_group_id,
    ], `
        select um.*, tw.is_active as tw_isActive
          from user_metas as um
          join user_roles ur
            on ur.uid = um.uid
           and ur.role_type = 'schl_student'
           and ur.is_revoked = 0
          join school_classes sc
            on sc.group_id = ur.group_id
           and sc.is_active = 1
          join school_semesters ss
            on ss.id = sc.semester_id
          join test_windows tw
            on tw.id = ss.test_window_id
         where um.key = 'StudentOEN'
           and um.key_namespace = 'eqao_sdc'
           and um.value = ?
           and sc.schl_group_id = ?
    `)

    const activeRecords = studentOENMetas.filter(record => record.tw_isActive == 1)
    const nonActiveRecords = studentOENMetas.filter(record => record.tw_isActive == 0)
    /*
    if(activeRecords.length > 0 || nonActiveRecords.length == 0) {
      return false;
    }
    */

    //2. set the old student's OEN to "000000000"
    /*
    if(nonActiveRecords.length > 0){
      const nonActiveRecordIds  = nonActiveRecords.map(async record => {
        await this.app.service('db/write/user-metas').update(record.id, {
          value: '000000000'
        })
      })
    }
    */

    //3. return undefined if there's more than 1 active record.
    const IS_G9 = student_meta.find( (record:any) => {
      if (record.key == "IS_G9"){
        return record
      }
    })
    const IS_G10 = student_meta.find( (record:any) => {
      if (record.key == "IS_G10"){
        return record
      }
    })

    if(activeRecords.length > 0) {
      if(IS_G9 && IS_G9.value == '1'){
        const uids = activeRecords.map(record => {return record.uid})
        const is_G9_DB =  await dbRawRead(this.app, [[uids]], `
          select * from user_metas um where um.key_namespace = 'eqao_sdc' and um.key = 'IS_G9' and um.uid in ?`
        )
        if(is_G9_DB.length == 0 || is_G9_DB.find(record =>record.value == '1')==undefined){
          return activeRecords[0].uid;
        }
      }
      if(IS_G10 && IS_G10.value == '1'){
        const uids = activeRecords.map(record => {return record.uid})
        const is_G10_DB =  await dbRawRead(this.app, [[uids]], `
          select * from user_metas um where um.key_namespace = 'eqao_sdc' and um.key = 'IS_G10' and um.uid in ?`
        )
        if(is_G10_DB.length == 0 || is_G10_DB.find(record =>record.value == '1')==undefined){
          return activeRecords[0].uid;
        }
      }
    }

    return undefined
  }

  /**
   * @param test_window_id
   * @description checks if the student info is registration locked for the given test window
   * @see https://www.notion.so/User-Info-Lock-Tracing-21edd3b634264eee83f153788909bcec?pvs=4
   * @throws ERR_STUDENT_INFO_LOCKED if the student info is locked
   */
  public async checkStudentInfoLock(test_window_id: NullableId) {
    if(!test_window_id) {
      throw new Error('ERR_MISSING_INFO_LOCK_PARAMS');
    }

    const knex = this.app.service('db/read/test-windows').knex;
    const result = await knex
      .from('test_windows as tw')
      .select('tw.id', 'tw.reg_lock_on', knex.raw(QCheckInfoLock + ' as reg_locked'))
      .where({
        'tw.id': test_window_id,
      })
      .first();

    // if the test window is not found, then it is not locked
    if (!result || !result?.reg_locked) {
      return false;
    }

    if(result.reg_locked == 1){
      logger.info('ERR_STUDENT_INFO_LOCKED', {test_window_id});
      throw new Errors.BadRequest('ERR_STUDENT_INFO_LOCKED');
    }
  }
  /**
   * Update classes_students and classes data for students brought over from old test windows
   * @param changeGrpStudents Students have class changed
   * @param classes_students 
   * @param classes 
   */
  private async updateChangedGrpStudentsAndClasses(changeGrpStudents:any[], classes_students:any[], classes:any[], group_type:string){
    const student_uids = changeGrpStudents.map(stu => stu.uid)
    const changedStudentClassRecords = <any>await dbRawRead(this.app, {group_type, student_uids}, `
      -- Benchmark: 0.047 sec, cost 108
              SELECT 
                  GROUP_CONCAT(ur.uid) AS uid
                , sc.group_id as class_group_id
                , sc.semester_id as class_semester_id
                , sc.id as class_id
                , sc.notes as class_notes
                , sc.group_type as class_group_type
                , sc.is_grouping as class_is_grouping
                , sc.access_code as class_access_code
                , sc.is_placeholder as class_is_placeholder
                , sc.schl_dist_group_id as school_dist_group_id
                , sc.schl_group_id
                , sc.name as class_name 
            FROM user_roles ur
            JOIN school_classes sc ON sc.group_id = ur.group_id and sc.group_type = (:group_type)
              AND sc.is_active = 1
            WHERE ur.role_type = "schl_student"
              AND ur.is_revoked = 0
              AND ur.uid in (:student_uids)
         GROUP BY sc.id
      ;`
    )

    changedStudentClassRecords.forEach((stuClassRecord:any) => {
      const updateStudentUIDs = stuClassRecord.uid.split(',')

      // Update classes_students records with the new (sc) group id
      updateStudentUIDs.forEach((uid: number) => {
        const classes_student_record = classes_students.find(csr => +csr.uid === +uid)
        classes_student_record.group_id = stuClassRecord.class_group_id

      })

      // Add new class to classes records
      if (!classes.find((theClass:any) => +theClass.id === +stuClassRecord.class_id)) {
        classes.push({
          id: stuClassRecord.class_id,
          group_id: stuClassRecord.class_group_id,
          schl_group_id: stuClassRecord.schl_group_id,
          schl_dist_group_id: stuClassRecord.school_dist_group_id,
          name: stuClassRecord.class_name,
          notes: stuClassRecord.class_notes,
          semester_id: stuClassRecord.class_semester_id,
          group_type: stuClassRecord.class_group_type,
          is_grouping: stuClassRecord.class_is_grouping,
          access_code: stuClassRecord.class_access_code,
          is_placeholder: stuClassRecord.class_is_placeholder
        })
      }
    })
  }
}
