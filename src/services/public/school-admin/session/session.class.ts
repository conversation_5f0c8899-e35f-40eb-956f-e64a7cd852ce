import { Id, NullableId, Pa<PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { currentUid } from '../../../../util/uid';
import { EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { dbRawRead } from '../../../../util/db-raw';
import { dbDateNow, dbDateOffsetHours, dbDateSetDateTime } from '../../../../util/db-dates';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { generateAccessCode } from '../../../../util/secret-codes';
import e from 'compression';
import moment from 'moment';
import logger from '../../../../logger';


const STANDARD_TIMEZONE = 'America/Toronto';

interface Data { }

interface ISessionEdit{
  scheduled_time: string,
}
export interface ISession {
  school_class_id: number,
  slug: string,
  caption: string,
  scheduled_time:ISessionTime,
  isScheduled: boolean,
  isRemovePrev?: boolean;
  is_fi_class?: boolean;
}
interface ISessionEdit {
  scheduled_time:string,
  slug:string,
}
export type ISessionTime = string[];
interface ServiceOptions { }

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    const { schl_group_id } = (<any>params).query;
    const classrooms = <any[]>await this.app
      .service('db/read/school-classes')
      .find({
        query: { schl_group_id, is_active: 1 },
        paginate: false
      });

    const classIds = classrooms.map(entry => entry.id)
    const classes_sessions = await this.getData([classIds], `
    select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, sc.name, sc.group_id, count(us.id) as num_students, ut.first_name,ut.last_name
    from mpt_dev.school_class_test_sessions scts
    join mpt_dev.test_sessions ts on ts.id = scts.test_session_id
    join mpt_dev.school_classes sc on sc.id = scts.school_class_id
    join mpt_dev.user_roles usr on usr.group_id = sc.group_id
    join mpt_dev.user_roles urt on urt.group_id = sc.group_id
    join mpt_dev.users us on us.id = usr.uid
    join mpt_dev.users ut on ut.id = urt.uid
    where scts.school_class_id IN (?)
      and ts.is_cancelled = 0
      and ts.is_closed = 0
      and usr.role_type ='schl_student'
      and urt.role_type = "schl_teacher"
      and urt.is_revoked = 0
      and usr.is_revoked = 0
	  group by test_session_id;
    ;`);
    const session_states: any[] = [];
    return Promise.all(
      classes_sessions.map(async (session) => {
        const session_state = await this.app.service('public/educator/session-sub').getTestSubSessions(session.test_session_id);
        session_states.push({ test_session_id: session.test_session_id, states: session_state });
      })
    )
      .then(res => {
        return [{
          classes_sessions,
          session_states
        }];
      })
    // return [{
    //   classes_sessions
    // }]
  }

  async getSessionStates(classes_sessions:any) {
    const session_states: any[] = [];
    return Promise.all(
      classes_sessions.map(async (session:any) => {
        const session_state = await this.app.service('public/educator/session-sub').getTestSubSessions(session.test_session_id);
        session_states.push({ test_session_id: session.test_session_id, states: session_state });
      })
    )
      .then(res => {
        return session_states
      })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    const { school_class_group_id,school_class_id } = (<any>params).query;
    logger.silly({ school_class_id });
    // long polling
    if (params) {
      return this.app.service('public/educator/session').getSessionInfo(id, school_class_id, false);
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: ISession, params?: Params): Promise<Data> {
    const { school_class_group_id } = (<any>params).query;
    if (params) {
      const created_by_uid = await currentUid(this.app, params); // should be currentUid
      const newSession = await this.createSession(created_by_uid, data);

      // Schedule session by admin
      // If the session is happening soon enough, students in the class may have approved alt requests that should be auto-operational sent
      // It will log any errors in db so don't await
      if (newSession.slug?.includes('OPERATIONAL') && newSession.school_class_id && newSession.date_time_start && newSession.test_window_id){
        const isClassOpSendRequired = await this.app.service('public/alt-version-ctrl/alt-version-requests').isClassOpSendRequired(+newSession.school_class_id, newSession.date_time_start, +newSession.test_window_id)
        if (isClassOpSendRequired){
          this.app.service('public/alt-version-ctrl/alt-version-requests').handleOpAutoSend({school_class_id: +newSession.school_class_id})
        }
      }


      return newSession;
    }
    return <any>{};
  }

  async getStudentRecords(school_class_id: any) {
    return await this.getData([school_class_id], `
    select sc.id, count(us.id) as num_students,ut.first_name, ut.last_name
    from mpt_dev.school_classes sc
    join mpt_dev.user_roles usr on usr.group_id = sc.group_id
    join mpt_dev.user_roles urt on urt.group_id = sc.group_id
    join mpt_dev.users us on us.id = usr.uid
    join mpt_dev.users ut on ut.id = urt.uid
    where sc.id = ?
      and usr.role_type ='schl_student'
      and urt.role_type = 'schl_teacher'
      and urt.is_revoked = 0
      and usr.is_revoked = 0
      group by id;
    ;`);

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // #2021-11-16-unclear-conflict
  // // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // async patch(id: NullableId, data: ISessionEdit, params?: Params): Promise<Data> {
  //   const date_time_start = dbDateSetDateTime(this.app, 4, data.scheduled_time)
  //   return await this.app
  //     .service('db/write/test-sessions')
  //     .patch(id, { date_time_start });
  // }

  async checkSessionStartTime(slug: string, scheduled_time:any[], school_class_id:number) {
    const tw_record = await dbRawRead(this.app, [school_class_id], `
      select tw.date_start
           , tw.date_end
        from school_classes sc
        join school_semesters ss on ss.id = sc.semester_id
        join test_windows tw on tw.id = ss.test_window_id
       where sc.id = ?
      ;`);
    const tw_date_start = tw_record[0].date_start;
    const tw_date_end = tw_record[0].date_end;
    let dateTime = [];
    if(slug === 'G9_OPERATIONAL' || slug === 'OSSLT_OPERATIONAL') {
      if(scheduled_time[0] && scheduled_time[1]){
        dateTime.push(scheduled_time[0])
        dateTime.push(scheduled_time[1])
      }
    } 
    else if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      // if Language scheduled only
      if(scheduled_time[0] && scheduled_time[1] && !scheduled_time[2] && !scheduled_time[3]){
        dateTime.push(scheduled_time[0])
        dateTime.push(scheduled_time[1])
      }
      // if Math scheduled only
      if(!scheduled_time[0] && !scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
        dateTime.push(scheduled_time[2])
        dateTime.push(scheduled_time[3])
      }
      // if Language and Math both scheduled
      if(scheduled_time[0] && scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
        dateTime.push(scheduled_time[0])
        dateTime.push(scheduled_time[1])
        dateTime.push(scheduled_time[2])
        dateTime.push(scheduled_time[3])
      }
    }

    if (dateTime.length !== scheduled_time.length)
      throw new Errors.BadRequest('MSG_ASSES_TIME_INVALID_WARNING');

    if(dateTime.length > 0) {
      dateTime.forEach(dt => {
        //const scheduledDateTimeStartDate = new Date(dt) // input time string is conver to API time without calculate the input time is Ontario timezone                                                 
        const scheduledDateTimeStartDate =  moment.tz(dt, STANDARD_TIMEZONE).toDate() // input time string is conver to API time with calculate the input time is Ontario timezone                                                                            
        if(tw_date_start > scheduledDateTimeStartDate || tw_date_end < scheduledDateTimeStartDate) {
          throw new Errors.BadRequest('MSG_ADMIN_WINDOW_WARNING'); 
        }
      });
    }
  }

  addHMtoPJScheduleTime(scheduled_time:string) {
    return scheduled_time + "T00:00"
  }

  mustValidateScheduledTimeAssessments(slug: string): boolean {
    const slugs = ['PRIMARY_OPERATIONAL', 'JUNIOR_OPERATIONAL', 'G9_OPERATIONAL', 'OSSLT_OPERATIONAL'];
    return slugs.indexOf(slug) > -1;
  } 

  convertToUTC(dateTimeString:string): string {
    const standardTimeMoment = moment.tz(dateTimeString, STANDARD_TIMEZONE);
    const utcTimeMoment = standardTimeMoment.utc()
    return utcTimeMoment.format('YYYY-MM-DDTHH:mm');
  };

  async createSession(created_by_uid: number, data: ISession) {
    //const test_ctrl_group_id = 6397; // hard coded to bc grad for now
    //const test_ctrl_group_id = 5403; // hard coded to g9 for now
    const delivery_format = EDeliveryOption.SCHOOL;
    const { school_class_id, slug, caption, isRemovePrev, isScheduled, is_fi_class } = data;

    //block sandbox school from schedule operational test
    await this.app.service('public/educator/session').blockSandBoxSchoolOperTest(school_class_id, slug);

    const studentRecords = await this.getStudentRecords(school_class_id);
    if(studentRecords.length === 0) {
      throw new Errors.BadRequest('MSG_NO_TEACHER'); 
    }

    //validate all scheduled_time in data
    data.scheduled_time?.forEach(scheduled_time =>{
      // only validate if scheduled_time is defined
      // New session with only session B need to be schedule (Due to all student finished A already)
      // In this case  data.scheduled_time[0] will be undefined/null and no need to check if its valid or not.
      if(scheduled_time){
        this.validateScheduleTime(scheduled_time);
      }
    })

    let date_time_start, date_time_end;
    if (isScheduled) {
      const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
      if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
        // validate if test session scheduled time is within tw start and end time for operational
        if(this.mustValidateScheduledTimeAssessments(slug)) {
          await this.checkSessionStartTime(slug, scheduled_time, school_class_id);
        }
        const scheduledUTCTime = this.convertToUTC(scheduled_time[0]);
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime);
      }
      if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL'){
        let startTimeMoment = '';
        let convertedScheduledTime: string[] = [];
        // if Language scheduled only
        if(scheduled_time[0] && scheduled_time[1] && !scheduled_time[2] && !scheduled_time[3]){
          startTimeMoment = scheduled_time[0];
          convertedScheduledTime = [this.addHMtoPJScheduleTime(scheduled_time[0]), this.addHMtoPJScheduleTime(scheduled_time[1])]
        }
        // if Math scheduled only
        if(!scheduled_time[0] && !scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
          startTimeMoment = (scheduled_time[0] <= scheduled_time[2]) ? scheduled_time[0] : scheduled_time[2];
          convertedScheduledTime = [this.addHMtoPJScheduleTime(scheduled_time[2]), this.addHMtoPJScheduleTime(scheduled_time[3])]
        }
        // if Language and Math both scheduled
        if(scheduled_time[0] && scheduled_time[1] && scheduled_time[2] && scheduled_time[3]){
          startTimeMoment = (scheduled_time[0] <= scheduled_time[2]) ? scheduled_time[0] : scheduled_time[2];
          convertedScheduledTime = scheduled_time.map(time => this.addHMtoPJScheduleTime(time));
        }

        if(this.mustValidateScheduledTimeAssessments(slug)) {
          await this.checkSessionStartTime(slug, convertedScheduledTime, school_class_id);
        }
        const scheduledUTCStartTime = this.convertToUTC(startTimeMoment);
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      }
    }
    else {
      //if the school admin schedule assessment that start now make sure the time is winthin scheduel if the assessment type need to be operational test
      if(this.mustValidateScheduledTimeAssessments(slug)) {
        const validTestWindows = await dbRawRead(this.app, {school_class_id}, `
          select tw.id
            from school_classes sc
            join school_semesters ss on ss.id = sc.semester_id
            join test_windows tw on tw.id = ss.test_window_id and tw.is_active = 1 and tw.date_start < now() and tw.date_end > now()
           where sc.id = :school_class_id
             and tw.is_active = 1
        ;`);
        if(!validTestWindows.length){
          throw new Errors.BadRequest('MSG_ASSES_TIME_INVALID_WARNING');
        }
      }
      date_time_start = dbDateNow(this.app);
    }

    const schoolClassRecord = <any>await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    const schl_group_id = schoolClassRecord.schl_group_id;
    // await this.app.service('public/educator/session').ensureStudentLang(schl_group_id); // todo: this can be long running!

    const semester_id = schoolClassRecord.semester_id
    //get the class's semester
    const semesterRecord = <any>await this.app
      .service('db/read/school-semesters')
      .get(semester_id);

    const test_window_id = semesterRecord.test_window_id;
    // get active test window (take the first active one)
    /*
    const testWindowRecords = <Paginated<any>>await this.app
      .service('db/read/test-windows')
      .find({ query: { test_ctrl_group_id } });
    logger.silly(testWindowRecords)
    */
    const testWindowRecord = <Paginated<any>>await this.app
      .service('db/read/test-windows')
      .get(test_window_id);

    logger.silly(testWindowRecord)

    const testWindow = testWindowRecord;
    //const test_window_id = testWindow.id;

    const test_window_td_alloc_rules = await dbRawRead(this.app, [test_window_id, slug], `
    select *
      from test_window_td_alloc_rules
      where test_window_id = ?
        and type_slug = ?
    ;`);
    const firstRule = test_window_td_alloc_rules[0];
    if (firstRule.is_secured) {
      await this.app.service('public/educator/session').validateTechnicalReadinessStatus(schl_group_id,slug);
    }

    const testSessionGroup = await this.app
      .service('db/write/u-groups')
      .create({
        group_type: DBD_U_GROUP_TYPES.mpt_test_session,
        created_by_uid,
      });
    logger.silly('hello', { testSessionGroup })
    const test_session_group_id = testSessionGroup.id;


    const classes_sessions = await dbRawRead(this.app, [school_class_id, slug], `
      select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, ts.access_code
      from school_class_test_sessions scts
      join test_sessions ts on ts.id = scts.test_session_id
      where scts.school_class_id = ?
        and ts.is_cancelled = 0
        and ts.is_closed = 0
        and scts.slug = ?
      ;`);

    if (classes_sessions.length > 0) {
      if (slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE' || slug === 'G9_SAMPLE' || slug === 'OSSLT_SAMPLE') {
        throw new Errors.BadRequest('ONGOING_SAMPLE_ASSESSMENT');
      } else if (slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_OPERATIONAL') {
        throw new Errors.BadRequest('ONGOING_LIVE_ASSESSMENT');
      }
    }

    const access_code = ''; // Test session acces code is deprecated
    const testSession = await this.app
      .service('db/write/test-sessions')
      .create({
        test_session_group_id,
        test_window_id,
        schl_group_id,
        delivery_format,
        date_time_start,
        access_code,
        is_access_code_enabled: false
      })
    const test_session_id = testSession.id;
    await this.app
      .service('db/write/school-class-test-sessions')
      .create({
        school_class_id,
        test_session_id,
        slug,
        caption,
      });
      let subSessionRecords
      if(isScheduled){
        const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, scheduled_time)
      }
      else{
        subSessionRecords = await this.app.service('public/educator/session-sub').initSubSessionRecords(test_session_id, undefined, is_fi_class)
      }

    //await this.app.service('public/educator/session').ensureSchool_class_common_forms(test_session_id, created_by_uid);

    await this.app.service('public/educator/session').ensureSchool_class_common_forms(test_session_id, created_by_uid);

    return <any>{
      ...subSessionRecords,
      test_session_id,
      date_time_start: testSession.date_time_start,
      school_class_id,
      test_window_id,
      slug,
      studentRecords,
      class_code: schoolClassRecord.name,
      caption,
      access_code //
    };
  }

  private async getData(props: any[], query: string) {
    const db:Knex = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]>res[0];
  }

  sanitizeScheduledTime(val:string | string[]){
    // temporary patch...
    if (typeof val === 'string'){
      return [
        val,
        val,
      ]
    }
    else{
      return val;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: ISessionEdit, params?: Params): Promise<Data> {
    const { slug } = data;
    let date_time_start, date_time_end, date_time_start2, date_time_end2, pj_date_time_start, pj_date_time_end;
    const scheduled_time = this.sanitizeScheduledTime(data.scheduled_time);
    
    //validate all scheduled_time in data
    scheduled_time?.forEach(scheduledTime =>{
      // only validate if scheduledTime is defined
      // New session with only session B need to be schedule (Due to all student finished A already)
      // In this case scheduledTime[0] will be undefined and no need to check if its valid or not.
      if(scheduledTime){
        this.validateScheduleTime(scheduledTime);
      }
    })
    
    const school_class = await dbRawRead(this.app, [id], `select school_class_id from school_class_test_sessions where test_session_id = ?;`);

    // PJ
    if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      // validate if test session scheduled time is within tw start and end time for operational
      if(this.mustValidateScheduledTimeAssessments(slug)) {
        const convertedScheduledTime = [this.addHMtoPJScheduleTime(scheduled_time[0]), this.addHMtoPJScheduleTime(scheduled_time[1]), this.addHMtoPJScheduleTime(scheduled_time[2]), this.addHMtoPJScheduleTime(scheduled_time[3])]
        await this.checkSessionStartTime(slug, convertedScheduledTime, school_class[0].school_class_id); 
      }
     
      // Language
      const scheduledStandardStartTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE);
      const scheduledStandardEndTimeMoment = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);
      const scheduledUTCStartTimeMoment = scheduledStandardStartTimeMoment.utc();
      const scheduledUTCEndTimeMoment = scheduledStandardEndTimeMoment.utc();
      const scheduledUTCStartTime = scheduledUTCStartTimeMoment.format('YYYY-MM-DDTHH:mm');
      const scheduledUTCEndTime = scheduledUTCEndTimeMoment.format('YYYY-MM-DDTHH:mm');
      date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime);
      date_time_end = dbDateSetDateTime(this.app, 0, scheduledUTCEndTime);
      // Math
      const scheduledStandardStartTimeMoment2 = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
      const scheduledStandardEndTimeMoment2 = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
      const scheduledUTCStartTimeMoment2 = scheduledStandardStartTimeMoment2.utc();
      const scheduledUTCEndTimeMoment2= scheduledStandardEndTimeMoment2.utc();
      const scheduledUTCStartTime2 = scheduledUTCStartTimeMoment2.format('YYYY-MM-DDTHH:mm');
      const scheduledUTCEndTime2 = scheduledUTCEndTimeMoment2.format('YYYY-MM-DDTHH:mm');
      date_time_start2 = dbDateSetDateTime(this.app, 0, scheduledUTCStartTime2);
      date_time_end2 = dbDateSetDateTime(this.app, 0, scheduledUTCEndTime2);
      // PJ session start time based on the earliest start time of Language and Math; PJ session end time based on the latest end time of Language and Math
      pj_date_time_start = date_time_start <= date_time_start2 ? date_time_start : date_time_start2;
      pj_date_time_end = date_time_end <= date_time_end2 ? date_time_end2 : date_time_end;
    }
    // G9 and OSSLT
    if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
      // validate if test session scheduled time is within tw start and end time for operational 
      if (this.mustValidateScheduledTimeAssessments(slug)) {
        await this.checkSessionStartTime(slug, scheduled_time, school_class[0].school_class_id);
      }

      const scheduledStandardTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
      const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      date_time_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime)

      logger.silly('SCHOOL_ADMIN_SESSION_PATCH_DATE', { scheduled_time: scheduled_time[0], scheduledUTCTime });

      const scheduledStandardTimeMoment2 = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment2 = scheduledStandardTimeMoment2.utc()
      const scheduledUTCTime2 = scheduledUTCTimeMoment2.format('YYYY-MM-DDTHH:mm');
      date_time_start2 = dbDateSetDateTime(this.app, 0, scheduledUTCTime2)
    }

    const subSessions =  <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: {test_session_id:id }, paginate: false });
    //const subSessions = subSessionsPage? <any[]>subSessionsPage:[]
    for(let subSession of subSessions){
      if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
        if(subSession.slug === 'session_a'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start})
        }
        if(subSession.slug === 'session_b'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{datetime_start:date_time_start2})
        }
      }
      if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
        if(subSession.slug === 'lang_session_a' || subSession.slug === 'lang_session_b' || subSession.slug === 'lang_session_c' || subSession.slug === 'lang_session_d' ){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{ datetime_start: date_time_start, datetime_end: date_time_end});
        }
        if(subSession.slug === 'math_stage_1' || subSession.slug === 'math_stage_2' || subSession.slug === 'math_stage_3' || subSession.slug === 'math_stage_4'){
          await this.app.service('db/write/test-session-sub-sessions').patch(subSession.id,{ datetime_start: date_time_start2, datetime_end: date_time_end2 });
        }
      }
    }
    let patchQuery = {date_time_start}
    if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      patchQuery = { date_time_start: pj_date_time_start };
    }
    const patchedTestSession = await this.app
      .service('db/write/test-sessions')
      .patch(id, patchQuery);

    // Re-scheduling from admin
    // If the session is happening soon enough, students in the class may have approved alt requests that should be auto-operational sent
    // It will log any errors in db so don't await
    if (slug.includes('OPERATIONAL') && school_class[0].school_class_id  && patchedTestSession.date_time_start && patchedTestSession.test_window_id){
      const isClassOpSendRequired = await this.app.service('public/alt-version-ctrl/alt-version-requests').isClassOpSendRequired(+school_class[0].school_class_id, patchedTestSession.date_time_start, +patchedTestSession.test_window_id)
      if (isClassOpSendRequired){
        this.app.service('public/alt-version-ctrl/alt-version-requests').handleOpAutoSend({school_class_id: school_class[0].school_class_id})
      }
    }

    return patchedTestSession;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    if (id && params) {
      const uid = await currentUid(this.app, params);
      const session = <any>await this.app
        .service('db/read/test-sessions')
        .find({
          query: { id },
          paginate: false
        });
      if(session && session[0].date_time_start < new Date()) {
        throw new Errors.BadRequest('NO_CANCEL_FOR_ACTIVE_SESSION');
      }
      await this.app
      .service('db/write/test-sessions')
      .patch(id, {
        /*  is_closed: 1,
        closed_on: dbDateNow(this.app), */
        is_cancelled: 1,
        cancelled_on: dbDateNow(this.app),
      });
      return <any>{ id }
    }
    throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
  }

  /**
   * Check if the input datetime is valid or not.
   * @param scheduled_time string
   */
  validateScheduleTime(scheduled_time:string){
    if(!scheduled_time || !moment.tz( scheduled_time, STANDARD_TIMEZONE).isValid()){
      throw new Errors.BadRequest('MSG_INVALID_SCHEDULE_TIME');
    } 
  }
}
