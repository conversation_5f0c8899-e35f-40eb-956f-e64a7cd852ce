import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class InvigilateClass implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
//   async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
//     throw new Errors.MethodNotAllowed();
//   }

async patch (id: NullableId, data: {class_id:any, schl_group_id:any}, params?: Params): Promise<Data> {
    if (!data || !params || !params.query) {
        throw new Errors.BadRequest();
    }
    const { class_id, schl_group_id } = data
    const uid = await currentUid(this.app, params);

    const classRecord = <any[]>await this.app
    .service('db/read/school-classes')
    .find({
      query: { id: class_id },
      paginate: false
    });

    let class_group_id;
    if(classRecord.length) {
        class_group_id = classRecord[0].group_id;
    }

    if(class_group_id){
      //revoke existing invigilators
      await this.app.service('public/educator/invigilators').revokeExistingInvigilator(uid, class_group_id, uid)

      const role_type = 'schl_teacher_invig';
      let invigilatorRecord;
      const invigilatorEntry ={
        role_type,
        uid,
        group_id:class_group_id,
        created_by_uid:uid
      }

      invigilatorRecord = await this.app
        .service('db/write/user-roles')
        .create(invigilatorEntry);
    }    
    
    return {...data, class_group_id};
}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
