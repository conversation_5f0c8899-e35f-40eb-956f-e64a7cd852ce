import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';
import logger from '../../../logger';
import { currentUid } from '../../../util/uid';
import { FeathersError, GeneralError } from '@feathersjs/errors';

interface Data {
  slug ?: string,
  data ?: any,
  stack ?: any
}

interface ServiceOptions {}

/**
 * Implement HTTP request status code 424 Failed Dependency
 * To handle client side direct resource fetching error
 */
class FailedDependency extends FeathersError {
  constructor(message: string, data?: any) {
    super(message, 'FailedDependency', 424, 'failed-dependency', data)
  }
}

const INTERNAL_ERROR_SLUGS: {[key: string]: any} = {
  // 'STYLE_PROFILE_HEALTHCHECK_FAILED': Errors.GeneralError,
  'CLIENT_FETCH_ERROR_TOP_BANNER': FailedDependency,
  'CLIENT_FETCH_ERROR_AUDIO': FailedDependency,
  'CLIENT_FETCH_ERROR_VIDEO': FailedDependency,
  'CLIENT_FETCH_ERROR': FailedDependency,
  'WS_SEND_TO_CONNECTION_ERROR': GeneralError,
  'BULK_CSV_REPORT_GENERATION_ERROR': GeneralError
}


export class Log implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (payload: Data, params?: Params): Promise<Data> {
    let created_by_uid : number | null = null;
    let user_agent:string | undefined;
    if (params){
      try {
        created_by_uid = await currentUid(this.app, params);
      }
      catch(e){}
    }
    await this.createLog(payload.slug || '--', JSON.stringify(payload.data), created_by_uid)
    if(payload.slug && INTERNAL_ERROR_SLUGS[payload.slug]) {
      const errorData = payload.data ? payload.data : undefined
      const errors = payload.stack ? payload.stack : undefined
      throw new INTERNAL_ERROR_SLUGS[payload.slug](payload.slug, errorData, errors);
    }
    return <any> {};
  }

  async createLog(slug:string, data:any, created_by_uid:number | null, user_agent?:string) {
    console.log(JSON.stringify({
      created_by_uid,
      slug: slug,
      data,
    }))
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
