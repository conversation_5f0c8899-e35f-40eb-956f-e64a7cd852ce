import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { IUTestAdmin } from '../../../db/schemas/u_test_admins.schema';
import { AccountType } from '../../../../types/account-types';
import { IUser } from '../../../db/schemas/users.schema';
import { IAuth } from '../../../db/schemas/auths.schema';
import * as Errors from '@feathersjs/errors';
import { AUTH_ERROR } from '../../../../errors/auth';
import { dbRawRead } from '../../../../util/db-raw';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

export interface IUserInfoCore {
  email?: string,
  id?: number,
  uid?: number,
  accountType?: string,
  accountTypes?: string,
  firstName?:string,
  lastName?:string,
  isTotpUser?:number
}

interface Data extends IUserInfoCore {}

interface ServiceOptions {}

export class UserInfoCore implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async retrieveUserInfo(uid:number):Promise<Data> {
    const app = this.app;
    const dbUserRead = app.service('db/read/users');
    const userQueryField:Partial<IUser> = {id: uid}
    const userRecords:Paginated<IUser> = <any> await dbUserRead.find({ query: {... userQueryField}});
    if(!userRecords.data.length){
      throw new Errors.NotFound("User Not Found")
    }
    const userRecord = userRecords.data[0];
    const accountType = userRecord.account_type;
    const firstName:string = userRecord.first_name;
    const lastName:string = userRecord.last_name;
    
    const IS_MFA_DISABLED = await getSysConstNumeric(app, 'IS_MFA_DISABLED');
    const isTotpUser: any = !IS_MFA_DISABLED && userRecord.is_totp_user;
    
    return {
      uid,
      accountType,
      firstName,
      lastName,
      isTotpUser
    }
  }

  async getAccountTypes(uid: number){
    const records = await dbRawRead(this.app, [uid], `
    select * from (
      select urgat.display_order
          -- , ur.role_type
          -- , ug.group_type
             , CONCAT( urgat.route_template, (CASE WHEN urgat.route_join = 0 THEN ur.group_id ELSE 0 END)) grouping_handler
           , urgat.route_template
             , urgat.caption
             , urgat.color
             , ur.group_id
             , urgat.account_type
             , s.name s_name
             , s.foreign_id s_foreign_id
             , sd.name sd_name
             , sd.foreign_id sd_foreign_id
          -- , u.account_type
             , ur.id as ur_id
             , ur.is_conf_req
             , ur.is_confirmed
        from users u
        join user_roles ur
          on ur.uid = u.id
          and ur.is_revoked = 0
        join u_groups ug
          on ug.id  = ur.group_id
        join user_role_group_account_types urgat
          on urgat.role_type = ur.role_type
          and urgat.group_type = ug.group_type
        left join schools s
          on s.group_id = ur.group_id
        left join school_districts sd
          on sd.group_id = ur.group_id
        where uid = ?
      ) t
      group by grouping_handler
      order by display_order
    `);
    return records;
  }

  async parseUserinfoJWT(params?:Params):Promise<Data> {
    if(params && params.authentication && params.authentication.payload){
      const user_info = params.authentication.payload;
      return user_info;
    }else{
      throw new Errors.NotAuthenticated(AUTH_ERROR.MISSING_JWT)
    }
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // const user = <IAuth>(<any>params).user;
    return [await this.parseUserinfoJWT(params)];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const user = <IAuth>(<any>params).user;
    if (user.uid !== id){
      throw new Error('Supplied UID does not match authenticated user.')
    }
    return await this.parseUserinfoJWT(params);
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
