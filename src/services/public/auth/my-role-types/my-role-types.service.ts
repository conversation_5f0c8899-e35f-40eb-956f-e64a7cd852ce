// Initializes the `public/auth/my-role-types` service on path `/public/auth/my-role-types`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { MyRoleTypes } from './my-role-types.class';
import hooks from './my-role-types.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/auth/my-role-types': MyRoleTypes & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/auth/my-role-types', new MyRoleTypes(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/auth/my-role-types');

  service.hooks(hooks);
}
