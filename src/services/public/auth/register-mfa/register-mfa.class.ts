import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import {dbRawRead, dbRawWrite} from '../../../../util/db-raw';
import { IAuth } from '../../../db/schemas/auths.schema';
import { AUTH_ERROR, AUTH_REG_ERROR } from '../../../../errors/auth';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

const speakeasy = require('speakeasy');

interface Data
{
  email: string;
}

interface MFAData
{
  [key: string]: boolean;
}

interface ServiceOptions {}

export class RegisterMfa implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
   * Use find function in this class solely to determine if a user requires registration of MFA or not
   * @returns result: {userRequiresMFA: boolean, isFirstLoginWithTotp: boolean}
   */
  async find (params?: Params): Promise<any | Paginated<any>> {
    if(await getSysConstNumeric(this.app, 'IS_MFA_DISABLED')) { // system-wide MFA disabler
      return {userRequiresMFA: false, isFirstLoginWithTotp: null, isTotpUser: false};
    }

    if (!params?.query?.email) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const result = await dbRawRead(this.app, [params.query.email], `
      SELECT 
        IF(MAX(urt.requires_mfa), true, false) as userRequiresMFA,
        IF(MAX(urt.mfa_exempt), true, false) as mfaExempt,
        IF(a.is_first_login_with_totp, true, false) as isFirstLoginWithTotp,
        u.is_totp_user as isTotpUser
      FROM auths as a
      JOIN users as u
        ON a.uid = u.id
      JOIN user_roles as ur
        ON a.uid = ur.uid
       AND ur.is_revoked = false
      JOIN u_role_types as urt
        ON ur.role_type = urt.role_type
      WHERE a.email = ?
        AND (urt.requires_mfa = true OR urt.mfa_exempt = true);
    `);

    if(result[0] && result[0].mfaExempt) {
      return {userRequiresMFA: false, isFirstLoginWithTotp: null};
    }

    return result[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<any> {
    if(await getSysConstNumeric(this.app, 'IS_MFA_DISABLED')) { // system-wide MFA disabler
      throw new Errors.GeneralError('MFA_DISABLED');
    }
    
    const dbAuthTableRead = this.app.service('db/read/auths');
    const matchingAuthRecord = <any> await dbAuthTableRead.find({query: { email: data.email }});

    if (matchingAuthRecord.total === 0) {
      throw new Errors.NotFound(AUTH_ERROR.NOT_FOUND);
    } 
    if (matchingAuthRecord.total > 1) {
      throw new Errors.GeneralError(AUTH_ERROR.MULTIPLE_AUTHS);
    }

    // throw error for already existing MFA user

    const retrievedAuthRecord = matchingAuthRecord.data[0];

    //retrievedUserRecord guranteed to have exactly 1 record found because of the above error checking
    const retrievedUserRecord = await dbRawRead(this.app, [retrievedAuthRecord.uid], `
    SELECT *
    FROM mpt_dev.users as u
    WHERE u.id = ?
    ;`);

    // the user is already a TOTP user; just form their otpauth_url from their existing secret and return it
    if (retrievedUserRecord[0].is_totp_user == 1) {
      const  existingOtpauthURL = "otpauth://totp/SecretKey?secret=" + retrievedAuthRecord.totp_secret;
      return {otpauthURL: existingOtpauthURL};
    }

    // generates object with keys "ascii", "hex", "base32", and "otpauth_url"
    // use base32 for authenticator applications
    const userSecret = speakeasy.generateSecret();

    // update auths table
    await dbRawWrite(this.app, [userSecret.base32, retrievedAuthRecord.id], `
    UPDATE mpt_dev.auths as a
    SET
      a.totp_secret=?,
      a.is_first_login_with_totp=true
    WHERE a.id = ?
    ;`);

    //update user table
    await dbRawWrite(this.app, [retrievedAuthRecord.uid], `
    UPDATE mpt_dev.users as u
    SET u.is_totp_user=true
    WHERE u.id = ?
    ;`);

    return {otpauthURL: userSecret.otpauth_url};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }
}
