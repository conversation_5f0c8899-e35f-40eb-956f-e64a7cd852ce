const _ = require('lodash');
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { generateSecretCode } from '../../../../util/secret-codes';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { dbDateNow } from '../../../../util/db-dates';
import { ITestSession } from '../../../db/schemas/test_sessions.schema';
import { ITestWindow } from '../../../db/schemas/test_windows.schema';
import { randArrEntry } from '../../../../util/random';
import axios from 'axios';
import { ITestAttempt, ITestAttemptInfo } from '../../../db/schemas/test_attempts.schema';
import { ITestDesignPayload } from '../../test-taker/invigilation/test-attempt/test-design.data';
import { IAttemptPayload, ITestAttemptQuestionResponses } from '../../student/session/session.class';
import { isTestSessionPaused } from '../../../../redis/table-key-operations/test-session';

interface Data {}

interface ServiceOptions {}

interface ITestDesignInfo {
  test_design_id: Id,
  source_item_set_id: Id,
  test_form_id: Id,
  framework: string,
}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    if (params && id){
      const test_session_id = <number> id;
      const uid = await currentUid(this.app, params);
      return this.findAttemptPayload({uid, test_session_id});
    }
    throw new Errors.BadRequest();
  }

  private async getTestDesignRecord(test_session_id:Id){
    const testSession = await this.app.service('db/read/test-sessions').get(test_session_id)
    const testWindow = await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const item_set_id = testWindow.item_set_id;
    const testDesignRecords = <Paginated<any>> await this.app
    .service('db/read/test-designs')
    .find({
      query: {
        source_item_set_id: item_set_id,
        is_public: 1,
      }
    })
    if (testDesignRecords.total === 0){
      throw new Errors.NotFound();
    }
    const testDesignRecord = testDesignRecords.data[testDesignRecords.data.length - 1];
    if (!testDesignRecord){
      throw new Errors.NotFound();
    }
    return {
      testSession,
      testWindow,
      item_set_id,
      testDesignRecord,
    }
  }

  private async loadNewStudentTestDesign(test_session_id:Id) : Promise<ITestDesignInfo>{
    const {
      testSession,
      testWindow,
      item_set_id,
      testDesignRecord,
    } = await this.getTestDesignRecord(test_session_id);
    const testFormRefs = <Paginated<any>> await this.app
    .service('db/read/test-forms')
    .find({
      query: {
        $select: ['id', 'lang'],
        test_design_id: testDesignRecord.id,
        is_revoked: 0,
        $limit: 1000,
      }
    });
    const testFormSelection = randArrEntry(testFormRefs.data);
    return {
      test_design_id: testDesignRecord.id,
      source_item_set_id: testDesignRecord.source_item_set_id,
      test_form_id: testFormSelection.id,
      framework: testDesignRecord.framework,
    }
  }

  public async loadTestDesignForm(test_design_id:number){
    return {
      test_design_id,
    };
  }

  public async getTestDesign(file_path:string){
    const testDesign = await this.getTestDesignPromise(file_path);
    return <any>testDesign.data;
  }

  public getTestDesignPromise(file_path: string) {
    const url = generateS3DownloadUrl(file_path, 60);
    return axios.get(url, {});
  }

  private async ensureAttemptMarkedStarted(currentAttempt:ITestAttempt){
    if (!currentAttempt.started_on){
      const dbNow = dbDateNow(this.app);
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          started_on: dbNow,
          last_touch_on: dbNow,
        });
    }
  }

  private async getAttemptQuestionResponses(test_attempt_id:number){
    // await this.app.service('public/student/session-question').removeDuplicateQuestionAttemptRecords(test_attempt_id); // this is not necessary because we are anyway pulling the records in order of id
    const questionResponseStates = <ITestAttemptQuestionResponses[]> await this.app
      .service('db/read/test-attempt-question-responses')
      .find({query:{
        test_attempt_id,
        is_invalid: 0,
      }, paginate: false});
    const questionStates:{[key:string]: string} = {};
    questionResponseStates.forEach(q => {
      questionStates[q.test_question_id] = JSON.parse(q.response_raw)
    });
    return questionStates;
  }

  private async getAttemptTimeInfo(currentAttempt:ITestAttempt){
    const testSession = <ITestSession> await this.app.service('db/read/test-sessions').get(currentAttempt.test_session_id);
    const testWindow =  <ITestWindow> await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const user_time_ext_m = currentAttempt.time_ext_m || 0;
    const test_session_time_ext_m = testSession.time_ext_m || 0;
    const test_window_time = testWindow.duration_m || 0;
    return {
      test_window_time,
      time_ext_m: user_time_ext_m + test_session_time_ext_m
    }
  }

  public async findAttemptPayload(data: IAttemptPayload){
    const { uid, test_session_id, } = data;
    const currentAttempt:ITestAttempt = await this.getCurrentAttempt(uid, +test_session_id);
    await this.ensureAttemptMarkedStarted(currentAttempt);
    const testDesign:ITestDesignPayload = await this.app.service('public/student/session').loadAttemptTestFormData(currentAttempt);
    const styleProfile = await this.app.service('public/student/session').loadStyleProfileByTestDesign(currentAttempt.test_form_id);
    const { testDesignRecord, } = await this.getTestDesignRecord(test_session_id);
    const questionStates = await this.getAttemptQuestionResponses(currentAttempt.id)
    const is_issue_reporting_enabled =  0; // todo: move this into the test window configuration
    const {time_ext_m, test_window_time} = await this.getAttemptTimeInfo(currentAttempt);
    currentAttempt.section_index = currentAttempt.section_index || 0;
    currentAttempt.question_index = currentAttempt.question_index || 0;
    return [
      {
        question_index: currentAttempt.question_index,
        section_index: currentAttempt.section_index,
        module_id: currentAttempt.module_id,
        attempt_key: currentAttempt.attempt_key,
        attemptId: currentAttempt.id,
        is_closed: (+currentAttempt.is_closed === 1),
        testDesign,
        framework: testDesignRecord.framework,
        is_issue_reporting_enabled,
        questionStates,
        time_ext_m,
        test_window_time,
        styleProfile
      }
    ];
  }

  private async loadTestFormById(test_form_id:Id){
    const testForm = await this.app.service('db/read/test-forms').get(test_form_id);
    return this.getTestDesign(testForm.file_path);
  }

  private async generateTestFormCache(testDesign:ITestDesignInfo) : Promise<string>{
    let cache:{[key:string]:any} = {};
    try {
      // temp: these are temporary... just need to cycle out the forms after completing this ticket: https://bubo.vretta.com/vea/platform/vea-web-client/-/issues/2246
      const framework = JSON.parse(testDesign.framework);
      cache.testFormType = framework.testFormType;
      cache.isTimerDisabled = framework.isTimerDisabled;
      cache.referenceDocumentPages = framework.referenceDocumentPages;
      cache.helpPageId = framework.helpPageId;
    }catch(e){}
    const testFormData = await this.loadTestFormById(testDesign.test_form_id);
    try {
      testFormData.panelModules.forEach((panelModule:{questions:number[]}) => {
        panelModule.questions = _.shuffle(panelModule.questions);
      })
      cache.panelModules = testFormData.panelModules
    }catch(e){}
    return JSON.stringify(cache);
  }

  private async createAttempt(uid:number, test_session_id:number){
    const testDesign = await this.loadNewStudentTestDesign(test_session_id);
    const test_form_id = <number> testDesign.test_form_id;
    const test_form_cache = await this.generateTestFormCache(testDesign);
    const createFields:Partial<ITestAttempt> = {
      uid,
      test_session_id,
      section_index: 0,
      question_index: 0,
      test_form_id,
      test_form_cache,
      attempt_key: generateSecretCode(5),
      is_absent: 0,
      started_on: dbDateNow(this.app)
    }

    return this.app
      .service('db/write/test-attempts')
      .create(createFields)
  }

  private async getCurrentAttempt(uid:number, test_session_id:number)  {
    const attempts = <ITestAttempt[]> await this.app
      .service('db/read/test-attempts')
      .find({query: {
        uid,
        test_session_id,
        $limit:1,
        is_invalid: {$ne: 1}
      }, paginate: false })
    if (attempts.length > 0){
      return attempts[0];
    }

    return await this.createAttempt(uid, test_session_id);

    throw new Errors.Forbidden("SESSION_CLOSED");
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: {test_attempt_id: number}, params?: Params): Promise<Data> {
    // to do: validate
    if (params){
      return this.app.service('public/student/session-question').submitTest({
        ... this.app.service('public/student/session-question').getReqData(data),
        uid: await currentUid(this.app, params),
      },
      this.app.service('public/field-tester/session-question').validateAttId)
    }
    throw new Errors.BadRequest();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  private async validateAttemptWindowAndTiming(attemptRecord:ITestAttemptInfo, testSession:ITestSession){
    const testWindow =  <ITestWindow> await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const isActive = testWindow.is_active;
    const isArchived = testWindow.is_archived;
    if (!isActive){
      throw new Errors.Forbidden('TEST_WINDOW_INACTIVE');
    }
    if (isArchived){
      throw new Errors.Forbidden('TEST_WINDOW_ARCHIVED');
    }
  }


  public async validateAttemptId(uid:number, attempt_id:number){
    const attemptRecord = <ITestAttemptInfo> await this.app
      .service('db/read/test-attempts-info')
      .get(attempt_id);
    if (!attemptRecord){
      throw new Errors.Forbidden('NOT_BOOKED_APPL');
    }
    if (attemptRecord.is_closed){
      throw new Errors.Forbidden('ATTEMPT_CLOSED');
    }

    if (attemptRecord.is_session_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }

    const testSession = <ITestSession> await this.app.service('db/read/test-sessions').get(attemptRecord.test_session_id);
    const is_paused = await isTestSessionPaused(this.app, attemptRecord.test_session_id)
    if (is_paused) {
      throw new Errors.Forbidden('NOT_VERIFIED');
    }
    if (testSession.is_closed == 1){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    if (testSession.is_cancelled == 1){
      throw new Errors.Forbidden('SESSION_CANCELLED');
    }

    await this.validateAttemptWindowAndTiming(attemptRecord, testSession);

    // this would indicate that it is the first time that this student is accessing this attempt
    return { attemptRecord, subSessionData: null };
  }

}
