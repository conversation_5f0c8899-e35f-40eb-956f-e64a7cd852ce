import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadReporting } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';

interface Data {}

interface ServiceOptions {}

export class RevokeStudentSnapshots implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query || !params.query.test_window_id || !params.query.student_uids) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const { test_window_id, student_uids } = params.query;

    const query = `
      -- took ~ 0.141s on mirror DB
      select distinct signoffs.test_window_id
		    , signoffs.tw_type_slug
        , signoffs.schl_group_id
        , signoffs.id as signoff_id
        , signoffs.created_on as signoff_created_on
        , signoffs.created_by_uid as signoff_created_by_uid
        , signoffs.is_auto_approve as signoff_is_auto_approve
        , snapshots.id as snapshot_id
        , snapshots.student_uid as snapshot_student_uid
        , snapshots.student_info_json as snapshot_student_info_json
        , snapshots.created_on as snapshot_created_on
        , snapshots.created_by_uid as snapshot_created_by_uid
      from school_student_asmt_info_snapshot snapshots
      join school_student_asmt_info_signoffs signoffs 
        on signoffs.id = snapshots.ssais_id 
        and signoffs.is_revoked = 0 
        and signoffs.test_window_id = :testWindowId
      where snapshots.is_revoked = 0
        and snapshots.student_uid in (:studentUids)
      ;
    `;

    const queryParams = {
      testWindowId: test_window_id,
      studentUids: student_uids.split(','),
    };

    return await dbRawReadReporting(this.app, queryParams, query);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params?: Params): Promise<any> {
    if (!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    // Get payload
    const { schl_group_id, tw_type_slug, test_window_id, revoke_reason } = data;

    // Validate data
    if (!schl_group_id || !tw_type_slug || !test_window_id || !revoke_reason) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }

    // Get UID
    const uid = await currentUid(this.app, params);

    // Log
    logger.info({
      created_by_uid: uid,
      slug: 'REVOKE_SCHL_ADMIN_SIGNOFFS',
      data: JSON.stringify({ schl_group_id, tw_type_slug, test_window_id, revoke_reason })
    });

    // Revoke
    await this.app
      .service('public/school-admin/student-asmt-info-signoff')
      .revokeSchStuAsmtSignoff(schl_group_id, tw_type_slug, uid, revoke_reason, test_window_id);

    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
