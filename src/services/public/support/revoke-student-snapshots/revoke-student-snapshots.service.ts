// Initializes the `public/support/revoke-student-snapshots` service on path `/public/support/revoke-student-snapshots`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { RevokeStudentSnapshots } from './revoke-student-snapshots.class';
import hooks from './revoke-student-snapshots.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/revoke-student-snapshots': RevokeStudentSnapshots & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/revoke-student-snapshots', new RevokeStudentSnapshots(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/revoke-student-snapshots');

  service.hooks(hooks);
}
