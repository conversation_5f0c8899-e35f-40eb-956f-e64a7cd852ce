import { Application } from '../../../../declarations';
import { Knex } from 'knex';
import * as Errors from '@feathersjs/errors';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';
import { getSysConstString } from '../../../../util/sys-const-string';

interface StudentInfo {
  uid: number;
  firstName: string;
  lastName: string;
  oen?: string;
  testWindows: { id: number; name: string }[];
}

interface SnapshotData {
  isSafeToUpdate: boolean;
  message: string;
  snapshotId: number;
  ssaisId: number;
  studentInfoJson: any;
}

interface MetaUpdate {
  id: number;
  value: any;
  key: string;
  key_namespace: string;
}

export class StudentMetaUpdate {
  app: Application;
  knex: Knex;
  
  constructor(options: any, app: Application) {
    this.app = app;
    this.knex = app.get('knexClientWrite');
  }

  /**
   * Finds student information or assessment snapshot data.
   *
   * @param params - The parameters for the find operation.
   * @returns A promise that resolves to either student information or snapshot data.
   */
  async find(params: any): Promise<StudentInfo | SnapshotData> {
    const { oen, studentUid, testWindowId } = params.query;

    if (oen) {
      return this.getStudentInfoByOen(oen);
    }

    if (studentUid && !testWindowId) {
      return this.getStudentInfoByUid(parseInt(studentUid, 10));
    }

    if (studentUid && testWindowId) {
      return this.getSnapshotData(studentUid, testWindowId);
    }

    throw new Errors.BadRequest('Invalid query parameters for find method.');
  }

  /**
   * Updates a student's metadata.
   *
   * @param data - The data for the create operation.
   * @param params - The parameters for the create operation.
   * @returns A promise that resolves when the update is complete.
   */
  async create(data: any, params: any): Promise<any> {
    const { studentUid, snapshotIdToRevoke, ssaisId, studentInfoJson, updates, password } = data;

    if (!studentUid || !snapshotIdToRevoke || !ssaisId || !studentInfoJson || !updates || !Array.isArray(updates) || updates.length === 0) {
      throw new Errors.BadRequest('Missing required fields for update.');
    }

    const correctPassword = await getSysConstString(this.app, 'SUPPORT_META_UPDATE_PASSWORD');
    if (password !== correctPassword) {
      throw new Errors.NotAuthenticated('Incorrect password.');
    }

    const changedByUid = await currentUid(this.app, params);

    return this.knex.transaction(async (trx) => {
      // Process each meta update
      for (const metaToUpdate of updates) {
        const { id: metaId, value: newValue } = metaToUpdate;

        // Skip virtual entries (negative IDs) - they'll be handled in the user table update section
        if (metaId < 0) continue;

        const originalMeta = await trx('user_metas').where({ id: metaId }).first();

        if (!originalMeta) {
          throw new Errors.NotFound(`user_meta with id ${metaId} not found.`);
        }
        const from_value = originalMeta.value;

        // Step 1: Log the change
        logger.info('STUDENT_META_UPDATE_SUPPORT_TOOL_LOG', {
          changed_by_uid: changedByUid,
          user_meta_id: metaId,
          from_value: from_value,
          to_value: newValue,
          student_uid: studentUid,
          snapshot_id: snapshotIdToRevoke,
          ssais_id: ssaisId,
          student_info_json: studentInfoJson,
          updates: updates
        });

        // Step 2: Update the user_metas table
        await trx('user_metas')
          .where({ id: metaId })
          .update({ value: newValue, updated_by_uid: changedByUid, updated_on: trx.fn.now() });
      }

      // Step 3: Handle special cases like user's name by building a single update object
      const userTableUpdate: { [key: string]: any } = {};
      const updatedStudentInfoJson = JSON.parse(JSON.stringify(studentInfoJson));

      for (const metaToUpdate of updates) {
        const { id: metaId, value: newValue, key, key_namespace } = metaToUpdate;

        if (metaId < 0) {
          // Virtual entries: only update users table and users section in snapshot
          if (key_namespace === 'eqao_sdc') {
            if (key === 'FirstName') {
              userTableUpdate.first_name = newValue;
              updatedStudentInfoJson.users.first_name = newValue;
            } else if (key === 'LastName') {
              userTableUpdate.last_name = newValue;
              updatedStudentInfoJson.users.last_name = newValue;
            }
          }
        } else {
          // Real user_metas: update user_metas array in snapshot, and users table if it's FirstName/LastName
          const metaIndex = updatedStudentInfoJson.user_metas.findIndex((m: any) => m.id === metaId);
          if (metaIndex !== -1) {
            updatedStudentInfoJson.user_metas[metaIndex].value = newValue;
          } else {
            logger.warn('Meta ID not found in snapshot JSON during update', { metaId, snapshotIdToRevoke });
          }

          // Also update users table for FirstName/LastName real metas
          if (key_namespace === 'eqao_sdc') {
            if (key === 'FirstName') {
              userTableUpdate.first_name = newValue;
              updatedStudentInfoJson.users.first_name = newValue;
            } else if (key === 'LastName') {
              userTableUpdate.last_name = newValue;
              updatedStudentInfoJson.users.last_name = newValue;
            }
          }
        }
      }

      if (Object.keys(userTableUpdate).length > 0) {
        await trx('users').where({ id: studentUid }).update(userTableUpdate);
      }

      // Step 4: Revoke old snapshot and create new one
      await trx('school_student_asmt_info_snapshot').where('id', snapshotIdToRevoke).update({
        is_revoked: 1,
        revoked_by_uid: changedByUid,
        revoked_on: trx.fn.now(),
      });

      await trx('school_student_asmt_info_snapshot').insert({
        ssais_id: ssaisId,
        student_uid: studentUid,
        student_info_json: JSON.stringify(updatedStudentInfoJson),
        created_on: trx.fn.now(),
        created_by_uid: changedByUid,
        is_revoked: 0,
      });

      const updatedKeys = updates.map((u: MetaUpdate) => `${u.key_namespace}.${u.key}`).join(', ');
      return { success: true, message: `Successfully updated: ${updatedKeys}.` };
    });
  }

  /**
   * Retrieves student information by OEN.
   *
   * @param oen - The student's OEN.
   * @returns A promise that resolves to the student's information.
   * @private
   */
  private async getStudentInfoByOen(oen: string): Promise<StudentInfo> {
    const studentMeta = await this.knex('user_metas')
      .where({ key_namespace: 'eqao_sdc', key: 'StudentOEN', value: oen })
      .first('uid');

    if (!studentMeta) {
      throw new Errors.NotFound('Student not found for the given OEN.');
    }

    const studentInfo = await this.getStudentInfoByUid(studentMeta.uid);
    return { ...studentInfo, oen };
  }

  /**
   * Retrieves student information by UID.
   *
   * @param studentUid - The student's UID.
   * @returns A promise that resolves to the student's information.
   * @private
   */
  private async getStudentInfoByUid(studentUid: number): Promise<StudentInfo> {
    const student = await this.knex('users')
      .where('id', studentUid)
      .first('id as uid', 'first_name as firstName', 'last_name as lastName');

    if (!student) {
      throw new Errors.NotFound('Student not found for the given UID.');
    }

    // Fetch the student's OEN
    const oenMeta = await this.knex('user_metas')
      .where({ uid: studentUid, key_namespace: 'eqao_sdc', key: 'StudentOEN' })
      .first('value');

    const testWindows = await this.getStudentTestWindows(studentUid);

    return { ...student, oen: oenMeta?.value, testWindows };
  }

  /**
   * Retrieves test windows for a student.
   *
   * @param studentUid - The student's UID.
   * @returns A promise that resolves to the student's test windows.
   * @private
   */
  private async getStudentTestWindows(studentUid: number): Promise<{ id: number; name: string }[]> {
    return await this.knex('student_reports as sr')
      .join('test_attempts as ta', 'ta.id', 'sr.attempt_id')
      .join('test_window_td_alloc_rules as twtdar', 'twtdar.id', 'ta.twtdar_id')
      .join('test_windows as tw', 'tw.id', 'twtdar.test_window_id')
      .where('sr.uid', studentUid)
      .where('sr.is_revoked', 0)
      .where('sr.is_isr', 1)
      .distinct('tw.id', 'tw.title as name')
      .orderBy('tw.id', 'desc');
  }

  /**
   * Retrieves snapshot data for a student and test window.
   *
   * @param studentUid - The student's UID.
   * @param testWindowId - The test window's ID.
   * @returns A promise that resolves to the snapshot data.
   * @private
   */
  private async getSnapshotData(studentUid: string, testWindowId: string): Promise<SnapshotData> {
    // Step 1: Check how many ISRs
    const isrCountResult = await this.knex('student_reports as sr')
      .join('test_attempts as ta', 'ta.id', 'sr.attempt_id')
      .join('test_window_td_alloc_rules as twtdar', 'twtdar.id', 'ta.twtdar_id')
      .where('sr.uid', studentUid)
      .where('sr.is_revoked', 0)
      .where('sr.is_isr', 1)
      .where('twtdar.test_window_id', testWindowId)
      .count('sr.id as count');

    const isrCount = isrCountResult[0].count as number;
    const isSafeToUpdate = isrCount === 1;
    const message = isSafeToUpdate
      ? `Safe to update: Student has ${isrCount} ISR for this test window.`
      : `Warning: Student has ${isrCount} ISRs for this test window.`;

    // Step 3: Find student snapshot
    const snapshot = await this.knex('school_student_asmt_info_snapshot as snapshot')
      .join('school_student_asmt_info_signoffs as ssais', {
        'ssais.id': 'snapshot.ssais_id',
      })
      .where('snapshot.is_revoked', 0)
      .where('ssais.is_revoked', 0)
      .where('ssais.test_window_id', testWindowId)
      .where('snapshot.student_uid', studentUid)
      .first('snapshot.id as snapshotId', 'snapshot.student_info_json as studentInfoJson', 'ssais.id as ssaisId');

    if (!snapshot) {
      throw new Errors.NotFound('Snapshot not found for the given student and test window.');
    }

    return {
      isSafeToUpdate,
      message,
      snapshotId: snapshot.snapshotId,
      ssaisId: snapshot.ssaisId,
      studentInfoJson: JSON.parse(snapshot.studentInfoJson),
    };
  }
} 