import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { Redis } from 'ioredis';
import { removeTestAttempt } from '../../../../redis/table-key-operations/test-attempt';
import { getTestAttemptTASSs } from '../../../../redis/relation-key-operations/test-attempt-tasss';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class TestAttemptReset implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {oen} = params.query;
      const twtdarIdentifyLinearPhrase = '%"eqao_dyn.Linear": ["1"]%';
      const twtdarIdentifyLinear2Phrase = '%"eqao_dyn.Linear": ["2"]%';
      const twtdarIdentifyLinear1or2Phrase = '%"eqao_dyn.Linear": ["1"%"2"]%';
      // OEN, uid, school miden,  test window id, test session id test attempt id, assisment slug*2
      if(oen){
        return await dbRawRead(this.app, [twtdarIdentifyLinearPhrase, twtdarIdentifyLinear2Phrase, twtdarIdentifyLinear1or2Phrase, oen], `
          select a.*
               , count(taqr.id) as question_answered 
           from  (   select ta.uid
                          , ta.test_session_id
                          , ta.id as ta_id
                          , ta.last_touch_on
                          , ta.is_submitted
                          , ta.is_closed
                          , scts.slug as assessment_slug
                          , tsss.id
                          , tsss.slug sub_session_slug
                          , s.foreign_id as schl_mident
                          , ts.test_window_id
                          , case when twtdar.user_metas_filter like ? then 1
                                when twtdar.user_metas_filter like ? then 2
                                when twtdar.user_metas_filter like ? then '1/2'
                                else 0
                            end as is_linear_test_form 
                      from test_attempts ta 
                      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
                      join school_classes sc on scts.school_class_id = sc.id
                      join schools s on sc.schl_group_id = s.group_id
                      join test_sessions ts on ts.id = ta.test_session_id
                      join test_session_sub_sessions tsss on tsss.test_session_id = ts.id and tsss.twtdar_order = ta.twtdar_order
                      join test_windows tw on tw.id = ts.test_window_id and tw.is_active = 1 -- and tw.date_end > now()
                      join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
                      where ta.uid in ( select um.uid 
                                          from user_metas um 
                                        where um.key = 'studentOEN' 
                                          and um.key_namespace = 'eqao_sdc' 
                                          and um.value = ?) 
                        and ta.is_invalid != 1 
                        and (ta.is_closed is null or ta.is_closed= 0 or (ta.is_submitted = 1 and ta.is_closed= 1))
                    group by ta.id
                  ) a 
          left join test_attempt_question_responses taqr on taqr.test_attempt_id = a.ta_id
           group by a.ta_id
        ;`)
      } 
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: Id, data: Data, params?: Params): Promise<Data> {
    if(!id || !data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    if (id && params && data){
      const {oen, uid, testSessionId, testWindowId, reason} = <any>data;
      const created_by_uid = await currentUid(this.app, params);
      const invalid_reason = reason ? reason : null

      await this.unlinkAndInvalidateTestAttempt(id, created_by_uid, invalid_reason)

      // log the reset/unlink test attempt
      let log_data = {
        OEN: oen,
        UID: uid,
        testWindowId,
        testSessionId,
        testAttemptId: id,
        reason: reason
      }

      let log_entry = {
        created_by_uid,
        slug: 'SUPPORT_RESET_TEST_ATTEMPT',
        data: JSON.stringify(log_data)
      }

      logger.info(log_entry);
    }
    return []
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  /**
   * Unlink and invalidate test attempt and associate test attempt sub sessions
   * @param test_attempt_id 
   * @param invalid_by_uid 
   * @param invalid_reason 
   */
  async unlinkAndInvalidateTestAttempt(test_attempt_id:Id, invalid_by_uid:number, invalid_reason:string|null = null) {
    const dbNow = dbDateNow(this.app);
    await dbRawWrite(this.app, [dbNow, invalid_by_uid, invalid_reason, test_attempt_id], `
      UPDATE test_attempts ta
      SET uid = 0 - abs(ta.uid)
        , test_session_id = 0 - abs(ta.test_session_id)
        , is_invalid = 1
        , invalid_on = ?
        , invalid_by_uid = ?
        , invalid_reason = ?
      WHERE id = ?
      ;
    `);

    await dbRawWrite(this.app, [test_attempt_id], `
      UPDATE test_attempt_sub_sessions tass
      SET uid = 0 - abs(tass.uid)
        , test_session_id = 0 - abs(tass.test_session_id)
        , is_invalid = 1
      WHERE test_attempt_id = ?
      ;
    `);

    //reset Redis data
    const redis: Redis = this.app.get('redis');
    if(redis){
      await removeTestAttempt(this.app, test_attempt_id)        
    }
  }
}
