import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Redis } from 'ioredis';
import { KTestSession, RedisKeyPrefixes } from '../../../../redis/redis';
// import { IResponseBufferStatus } from '../../cron/process-response-buffer/process-response-buffer.class';

interface Data {
  name: string;
  description?: string;
  value: number | string;
}

interface ServiceOptions {}

export class RedisStats implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const redis: Redis = this.app.get('redis');
    const dbSize = await redis.dbsize();
    const pendingSubmWritesLen = await redis.llen(RedisKeyPrefixes.PendingSubmWrites);
    const processingSubmWritesLen = await redis.llen(RedisKeyPrefixes.ProcessingSubmWrites);
    const pendingSAPBufferLen = await redis.llen(RedisKeyPrefixes.PendingSAPBuffer);
    const activeSessions = await redis.keys(KTestSession('*'));

    // const responseBufferStatus = <any>(await redis.hgetall(RedisKeyPrefixes.ResponseBufferStatus)) as IResponseBufferStatus;
    // const isFaulted = responseBufferStatus?.is_faulted === '1';

    // parse last_success and check if it was too long ago
    // const isRunningOk = responseBufferStatus?.last_success && Date.now() - new Date(responseBufferStatus.last_success).getTime() < 1000 * 60 * 5;
    // const bufferHealthy = !isFaulted && isRunningOk

    return [
      {
        name: 'DB Size',
        description: 'Number of all keys in Redis',
        value: dbSize
      },
      {
        name: 'Pending Submission Buffer Writes',
        description: 'Current size of the student responses in the Redis submissions buffer, not yet saved to the database',
        value: pendingSubmWritesLen
      },
      {
        name: 'Processing Submission Buffer Writes',
        description: 'Current size of the student responses being processed in the Redis submissions buffer',
        value: processingSubmWritesLen
      },
      {
        name: 'Pending SAP Buffer Length',
        description: 'Current size of the Student Attempt Purchase buffer in Redis',
        value: pendingSAPBufferLen
      },
      {
        name: 'Active Sessions (In Redis)',
        description: 'Size of testSessions:* keys in Redis, should indicate how many total test sessions were initialized and active in redis at the time of this query',
        value: activeSessions.length
      },
      // {
      //   name: 'Is Response Buffer Healthy',
      //   description: 'Indicates if the response buffer is healthy, if not, it may be a sign of a problem with the response buffer',
      //   value: bufferHealthy ? 'Yes' : 'NO'
      // },
      // {
      //   name: 'Last Suceessful Response Buffer Run',
      //   description: 'The last time the response buffer was successfully processed',
      //   value: responseBufferStatus?.last_success || 'N/A'
      // },
      // {
      //   name: 'Last Attempted Response Buffer Run',
      //   description: 'The last time the response buffer was attempted to be processed',
      //   value: responseBufferStatus?.last_run || 'N/A'
      // }
    ]
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
