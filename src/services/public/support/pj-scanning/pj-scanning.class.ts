import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import {BULK_SCAN_STATUS} from './../../educator/resp-sheet-upload/upload-response-sheets/upload-response-sheets.class'
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { dbRawReadReporting, dbRawRead, dbEscapeString, dbRawReadSingle } from '../../../../util/db-raw';
import qs from 'qs';
import { textFilterColumns, dateTimeFiltercolumns, DateFilters, TextFilters } from './types'
import { Knex } from 'knex';

interface Data {}

interface ServiceOptions {}

export class PjScanning implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /** Return to the support dashboard any bulk uploads currently in running or in waitlist queue, and the max allowed to run at once number */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    const maxAllowedRunning = await getSysConstNumeric(this.app, 'BULK_CLASS_SCAN_UPLOAD_MAX_RUNNING', true)

    const SUPPORT_RUNNING_QUEUED_UPLOADS = `
      select
      bulk.id
      , bulk.test_window_id
      , bulk.created_on
      , bulk.created_by_uid
      , bulk.started_on
      , bulk.completed_on
      , bulk.bulk_file_path
      , bulk.status
      , bulk.client_queue_last_check_on
      , bulk.is_override_flagged_only
      , CONCAT(u.first_name, ' ', u.last_name, ' (', u.contact_email, ')') as uploader
      , sc.name as class_name
      , schl.name as schl_name
      from class_bulk_scan_upload as bulk
      join test_sessions as ts
        on ts.id = bulk.test_session_id
      join school_class_test_sessions as scts
        on ts.id = scts.test_session_id
      join school_classes as sc
        on sc.id = scts.school_class_id
      join schools as schl
        on sc.schl_group_id = schl.group_id
      left join users as u
        on u.id = bulk.created_by_uid
      where bulk.status in (?)
      order by bulk.id desc
    `

    const bulkUploadRecords = await dbRawReadReporting(this.app, [[BULK_SCAN_STATUS.IN_PROGRESS, BULK_SCAN_STATUS.IN_QUEUE]], SUPPORT_RUNNING_QUEUED_UPLOADS)

    // Process data to be returned
    const expireSeconds = 24 * 60 * 60 // 1 day
    bulkUploadRecords.forEach((r:any) => {
      r.bulk_file_url = generateS3DownloadUrl(r.bulk_file_path, expireSeconds)
    })

    return {
      maxAllowedRunning,
      bulkUploadRecords
    };
  }

  /** Get the failed upload records and their total count */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {offset, limit, clientFilters, clientSort} = params.query;
    const is_exclude_qa = !!(+params.query.is_exclude_qa)

    // Convert the filters and sort back into objects
    const filters = clientFilters ? qs.parse(clientFilters): undefined
    const sort = clientSort ? qs.parse(clientSort) : undefined

    const failedUploadsQuery = await this.getFailedUploadsQuery({offset, limit, filters, sort, is_exclude_qa})
    const uploads = await dbRawReadReporting(this.app, {statusError: BULK_SCAN_STATUS.ERROR}, failedUploadsQuery)
    // Process data to be returned
    const expireSeconds = 24 * 60 * 60 // 1 day
    uploads.forEach((r:any) => {
      r.bulk_file_url = generateS3DownloadUrl(r.bulk_file_path, expireSeconds)
    })
    
    // Get the total count without offset and limit
    const failedUploadsQueryTotal = await this.getFailedUploadsQuery({filters, sort, is_exclude_qa})
    const countRes = await dbRawReadReporting(this.app, {statusError: BULK_SCAN_STATUS.ERROR}, `SELECT COUNT(*) as count FROM (${failedUploadsQueryTotal}) query`);
    const count = countRes[0].count

    return {uploads, count}

  }

  /** Return query string to get falied uploads, modified for user inputs */
  async getFailedUploadsQuery(config: {offset?: number, limit?: number, filters: any, sort: any, is_exclude_qa: boolean}): Promise<string> {
    const {offset, limit, filters, sort, is_exclude_qa} = config

    /** Return string to apply client sort order to the query */
    const getClientSort = (sort: {colId: string, sort: string}): string => {
      const defaultSort = 'order by id desc'
      if (sort.sort == "asc" || sort.sort== "desc"){
        if (textFilterColumns.has(sort.colId) || dateTimeFiltercolumns.has(sort.colId)){
          return `order by ${sort.colId} ${sort.sort}`
        }
      }
      return defaultSort;
    }

    /** Return string to apply client filters to the query */
    const getClientFilterConditions = async (filters:{[key:string]:any}): Promise<string> => {
      const conditions = []
      for (const [prop, filter] of Object.entries(filters)) {

        if (dateTimeFiltercolumns.has(prop)){
          switch (filter.type) {
            // Has to align with the timezone date of the client, e.g. 2023-06-16 chosen in EST is '2023-06-16 04:00:00' to '2023-06-17 03:59:59' in UTC
            case DateFilters.equals:
              conditions.push(`${prop} >= ${await dbEscapeString(filter.dateFrom)} AND ${prop} < DATE_ADD(${await dbEscapeString(filter.dateFrom)}, INTERVAL 1 DAY)`)
              break;
            case DateFilters.notEqual:
              conditions.push(`${prop} < ${await dbEscapeString(filter.dateFrom)} OR ${prop} >= DATE_ADD(${await dbEscapeString(filter.dateFrom)}, INTERVAL 1 DAY)`)
              break;
            case DateFilters.greaterThan:
              conditions.push(`${prop} >= DATE_ADD(${await dbEscapeString(filter.dateFrom)}, INTERVAL 1 DAY)`)
              break;
            case DateFilters.lessThan:
              conditions.push(`${prop} < ${await dbEscapeString(filter.dateFrom)}`)
              break;
            case DateFilters.inRange:
              conditions.push(`${prop} >= ${await dbEscapeString(filter.dateFrom)} AND ${prop} < DATE_ADD(${await dbEscapeString(filter.dateTo)}, INTERVAL 1 DAY)`)
              break;
          }
        }
        else if (textFilterColumns.has(prop)){
          switch (filter.type) {
            case TextFilters.equals:
              conditions.push(`${prop} = ${await dbEscapeString(filter.filter)}`)
              break;
            case TextFilters.notEqual:
              conditions.push(`${prop} != ${await dbEscapeString(filter.filter)}`)
              break;
            case TextFilters.contains:
              conditions.push(`${prop} LIKE ${await dbEscapeString("%" + filter.filter + "%")}`)
              break;
            case TextFilters.notContains:
              conditions.push(`${prop} NOT LIKE ${await dbEscapeString("%" + filter.filter + "%")}`)
              break;
            case TextFilters.startsWith:
              conditions.push(`${prop} LIKE ${await dbEscapeString(filter.filter + "%")}`)
              break;
            case TextFilters.endsWith:
              conditions.push(`${prop} LIKE ${await dbEscapeString("%" + filter.filter)}`)
              break;
          }

        }
      }

      if (conditions.length){
        return 'where ' + conditions.join(" and ")
      } else {
        return ''
      }
    }

    // < 1.5 sec, limit of 20 is passed from UI
    const SUPPORT_FAILED_BULK_UPLOADS = `
    select * from (
      select
      bulk.id
      , bulk.test_window_id
      , bulk.created_on
      , bulk.created_by_uid
      , bulk.started_on
      , bulk.completed_on
      , bulk.bulk_file_path
      , bulk.is_override_flagged_only
      , CONCAT(u.first_name, ' ', u.last_name, ' (', u.contact_email, ')') as uploader
      , sc.name as class_name
      , schl.name as schl_name
      from class_bulk_scan_upload as bulk
      join test_sessions as ts
        on ts.id = bulk.test_session_id
      join school_class_test_sessions as scts
        on ts.id = scts.test_session_id
      join school_classes as sc
        on sc.id = scts.school_class_id
      join schools as schl
        on sc.schl_group_id = schl.group_id
      join school_districts as sd
        on sd.group_id = schl.schl_dist_group_id
      left join users as u
        on u.id = bulk.created_by_uid
      where bulk.status = :statusError
      ${is_exclude_qa? 'and sd.is_sample = 0 and schl.is_sandbox = 0' : ''}
      order by bulk.created_on desc
      ${limit ? `limit ${+limit}` : ''}
      ${offset ? `offset ${+offset}` : ''}
    ) scans
      ${ filters ? await getClientFilterConditions(filters): ''}
      ${ sort ? getClientSort(sort) : 'order by id desc'}
    `
    
    return SUPPORT_FAILED_BULK_UPLOADS;
  }


  /** Triggered by support to attempt clearing the waitlist queue - if has remaining capacity to run more bulk processes, take at most that many from the queue and run them */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const maxAllowedRunning = await getSysConstNumeric(this.app, 'BULK_CLASS_SCAN_UPLOAD_MAX_RUNNING', true)

    const db = this.app.get('knexClientRead')
    const { count: numBulkRunning } =  await db('class_bulk_scan_upload as bulk')
    .count('* as count')
    .where('bulk.status', BULK_SCAN_STATUS.IN_PROGRESS)
    .first()

    const numNewToRun = (+maxAllowedRunning) - (+numBulkRunning)
    const newRunningIds: number[] = []
    if (numNewToRun > 0) {
      // Choose earliest created records from the queue to run, get necessary info
      const SUPPORT_CLEAR_QUEUE_TARGETS = `
        select 
        bulk.id as bulk_id
        , bulk.bulk_file_path
        , bulk.is_override_flagged_only
        , bulk.created_by_uid
        , bulk.test_session_id
        , sc.group_id as schl_class_group_id
        , sc.id as schl_class_id
        , schl.is_sasn_login
        from 
        class_bulk_scan_upload as bulk
        join test_sessions as ts
          on ts.id = bulk.test_session_id
        join school_class_test_sessions as scts
          on ts.id = scts.test_session_id
        join school_classes sc
          on sc.id = scts.school_class_id
        join schools as schl
          on sc.schl_group_id = schl.group_id
        where bulk.status = :statusQueue
        order by bulk.created_on desc
        limit :numNewToRun;
      `
      const newRunningTargets = await dbRawRead(this.app, {numNewToRun, statusQueue: BULK_SCAN_STATUS.IN_QUEUE}, SUPPORT_CLEAR_QUEUE_TARGETS)

      // Launch all new processes to run
      newRunningTargets.forEach((target: any) => {
        const {bulk_id, is_override_flagged_only, bulk_file_path, test_session_id, schl_class_group_id, schl_class_id, is_sasn_login, created_by_uid} = target;
        newRunningIds.push(+bulk_id);
        (async () => {
          this.app.service('public/educator/resp-sheet-upload/upload-response-sheets').runBulkUpload(params, bulk_file_path, bulk_id, test_session_id, schl_class_group_id, schl_class_id, !!is_override_flagged_only, +is_sasn_login, created_by_uid, true)
        })()
      })
    }

    return {
      newRunningIds
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (bulkUploadRecordId: NullableId, data: Data, params?: Params): Promise<Data> {

    const targetRecord = await dbRawReadSingle(this.app, { bulkUploadRecordId }, `
    select bulk.id
    , bulk.created_by_uid
    , bulk.status
    , scts.school_class_id
    from class_bulk_scan_upload bulk 
    join school_class_test_sessions scts
    on scts.test_session_id = bulk.test_session_id
    where bulk.id = :bulkUploadRecordId
    `)
    
    if (!targetRecord || targetRecord.status !== BULK_SCAN_STATUS.IN_PROGRESS){
      throw new Errors.BadRequest();
    }
    
    const knex:Knex = this.app.get('knexClientWrite')
    // Change status to error
    await knex('class_bulk_scan_upload').where('id', targetRecord.id)
    .update({
      status: BULK_SCAN_STATUS.ERROR
    });
    // Send notification of error to user
    this.app.service('public/educator/resp-sheet-upload/upload-response-sheets')
    .sendScanNotif(targetRecord.created_by_uid, targetRecord.school_class_id, targetRecord.id, true);

    return {}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
