// Initializes the `public/testing/axios/forced-errors` service on path `/public/testing/axios/forced-errors`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ForcedErrors } from './forced-errors.class';
import hooks from './forced-errors.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/testing/axios/forced-errors': ForcedErrors & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/testing/axios/forced-errors', new ForcedErrors(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/testing/axios/forced-errors');

  service.hooks(hooks);
}
