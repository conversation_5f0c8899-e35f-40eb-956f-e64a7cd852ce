import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import axios from 'axios';

interface Data {success:boolean}

interface ServiceOptions {}

enum ErrorMode {
  THROW_ERROR = 1,
  TIMEOUT_ERROR = 2,
  UNREACHABLE_ENDPOINT_ERROR = 3,
}
export class ForcedErrors implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  url: string;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.url = this.getAppUrl()
  }
  
  private getAppUrl(){
    const host = this.app.get('host');
    const port = this.app.get('port');
    const protocol = this.app.get('protocol') || 'http';
    return `${protocol}://${host}:${port}`;
  }

  /**
   * 
   * @param params query.id indicates the type of mode it'll use
   * @returns 
   */
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    const mode = +params?.query?.id;
    switch (mode) {
      case ErrorMode.THROW_ERROR:
        throw new Error("Test Error");
  
      case ErrorMode.TIMEOUT_ERROR:
        await new Promise(resolve => setTimeout(resolve, 600000));
        return [{ success: true }];
  
      case ErrorMode.UNREACHABLE_ENDPOINT_ERROR:
        const response = await axios.get(`${this.url}/public/testing/axios/invalid-endpoint`);
        return response.data;
  
      default:
        return [{ success: true }];
    }
  }
  
  /**
   * 
   * @param id sets the type of failure we're tryig to replicate
   * @returns success: true or error message
   */
  async get(id: Id): Promise<Data> {
    const response = await axios.get(`${this.url}/public/testing/axios/forced-errors`, {
      params: { id }
    });
  
    return response.data;
  }

  /**
   * This function shouldn't be used but being kept in case we want to add different types of errors 
   * @param data data to create 
   * @returns 
   */
  async create(data: Data): Promise<Data> {
    const response = await axios.post('https://jsonplaceholder.typicode.com/posts', {data});
    return response.data;
  }

  /**
   * This function shouldn't be used but being kept in case we want to add different types of errors 
   * @param id id to change
   * @param data data to change 
   * @returns 
   */
  async update(id: NullableId, data: Data): Promise<Data> {
    // This will trigger a 404 error if the id doesn't exist
    const response = await axios.put(`https://jsonplaceholder.typicode.com/posts/${id}`, data);
    return response.data;
  }
  /**
   * This function shouldn't be used but being kept in case we want to add different types of errors 
   * @param id 
   * @param data 
   * @returns 
   */
  async patch(id: NullableId, data: Data): Promise<Data> {
    const response = await axios.patch(`https://jsonplaceholder.typicode.com/posts/${id}`, data);
    return response.data;
  }
  /**
   * This function shouldn't be used but being kept in case we want to add different types of errors 
   * @param id 
   * @returns 
   */
  async remove(id: NullableId): Promise<any> {
    await axios.delete(`https://jsonplaceholder.typicode.com/posts/${id}`);
    return { id };
  }
}
