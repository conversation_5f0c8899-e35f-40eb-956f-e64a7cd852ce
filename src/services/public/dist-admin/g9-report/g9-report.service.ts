// Initializes the `public/dist-admin/g9-report` service on path `/public/dist-admin/g9-report`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { G9Report } from './g9-report.class';
import hooks from './g9-report.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/dist-admin/g9-report': G9Report & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/dist-admin/g9-report', new G9Report(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/dist-admin/g9-report');

  service.hooks(hooks);
}
