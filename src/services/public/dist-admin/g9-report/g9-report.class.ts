import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { getSysConstString } from '../../../../util/sys-const-string';
import { TW_EXCEPTION_STUDENT_STATUS } from '../../educator/g9-reports/g9-reports.class';
import { dbRawReadReporting, dbRawReadSingleReporting, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import ExcelJS, { Workbook } from 'exceljs';
import { Readable, Stream, PassThrough } from 'stream';
import { convertObjectsToArrayOfArraysWithHeader } from '../../../../util/param-sanitization';
import logger from '../../../../logger';
import { generateS3DownloadUrl, storeInS3 } from '../../../upload/upload.listener';
import { dbDateNow } from '../../../../util/db-dates';
import { writeToBuffer, writeToStream } from '@fast-csv/format';
import moment from 'moment';
import { maxReportGenerateMinutes } from '../../school-admin/pj-reports/pj-reports.class';
import { TestWindowType } from '../../alt-version-ctrl/alt-version-requests/types';
import { Knex } from 'knex';

interface Data {}

interface ServiceOptions {}

enum ROLE_LEVEL {
  BOARD = 'BOARD',
  SCHOOL = 'SCHOOL'
}

interface IG9CsvColumn {
  col: string;
  dbHeader?: string;
  modifyNull?: boolean;
  nullValue?: any;
  modifyDate?: boolean;
  dateFormat?: string;
}

const G9ResultColumns: readonly IG9CsvColumn[] = [
  {col: "SchoolMident"},
  {col: "SchoolName"},
  {col: "StudentOEN", modifyNull: true, nullValue: -1},
  {col: "StudentSASN", dbHeader: "SASN", modifyNull: true, nullValue: -1},
  {col: "FirstName"},
  {col: "LastName"},
  {col: "ClassCode"},
  {col: "SessionStartTime", modifyNull: true, nullValue: -9, modifyDate: true, dateFormat: 'YYYY-MM-DD HH:mm:ss Z'},
  {col: "TestParticipationStatus"},
  {col: "OverallOutcomeLevel"},
  {col: "OverallDotScore"}
]

export class G9Report implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  KW_TIMESTAMP:string = 'timestamp';
  CSV_FILE_PATH:string = 'data_release/eqao_g9_report';
  CSV_FILE_TYPES: Record<ROLE_LEVEL, string> = {
    [ROLE_LEVEL.SCHOOL]: 'schl_mident',
    [ROLE_LEVEL.BOARD]: 'board_mident'
  };

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // Board admin find BulkSchlDistReportsGenerationRecord
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const { schl_dist_group_id, testWindowId, isSecreteUser} = (<any>params).query;

    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS') // Need to add REPORT_CSV_BYPASS or use the same password
    const isValidSecreteUser = isSecreteUser == secreteUserRecord

    const test_window = await this.app.service('db/read/test-windows').get(testWindowId)

    if (test_window.show_report_to_Board || isValidSecreteUser) {
      const bulk_schl_dist_report_generation_records = await this.getBulkSchlDistReportsGenerationRecords(schl_dist_group_id, testWindowId)
  
      return bulk_schl_dist_report_generation_records
    }
    
    return []
  }

  // Board admin download CSV report
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const { schl_dist_group_id, testWindowId, isSecreteUser} = (<any>params).query;
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS') // Need to add REPORT_CSV_BYPASS
    const isValidSecreteUser = isSecreteUser == secreteUserRecord

    const test_window = await this.app.service('db/read/test-windows').get(testWindowId)
    
    //throw error if test window can't be found
    if (!test_window) {
      throw new Errors.BadRequest('TEST_WINDOW_NOT_FOUND')
    }
    
    //throw error if test window reg_lock_on has not passed yet
    if (!this.app.service('public/test-ctrl/test-window/auto-approve-stu-data').isTestWindowRegLock(test_window)) {
      throw new Errors.BadRequest('ERR_STUDENT_INFO_NOT_LOCKED')
    }

    if (!test_window.show_report_to_Board && !isValidSecreteUser) {
      throw new Errors.BadRequest('NO_REPORT_TO_SHOW')
    }

    const bulk_schl_dist_report_generation_records = await this.getBulkSchlDistReportsGenerationRecords(schl_dist_group_id, testWindowId)

    if (!bulk_schl_dist_report_generation_records.length) {
      throw new Errors.BadRequest('NO_REPORT_TO_SHOW')
    }

		if (!bulk_schl_dist_report_generation_records[0].s3_report_link) {
			throw new Errors.BadRequest('REPORT_GENERATING')
		}

    return { message: 'REPORT_GENERATED', reportURL: generateS3DownloadUrl(bulk_schl_dist_report_generation_records[0].s3_report_link) }
  }

  // Regenerate Report From Test controller
  async create (data: { board_midents: number[], test_window_id: number }, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const created_by_uid = await currentUid(this.app, params);

    const { isSecreteUser } = (<any>params).query;
    const { board_midents, test_window_id } = data
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord

    const test_window = await this.app.service('db/read/test-windows').get(test_window_id)
    
    //throw error if test window can't be found
    if (!test_window) {
      throw new Errors.BadRequest('TEST_WINDOW_NOT_FOUND')
    }
    
    //throw error if test window reg_lock_on is not passed yet
    if (!this.app.service('public/test-ctrl/test-window/auto-approve-stu-data').isTestWindowRegLock(test_window)) {
      throw new Errors.BadRequest('ERR_STUDENT_INFO_NOT_LOCKED')
    }
    
    let generationResults = []
    
    for (const brd_mident of board_midents) {
      const result = {
        brd_mident,
        message: ''
      }
      try {
      
        // Get and Revoke previous records
        const schools = await this.getSchoolsByTwSchlDist(test_window_id, null, brd_mident)
        
        if (!schools.length) {
          throw new Errors.BadRequest('NO_SCHOOL_FOR_REPORT')
        }
        
        const schl_dist_group_id = schools[0].sd_group_id
        
        const prevBSDRGRecords = await this.getBulkSchlDistReportsGenerationRecords([schl_dist_group_id], test_window_id)

        // Prevent csv report generation if ongoing bulk process
        await this.app.service('public/test-ctrl/test-window/bulk-generate-report-csv').checkOngoingBulkCSVGenerationProcess(test_window_id)
    
        // Prevent csv report generation if report is generating 
        await this.checkPreviousGeneratingReportStatus(schl_dist_group_id, test_window_id, created_by_uid)
    
        // Run in the background
        this.getG9ReportDataAndGenerateCSV(schools, created_by_uid, test_window, schl_dist_group_id, isValidSecreteUser)
          .then(res => {
            logger.info({
              created_by_uid: created_by_uid,
              slug: 'TEST_CTRL_REPORT_CSV_REGENERATION', 
              csvReportGenerationRecords: res
            })

            // Revoke old records
            if(prevBSDRGRecords.length) {
              const bsdrg_ids = prevBSDRGRecords.map(bsdrg => bsdrg.id)
              this.patchBulkSchlDistReports(created_by_uid, bsdrg_ids, true)
            }
          })

        result.message = 'REPORT_REGENERATING'

      } catch (err:any) {
        result.message = `ERROR - ` + err.message
      }

      generationResults.push(result)
    }

    return generationResults
  }

  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // Revoke or Reset bulk_schl_dist_report_generations records
  // Endpoint only, no UI entry at the moment
  async patch (id: NullableId, data: {test_window_id: number, schl_dist_group_ids: number[], is_revoke:boolean, record_id:number}, params?: Params): Promise<Data> {
    if(!data || !params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const created_by_uid = await currentUid(this.app, params);

    const {test_window_id, schl_dist_group_ids, is_revoke, record_id} = data
    // record_id is bulk_schl_dist_report_generations id

    if (!record_id && (!schl_dist_group_ids.length || !test_window_id)) {
      throw new Errors.BadRequest('MISSING_DATA');
    }

    let bsdrg_ids
    if (!record_id) { 
      const bulkSchlDistReportsGenerationRecords = await this.getBulkSchlDistReportsGenerationRecords(schl_dist_group_ids, test_window_id)
      bsdrg_ids = bulkSchlDistReportsGenerationRecords.map(bsdrg => bsdrg.id)
    } else {
      const bsdrgRecord = await this.app.service('db/read/bulk-schl-dist-report-generations').get(record_id)
      bsdrg_ids = [bsdrgRecord.id]
    }
    
    if (!bsdrg_ids.length) {
      throw new Errors.BadRequest('NO_MATCHING_RECORDS');
    }

    await this.patchBulkSchlDistReports(created_by_uid, bsdrg_ids, is_revoke)

    logger.info({
      created_by_uid: created_by_uid,
      slug: 'PATCH_BSDRG_RECORDS',
      data: JSON.stringify({
        bsdrg_ids,
        is_revoke
      })
    });

    return { revoked_bsdrg_ids: bsdrg_ids }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  /**
   * Get G9 Report student data
   * And generate CSV report for schools and board
   * @param schools 
   * @param created_by_uid 
   * @param testWindowId 
   * @param schl_dist_group_id 
   * @param isValidSecreteUser 
   * @returns 
   */
  async getG9ReportDataAndGenerateCSV(schools:any[], created_by_uid:number, test_window:any, schl_dist_group_id:number, isValidSecreteUser = false, bcrg_id?:number) {
    const csvReportGenerationRecords: {
      overall_csv_school_group_ids: any[],
      fail_csv_school_group_ids: { schl_group_id: number, error: any }[]
    } = {
      overall_csv_school_group_ids: [],
      fail_csv_school_group_ids: []
    };

    try {
      const testWindowId = test_window.id
      const schoolsGrpIds = schools.map(schl => schl.schl_group_id)
      const timestamp = Date.now()
      const boardMident = schools[0]?.board_mident
      const lang = schools[0]?.lang
  
      const newBoardCSVReportRecord = await this.app
        .service('db/write/bulk-schl-dist-report-generations')
        .create({
          created_by_uid,
          created_on: dbDateNow(this.app),
          test_window_id: testWindowId,
          schl_dist_group_id: schl_dist_group_id,
          bulk_csv_report_generation_id: bcrg_id ? bcrg_id : null
        });

      csvReportGenerationRecords.overall_csv_school_group_ids = schoolsGrpIds

      const boardFilePath = await this.getCSVFilePath(ROLE_LEVEL.BOARD, test_window, schl_dist_group_id, timestamp, boardMident, lang)
      const boardCsvWriteStream = new PassThrough();
      boardCsvWriteStream.write('\ufeff', 'utf8');
  
      for (const [index, school] of schools.entries()) {
        const isFirstSchool = index === 0;
        const schlGrpId = school.schl_group_id
        const schlMident = school.schl_mident
        try {

          const fullyParticipateStudentData = await this.getG9StudentReportsData(ROLE_LEVEL.SCHOOL, testWindowId, schlGrpId, isValidSecreteUser)
          const getG9NFPStudentReportsData = await this.getG9NFPStudentReportsData(ROLE_LEVEL.SCHOOL, testWindowId, schlGrpId, isValidSecreteUser)
          const fullReport = fullyParticipateStudentData.concat(getG9NFPStudentReportsData)
          this.app.service('public/dist-admin/osslt-report').retrieveReportDataFromSnapshots(fullReport)
          const schoolCsvReportData = await this.renderReportDataValues(fullReport)
    
          schoolCsvReportData.sort((a:any,b:any) => +a.StudentOEN - +b.StudentOEN);
    
          // Create school CSV report even if school has no student for report
          const schoolFilePath = await this.getCSVFilePath(ROLE_LEVEL.SCHOOL, test_window, schl_dist_group_id, timestamp, schlMident, lang)
          const csvHeaders = [G9ResultColumns.map(colConfig => colConfig.col)]
          const schoolCsvData = await this.createCSVfile(
            csvHeaders,
            schoolCsvReportData,
            schoolFilePath
          );

          // Write headers for first school (always)
          if (isFirstSchool) {
            let headerBuffer = await writeToBuffer(csvHeaders);
            if (headerBuffer[headerBuffer.length - 1] !== 0x0A) {
              headerBuffer = Buffer.concat([headerBuffer, Buffer.from('\n')]);
            }
            boardCsvWriteStream.write(headerBuffer);
          }

          // Write school data to board CSV (if any)
          if (schoolCsvData.length) {
            let dataBuffer = await writeToBuffer(schoolCsvData);
            if (dataBuffer[dataBuffer.length - 1] !== 0x0A) {
              dataBuffer = Buffer.concat([dataBuffer, Buffer.from('\n')]);
            }
            boardCsvWriteStream.write(dataBuffer);
          }

        } catch (e:any) {
          csvReportGenerationRecords.fail_csv_school_group_ids.push({
            schl_group_id: schlGrpId,
            error: e.stack
          })
        }
      }
  
      boardCsvWriteStream.end();
      await storeInS3(boardFilePath, boardCsvWriteStream);
  
      await this.app.service('db/write/bulk-schl-dist-report-generations')
        .patch(newBoardCSVReportRecord.id, {
          s3_report_link: boardFilePath,
          report_generate_on: dbDateNow(this.app)
        })
  
      return csvReportGenerationRecords
    } catch(e:any) {
      // This handles unexpected top-level errors
      throw new Error(`Top-level G9 CSV error for sd group id ${schl_dist_group_id}: ${e.stack}`);
    }
  }

  
  /**
   * Check previous generating report status.
   * If generating report record found and is recent, throw error.
   * @param schl_dist_group_id 
   * @param testWindowId 
   * @param revoked_by_uid 
   */
  async checkPreviousGeneratingReportStatus(schl_dist_group_id:number, testWindowId:number, revoked_by_uid:number){
    const generatingSchlDistReportRecords = await this.getOngoingReportsGeneratingRecords(schl_dist_group_id, testWindowId)

    // Revoke long generate_reports
    const maxReportGenerateMS = maxReportGenerateMinutes * 60000;
    const long_generate_reports = generatingSchlDistReportRecords.filter( bsdrg => new Date().getTime() - (new Date(bsdrg.created_on)).getTime() > maxReportGenerateMS)
    const long_generate_report_ids = long_generate_reports.map(lgr => {return lgr.id})
    await this.patchBulkSchlDistReports(revoked_by_uid, long_generate_report_ids, true)

    // Throw error if there currently reports generating
    if (generatingSchlDistReportRecords.length - long_generate_reports.length > 0) {
      throw new Errors.BadRequest("REPORT_GENERATING");
    }
  }

  /** 
   * Get ongoing school distric CSV generation records by test window
   */
  async getOngoingReportsGeneratingRecords(schl_dist_group_id: number, test_window_id:number){
    const bulk_sd_report_generatings = (await dbRawReadReporting(this.app, { schl_dist_group_id, test_window_id }, `
      -- 0.051s 
      select bsdrg.id
           , bsdrg.created_on
        from bulk_schl_dist_report_generations bsdrg
       where bsdrg.schl_dist_group_id = :schl_dist_group_id
         and bsdrg.test_window_id = :test_window_id
         and bsdrg.s3_report_link is null
         and bsdrg.is_revoked != 1
    ;`))
    return bulk_sd_report_generatings
  }

  /**
   * Get valid bulk_schl_dist_report_generations records by test window,
   * In descending order
   * @param schl_dist_group_ids 
   * @param test_window_id 
   * @returns 
   */
  async getBulkSchlDistReportsGenerationRecords(schl_dist_group_ids:number[], test_window_id:number) {
    const bulk_student_results_reports = await dbRawReadReporting(this.app, {schl_dist_group_ids, test_window_id}, `
      -- 0.04s
        select bsdrg.id
            , bsdrg.schl_dist_group_id
            , bsdrg.test_window_id
            , bsdrg.created_on
            , bsdrg.s3_report_link
            , bsdrg.report_generate_on
            , bsdrg.is_revoked
            , bsdrg.bulk_csv_report_generation_id
         from bulk_schl_dist_report_generations bsdrg
        where bsdrg.schl_dist_group_id IN (:schl_dist_group_ids)
          and bsdrg.test_window_id = :test_window_id
          and bsdrg.is_revoked != 1
     order by bsdrg.report_generate_on DESC
    ;`)
    return bulk_student_results_reports
  }

  /**
   * Revoke bulk_schl_dist_report_generations record
   */
  async patchBulkSchlDistReports(updated_by_uid:number, bsdrg_ids:number[], is_revoke:boolean){
    if(bsdrg_ids.length > 0){
      let updateData:any = {
        is_revoked: is_revoke,
        revoked_by_uid: is_revoke ? updated_by_uid : null,
        revoked_on: is_revoke ? dbDateNow(this.app) : null,
      }

      const knex: Knex = this.app.get('knexClientWrite');
      await knex('bulk_schl_dist_report_generations')
        .whereIn('id', bsdrg_ids)
        .update(updateData);

    }
  }

  /**
   * Get school data by board
   * @param test_window_id 
   * @param schl_dist_group_id 
   * @param schl_dist_foreign_id 
   * @returns 
   */
  async getSchoolsByTwSchlDist(test_window_id:number, schl_dist_group_id:number|null, schl_dist_foreign_id:number|null) {
    const EQAO_G9M = TestWindowType.EQAO_G9M
    const schools = await dbRawReadReporting(this.app, {test_window_id, schl_dist_group_id, schl_dist_foreign_id, EQAO_G9M}, `
      -- 1 sec
      select 
          sd.foreign_id as board_mident
        , sd.group_id as sd_group_id  
        , sd.brd_lang as lang
        , s.group_id as schl_group_id
        , s.foreign_id as schl_mident
      from school_districts sd 
      join schools s on s.schl_dist_group_id = sd.group_id
      join school_classes sc on sc.schl_group_id = s.group_id
      join school_semesters ss on ss.id = sc.semester_id
      join test_windows tw on tw.id = ss.test_window_id
        and tw.id = (:test_window_id)
        and tw.type_slug = (:EQAO_G9M)
      join user_roles ur on ur.group_id = sc.group_id 
        and ur.role_type = "schl_student" 
      where 
      ${ schl_dist_group_id ? 'sd.group_id = :schl_dist_group_id' : '' }
      ${ schl_dist_foreign_id ? 'sd.foreign_id = :schl_dist_foreign_id' : '' } 
      group by s.group_id
      order by sd.foreign_id asc
    ;`)
    return schools
  }

  /**
   * Get fully participating student report data 
   * regardless of their ur.is_revoked status
   * @param query_level 
   * @param test_window_id 
   * @param group_id 
   * @returns 
   */
  async getG9StudentReportsData(query_level:string, test_window_id:number, group_id:number, bypassAdminSignOff = false) {
    const STU_EX_PENDED = TW_EXCEPTION_STUDENT_STATUS.PENDED
    const STU_EX_WITHHOLD = TW_EXCEPTION_STUDENT_STATUS.WITHHOLD
    const STU_EX_EXCLUDE_FROM_CSV = TW_EXCEPTION_STUDENT_STATUS.EXCLUDE_FROM_CSV
    const fpStudentReportData = await dbRawReadReporting(this.app, {test_window_id, group_id, STU_EX_PENDED, STU_EX_WITHHOLD, STU_EX_EXCLUDE_FROM_CSV}, `
          -- 2.7s to get 233 students schl_group_id = 1649 in tw 151
          select /*+ MAX_EXECUTION_TIME(1440000000)*/
                  ur.uid
                , u.first_name as FirstName
                , u.last_name as LastName
                , sr.student_oen as StudentOEN
                , um2.value as SASN
                , schl.name as SchoolName
                , schl.lang as lang
                , schl.foreign_id as SchoolMident
                , sc.name as ClassCode
                , ta.started_on as SessionStartTime
                , sr.overall
                , sr.dot_score
                , 1 as TestParticipationStatus
                , IF (twes_withheld.id, 1, 0) AS is_withheld
                , IF (twes_pended.id, 1, 0) AS is_pended
                , snapshots.student_info_json AS snapshot_data
            from schools schl
            join school_districts sd on sd.group_id = schl.schl_dist_group_id 
            join school_classes sc on sc.schl_group_id = schl.group_id 
             and sc.group_type = 'EQAO_G9'
       left join school_classes_guest scg on scg.guest_sc_group_id = sc.group_id 
			       and scg.is_revoked = 0
       left join school_classes sc_invig on sc_invig.group_id = scg.invig_sc_group_id
            join school_semesters ss on ss.id = sc.semester_id
			      join test_windows tw on tw.id = ss.test_window_id
            join user_roles ur on ur.group_id = sc.group_id 
             and ur.role_type = 'schl_student'
       left join user_metas um2 on um2.uid = ur.uid 
             and um2.key_namespace = 'eqao_sdc' 
             and um2.key = 'SASN'
            join users u on u.id = ur.uid
            join test_attempts ta on ta.uid = ur.uid 
             and ta.twtdar_order = 0
						 and ta.is_invalid = 0
            join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
             and twtdar.test_window_id = ss.test_window_id
             and twtdar.generate_report = 1
            join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
             and scts.slug = "G9_OPERATIONAL"
             and scts.school_class_id in (sc.id, sc_invig.id) -- To get the class/invig class where student wrote assessment in
            join student_reports sr on sr.uid = ur.uid 
             and sr.attempt_id = ta.id and sr.is_revoked = 0 
             and sr.test_window_id = ss.test_window_id 
            join auto_student_report_generations asrg on asrg.test_attempt_id = sr.attempt_id 
             and asrg.student_uid = ur.uid 
             and asrg.is_revoked = 0
       left join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = sc.schl_group_id 
             and ssais.tw_type_slug = tw.type_slug 
             and ssais.is_revoked != 1 
             and ssais.test_window_id = tw.id
       left join school_student_asmt_info_snapshot snapshots 
              on snapshots.student_uid = ur.uid 
             and snapshots.ssais_id = ssais.id 
             and snapshots.is_revoked = 0
       left join tw_exceptions_students twes_pended on twes_pended.test_window_id = tw.id 
             and twes_pended.uid = ur.uid 
             and twes_pended.is_revoked = 0 
             and twes_pended.category = :STU_EX_PENDED
       left join tw_exceptions_students twes_withheld on twes_withheld.test_window_id = tw.id 
             and twes_withheld.uid = ur.uid 
             and twes_withheld.is_revoked = 0 
             and twes_withheld.category = :STU_EX_WITHHOLD
       left join tw_exceptions_students twes_ex_csv on twes_ex_csv.test_window_id = tw.id 
             and twes_ex_csv.uid = ur.uid 
             and twes_ex_csv.is_revoked = 0 
             and twes_ex_csv.category = :STU_EX_EXCLUDE_FROM_CSV
           where ss.test_window_id = :test_window_id
             and twes_ex_csv.id IS NULL
      ${ query_level === ROLE_LEVEL.BOARD ? 'and sd.group_id = :group_id' : '' }
      ${ query_level === ROLE_LEVEL.SCHOOL ? 'and schl.group_id = :group_id' : '' }
      ${ bypassAdminSignOff ? '' : 'and ssais.id is not null' }
        group by ur.uid
    ;`)

    return fpStudentReportData
  }
  
  /**
   * Get G9 not fully participating student data.
   * Active student only
   * @param query_level 
   * @param test_window_id 
   * @param group_id 
   * @returns 
   */
  async getG9NFPStudentReportsData(query_level:string, test_window_id:number, group_id:number, bypassAdminSignOff = false) {
    const STU_EX_PENDED = TW_EXCEPTION_STUDENT_STATUS.PENDED
    const STU_EX_WITHHOLD = TW_EXCEPTION_STUDENT_STATUS.WITHHOLD
    const STU_EX_EXCLUDE_FROM_CSV = TW_EXCEPTION_STUDENT_STATUS.EXCLUDE_FROM_CSV
    const nfpStudentReportData = await dbRawReadReporting(this.app, {test_window_id, group_id, STU_EX_PENDED, STU_EX_WITHHOLD, STU_EX_EXCLUDE_FROM_CSV}, `
      -- Benchmark: 7.2 sec on mirror db for sd group id 1173 with 881 records
      with student_info as (
          select distinct
              ur.uid
            , schl.group_id as schl_group_id
            , schl.foreign_id as schl_mident
            , schl.name as schl_name
            , schl.lang as lang
            , ss.test_window_id
            , sc.name as class_code
        from schools schl
        join school_classes sc on sc.schl_group_id = schl.group_id
        join school_districts sd on sd.group_id = schl.schl_dist_group_id
        join user_roles ur on ur.group_id = sc.group_id 
         and ur.role_type ='schl_student' 
         and ur.is_revoked = 0 -- active student only
        join school_semesters ss on ss.id = sc.semester_id 
         and ss.test_window_id = (:test_window_id)
        join auto_nfp_student_report_generations ansrg on ansrg.student_uid = ur.uid 
         and ansrg.test_window_id = ss.test_window_id 
         and ansrg.is_revoked = 0
        where ${ query_level === ROLE_LEVEL.BOARD ? 'sd.group_id = :group_id' : ''}
              ${ query_level === ROLE_LEVEL.SCHOOL ? 'schl.group_id = :group_id' : ''}
      ),
      student_attempts as (
              select distinct
                  ur.uid
                , ta.id as ta_id
                , ta.started_on as ta_started_on
                , tsss.id as tsss_id
                , tqr.is_field_trial
                , CASE 
                    WHEN COUNT(
											DISTINCT CASE 
												WHEN tqr.is_field_trial IS NULL OR tqr.is_field_trial = 0 
												THEN taqr.id 
                      END) > 0 
                    THEN 1 
                    ELSE 0 
                  END AS session_have_answer
              from student_info ur
              join test_attempts ta on ta.uid = ur.uid 
               and ta.is_invalid = 0
              join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id 
               and twtdar.test_window_id = ur.test_window_id 
               and twtdar.generate_report = 1
              join test_attempt_sub_sessions tass on tass.test_attempt_id = ta.id
              join test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
         left join test_attempt_question_responses taqr 
                on taqr.test_attempt_id = ta.id 
               and taqr.is_nr != 1 
               and (
                   JSON_CONTAINS(tsss.sections_allowed, CAST(taqr.section_id AS JSON), '$')
                 or (taqr.section_id = 0 and JSON_CONTAINS(tsss.sections_allowed, '0', '$'))
                 or JSON_CONTAINS(tass.sections_allowed, CAST(taqr.section_id AS JSON), '$')
                 or (taqr.section_id = 0 and JSON_CONTAINS(tass.sections_allowed, '0', '$'))
               )
         left join test_question_register tqr 
                on tqr.question_id = taqr.test_question_id
               and tqr.test_design_id = ifnull(twtdar.tqr_ovrd_td_id, twtdar.test_design_id)
          group by ta.uid, ta.id, tsss.id
      ),
      student_sorted_attempts AS (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY uid ORDER BY session_have_answer DESC, ta_started_on) AS rn
            FROM student_attempts
      )
        select 
              stu_info.uid
            , u.first_name as FirstName
            , u.last_name as LastName
            , um_oen.value as StudentOEN
            , um_sasn.value as SASN
            , stu_info.schl_name as SchoolName
            , stu_info.lang
            , stu_info.schl_mident as SchoolMident
            , stu_info.class_code as ClassCode
            , sa.ta_started_on as SessionStartTime
            , null as overall
            , null as dot_score
            , CASE 
                WHEN SUM(session_have_answer) > 0 THEN 2  -- partially participating - 1 sub session only
                WHEN MAX(sa.ta_id) IS NOT NULL
                 AND sa.ta_started_on IS NOT NULL 
                THEN 3     -- partially participating - has attempt but no responses
                ELSE 0                                    -- no attempt record
              END AS TestParticipationStatus
            , IF (twes_pended.id, 1, 0) AS is_pended
            , IF (twes_withheld.id, 1, 0) AS is_withheld
            , snapshots.student_info_json AS snapshot_data
          from student_info stu_info
          join users u on u.id = stu_info.uid
          join user_metas um_oen on um_oen.uid = stu_info.uid 
           and um_oen.key = 'studentOEN' 
           AND um_oen.key_namespace = 'eqao_sdc'
     left join user_metas um_sasn on um_sasn.uid = stu_info.uid 
           and um_sasn.key_namespace = 'eqao_sdc' 
           and um_sasn.key = 'SASN'
     left join student_sorted_attempts sa on sa.uid = stu_info.uid
           and sa.rn = 1
     left join tw_exceptions_students twes_pended on twes_pended.test_window_id = stu_info.test_window_id 
           and twes_pended.uid = stu_info.uid 
           and twes_pended.is_revoked = 0 
           and twes_pended.category = :STU_EX_PENDED
     left join tw_exceptions_students twes_withheld on twes_withheld.test_window_id = stu_info.test_window_id 
           and twes_withheld.uid = stu_info.uid 
           and twes_withheld.is_revoked = 0 
           and twes_withheld.category = :STU_EX_WITHHOLD
     left join tw_exceptions_students twes_ex_csv on twes_ex_csv.test_window_id = stu_info.test_window_id
           and twes_ex_csv.uid = stu_info.uid 
           and twes_ex_csv.is_revoked = 0 
           and twes_ex_csv.category = :STU_EX_EXCLUDE_FROM_CSV
     left join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = stu_info.schl_group_id 
           and ssais.is_revoked != 1 
           and ssais.test_window_id = stu_info.test_window_id
     left join school_student_asmt_info_snapshot snapshots
            on snapshots.student_uid = stu_info.uid 
           and snapshots.ssais_id = ssais.id 
           and snapshots.is_revoked = 0
     		 where twes_ex_csv.id IS NULL
     ${ bypassAdminSignOff ? '' : 'AND ssais.id is not null' }
      group by stu_info.uid
    ;`)

    return nfpStudentReportData
  }

  /**
   * Render student data according to G9ResultColumns configuration.
	 * In respect of G9 reporting hierarchy rules
   * @param reports 
   */
  async renderReportDataValues(reports: any[]) {
    const csvReportData = [];
    const additionalNullValue = ['', '#', '000000000'];

    for (const report of reports) {
      const singleReport: any = {};

      for (const column of G9ResultColumns) {
        const {
          col: csvColumnName,
          dbHeader,
          modifyNull,
          nullValue,
          modifyDate,
          dateFormat
        } = column;

        // Use dbHeader if defined, otherwise use csvColumnName directly
        let reportValue = dbHeader ? report[dbHeader] : report[csvColumnName];

        // Format date if required
        if (modifyDate && reportValue) {
          reportValue = moment(reportValue).utc().format(dateFormat);
        }

        // Apply null replacement only if value is null or undefined
        if (modifyNull && (reportValue === null || reportValue === undefined || additionalNullValue.includes(reportValue.trim()))) {
          reportValue = nullValue;
        }

        singleReport[csvColumnName] = '' + reportValue;
      }

      const { OverallOutcomeLevel, OverallDotScore } = await this.renderOverallValues(report);
      singleReport.OverallOutcomeLevel = OverallOutcomeLevel;
      singleReport.OverallDotScore = OverallDotScore;

      csvReportData.push(singleReport);
    }

    return csvReportData;
  }

  /**
   * Render OverallOutcomeLevel and OverallDotScore values
   * @param report 
   * @returns 
   */
  async renderOverallValues(report:any) {
    const translation = this.app.service('public/translation');

    const generateNonNumericValue = async (slug:string, lang:string = 'en'):Promise<{ OverallOutcomeLevel: string; OverallDotScore: string; }> => {
      const traWord = await translation.getOneBySlug(slug, lang)
      return {OverallOutcomeLevel: traWord[0], OverallDotScore: traWord[0] }
    }

    const generateNFPValue = () => {
      return { OverallOutcomeLevel: -9, OverallDotScore: -99 }
    }

    let resultOrder = [
      {
        target: 'is_pended',
        targetValue: '1',
        returnValue: () => generateNonNumericValue('lbl_g9_reports_pended') // Default to English
      },
      {
        target: 'is_withheld',
        targetValue: '1',
        returnValue: () => generateNonNumericValue('lbl_g9_reports_withheld', report.lang)
      },
      {
        target: 'TestParticipationStatus',
        targetValue: '1',
        returnValue: () => Promise.resolve({ OverallOutcomeLevel: report.overall, OverallDotScore: report.dot_score })
      },
      {
        target: 'TestParticipationStatus',
        targetValue: '2',
        returnValue: () => Promise.resolve(generateNFPValue())
      },
      {
        target: 'TestParticipationStatus',
        targetValue: '3',
        returnValue: () => Promise.resolve(generateNFPValue())
      },
      {
        target: 'TestParticipationStatus',
        targetValue: '0',
        returnValue: () => Promise.resolve(generateNFPValue())
      }
    ];

    for (let index = 0; index < resultOrder.length; index++) {
      const { target, targetValue, returnValue } = resultOrder[index];

      if (report[target] != null && +report[target] === +targetValue) {
        return await returnValue();

      }
    }
    // Fallback value to NFP, should not run to this line
    return generateNFPValue()
  }

  /**
   * Create CSV and store to S3
   * @param data 
   * @param filePath 
   * @returns 
   */
  async createCSVfile(headers:string[][], data: any[], filePath: string) {
    let csvData: any[][];
    
    if(data.length > 0) {
      csvData = convertObjectsToArrayOfArraysWithHeader(data);
    } else{
      // Insert headers
      csvData = headers
    }
    
    let buffer = await writeToBuffer(csvData);

    // upload individual school CSV to S3
    const stream = new PassThrough();
    stream.write('\ufeff', 'utf8');  // Add BOM
    stream.end(buffer);
    await storeInS3(filePath, stream);

    return csvData.slice(1); // Return student data without headers
  }

  /**
   * Genrate CSV file path 
   * @param file_type 
   * @param test_window Full test window data
   * @param schl_dist_group_id 
   * @param timestamp 
   * @param entity_id board mident or school mident
   * @param lang brd_lang
   * @returns 
   */
  async getCSVFilePath(file_type:ROLE_LEVEL, test_window:any, schl_dist_group_id:number, timestamp:number|string, entity_id:number, lang:string){
    const tw_id = test_window.id
    const s3_file_path = [
      this.CSV_FILE_PATH,
      `tw_${tw_id}`,
      `sd_group_id_${schl_dist_group_id}`,
      `${this.KW_TIMESTAMP}_${timestamp}`,
    ].join('/');

    let s3_file_name
    const { endOnYear, semesterSeason } = await this.generateG9YearAndSeason(test_window)
    const langChar = lang.slice(0, 1).toUpperCase()
    switch(file_type){
      case ROLE_LEVEL.BOARD:
        s3_file_name = `/G9_${endOnYear}_${semesterSeason}_B0${langChar}${entity_id}.csv`;
        break
      case ROLE_LEVEL.SCHOOL:  
        s3_file_name = `/schl_mident_${entity_id}.csv`;
        break
    }

    const fullPath = s3_file_path + s3_file_name
    return fullPath
  }

	/**
	 * Generate administration year by extracting year of the test window.date_end.
   * Get semester season from test_window.window_code.
	 * @param tw_date_end 
	 * @returns
	 */
  async generateG9YearAndSeason(test_window:any):Promise<{ endOnYear: string; semesterSeason: string; }>{
		const tw_code = test_window.window_code
    const twDateEnd = new Date(test_window.date_end)
    const endOnYear = '' + twDateEnd.getFullYear()

    let semesterSeason

    if (tw_code?.trim()) {
      semesterSeason = tw_code.trim()
    } else { // Fallback in case tw_code is missing
      const endOnMonth = twDateEnd.getMonth() + 1
      const translation = this.app.service('public/translation');
      const spring = (await translation.getOneBySlug('lbl_osslt_report_term_spring', 'en')).trim()
      const winter = (await translation.getOneBySlug('lbl_report_term_winter', 'en')).trim()

      semesterSeason = endOnMonth < 4 ? winter : spring
    }

    return { endOnYear, semesterSeason }
  }
  
  // get the timestamp from the link
  extraTimeStampFromLink(s3_path_link: string){
    const pathSegments = s3_path_link.split('/');

    const timestampString = pathSegments.find(seg => seg.includes(this.KW_TIMESTAMP))
    if(!timestampString) {
      throw new Errors.BadRequest('INCORRECT_URL')
    }
    const timestampSegments = timestampString.split('_')
    
    // The timestamp is the second segment (index 1)
    const timestamp = timestampSegments[1]
    return timestamp
  }
}

