import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { generateSebDownloadUrl, generateS3DownloadUrl } from '../../../upload/upload.listener';
import { Knex } from 'knex';
import { getSysConstString } from '../../../../util/sys-const-string';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

interface Data {}

interface ServiceOptions {}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }


  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query || !params.query.clientDomain || !params.query.schlDistGroupId) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);
    const { clientDomain, schlDistGroupId, isSecreteUser} = params.query;

    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props:any[], query:string) => {
      const res = await db.raw(query, props);
      return <any[]> res[0];
    }

    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord

    const schoolDistrictRecords = await getData([uid, schlDistGroupId], `
      select sd.id
            , sd.group_id
            , sd.foreign_id
            , sd.name
            , sd.brd_lang
            , sd.lockdown_file_kiosk_instr
            , sd.lockdown_file_kiosk
            , sd.lockdown_file_seb_instr
            , sd.lockdown_file_seb
      from school_districts sd
      join user_roles ur
        on ur.group_id = sd.group_id
        and ur.role_type in ('schl_dist_admin','schl_disct_curr_ele','schl_disct_curr_sec','school_district_curr')
        and ur.uid = ?
        and ur.is_revoked != 1
      where sd.group_id = ? 
    ;`)
    if (schoolDistrictRecords.length === 0){
      throw new Errors.Forbidden('NO_ROLE_ON_SD');
    }

    const allowBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain();
    const isBypassDomain = allowBypassDomain.indexOf(clientDomain) > -1

    const csvTestWindows = await getData([schlDistGroupId], `
      -- Benchmark: 0.8 sec
      select tw.* 
        from test_windows tw
        join school_semesters ss on ss.test_window_id = tw.id
        join school_classes sc on sc.semester_id = ss.id
        join schools s on s.group_id = sc.schl_group_id
       where tw.type_slug in ("EQAO_G9M", "EQAO_G10L") 
         and tw.is_active = 1 
         and s.schl_dist_group_id = ?
         ${isBypassDomain || isValidSecreteUser ? '' : 'and show_report_to_Board = 1' }
       group by tw.id
       order by tw.date_end desc
    ;`)

    const school_district = schoolDistrictRecords[0];
    //const ttl = 240;
    const getValue = true
    const ttl = await getSysConstNumeric(this.app, 'DIST_ADMIN_TR_SEB_KIOSK_S3_EXPIRE_TIME', getValue)
    const generateLink = (path:string) => {
      path = path.split('//').join('/');
      if (path.substr(0, 1) === '/'){
        path = path.substr(1, path.length - 1);
      }
      return generateS3DownloadUrl(path, +ttl);
    }
    
    return {
      sd_id: school_district.id,
      name: school_district.name,
      group_id: school_district.group_id,
      foreign_id: school_district.foreign_id,
      brd_lang: school_district.brd_lang,
      seb: {
        config: generateLink(school_district.lockdown_file_seb),
        creds: 'Password: ' + school_district.lockdown_file_seb_instr
      },
      kiosk: {
        config: generateLink(school_district.lockdown_file_kiosk),
        creds: 'Username: admin \n Password: ' + school_district.lockdown_file_kiosk_instr
      },
      csvTestWindows
    }
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

}
