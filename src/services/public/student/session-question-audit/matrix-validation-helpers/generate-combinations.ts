//! DO NOT MODIFY FILE UNLESS TO MATCH WEB CLIENT src/app/ui-item-maker/config-score-matrix/expected-answer-validation/generate-combinations.ts
//? Please look at comments before modifying 
import { getDndState, getDropdownStateMCQ, getGroupingState, getInsertionState, getOrderingState, getSelectionTableState, getStateMCQ } from "./get-tei-states";
import { ElementType, ESelectionTableScoreMode, McqDisplay, OrderMode } from "./model";
import { elementToMatrixHeader, getDerivedLegacyScoreMode } from "./utils";

type Combination = (string[] | null)[];
const MAX_NUMBER_ELEMENTS = 10;
const MAX_COMBINATION = 16000; // this would require 500 api requests 
const MAX_SELECTION_LIMIT_MCQ = 5;
const MAX_SELECTION_LIMIT_TABLE = 5;

export const generatePossibleElementCombinations = (element:any) : any[] => {
    let possibleCombinations: any[] = [];
    switch (element.elementType) {
        case ElementType.GROUPING:
          possibleCombinations = generatePossibleElementCombinationsGrouping(element);
          break;
        case ElementType.MOVEABLE_DND:
          possibleCombinations = generatePossibleElementCombinationsDND(element);
          break;
        case ElementType.INSERTION:
          possibleCombinations = generatePossibleElementCombinationsInsertion(element);
          break;
        case ElementType.ORDER:
          possibleCombinations = generatePossibleElementCombinationsOrder(element);
          break;
          case ElementType.CUSTOM_MCQ:
        case ElementType.MCQ:
          possibleCombinations = <any[]>generatePossibleElementCombinationsMCQ(element);
          break;
        case ElementType.SELECT_TABLE:
          possibleCombinations = <any[]>generatePossibleElementCombinationsSelectTable(element);
          break;
        default:
          console.error("Unable to generate combinations for element type " + element.elementType);
      }
    return possibleCombinations;
}

const generatePossibleElementCombinationsDND = (element:any) =>{
    const options = element.scoreMatrix?.rows; 
    // modified from client as sometimes the matrix won't be validatted
    if(!options){
        return []
    }
    const isOptionsReusable = element.isOptionsReusable;
    const slots = element.targets.length;
    const formatted_combinations: any[] = [];
    const initTargert = options.map((draggable:any) => ({ 
        contents: [{
            isTarget: false,
            ref: draggable.option
        }], 
        originalPosition: true, 
        targetContext: draggable }));
    const combinations: any[][] = generatePermutationsInsertion(slots, options, true, isOptionsReusable); 
    const drag2Targ = new Map();
    element.pairMapping?.forEach((pair:any)=>{
        if (!drag2Targ.has(pair.optionID)) {
        drag2Targ.set(pair.optionID, [])
        }
        drag2Targ.get(pair.optionID).push(pair.targetID)
    })
    for(let combination of combinations){
        const elementCopy = JSON.parse(JSON.stringify(element));
        const targets = JSON.parse(JSON.stringify(initTargert));
        for(let target of element.targets){
            let ref = combination.pop();
            if(ref.length === 0){
                targets.push({
                    contents:[],
                    originalPosition: false,
                    targetContext: target
                })
            } else {
                targets.push({ 
                    contents: [{
                        isTarget: false,
                        ref: ref.option
                    }], 
                    originalPosition: false, 
                    targetContext: target });
            }

        }
        const homeTargetContents = new Map();
        for (let draggable_idx = 0; draggable_idx < initTargert.length; draggable_idx++) {
            if(targets[draggable_idx].contents.length === 0 ){continue;}
            for(let target_idx = initTargert.length; target_idx < targets.length; target_idx++){
                if(JSON.stringify(targets[draggable_idx].contents)===JSON.stringify(targets[target_idx].contents)){
                    targets[draggable_idx].contents = []
                }
            }
            homeTargetContents.set(targets[draggable_idx].contents, true);
            
        }

        formatted_combinations.push({1: getDndState(elementCopy, homeTargetContents, targets)});

    }
    return formatted_combinations

}

const generatePossibleElementCombinationsGrouping = (element: any) => {
    const options = element.scoreMatrix?.rows.map((row: any) => ({ ref: row.option }));
    // modified from client as sometimes the matrix won't be validatted
    if(!options){
        return []
    }
    const isOptionsReusable = element.isOptionsReusable;
    // If max group size is set by element use it if it's less than the default max else use max
    const max_group_size = element.isGroupSizeLimited ? (element.groupSize < MAX_NUMBER_ELEMENTS? element.groupSize: MAX_NUMBER_ELEMENTS) : MAX_NUMBER_ELEMENTS
    const slots = element.targets.length;
    const combinations = generateCombinations(slots, options, isOptionsReusable, max_group_size);
    const formatted_combinations: any[] = [];

    for(let combination of combinations){
        const elementCopy = JSON.parse(JSON.stringify(element));
        const targets = []
        for( let target of elementCopy.targets){
            targets.push(
                {
                    targetContext: target,
                    contents: combination.pop()
                }
            )
        }

        formatted_combinations.push({1:getGroupingState(elementCopy, targets, options)});
    }
    // for(let combination of combinations){
    //       formatted_combinations.push({1:updateStateOrder(element,combination)})
    // }
    return formatted_combinations;
  }
  
const generatePossibleElementCombinationsOrder = (element: any) => {
    const orderModeTarget = element.orderMode === OrderMode.TARGET;
    const options = element.scoreMatrix.rows.map((row:any)=> [row.option]); 
    // modified from client as sometimes the matrix won't be validatted
    if(!options){
        return []
    }
    const staticAnswers:Set<number> = new Set();
    for (let [index, option] of element.options.entries()){
    if(orderModeTarget && option.isReadOnly){
        options.splice(index, 0,[option]);
        staticAnswers.add(index);
    }

    }
    const combinations = generatePermutationsOrder(options, staticAnswers, orderModeTarget);
    const formatted_combinations = [];
    for(let combination of combinations){
        formatted_combinations.push({1:getOrderingState(element, element.options, () => {return combination})});
    }
    return formatted_combinations;
}

const generatePossibleElementCombinationsInsertion = (element: any) => {
const options = element.scoreMatrix?.rows?.map((row:any)=> row.option); 
// modified from client as sometimes the matrix won't be validatted
if(!options){
    return [];
}
const isRepeatableOptions = element?.isRepeatableOptions;
const slots = element.textBlocks.filter((textblock:any)=> textblock.element.elementType === ElementType.BLANK || textblock.element.elementType === ElementType.BLANK_DEPRECIATED).length
const combinations: any[][] = generatePermutationsInsertion(slots, options, true, isRepeatableOptions); 
const formatted_combinations: any[] = [];
const blocks: any[][] = [];
for(let combination of combinations){
    const block: any[] = [];
    for (let [index, textblock] of element.textBlocks.entries()){
    if(textblock.element.elementType === ElementType.BLANK || textblock.element.elementType === ElementType.BLANK_DEPRECIATED){
        let ctx = combination.pop(); // Pop the last element from the combination array
        const contents = Array.isArray(ctx) ? null : [{ref:ctx}]; // so that blanks ge treated properly
        block.push({ isTarget: true, ctx:textblock, contents});
    } else {
        block.push({isTarget:false, ctx: {element:textblock.element}});
    }
    }
    blocks.push(block)
}
for (let block of blocks){
    formatted_combinations.push({1:getInsertionState(element, block, [])});
}
return formatted_combinations;
}

const generatePossibleElementCombinationsMCQ = (element: any) => {
    const options = element.scoreMatrix?.rows?.map((row:any)=> row.option) || element.options;
    let maxSelectable:number = 1;
    const numOfOptions = options?.length ?? 0;
    if(element.displayStyle === McqDisplay.DROPDOWN || element.displayStyle === McqDisplay.CUSTOM_DROPDOWN){
        maxSelectable = 1;
    } else if (element.isMultiSelect){
        if(+element.maxOptions) maxSelectable = +element.maxOptions; // if 0 or null it'll allow unlimited selections
        else maxSelectable = numOfOptions;
    }
    // Check if max selectable is greater than options
    if(maxSelectable > numOfOptions){
        maxSelectable = numOfOptions;
    }
    // Check if max selectable is greater than max selectable to keep combinations within limit
    if(maxSelectable > MAX_SELECTION_LIMIT_MCQ){
        maxSelectable = MAX_SELECTION_LIMIT_MCQ;
    }
    const numberOfCombinations = calculateSelectionCombinations(maxSelectable, numOfOptions);
    // if(numberOfCombinations > MAX_COMBINATION){
    //     const continueValidation = confirm("Large number of combinations generated, validation will take some time\nDo you want to proceed?");
    //     if(!continueValidation){
    //         return;
    //     }
    // }

    const combinations: number[][] = generateSelectionCombinations(maxSelectable, numOfOptions);
    const formatted_combinations: any[] = [];
    const elementCopy = JSON.parse(JSON.stringify(element));
    elementCopy.options = options;

    for(let combination of combinations){
        const currentSelections = [];
        let lastSelection = -1;
        for(let [index, select] of combination.entries()){
            if(select){
                currentSelections.push(options[index]);
                lastSelection = index;
            }
        }
        if(element.displayStyle === McqDisplay.DROPDOWN){
            formatted_combinations.push({1:getDropdownStateMCQ(element, lastSelection)});
        } else {
            formatted_combinations.push({1:getStateMCQ(element, currentSelections)});
        }
       
    }
    return formatted_combinations;
}

const generatePossibleElementCombinationsSelectTable = (element: any) => {
   const numOfRows = element.scoreMatrix?.rows.length || element.leftCol?.length;
   const numOfCols = element.scoreMatrix?.columns.length || element.topRow?.length;

   const correctAns = element.checkBoxRows?.reduce((sum:any, row: any) => {
        return sum + row.filter((cell:any) => cell.value === true).length;
    }, 0);
   let maxPerRow = 0;
   let maxPerCol = 0;
   const scoreMode = getDerivedLegacyScoreMode(element);
   let maxSelections = 0;
   switch(scoreMode){
    case ESelectionTableScoreMode.ROW_COL:
        maxSelections = numOfCols * numOfRows;
        maxPerRow = element.maxCheckedPerRow;
        maxPerCol = element.maxCheckedPerCol;
        break;
    case ESelectionTableScoreMode.CELL:
        if(element.isMaxDenoScoring){
            maxSelections = correctAns;
        } else {
            maxSelections = numOfCols * numOfRows;
        }
        break;
   }
   const combinations = generateSelectionTableCombinations(numOfRows, numOfCols, maxSelections, maxPerRow, maxPerCol);
//    if(combinations.length > MAX_COMBINATION){
//         const continueValidation = confirm("Large number of combinations generated, validation will take some time\nDo you want to proceed?");
//         if(!continueValidation){
//             return;
//         }
//    }
   const formatted_combinations: any[] = [];
   for(let combination of combinations){
    const testTakerAnswers = combination.map(row =>{
        return row.map((cell) => {return {value: !! cell}});
    });
    formatted_combinations.push({1: getSelectionTableState(element, testTakerAnswers)})
   }
   return formatted_combinations;
}

function generatePermutationsInsertion(
length: number,
options: any[],
allowEmpty: boolean = false,
allowReuse: boolean = false
): any[][] {
const results: any[][] = [];

// Helper function to create permutations
function permute(currentArray: any[]) {
    if (currentArray.length === length) {
    results.push(currentArray.slice());
    return;
    }

    // Iterating through each option and adding it to the current permutation
    for (const option of options) {
    // Check if the current option can be reused
    if (allowReuse || !currentArray.includes(option)) {
        permute([...currentArray, option]);
    }
    }

    // If allowing empty values, add an empty value and continue
    if (allowEmpty && currentArray.length < length) {
    permute([...currentArray, []]); // Using 'null' to represent empty values
    }
}

// Initialize the permutation generation
permute([]);

return results;
}

function generatePermutationsOrder(array: any[], reservedIndices: Set<number>, allowEmpty = false, areOptionsReusable = false) {
const results: any[] = [];
const blank: any[] = [];  // Using an empty array as the placeholder for blank

function swap(arr: { [x: string]: any; }, i: string | number, j: string | number) {
    const temp = arr[i];
    arr[i] = arr[j];
    arr[j] = temp;
}

function permute(arr: any[], start: number) {
    if (start >= arr.length) {
    results.push(arr.slice());  // Clone and store the current permutation
    return;
    }

    if (reservedIndices.has(start)) {
    permute(arr, start + 1);  // Skip permutation for reserved indices
    } else {
    const seen = new Set();  // Track elements that have been used at this position

    for (let i = start; i < arr.length; i++) {
        if (!reservedIndices.has(i) && (!seen.has(arr[i]) || areOptionsReusable)) {
        seen.add(arr[i]);
        swap(arr, start, i);
        permute(arr, start + 1);
        swap(arr, start, i);  // Backtrack
        }
    }
    if (allowEmpty) {
        const original = arr[start];
        if (!seen.has(blank) || areOptionsReusable) {  // Add blank if it's not considered as seen or if reusable
        arr[start] = blank.slice();  // Set current index as blank and permute
        permute(arr, start + 1);
        arr[start] = original;  // Restore original element after the recursive call
        }
    }
    }
}

permute(array, 0);
return results;
}

function generateCombinations(
    numSlots: number,
    options: any[],
    areOptionsReusable: boolean,
    maxItemsPerSlot: number
): Combination[] {
    const results: Combination[] = [];
    
    // Helper function to generate all possible subsets of options up to maxItemsPerSlot
    function generateSubsets(availableOptions: string[]): string[][] {
        const subsets: string[][] = [];
        const totalOptions = availableOptions.length;
        // Calculate 2^totalOptions combinations, representing each possible subset
        const maxCombinations = 1 << totalOptions;

        for (let i = 0; i < maxCombinations; i++) {
            let subset: string[] = [];
            for (let j = 0; j < totalOptions; j++) {
                if (i & (1 << j)) {
                    subset.push(availableOptions[j]);
                    if (subset.length > maxItemsPerSlot) break; // Stop if the subset exceeds the max items per slot
                }
            }
            if (subset.length <= maxItemsPerSlot) {
                subsets.push(subset);
            }
        }
        return subsets;
    }

    // Recursive function to generate all combinations
    function permute(currentCombination: Combination, availableOptions: string[]) {
        if (currentCombination.length === numSlots) {
            results.push(currentCombination.slice());
            return;
        }

        const subsets = generateSubsets(availableOptions);

        // Iterate over each subset and add it to the current combination
        subsets.forEach(subset => {
            // If options are reusable, we pass the same available options array
            // Otherwise, we remove the used options from the available pool for the next calls
            const nextAvailableOptions = areOptionsReusable ? availableOptions : availableOptions.filter(opt => !subset.includes(opt));

            permute([...currentCombination, subset], nextAvailableOptions);
        });

        // Consider the slot as empty if allowed (empty array represents an empty slot)
        permute([...currentCombination, []], availableOptions);
    }

    // Initialize the recursive function
    permute([], options);

    return results;
}
function generateSelectionCombinations(maxSelectable: number, numOfOptions: number): number[][] {
    const results: number[][] = [];

    // Helper function to generate combinations recursively
    function backtrack(_current: number[], index: number, selectedCount: number) {
        const current = [..._current]// so we don't change original
        // If we've processed all options, add the current combination if valid
        if (index === numOfOptions) {
            results.push([...current]);
            return;
        }

        // Case 1: Exclude the current option (set it to 0)
        current[index] = 0;
        backtrack(current, index + 1, selectedCount);

        // Case 2: Include the current option (set it to 1) if it doesn't exceed maxSelectable
        if (selectedCount < maxSelectable) {
            current[index] = 1;
            backtrack(current, index + 1, selectedCount + 1);
        }
    }

    // Initialize the recursion with an empty combination
    backtrack(new Array(numOfOptions).fill(0), 0, 0);

    return results;
}

function generateSelectionTableCombinations(numRows: number, numColumns: number, maxSelectable: number = MAX_SELECTION_LIMIT_TABLE, maxPerRow?: number, maxPerCol?: number){
    if(maxSelectable > MAX_SELECTION_LIMIT_TABLE) maxSelectable = MAX_SELECTION_LIMIT_TABLE;
    
    const results: number[][][] = [];
    const numOfOptions = numColumns + numRows;
    function getSelected(map: { [index: number]: number }): number {
        return Object.values(map).reduce((count, selected) => count + (isNaN(selected) ? 0 : selected), 0);
    }    
    function backtrack(
        _current: number[][],
        rowIndex: number,
        columnIndex: number,
        _rowMap: { [index: number]: number },
        _colMap: { [index: number]: number }
    ) {
        // Proper deep copy
        const current = _current.map(row => [...row]); 
        const rowMap = { ..._rowMap };
        const colMap = { ..._colMap };
    
        const currSelected = getSelected(rowMap);
    
        // Base case: stop when max selections reached or out of bounds
        if (
            currSelected === maxSelectable || 
            currSelected === numOfOptions || 
            columnIndex >= numColumns
        ) {
            results.push(current);
            return;
        }
    
        // Move to next column when we reach the last row
        if (rowIndex >= numRows) {
            backtrack(current, 0, columnIndex + 1, rowMap, colMap);
            return;
        }
    
        // Path 1: Without selecting this cell
        backtrack(current, rowIndex + 1, columnIndex, rowMap, colMap);
    
        const maxSelectedPerRow = maxPerRow ? maxPerRow <= (rowMap[rowIndex] || 0) : false;
        const maxSelectedPerCol = maxPerCol ? maxPerCol <= (rowMap[maxPerCol] || 0) : false;
        // Path 2: Selecting this cell (only if allowed)
        if (currSelected < maxSelectable && !maxSelectedPerRow && !maxSelectedPerCol) {
            current[rowIndex][columnIndex] = 1;
            rowMap[rowIndex] = (rowMap[rowIndex] || 0) + 1;
            colMap[columnIndex] = (colMap[columnIndex] || 0) + 1;
            
            backtrack(current, rowIndex + 1, columnIndex, rowMap, colMap);
        }
    }
    
    const matrix = new Array(numRows).fill(0).map(() => new Array(numColumns).fill(0));
    backtrack(matrix, 0, 0, {}, {});
    return results;
}

function calculateSelectionCombinations(maxSelectable: number, numOfOptions: number): number {
    // Helper function to compute binomial coefficient iteratively
    function binomialCoefficient(n: number, k: number): number {
        if (k > n) return 0;
        if (k === 0 || k === n) return 1;

        // Optimize calculation by using the smaller of k and (n-k)
        k = Math.min(k, n - k);

        let result = 1;
        for (let i = 0; i < k; i++) {
            result *= (n - i);
            result /= (i + 1);
        }

        return result;
    }

    let totalCombinations = 0;

    // Sum up binomial coefficients for k = 0 to maxSelectable
    for (let k = 0; k <= maxSelectable; k++) {
        totalCombinations += binomialCoefficient(numOfOptions, k);
    }

    return totalCombinations;
}
  