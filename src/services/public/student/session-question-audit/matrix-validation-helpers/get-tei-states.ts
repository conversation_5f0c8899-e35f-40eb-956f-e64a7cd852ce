import _ from "lodash";
import { ElementType, ESelectionTableScoreMode, GradingType, IScoreResult, McqDisplay, ScoringTypes } from "./model";
import { elementToMatrixHeader, getDerivedLegacyScoreMode, getScoreMode, indexOf, roundNumeric } from "./utils";
//! DO NOT modify unless you are making updates because the client changed how it handles the following elements states

// dnd
export const getDndState = (
    element: any,
    homeTargetContents: any,
    targets: any,
) => {
    const drag2Targ: Map<string, string[]> = new Map<string, string[]>()
    if (element.pairMapping) {
        element.pairMapping.forEach((pair: any) => {
            if (!drag2Targ.has(pair.optionID)) {
                drag2Targ.set(pair.optionID, [])
            }
            drag2Targ.get(pair.optionID)?.push(pair.targetID)
        })
    }
    const entryId = element.entryId;
    const simplifiedStateTargets: any = [];
    let weight = getElementWeight(element) || 1;
    let numCorrect = 0;
    let numWrong = 0;
    let numTargets = 0;
    let numDraggablesPlaced = 0; // 
    let isAllFilled = true;
    let isStarted = false;
    let isKeyIdUsed = true;
    const draggableLocation = new Map();
    const targetLocation = new Map()
    targets.forEach((target: any) => {
        let context = target.targetContext;
        const isHomeTarget = homeTargetContents.has(target.contents)
        if (!isHomeTarget) {
            const simpTargetState: { key_id: number | string, id: number, contents: any } = {
                key_id: target.targetContext.key_id,
                id: target.targetContext.id,
                contents: [],
            }
            if (!target.targetContext.key_id) {
                isKeyIdUsed = false;
            }
            simplifiedStateTargets.push(simpTargetState);
            const draggableEl = target.contents[0];
            numTargets++;
            if (draggableEl) {
                simpTargetState.contents.push({
                    key_id: draggableEl.ref.key_id,
                    id: draggableEl.ref.id,
                })
                numDraggablesPlaced++;
                isStarted = true;
                const draggable = draggableEl.ref;
                // console.log('real target ', target.contents.length, draggable.targetId, context.id)
                if (element.isCustomValidataion) {
                    const draggableId = draggable.id.toString();
                    if (!draggableLocation.has(draggableId)) draggableLocation.set(draggableId, []);
                    draggableLocation.get(draggableId).push(context.id.toString());
                }
                if (element.isAcceptMultipleCombinations) {
                    const targetId = context.id.toString()
                    // if(!targetLocation.has(targetId)) targetLocation.set(targetId, "")
                    targetLocation.set(targetId, draggable.id.toString());
                }

                if ((!element.isMultipleOptionsRight && draggable.targetId == context.id) || (element.isMultipleOptionsRight && drag2TargPairingExists(draggable.id.toString(), context.id.toString(), drag2Targ))) {
                    numCorrect++;
                } else {
                    numWrong++;
                }
            }
            else {
                isAllFilled = false;
            }
        }
    })

    let score = 0;
    let isCorrect: boolean = false;
    let isFilled = true;
    //custom validation
    if (element.isCustomValidataion) {
        for (let i = 0; i < element.draggables.length; i++) {

            let draggable = element.draggables[i];
            let id = draggable.id.toString();
            const locations = draggableLocation.has(id) ? draggableLocation.get(id) : [];

            if (isExactDraggableRequired(draggable)) {
                isCorrect = locations.length == draggable.exactDraggableRequired;
                if (!isCorrect) break;
            }

            if (isRangeProvided(draggable)) {
                isCorrect = locations.length >= draggable.minRequired && locations.length <= draggable.maxRequired;
                if (!isCorrect) break;
            }

        }

        if (element.isAllTargetsToPlace) {
            if (!isAllFilled) isCorrect = false;
        }
        score = isCorrect ? 1 : 0
    }
    //Multiple combo
    else if (element.isAcceptMultipleCombinations) {
        if (element.multipleCombinations) {
            isCorrect = element.multipleCombinations.some((combination: any) => {
                let correctVal = 0;
                for (const comb in combination) {
                    const placedId = targetLocation.get(comb);
                    const expectedId = combination[comb];
                    if (placedId !== expectedId) {
                        correctVal = 0;
                        break;
                    }
                    correctVal++;
                }
                return correctVal === element.targets.length
            });
            if (isCorrect) score = weight;
        }
    } else if (element.gradingMode == GradingType.PLUS_MINUS) {
        isFilled = numDraggablesPlaced > 0
        isCorrect = (numCorrect == element.targets.length) ? true : false
        score = 2 * numCorrect - numTargets
        if (score < 0) {
            score = 0
        }
        score = weight * score / numTargets
    } else if (element.isAllTargetsToPlace || (element.draggables.length < element.targets.length)) {
        isFilled = element.isOptionsReusable ? numDraggablesPlaced === element.targets.length : numDraggablesPlaced === element.draggables.length;
        if (numCorrect === numDraggablesPlaced) {
            isCorrect = element.isOptionsReusable ? numDraggablesPlaced === element.targets.length : numDraggablesPlaced === element.draggables.length;
        }
        if (numDraggablesPlaced > 0) {
            score = element.isOptionsReusable ? weight * (numCorrect / element.targets.length) : weight * (numCorrect / element.draggables.length)
        }
    }
    else {
        isFilled = isAllFilled;
        if (numCorrect === numTargets) {
            isCorrect = true;
        }
        if (element.enableProportionalScoring) {
            if (numTargets > 0) {
                score = weight * numCorrect / numTargets;
                if (element.gradingMode == GradingType.REDUCTION) {
                    isFilled = numDraggablesPlaced > 0
                    isCorrect = (numCorrect == element.targets.length) ? true : false
                    score = weight - (numTargets - numCorrect)
                    if (score < 0) {
                        score = 0
                    }
                }
            }

        }
        else {
            if (isCorrect) {
                score = weight;
            }
        }

    }

    if (element.howManyToFill || element.howManyToFill == 0) {
        isFilled = numDraggablesPlaced >= element.howManyToFill
    }

    let isResponded = numDraggablesPlaced >= 1;
    // console.log(missed==0)
    let entryState: any = {
        type: ElementType.MOVEABLE_DND,
        isCorrect,
        isStarted,
        isFilled,
        isResponded,
        score,
        weight,
        scoring_type: ScoringTypes.AUTO,
        targets: targets, // this.textBlocks
        isKeyIdUsed,
        simplifiedStateTargets,
    };

    return entryState;
}

const drag2TargPairingExists = (dragID: string, targetID: string, drag2Targ: Map<string, string[]>) => {
    if (drag2Targ.has(dragID) && drag2Targ.get(dragID)?.indexOf(targetID) != -1) {
        return true;
    }
    return false;
}

const isExactDraggableRequired = (draggable: any) => {
    return "exactDraggableRequired" in draggable && draggable.exactDraggableRequired !== null
}

const isRangeProvided = (draggable: any) => {
    return "minRequired" in draggable && "maxRequired" in draggable && draggable.minRequired !== null && draggable.maxRequired !== null
}

// grouping

export const getGroupingState = (
    element:any,
    targets: any[],
    draggables: any[],
) => {
    const weight = getElementWeight(element); 
    // type DragIds = Set<number>
    // let targetsDragMap: Map<number , DragIds> = new Map()
    let score; //this.initialNumOptions;
    let totalPartialScore = 0;
    let numMatched = 0;
    let isAnyFilled = false;
    let isAllFilled = true;
    let numFilled = 0;
    let numPlaced = 0;
    let isCorrect: boolean | undefined = false;
    let groupSize = element.isGroupSizeLimited ? element.groupSize : undefined;
    const numDraggables = element.draggables.length;
    // are any of the targets filled? are they all filled?
    targets.forEach((target)=>{
      numPlaced += target.contents.length;
      if (target.contents.length > 0){
        isAnyFilled = true;
        numFilled++;
      }
    })
    
    if (numPlaced < numDraggables){
        isAllFilled = groupSize * targets.length === numPlaced ? true : false
    }
    // identify all placements into known targets (map to target's ID)
    if (!element.isTargetsSame && !element.isTargetsOrdered) {
      if (!element.isOptionsReusable) {
        const knownDraggablePlacements = new Map();
        targets.forEach(target=>{
          const targetId = target.targetContext.id;
          target.contents.forEach((draggableEl: any) => {
            const draggableRef = draggableEl.ref;
            const draggableRefStr =  JSON.stringify(draggableRef);
            knownDraggablePlacements.set(draggableRefStr, targetId)
          })
        })
        // count up the elements that are in the correct position (assumes that all elements should have a target position, this can be updated later to allow for draggables that do not have an intended target)
        element.draggables.forEach((draggableRef: any) => {
          const draggableRefStr = JSON.stringify(draggableRef)
          if (draggableRef.id == knownDraggablePlacements.get(draggableRefStr) || (!draggableRef.id && !knownDraggablePlacements.get(draggableRefStr)) ){
            numMatched++;
            totalPartialScore += draggableRef.partialWeight || 0
          }
        })
        numMatched = Math.min(numMatched, numDraggables);
        // finalize score
        isCorrect = (numMatched == numDraggables);
        if (element.enableProportionalScoring) {
          score = weight * numMatched/numDraggables;
        }
        // else{
        //   score = isCorrect ? weight : 0;
        // }
      } 
      else {
        isCorrect = true;
        targets.forEach(target=>{
          const numDragsEach:any = {}
          target.contents.forEach((draggableEl:any)=>{
            const draggableRef = draggableEl.ref
            const dragID = draggableRef.id
            if (numDragsEach[dragID] && numDragsEach[dragID]>=0) {
              numDragsEach[dragID]++
            } else {
              numDragsEach[dragID] = 1
            }
          })
          element.draggables.forEach((draggableRef:any)=>{
            const drag = draggableRef;
            let num1 = numDragsEach[drag.id]
            if (!num1) num1 = 0;
            let num2 = draggableRef.targetID2Amount[target.targetContext.id]
            if (!num2) num2 = 0;
            if (num1!=num2) {
              isCorrect=false;
            } 
          })
        })
      }
    } 
    else if (element.isTargetsOrdered) {
      let correct = 0;
      const orders:any[] = JSON.parse(JSON.stringify(element.correctOrders))
      const indicesUsed = new Map()
      targets.forEach((target)=>{
        let index = -1
        orders.forEach((order, i)=>{
          if (index!=-1 || indicesUsed.get(i)) return
          const arrOrder = order.order.split(',')
          if (arrOrder.length!=target.contents.length) {
            return
          }
          arrOrder.forEach((val:any, index:number)=>{
            arrOrder[index] = val.trim()
          })
          let isEqual = true
          arrOrder.forEach((val:any, index:number)=>{
            //console.log(val, target.contents[index].ref.id)
            if (val!=String(target.contents[index].ref.id)) {
              //console.log("not equal")
              isEqual = false
            }
          })
          if (isEqual) {
            index = i
          }
        })
        if (index!=-1) {
          indicesUsed.set(index, true)
          correct += 1
        }
      })
      if (correct == targets.length) {
        isCorrect = true
      } else {
        isCorrect = false
      }
    } 
    else {
      let correct = 0;
      const lookForCombos: any[] = [];
      if (element.correctCombinations) {
        element.correctCombinations.forEach((combo:any)=>{
          lookForCombos.push(JSON.parse(JSON.stringify(combo)))
        })
      }


      let id2amountBase:any = {}
      targets.forEach((targ)=>{
        targ.contents.forEach((drag:any)=>{
          if (drag.ref.id) {
            id2amountBase[drag.ref.id]=0
          }
        })
      })
      draggables.forEach((drag)=>{
        if (drag.ref.id) {
          id2amountBase[drag.ref.id]=0
        }
      })
      targets.forEach((targ)=>{
        const id2amount = JSON.parse(JSON.stringify(id2amountBase));
        targ.contents.forEach((drag:any)=>{
          if (!id2amount[drag.ref.id]) {
            id2amount[drag.ref.id]=1;
          } else {
            id2amount[drag.ref.id]++;
          }
        })
        draggables.forEach((drag)=>{
          const anID = drag.ref.id
          if (!id2amount[anID]) {
            id2amount[anID]=0;
          }
        })
        for (let i = 0;i<lookForCombos.length;i++) {
          let mapping = lookForCombos[i].id2amount;
          if (mapping && _.isEqual(mapping, id2amount)) {
            correct++
            lookForCombos.splice(i, 1)
            break;
          }
        }
      })
      if (correct==targets.length) {
        score = weight
        isCorrect = true;
      }
    }
    if (score === undefined){
      score = isCorrect ? weight : (element.isPartialWeightsEnabled ? roundNumeric(totalPartialScore)  : 0);
    }
    // conclude
    let isFilled:boolean;
    let isStarted = isAnyFilled;
    isAnyFilled = numPlaced >= 1;
    if ( element.reqMinimumPlacedTarget && element.reqMinimumPlacedTarget > 0){
      if (!element.perGroupCheck) {
        isFilled = numPlaced >= element.reqMinimumPlacedTarget ? true : false
      } else {
        isFilled = true
        if (element.reqMinimumPlacedTarget && element.reqMinimumPlacedTarget>0) {
          targets.forEach((targ)=>{
            if (targ.contents.length<element.reqMinimumPlacedTarget) {
              isFilled = false
            }
          })
        }
      }
      
    } else {
      isFilled = groupSize ? isAllFilled : isAnyFilled
    }

    let isScoringDisabled = false
    //check for isOptional
    if(element.isOptional){
      score = 0;
      //isStarted = true
      isFilled = true;
      isCorrect = undefined;
      isScoringDisabled = true
    }
    
    let isResponded = isAnyFilled; 
    let entryState: any = {
      type: ElementType.GROUPING,
      isCorrect,
      isStarted,
      isFilled: isFilled,
      isResponded,
      score, //targets.find((target:any) => target.groups.length) ? getElementWeight(element) : 0,
      weight,
      scoring_type: ScoringTypes.AUTO,
      draggables: draggables,
      targets: targets,
      isScoringDisabled
    };
    return entryState
}

// order

export const getOrderingState = (
  element:any,
  options:any,
  getAnswers: () => any,
) => {
  let isStarted = false;
  let isCorrect = true;
  let isFilled = true;
  let weight = getElementWeight(element);
  const optionWeight = getOptionWeightOrder(element);  
  let score = weight;
  
  let answers = getAnswers();
  let numWrong = 0
  let numPlaced = 0;
  answers.forEach((option: any, index: any)=>{
    if (element.options[index].isReadOnly) return;

    if (option.length==0) {
      isCorrect = false;
      //score = score - optionWeight;
      numWrong++;
      isFilled = false;
      return;
    }
    numPlaced += 1;
    isStarted = true;
    const currAnswer = option[0];
    
    if (currAnswer == undefined || currAnswer.optionId!=element.options[index].optionId) {
      isCorrect = false;
      numWrong++;
      //score = score - optionWeight;
    }
  })
  if (numWrong==0) {
    score = weight
  } else if (numWrong==answers.length) {
    score = 0
  } else {
    score = weight - optionWeight*numWrong
  }
  score = +score.toFixed(2);

  if (score<0 || (!element.enableProportionalScoring && score<weight)){
    score=0;
  }

  let isResponded = numPlaced >= 1;
  let entryState: any = {
    type: ElementType.ORDER,
    isCorrect,
    isStarted,
    isFilled,
    isResponded,
    answers,
    options: options,
    score,
    weight,
    scoring_type: ScoringTypes.AUTO, 
  }
  //console.log(entryState);
  return entryState; 
}

const getOptionWeightOrder = (element:any) => {
  const weight = getElementWeight(element);
  return (weight / element.options.filter((option: any) => !option.isReadOnly).length);
}

export const getInsertionState = (
  element: any,
  blocks: any[],
  draggables: any[]
) => {
  const entryId = element.entryId;
  const weight = getElementWeight(element);
  let score = weight;
  let missed = 0;
  let correct = 0;
  let numPlaced = 0;
  let isFilled = true;
  let isStarted = false;
  if (!element.isDragBetweenWords) {
    blocks.forEach((block)=>{
      if (block.isTarget) {
        if (block.contents && block.contents.length>0) {
          numPlaced += block.contents.length;
          const droppedObj = block.contents[0].ref;
          if (!block.ctx || droppedObj.id != block.ctx.id) {
            missed++;
          } else {
            correct++;
          }
          isStarted = true;
        } else {
          missed++;
          isFilled=false;
        }
      }
    })
  } else {
      blocks.forEach((block)=>{
        if (!block.isTarget) return;
        if (block.contents.length>0) {
          numPlaced += block.contents.length;
          if (!block.ctx) missed++;
          else {
            const droppedObj = block.contents[0].ref;
            if (droppedObj.id != block.ctx.id) missed++;
            else correct++;
          }
          isStarted = true;
        } else {
          if (block.isTarget && block.ctx) {
            missed++;
            isFilled=false;
          }
        }
      })
  }

  if (element.enableProportionalScoring) {
    const ratio = correct/(missed+correct);
    score = ratio*weight;
  } else if (missed>0) {
    score = 0;
  } else {
    score = weight;
  }
  //console.log(score);

  let isResponded = numPlaced >= 1;
  let entryState: any = {
    type: ElementType.INSERTION,
    isCorrect: score==weight,
    isStarted: isStarted, //this.targets.find((target:any) => target.groups.length) ? true : false,
    isFilled: isFilled, //this.targets.find((target:any) => target.groups.length) ? true : false,
    isResponded,
    score: score, //this.targets.find((target:any) => target.groups.length) ? getElementWeight(element) : 0,
    weight: getElementWeight(element),
    scoring_type: ScoringTypes.AUTO,
    draggables: draggables,
    targets: blocks, // this.textBlocks
  };
  return entryState
}

// mcq
export const getDropdownStateMCQ = (element: any, forceSelectOption: number) => {
  if (!isDisplayStyleDropdown(element)){
    return;
  }
  let isCorrect = false;
  let isFilled = false;
  let i = forceSelectOption;
  let option;
  if (i>=0){
    // if (this.hasDefaultDropDownText()) option = this.element.options[i-2];
    // else option = this.element.options[i]
    option = element.options[i]
    if (option){
      isFilled = true
      isCorrect = option.isCorrect;
    }
  }

  const weight = getElementWeight(element);
  const entryState = ensureStateMCQ(element);
  entryState.isCorrect = isCorrect;
  entryState.isFilled = isFilled;
  entryState.isStarted = true;
  if (isFilled){
    entryState.selections = [{
      i: +i, 
      id: option.optionId, 
      elementType: ElementType.TEXT, 
      content: option.content,
    }]
  }
  else{
    entryState.selections = [];
  }
  entryState.score =  isCorrect ? weight : 0 ;
  entryState.isResponded = entryState.selections.length >= 1;
  return entryState
}

export const getStateMCQ = (element: any, currentSelections: any[]) => {
  const entryState = ensureStateMCQ(element);
  const weight = getElementWeight(element);
  let score =  element.isOptional ? undefined :weight ;
  let totalPartialScore = 0;
  let isCorrect = false;
  let isOneCorrectOptionSelected = false;

  let selections = currentSelections.map( selection => {
    let i = indexOf(element.options, selection);
    if (selection.isCorrect){
      isOneCorrectOptionSelected = true
    }
    const content = getMcqContent(selection);
    return {
      i,
      id: selection.optionId,
      elementType: selection.elementType,
      content
    }
  });
  let selectionsMap = new Map<number,any>();
  updateSelectionsMapMCQ(selections, selectionsMap);

  let isFilled = selections.length > 0;
  if (element.isMultiSelect && element.maxOptions && selections.length < element.maxOptions){
    isFilled = false;
  }
  
  score = 0
  let numCorrect = 0;
  let numLimittedCorrect = 0;
  let numLimittedMax = 0;
  let numTotalMax = 0;
  let numIncorrect = 0;
  element.options.forEach((option: any, i: number) => {
    const expected = !!option.isCorrect;
    const input = !!selectionsMap.has(i);
    const isMatch = (expected === input);
    numTotalMax ++
    if (expected){
      numLimittedMax ++
      if (input){
        numLimittedCorrect ++ 
        numCorrect ++
        totalPartialScore += option.partialWeight || 0
      }
    }
    else if (isMatch){
        numCorrect ++
        totalPartialScore += option.partialWeight || 0
    }

    if (!isMatch){
      numIncorrect ++
    }

  })
    
  // conclude the scoring


  if (element.isMultiSelect){
    
    if (element.enableProportionalScoring){
      if (element.maxOptions){
        if (numLimittedMax){
          score = weight * (numLimittedCorrect/numLimittedMax)
        }
      }
      else{
        if (numTotalMax){
          score = weight * (numCorrect/numTotalMax)
        }
      }
    }
    else if (element.isEnablePartialweights){
      let fullMarks = numLimittedMax == numLimittedCorrect && selections.length == numLimittedMax
      score = fullMarks ? weight : totalPartialScore
    }
    else{ // deduction by default
      if (numIncorrect < weight){
        score = weight - numIncorrect
      }
    }
    isCorrect = (score == weight)
  }
  else {
    if (numLimittedCorrect > 0){
      isCorrect = true;
      score = weight;
    }
    else{
      isCorrect = false
      score = 0;
    }
  }

  // console.log({
  //   maxOptions: !!this.element.maxOptions,
  //   isMultiSelect: this.element.isMultiSelect,
  //   enableProportionalScoring: this.element.enableProportionalScoring,
  //   numIncorrect,
  //   numLimittedCorrect,
  //   numLimittedMax,
  //   numCorrect,
  //   numTotalMax,
  //   score,
  //   isCorrect
  // })

  score = Math.max(0, score)

  entryState.isCorrect = element.isOptional? undefined :isCorrect;
  entryState.isStarted = true;
  entryState.isFilled = isFilled;
  entryState.isResponded = selections.length >= 1;
  entryState.selections = selections;
  entryState.score = score;
  entryState.weight = weight;

  return entryState
}

function ensureStateMCQ(element:any) {
  let entryState = {
    type: 'mcq',
    isCorrect: <any>false,
    isStarted: false,
    isFilled: false,
    isResponded: false,
    selections: <any[]>[],
    score: 0,
    weight: getElementWeight(element),
    scoring_type: ScoringTypes.AUTO, 
  }
  return entryState;
}

const getMcqContent = (selection: any) => {return elementToMatrixHeader(selection)?.content};

function updateSelectionsMapMCQ(selections: any, selectionsMap: any) {
  for(const selection of selections) {
    selectionsMap.set(selection.i, true);
  }
}

const isDisplayStyleDropdown = (element:any) => { return element.displayStyle === McqDisplay.DROPDOWN; }

// selection table
export const getSelectionTableState = (element: any, testTakerAnswers: {value:boolean}[][]) => {
  //console.log(this.questionState);
    const checkMarks = captureCheckState(testTakerAnswers);
    const result = getNewChecks(element, testTakerAnswers);
    // const isResponded = this.questionState[entryId].isResponded || result.isFilled;
    let entryState:any = {
      type: ElementType.SELECT_TABLE,
      isStarted: result.isStarted,
      isCorrect: result.isCorrect,
      isFilled: result.isFilled,
      isResponded: result.isResponded,
      score: result.score,
      isCustomGrading: false,
      checkMarks,
      weight: getElementWeight(element),
      scoring_type: ScoringTypes.AUTO,
    }
    return entryState;
}

const captureCheckState = (testTakerAnswers: {value:boolean}[][]) => {
  const capturedGridState:{value: boolean}[][] = [];
  const colIndex = new Map();
  testTakerAnswers.forEach((row, rowIndex)=>{
    const capturedRowState:{value:boolean}[] = [];
    capturedGridState.push(capturedRowState);
    row.forEach((cell, colIndex)=>{
      capturedRowState.push({
        value: !! cell.value
      })
    })
  })
  return capturedGridState
}

const getNewChecks = (element:any, testTakerAnswers: {value:boolean}[][]) => {
  const weight = getElementWeight(element);
  const scoreResult = getScoreResult(element, testTakerAnswers);
  const {isFilled, isStarted, isResponded, percentCorrect} = <IScoreResult> scoreResult;
  const isCorrect = percentCorrect === 1
  const score = element.enableProportionalScoring ?  weight*percentCorrect : (isCorrect ? 1 : 0);
  
  return { score, isStarted, isFilled, isCorrect, isResponded};
}

const getScoreResult = (element: any, testTakerAnswers: {value:boolean}[][]) => {
  switch(getDerivedLegacyScoreMode(element)) {
    case ESelectionTableScoreMode.ROW_COL :
      return getRowColScore(element, testTakerAnswers);
    case ESelectionTableScoreMode.CELL :
      return getCellScore(element, testTakerAnswers);
  }
}

const getCellScore = (element: any, testTakerAnswers: {value:boolean}[][]) : IScoreResult => {
  const checkBoxGridDef = element.checkBoxRows;
  const firstRowDef = checkBoxGridDef?.[0];
  const numRows = checkBoxGridDef.length;
  const numCols = firstRowDef.length;

  let numIncorrect = 0;
  let numCorrect = 0;
  let isStarted = false;
  let isFilled = false;
  let isResponded;
  let percentCorrect;

  let onePerRow = true;
  let onePerCol = true;

  let numChecked = 0;

  let requiredAnswers;
  if(element.isMaxDenoScoring) {
    requiredAnswers = getNumRequiredAnswersSelectTable(element);
  }

  const numCells:number = numRows * numCols;
    const colIndex = new Map();
    testTakerAnswers.forEach((row, r)=>{
      let rowChecked = false;
      row.forEach((col, c)=>{
        if (testTakerAnswers[r][c].value) {
          rowChecked = true;
          isStarted = true;
          colIndex.set(c, true);
          numChecked++;
        } 
        
        if (element.checkBoxRows[r]?.[c] && element.checkBoxRows[r][c].value !== testTakerAnswers[r][c].value) {
          numIncorrect++;
        } else if(element.checkBoxRows[r]?.[c].value === testTakerAnswers[r][c].value && testTakerAnswers[r][c].value) {
          numCorrect++;
        }
      })
      if (!rowChecked) onePerRow = false;
    })

    if(getScoreMode(element) === ESelectionTableScoreMode.LEGACY) {
      const colLen = testTakerAnswers[0].length;
      for (let i = 0;i<colLen;i++) {
        if (!colIndex.has(i)) onePerCol = false;
      }
      let isColReqFulfilled = false;
      let isRowReqFulfilled = false;
      
      if (!element.isMustHaveOnePerCol || onePerCol) isColReqFulfilled = true;
      if (!element.isMustHaveOnePerRow || onePerRow) isRowReqFulfilled = true;
      if (isStarted && isColReqFulfilled && isRowReqFulfilled) isFilled = true;
    } else {
      if(requiredAnswers) {
        isFilled = numChecked === requiredAnswers;
      } else {
        isFilled = isStarted;
      }
    }

    isResponded = numChecked >= 1;
    let denom = requiredAnswers || numCells;
    let numerator = requiredAnswers ? numCorrect : (denom - numIncorrect);
    percentCorrect =  Math.max(0, numerator/denom);
    return {
      isStarted,
      isFilled,
      isResponded,
      percentCorrect
    }
}

const getRowColScore = (element: any, testTakerAnswers: {value:boolean}[][]) : IScoreResult => {
  const checkBoxGridDef = element.checkBoxRows;
  const firstRowDef = checkBoxGridDef[0];
  const numCols = firstRowDef.length;
  const gridSelections = captureCheckState(testTakerAnswers);
  const numRows = checkBoxGridDef.length;
  const gridAnswerKey = element.checkBoxRows;

  let isMaxDenoScoring;
  
  let requiredPerCol = Math.min(numRows, element.maxCheckedPerCol);
  let requiredPerRow = Math.min(numCols, element.maxCheckedPerRow);

  if(getScoreMode(element) === ESelectionTableScoreMode.LEGACY) {
    isMaxDenoScoring = element.isMaxDenoScoring && requiredPerCol > 1 && !(numRows > 1);
  } else {
    isMaxDenoScoring = element.isMaxDenoScoring;
  }
  
  let isStarted = false;
  let isFilled = false;
  let isResponded = false;
  let percentCorrect = 0;

  let numRowsFilled = 0;
  let numCorrect = 0;
  let numPlaced = 0;
  if (numRows > 0){
    for (let iRow=0; iRow<numRows; iRow++){
      let numFilledInRow = 0;
      for(let iCol=0; iCol < numCols; iCol++) {
        const cellKey = gridAnswerKey[iRow][iCol];
        
        const cellSelection = gridSelections[iRow][iCol];

        if(!isMaxDenoScoring || cellKey.value ) {
          if(!!cellKey.value === cellSelection.value){
            numCorrect++;
          }
        }
        
        if (cellSelection.value){
          numFilledInRow++;
          numPlaced += 1;
        }
      }

      if ((requiredPerRow && numFilledInRow >= requiredPerRow) || (!requiredPerRow && numFilledInRow > 0) ){
        numRowsFilled++;
      } 
    }
    isStarted = numRowsFilled > 0;
    isFilled = requiredPerCol ?  (numRowsFilled  === requiredPerCol) : (numRowsFilled === numRows);

    let rowColDenom = numRows;
    let colRowDenom = numCols;
    if(isMaxDenoScoring) {
      rowColDenom =  Math.min(numRows, (requiredPerCol ?? numRows));
      colRowDenom = Math.min(numCols, (requiredPerRow ?? numCols));
    }
    const denom = rowColDenom*colRowDenom;
    percentCorrect = denom === 0 ? 0 : (numCorrect/denom);
    isResponded = numPlaced >= 1;
  }

  return {
    isStarted,
    isFilled,
    isResponded,
    percentCorrect
  }  
}

const getNumRequiredAnswersSelectTable = (element:any) => {
  let numRequired = 0;
  for(const row of element.checkBoxRows) {
    for(const col of row) {
      if(col.value) {
        numRequired++;
      }
    }
  }

  return numRequired;
}

export const getElementWeight = (element: any) => {
    const weight = element.scoreWeight;
    if (weight === 0) {
        return weight;
    }
    if (!weight) {
        return 1;
    }
    return +weight;
};