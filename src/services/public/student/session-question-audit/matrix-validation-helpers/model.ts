//! This file is to support matrix validation in RT audits, please don't change unless updating one of the other files in this folder

export enum ElementType {
    ORDER = "order",
    INSERTION = "insertion",
    GROUPING = "Grouping",
    MOVEABLE_DND = "moveable_dnd",
    BLANK = 'BLANK',
    BLANK_DEPRECIATED = '',
    MCQ = "mcq",
    CUSTOM_MCQ = "custom_mcq",
    SELECT_TABLE = "select_table",
    TEXT = "TEXT",
    IMAGE = "IMAGE",
    MATH = "MATH",
    TABLE = "TABLE",
    AUDIO = "audio",
}


export enum OrderMode {
    TARGET = "target",
    REORDER = "reorder"
}
export const scoreMatrixElementTypes: ElementType[] = [
    ElementType.GROUPING,
    ElementType.MOVEABLE_DND,
    ElementType.INSERTION,
    ElementType.ORDER,
    ElementType.MCQ,
    ElementType.CUSTOM_MCQ,
    ElementType.SELECT_TABLE
]

export enum ScoringTypes {
    AUTO = "AUTO",
    REVIEW = "REVIEW",
    MANUAL = "MANUAL"
}

export enum GradingType {
    PLUS_MINUS = "plus_minus",
    REDUCTION = "reduction",
    NORMAL = "normal"
}

export enum McqDisplay {
    BUBBLE = "bubble",
    DROPDOWN = "dropdown",
    CUSTOM_DROPDOWN = "custom_dropdown",
    FREEFORM = "freeform",
    GRID = "grid",
    HORIZONTAL = "horizontal",
    LIKERT = "likert",
    VERTICAL = "vertical",
    WRAP = "wraparound",
}

export enum MatrixHeaderType {
    TEXT = "TEXT",
    IMAGE = "IMAGE",
    MATH = "MATH",
    TABLE = "TABLE",
}

export enum ESelectionTableScoreMode {
    ROW_COL = 'ROW',
    CELL = 'CELL',
    LEGACY = 'LEGACY'
}

export interface IScoreResult {
    isFilled: boolean,
    isStarted: boolean,
    isResponded: boolean,
    percentCorrect: number,
  }