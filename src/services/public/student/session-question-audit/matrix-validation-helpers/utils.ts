//! This file is to support matrix validation in RT audits, please don't change unless updating one of the other files in this folder
//! Or if these functions are modified in client (they probably shouldnt)

import { ElementType, ESelectionTableScoreMode, MatrixHeaderType } from "./model";

/**
 * Function used to retrieve the respondable elements from a question
 * @param question 
 * @returns an array of question elements that are respondable
 */
export const getQuestionRespondableDeep = (question: any) => {
    const elements = [];
    const item = getQuestionContent(question);
    const entryOrder = question?.entryOrder;
    for (let i = 0; i < entryOrder.length; i++) {
        const entryId = entryOrder[i];
        const targetElem = deepFind(item, 'entryId', entryId);
        elements.push(targetElem)
    }
    return JSON.parse(JSON.stringify(elements));
}

export const getQuestionContent = (question: any, lang: string = 'en'): any[] => {
    let content: any[] = []
    if (lang === 'en') {
        content = question.content;
    }
    else {
        content = question.langLink ? question.langLink.content : [];
    }
    return content;
}

export const deepFind = (obj: any, targetKey: string | number, targetVal: any): any => {
    if (!obj || typeof obj !== 'object') return null;
    if (obj[targetKey] === targetVal) return obj;
    return Object.values(obj).reduce((acc, val) => acc || deepFind(val, targetKey, targetVal), null);
}

export const roundNumeric =(num: number) => { 
    return Math.round(100*num)/100; // fixed to 2 decimal places for now
}

/**
 * Transform an element into content to be rendered inside the row/column header cell
 * Can be reused by different `getMatrix...` functions
 */
export const elementToMatrixHeader = (element: any) => {
  let type, content;
  switch (element.elementType) {
    case ElementType.TEXT:
      type = MatrixHeaderType.TEXT;
      content = element.caption || element.content;
      break;
    case ElementType.IMAGE:
      type = MatrixHeaderType.IMAGE;
      content = element.images?.default?.image?.url || element.url;
      break;
    case ElementType.MATH:
      type = MatrixHeaderType.MATH;
      content = element.latex || element.content;
      break;
    case ElementType.TABLE:
      type = MatrixHeaderType.TEXT;
      content = `Table ${element.entryId ? element.entryId : ""}`;
      break;
    case ElementType.AUDIO:
      type = MatrixHeaderType.TEXT;;
      content = `Audio ${element.entryId ? element.entryId : ""}`;
      break;
    default:
      type = MatrixHeaderType.TEXT;
      content = ""
      break;
  }
  return { type, content }
}

export const getDerivedLegacyScoreMode = (element: any) => {
  const scoreMode = getScoreMode(element);
  if (scoreMode !== ESelectionTableScoreMode.LEGACY) {
    return scoreMode;
  }

  const isOneSelPerRow = (+element.maxCheckedPerRow === 1);
  const isOneSelPerCol = (+element.maxCheckedPerCol === 1);
  const isRowsAsEntries = isOneSelPerRow && !isOneSelPerCol;

  if (isRowsAsEntries) {
    return ESelectionTableScoreMode.ROW_COL
  } else {
    return ESelectionTableScoreMode.CELL
  }
}

export const getScoreMode = (element: any) => {
  if (!element.scoreMode) {
    return ESelectionTableScoreMode.LEGACY
  }

  return element.scoreMode;
}

export const indexOf = (arr:any[], t:any) => {
  let i = -1;
  arr.forEach((_t, _i) => {
    if (_t === t){
      i = _i;
    }
  });
  return i;
}
