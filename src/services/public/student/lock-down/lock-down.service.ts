// Initializes the `public/student/lock-down` service on path `/public/student/lock-down`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { LockDown } from './lock-down.class';
import hooks from './lock-down.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/lock-down': LockDown & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/lock-down', new LockDown(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/lock-down');

  service.hooks(hooks);
}
