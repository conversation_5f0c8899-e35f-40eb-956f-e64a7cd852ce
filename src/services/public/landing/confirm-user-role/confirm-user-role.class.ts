import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawReadReporting } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ConfirmUserRole implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query || !id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {uid, group_id} = (<any>params).query;
    if (!uid || !group_id) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const ref_ur_id = +id;

    const userRolesGrpIdWithSameAccountType = await this.getUserRolesByRefUrId(ref_ur_id, uid)
    
    let confirmedUserRoles = []
    
    for(const userRole of userRolesGrpIdWithSameAccountType) {
      if (!userRole.is_confirmed) {
        const user_role = await this.app
          .service('db/write/user-roles')
          .patch( userRole.id, {
            is_confirmed: 1,
            confirmed_on: dbDateNow(this.app),
          })
        confirmedUserRoles.push(user_role)
      }
    }
  
    return confirmedUserRoles[0]
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {uid, group_id} = (<any>params).query;
    if(!uid || !group_id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const revoked_by_uid = await currentUid(this.app, params);
    const ref_ur_id = +id;

    const userRolesToRevoke = await this.getUserRolesByRefUrId(ref_ur_id, uid)

    for(const userRole of userRolesToRevoke) {
      await this.app
        .service('db/write/user-roles')
        .patch( userRole.id, {
          is_revoked: 1,
          revoked_on: dbDateNow(this.app),
          revoked_by_uid
        })
    }
    
    return {}
  }

  /**
   * Get all user roles by urgat.account_type, uid and group id
   * @param uid 
   * @param group_id 
   * @param account_type 
   * @returns 
   */
  async getUserRolesByRefUrId(ref_ur_id:number, uid:number|Id): Promise<any[]> {
    const userRoles = await dbRawReadReporting(this.app, {ref_ur_id, uid}, `
     -- 0.035s
     select ur.* 
       from user_roles ur_ref
       join u_groups ug on ug.id = ur_ref.group_id
       join user_role_group_account_types urgat_ref on urgat_ref.group_type = ug.group_type 
        and urgat_ref.role_type = ur_ref.role_type
       join user_role_group_account_types urgat on urgat.route_template = urgat_ref.route_template -- roles with the same route_template share 1 mosaic panel
       join user_roles ur on ur.role_type = urgat.role_type
        and ur.group_id = ur_ref.group_id
        and ur.uid = ur_ref.uid
      where ur_ref.id = :ref_ur_id
        and ur.uid = :uid
        and ur_ref.is_revoked = 0
        and ur.is_revoked = 0
    ;`)

    return userRoles
  }
}
