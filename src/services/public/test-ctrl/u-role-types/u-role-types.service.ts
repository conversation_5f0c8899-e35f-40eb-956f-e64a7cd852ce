// Initializes the `public/test-ctrl/u-role-types` service on path `/public/test-ctrl/u-role-types`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { URoleTypes } from './u-role-types.class';
import hooks from './u-role-types.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/u-role-types': URoleTypes & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/u-role-types', new URoleTypes(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/u-role-types');

  service.hooks(hooks);
}
