import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import logger from '../../../../logger';
import { dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

interface Data {}

interface ServiceOptions {}

const ALLOWED_COLUMNS = ['requires_mfa']

export class URoleTypes implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    const knex = this.app.get('knexClientRead');

    const IS_MFA_DISABLED = await getSysConstNumeric(this.app, 'IS_MFA_DISABLED')

    const u_role_types = await  knex('u_role_types')
        .where('is_shown', 1)
        .select('*');
    
    return {
      IS_MFA_DISABLED,
      u_role_types
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  /**
   * Patch u_role_types
   *  - only patches the columns in ALLOWED_COLUMNS
   *  - only shown records can be patched
   */
  async patch (role_type: NullableId, data: any, params?: Params): Promise<any> {
    if (!data) {
      return [];
    }

    const updateData = Object.keys(data)
      .filter(key => ALLOWED_COLUMNS.includes(key))
      .reduce((obj: any, key) => {
        obj[key] = data[key];
        return obj;
      }, {});
    logger.info('U_ROLE_TYPE_LOG', { role_type, updateData });
    
    const knex = this.app.get('knexClientWrite');

    return knex
      .update(updateData)
      .from('u_role_types')
      .where({
        role_type,
        is_shown: 1
      })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
