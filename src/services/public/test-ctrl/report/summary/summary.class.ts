import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { Knex } from 'knex';
const _ = require('lodash')

interface Data {}

interface IAutoReportSummary {
  slug: string;
  pendingAutoReports: number;
  oldestPendingAutoReport: string;
  newestPendingAutoReport: string;
}

interface ISummary {
  autoReportSummary: IAutoReportSummary[];
}

interface ServiceOptions {}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<ISummary> {

    // For official test windows, only get pending report asrg for students in non QA schools
    // 1.16 sec
    const records = await this.getSummaryReportQuery()
      .join('user_roles as ur', 'ur.uid', 'asrg.student_uid')
      .join('schools as s', 's.group_id', 'ur.group_id')
      .join('school_districts as sd', 'sd.group_id', 's.schl_dist_group_id')
      .andWhere('tw.is_qa', 0)
      .andWhereNot('sd.is_sample', 1) as IAutoReportSummary[];

    // 0.2 sec
    const recordsQA = await this.getSummaryReportQuery()
      .andWhere('tw.is_qa', 1) as IAutoReportSummary[];

    const fullRecords = records.concat(recordsQA)
    const autoReportSummary = _.orderBy(fullRecords, ['test_window_id', 'slug'])

    return { autoReportSummary };
  }

  getSummaryReportQuery() {
    const knex = this.app.service('db/read/auto-student-report-generations').knex as Knex;
    return this.app.service('db/read/auto-student-report-generations').db()
      .from('auto_student_report_generations as asrg')
      .select(
        'twtdar.slug', 'twtdar.test_window_id',
        knex.raw('count(asrg.id) as pendingAutoReports'),
        knex.raw('min(asrg.created_on) as oldestPendingAutoReport'),
        knex.raw('max(asrg.created_on) as newestPendingAutoReport')
      )
      .join('test_attempts as ta', function (){
        this.on('ta.id', '=', 'asrg.test_attempt_id')
        .andOn('ta.uid', '=', 'asrg.student_uid')
      })
      .join('test_window_td_alloc_rules as twtdar', 'twtdar.id', 'ta.twtdar_id')
      .join('test_windows as tw', 'tw.id', 'twtdar.test_window_id')
      .where('asrg.is_revoked', 0)
      .whereNull('asrg.student_pdf_generated_on')
      .groupBy('twtdar.test_window_id', 'twtdar.slug')
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
