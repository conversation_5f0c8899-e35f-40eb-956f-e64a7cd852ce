import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle, dbBulkInsert, dbRawReadReporting, dbRawWrite} from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { expectedSetVariants, SetTypes, TEST_CTRL_MWI_PROCESS_TYPE, SYNC_WARNING_TYPES, DATA_SOURCE_OPTION_IDS} from './types'
import { TestFormConstructionMethod } from '../../test-auth/test-design-question-versions/test-design-question-versions.class';
import _ from 'lodash';

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class SyncScoringAuthoring implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /** Find what remains to be synced from authoring to scoring, to be completed and confirmed on the UI before syncing */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {

    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const {marking_window_id, is_ensure_no_process_running} = params.query;

    //Throw error if a sync is in progress
    if (+!!is_ensure_no_process_running) await this.ensureNoProcessRunning(marking_window_id);

    // < 0.1 sec
    // Get the language and test window or target marking window
    const mwTwRecord = await dbRawReadSingle(this.app, {marking_window_id}, `
      select mw.lang, mwtw.test_window_id
      from marking_window_test_window mwtw
      join marking_windows mw
        on mw.id = mwtw.marking_window_id
      where mwtw.marking_window_id = :marking_window_id
      and (mwtw.is_material = 0 or mwtw.is_material is null);
    `)
    if (!mwTwRecord) throw new Errors.NotFound('MISSING MARKING WINDOW SETUP')
    const {test_window_id, lang} = mwTwRecord;

    // < 0.1 sec
    //Get the test designs of the test window
    const twtdarRecords = await dbRawRead(this.app, {test_window_id, lang}, `
      select test_design_id from
      test_window_td_alloc_rules twtdar
      where twtdar.test_window_id = :test_window_id
      and twtdar.lang = :lang
      and twtdar.is_questionnaire = 0
      and twtdar.is_sample = 0;
    `)
    const test_design_ids = twtdarRecords.map(td => td.test_design_id)
    if (!test_design_ids.length) throw new Errors.NotFound('No test designs published in this test window yet.')

    // < 0.5 sec
    // Get all human-scoreable items in these test designs
    // Reading selection items are currently not included in TQR, therefore look for candidate passages by choosing:
    // - An item with a label matching the "Associated text" of the OR item
    // - The OR item and passage should be in the same framework
    // - The passage should not be archived
    const scoreableQuestionsFromAuth = await dbRawReadReporting(this.app, { test_design_ids, lang }, `
      select tqr.question_id as item_id
      , IFNULL(tqr.is_field_trial, 0) AS is_item_ft
      , tq_passage.id as reading_item_id
      , tqsi.scales as scoring_scales
      , tq.question_label as item_label
      , tq_passage.question_label as reading_item_label
      , tq.question_set_id as item_set_id
      , tq_passage.question_set_id as reading_item_set_id
      , framework_set.id as framework_set_id
      , framework_set.name as framework_set_name
      , tqr.test_form_id
      from test_question_register tqr
      join test_questions tq
        on tq.id = tqr.question_id
      join test_question_scoring_info tqsi
        on tqr.test_design_id in (:test_design_ids)
        and tqr.lang = :lang
          and tqsi.item_id = tqr.question_id
          and tqsi.lang = tqr.lang
      left join test_designs td
        on td.id = tqr.test_design_id
      left join temp_question_set framework_set
        on framework_set.id = td.source_item_set_id
      left join test_questions tq_passage
        on FIND_IN_SET(tq_passage.question_set_id, REPLACE(REPLACE(framework_set.test_design_question_sets, '[', ''), ']', ''))
        and tq_passage.is_archived = 0
        and JSON_UNQUOTE(JSON_EXTRACT(tq.config, '$.readSel')) = tq_passage.question_label
      left join test_question_scoring_info tqsi_dup
        on tqsi_dup.item_id = tqr.question_id
        and tqsi_dup.lang = tqr.lang
        and tqsi_dup.id < tqsi.id
        and tqsi_dup.is_human_scored = 0
      where tqsi.is_human_scored = 1 and tqsi_dup.id is null
      group by tq.id, tq_passage.id
    `);


    // < 0.1 sec
    // Pull all score profile and batch policies (small number of records)
    const scoreProfiles = await 
    dbRawRead(this.app, {}, `select id, short_name, skill_code from marking_score_profiles;`)

    const warnings: {item_id: number, item_label: number, item_set_id: number, type: SYNC_WARNING_TYPES}[] = [];
    const expectedMwis: any[] = [];
    scoreableQuestionsFromAuth.forEach(qRecord => {
      const scoring_scales: {score_profile_id: number, scale_id: number}[] = JSON.parse(qRecord.scoring_scales)
      // Flag if expected scoring scales were not found
      if (!scoring_scales || !scoring_scales.length){
        const newWarning = {
          item_id: qRecord.item_id,
          item_label: qRecord.item_label,
          item_set_id: qRecord.item_set_id,
          type: SYNC_WARNING_TYPES.NO_SCALES
        }
        warnings.push(newWarning)
      }
      // Flag an error with old data where the scales string is stored incorrectly - fixed by re-adding the scales in authoring again
      else if (!Array.isArray(scoring_scales)){
        const newWarning = {
          item_id: qRecord.item_id,
          item_label: qRecord.item_label,
          item_set_id: qRecord.item_set_id,
          type: SYNC_WARNING_TYPES.MISFORMATTED_SCALES
        }
        warnings.push(newWarning)
      } 
      // Otherwise find what marking window items are expected to exist based on the scales
      else {
        scoring_scales?.forEach(scale => {
          const scoreProfile = scoreProfiles.find(p => p.id == scale.score_profile_id)
          if (scoreProfile){
            const expectedMwi = {
              ...qRecord,
              score_profile_id: scoreProfile.id,
              score_profile_name: scoreProfile.short_name,
              skill_code: scoreProfile.skill_code,
              scoring_scales: undefined,
            }
            // Just in case, if it's not a reading item ensure it doesn't get an associated passage (shouldn't be necessary)
            if (expectedMwi.skill_code !== "OR"){
              expectedMwi.reading_item_id = undefined;
              expectedMwi.reading_item_label = undefined;
              expectedMwi.reading_item_set_id = undefined;
            }
            expectedMwis.push(expectedMwi)
          }
        })
      }
    })

    // Sort the list of expected items
    expectedMwis.sort((a, b) => {
      // First priority: is_item_ft (0 first, 1 last)
      if (a.is_item_ft !== b.is_item_ft) {
        return a.is_item_ft - b.is_item_ft;
      }
      // Second priority: skill_code ("OR" last)
      if (a.skill_code === 'OR' && b.skill_code !== 'OR') {
        return 1; // Move "OR" to the last
      } else if (a.skill_code !== 'OR' && b.skill_code === 'OR') {
        return -1; // Keep other items before "OR"
      }
      // Third priority: item_id (least to greatest)
      return a.item_id - b.item_id;
    });

    const newMwis: any[] = [];
    const updatedMwis: any[] = [];
    const mwisToRemove: any[] = [];

    // Pull the MWIs already synced to this marking window
    const currentMwiRecords = await this.app.service('public/test-ctrl/marking-window-items').find({
      ...params, query: {marking_window_id, lang}
    })
    
    // Compare authoring to current MWIs
    expectedMwis.forEach(expMwi => {
      // Find if MWI expected by the authoring data already exists
      const currMwiMatch = currentMwiRecords.find((currMwi: any) => currMwi.item_id == expMwi.item_id && currMwi.score_profile_id == expMwi.score_profile_id)
      if (currMwiMatch){
        // Flag if anything changed in authoring that is not in the scoring system, otherwise ignore item as already synced
        const updatedProperties = ['skill_code', 'reading_item_id', 'reading_item_label', 'reading_item_set_id', 'is_item_ft'].filter(prop => expMwi[prop] != currMwiMatch[prop])
        if (updatedProperties.length){
          const updatedProps: any = {};
          updatedProperties.forEach(prop => updatedProps[prop] = expMwi[prop])
          updatedMwis.push({
            ...currMwiMatch,
            framework_set_id: expMwi.framework_set_id,
            framework_set_name: expMwi.framework_set_name,
            updatedProps
          })
        }
      } else {
        // If not flag as a new MWI to be created
        newMwis.push(expMwi)
      }
    })

    // Look for any MWIs in the scoring system that are no longer expected by authoring, flag as MWIs to remove
    currentMwiRecords.forEach((currMwi: any) => {
      const expMwiMatch = expectedMwis.find((expMwi: any) => currMwi.item_id == expMwi.item_id && currMwi.score_profile_id == expMwi.score_profile_id)
      if (!expMwiMatch){
        mwisToRemove.push(currMwi)
      }
    })

    // For new MWIs, also find potential sources of material data to select from
    await this.app.service('public/test-ctrl/marking-window-items').appendPotentialMaterialDataSourcesToMwis(newMwis, marking_window_id, lang)

    return {newMwis, updatedMwis, mwisToRemove, warnings};
  }

  /** Get the process log records (to sync data or adjust material data) for the target marking window */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (marking_window_id: Id, params?: Params): Promise<any> {
    // < 0.2 sec
    return dbRawRead(this.app, {marking_window_id}, `
      select 
      log.*
      , CONCAT(u.first_name, " ", u.last_name) as created_by
      from marking_window_item_setup_process_log log
      left join users u
        on u.id = log.created_by_uid
      where log.marking_window_id = :marking_window_id
      order by id desc;
    `)

  }

  /** Create, edit, or remove marking window items to sync authoring to scoring */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const { marking_window_id } = params.query;
    const current_uid = await currentUid(this.app, params);

    //Throw error if another sync in progress
    await this.ensureNoProcessRunning(marking_window_id);

    if (!data.newMwis?.length && !data.updatedMwis?.length && !data.mwiIdsToRemove?.length) throw new Errors.BadRequest('No input provided to sync')

    if (data.newMwis.some((mwi: any) => +mwi.source_mwi_id && !mwi.materialDataSourceOption))  throw new Errors.BadRequest('Missing material data source option')

    const dataLog = {inputData: data}
    // Start new record to keep track of sync process
    const processLog = await this.app.service('db/write/marking-window-item-setup-process-log').create({
      marking_window_id,
      log_type: TEST_CTRL_MWI_PROCESS_TYPE.SYNC,
      data_log: JSON.stringify(dataLog),
      created_by_uid: current_uid
    });

    (async () => {
      try {
        await this.createNewItems(data.newMwis, marking_window_id, current_uid, processLog.id, dataLog);
        await this.updateItems(data.updatedMwis, marking_window_id, current_uid, processLog.id, dataLog);
        await this.removeItems(data.mwiIdsToRemove, marking_window_id, current_uid, processLog.id, dataLog);

        //Log process as completed in db
        this.app.service('db/write/marking-window-item-setup-process-log').patch(processLog.id, {
          is_completed: 1
        })
      }
      catch (err: any) {
        //Log error in db
        const errorDetails = {
          message: err.message,
          stack: err.stack,
          name: err.name
        };
        this.app.service('db/write/marking-window-item-setup-process-log').patch(processLog.id, {
          is_error: 1,
          error_log: JSON.stringify(errorDetails)
        })

      }
    })();

    return {};
  }

  /** Create new marking window items, clone material data from a source MWI if specified, log progress */
  async createNewItems(newMwis: any[], marking_window_id: number, current_uid: number, logRecordId: number, dataLog: any){
    if (!newMwis.length) return;

    dataLog.newMwis = [];

    const markingWindowRecord = await this.app.service('db/read/marking-windows').get(marking_window_id)
    const { group_id } = markingWindowRecord;

    for (const newMwi of newMwis){
      const newMwiData: any = {
        slug: newMwi.slug,
        caption: newMwi.caption,
        item_id: newMwi.item_id,
        is_item_ft: newMwi.is_item_ft,
        score_profile_id: newMwi.score_profile_id,
        skill_code: newMwi.skill_code,
        batch_alloc_policy_id: newMwi.batch_alloc_policy_id,
        reading_item_id: newMwi.reading_item_id,
        created_by_uid: current_uid,
        marking_window_id,
        group_id
      }

      const {source_mwi_id, materialDataSourceOption} = newMwi
      if (source_mwi_id){

        const sourceMwi = await this.app.service('db/read/marking-window-items-v2').get(source_mwi_id);
        const mwiFieldsToCopy: string[] = [
          'anchors_updated_by_uid',
          'anchors_updated_on',
          'anchors_url',
          'guide1_updated_by_uid',
          'guide1_updated_on',
          'guide1_url',
          'guide_file_path',
          'rubric_file_path',
          'rubric_updated_by_uid',
          'rubric_updated_on',
          'rubric_url'
        ];
        mwiFieldsToCopy.forEach(fieldName => newMwiData[fieldName] = sourceMwi[fieldName]);
        newMwiData.material_data_source_mwi_id = source_mwi_id; 
      }

      //Create new MWI and log it
      const newMwiCreated = await this.app.service('db/write/marking-window-items-v2').create(newMwiData)
      dataLog.newMwis.push(`Created MWI ID ${newMwiCreated.id} - ${new Date().toString()}`)
      await this.updateLog(logRecordId, dataLog);
      await this.ensureNoProcessManualError(logRecordId);

      // If source MWI provided, bring over material data
      if (source_mwi_id) {
        //Clone data into TAQR cache and log this
        const numMtcRecords = await this.cloneTaqrCache(newMwiCreated.id, source_mwi_id, marking_window_id, materialDataSourceOption as DATA_SOURCE_OPTION_IDS)
        dataLog.newMwis.push(`MWI ID ${newMwiCreated.id} - inserted ${numMtcRecords} marking_taqr_cache records - ${new Date().toString()}`)
        await this.updateLog(logRecordId, dataLog);
        await this.ensureNoProcessManualError(logRecordId);
  
        //Clone data into Mrsel and log this
        const numMrselRecords = await this.cloneMrsel(newMwiCreated.id, source_mwi_id, materialDataSourceOption as DATA_SOURCE_OPTION_IDS)
        dataLog.newMwis.push(`MWI ID ${newMwiCreated.id} - inserted ${numMrselRecords} marking_response_selections records - ${new Date().toString()}`)
        await this.updateLog(logRecordId, dataLog);
        await this.ensureNoProcessManualError(logRecordId);
  
        //Clone sets and log this
        const numSetRecords = await this.cloneSets(newMwiCreated.id, source_mwi_id)
        dataLog.newMwis.push(`MWI ID ${newMwiCreated.id} - inserted ${numSetRecords} marking_response_set  records - ${new Date().toString()}`)
        await this.updateLog(logRecordId, dataLog);
        await this.ensureNoProcessManualError(logRecordId);
  
        //Clone set selection data and log this
        const numSetSelRecords = await this.cloneSetSelections(newMwiCreated.id, source_mwi_id)
        dataLog.newMwis.push(`MWI ID ${newMwiCreated.id} - inserted ${numSetSelRecords} marking_response_set_selections records - ${new Date().toString()}`)
        await this.updateLog(logRecordId, dataLog);
        await this.ensureNoProcessManualError(logRecordId);
      };

      // Create any sets (if necessary after cloning material data)
      const numNewSetRecords = await this.createMissingSets(newMwiCreated.id, !!+newMwiCreated.is_item_ft, current_uid)
      dataLog.newMwis.push(`MWI ID ${newMwiCreated.id} - created ${numNewSetRecords} new marking_response_set records - ${new Date().toString()}`)
    }
  }


  /** Update the progress of the sync in the API log */
  async updateLog(logRecordId: number, dataLog: any){
    return this.app.service('db/write/marking-window-item-setup-process-log').patch(logRecordId, {
      data_log: JSON.stringify(dataLog)
    })
  }

  /** Update MWIs with new settings and log this */
  async updateItems(updatedMwis: any[], marking_window_id: number, current_uid: number, logRecordId: number, dataLog: any){
    dataLog.updatedMwis = [];
    for (const mwi of updatedMwis){
      await this.app.service('db/write/marking-window-items-v2').patch(mwi.id, mwi.patchData);

      dataLog.updatedMwis.push(`Updated MWI ID ${mwi.id} - ${new Date().toString()}`)
      await this.updateLog(logRecordId, dataLog);
      await this.ensureNoProcessManualError(logRecordId);
    }
  }

  /** Unlink outdated MWIs and log this */
  async removeItems(mwiIdsToRemove: any[], marking_window_id: number, current_uid: number, logRecordId: number, dataLog: any){
    dataLog.removedMwis = [];
    for (const mwiId of mwiIdsToRemove){
      // Unlink the MWI from the marking window
      const data = {
        marking_window_id: -marking_window_id
      }
      await this.app.service('db/write/marking-window-items-v2').patch(mwiId, data)
      dataLog.removedMwis.push(`Unlinked MWI ID ${mwiId} - ${new Date().toString()}`)
      await this.updateLog(logRecordId, dataLog);
      await this.ensureNoProcessManualError(logRecordId);

      // Unlink scorer tasks from the MWI so that they don't still see them on the dashboard (which is not window-specific)
      const numTasksUnlinked = await this.unlinkScorerTasks(mwiId)
      dataLog.removedMwis.push(`Unlinked ${numTasksUnlinked} marker tasks for MWI ID ${mwiId} - ${new Date().toString()}`)
      await this.updateLog(logRecordId, dataLog);
      await this.ensureNoProcessManualError(logRecordId);
    }
  }

  /** set a running process to error (edge case if a process stalls for a while, can force-flag it so that new one can start) */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params) throw new Errors.BadRequest('MISSING_PARAMS');
    const current_uid = await currentUid(this.app, params)
    const {is_error} = data;
    return this.app.service('db/read/marking-window-item-setup-process-log').patch(id, {
      is_error,
      error_log: +is_error ? `Manually set to error by uid ${current_uid}` : null,
    })
  }

  async ensureNoProcessManualError(logRecordId: number){
    const logRecord = await this.app.service('db/read/marking-window-item-setup-process-log').get(logRecordId);
    if (logRecord.is_error == 1){
      const errorLog = JSON.parse(logRecord.error_log)
      throw new Errors.NotAcceptable(`Cannot continue process after manual error set: ${errorLog.message}`)
    }
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  /** Throw error if another sync process for this marking window is in progress */
  async ensureNoProcessRunning(marking_window_id: number){
    // < 0.1 sec
    const isMwSyncRunning = await dbRawReadSingle(this.app, {marking_window_id}, `
      select * from marking_window_item_setup_process_log
      where marking_window_id = :marking_window_id
      and is_completed = 0
      and is_error = 0;
    `);
    if (isMwSyncRunning) throw new Errors.Unavailable('Another sync process is currently running for this marking window.')
  }

  /** Clone relevant marking_taqr_cache records from source item into new item*/
  async cloneTaqrCache(targetMwiId: number, sourceMwiId: number, marking_window_id: number, materialDataSourceOption: DATA_SOURCE_OPTION_IDS){
    // < 0.5 sec (background process)
    // Pull taqr cache records for responses that have data in range finding
    const mtcRecords = await dbRawReadReporting(this.app, {sourceMwiId}, `
      select distinct 
      mtc.taqr_id 
      , mtc.schl_group_id
      from marking_window_items_v2 mwi
      ${materialDataSourceOption !== DATA_SOURCE_OPTION_IDS.ALL_TAQRS ? `
        join marking_response_selections mrs 
          on mrs.window_item_id = mwi.id
      ` : ``}
      ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_2 ? `and ((mrs.tag1 is not null and mrs.tag1 != '') or (mrs.score_option_id is not null and mrs.score_option_id != ''))` : ''}
      ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_3 ? `and (mrs.score_option_id is not null and mrs.score_option_id != '')` : ''}
      ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_4 ? `and (mrs.tag2 is not null and mrs.tag2 != '')` : ''}
      ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.IS_IN_SET ? `
      join marking_response_set_selections setsel
        on setsel.selection_id = mrs.id
      join marking_response_set rset
        on rset.id = setsel.set_id
        and (rset.is_revoked = 0 or rset.is_revoked is null)
      ` : ''}
      join marking_taqr_cache mtc 
        on mtc.mwi_id = mwi.id 
         ${materialDataSourceOption !== DATA_SOURCE_OPTION_IDS.ALL_TAQRS ? 'and mtc.taqr_id = mrs.taqr_id ' : ''}
      where mwi.id = :sourceMwiId
    `)

    // Import the data for the new item as material records
    mtcRecords.forEach(record => {
      record.marking_window_id = marking_window_id,
      record.mwi_id = targetMwiId
      record.is_material = 1
    })

    // Insert to the db in chunks
    for(let mtcRecordsChunk of _.chunk(mtcRecords, 1000)){
      await dbBulkInsert(this.app, 'marking_taqr_cache', mtcRecordsChunk);
    }

    return mtcRecords.length
  }

  /** Clone relevant marking_response_selections records from source item into new item
   * Choose whether all or only specific records are cloned depending on option chosen by user
  */
  async cloneMrsel(targetMwiId: number, sourceMwiId: number, materialDataSourceOption: DATA_SOURCE_OPTION_IDS){
    // < 0.2 sec (background process)
    // Pull mrsel records
    const mrselRecords = await dbRawReadReporting(this.app, {sourceMwiId}, `
      select
      mrs.taqr_id 
      , mrs.tag1 
      , mrs.tag2 
      , mrs.score_option_id 
      , is_expert_score
      , mrs.rationale 
      , mrs.rationale_hash 
      , mrs.last_touched_on
      , mrs.last_touched_by_uid 
      , mrs.is_excluded 
      , mrs.is_included 
      , mrs.response_set_num 
      , mrs.response_set_order 
      , mrs.response_set_id 
      , mrs.id cloned_from_id 
      from marking_window_items_v2 mwi
      join marking_response_selections mrs 
        on mrs.window_item_id = mwi.id
        ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_2 ? `and ((mrs.tag1 is not null and mrs.tag1 != '') or (mrs.score_option_id is not null and mrs.score_option_id != ''))` : ''}
        ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_3 ? `and (mrs.score_option_id is not null and mrs.score_option_id != '')` : ''}
        ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.RF_STAGE_4 ? `and (mrs.tag2 is not null and mrs.tag2 != '')` : ''}
      ${materialDataSourceOption == DATA_SOURCE_OPTION_IDS.IS_IN_SET ? `
      join marking_response_set_selections setsel
        on setsel.selection_id = mrs.id
      join marking_response_set rset
        on rset.id = setsel.set_id
        and (rset.is_revoked = 0 or rset.is_revoked is null)
      ` : ''}
      where mwi.id = :sourceMwiId
    `)

    //Import it as data for the new item
    mrselRecords.forEach(record => {
      record.window_item_id = targetMwiId
    })

    // Insert to the db in chunks
    for(let mrselRecordsChunk of _.chunk(mrselRecords, 1000)){
      await dbBulkInsert(this.app, 'marking_response_selections', mrselRecordsChunk);
    }
    return mrselRecords.length
  }

  /** Create the expected sets for the item depending on if it's FT, first check any previous sets created by cloning and only create missing ones */
  async createMissingSets(mwiId: number, isFt: boolean, currentUid: number){
    const expectedSetsForType = isFt ? expectedSetVariants.isFt : expectedSetVariants.isNotFt
    // < 0.1 sec (background process)
    const existingSets = await dbRawRead(this.app, {mwiId}, `
      select set_type_id, set_type_variant_num
      from marking_response_set
      where window_item_id = :mwiId
      and (is_revoked = 0 or is_revoked is null);
    `);

    const missingSetRecords: {set_type_id: number, set_type_variant_num: number, created_by_uid: number}[] = []
    for (const expectedTypeId in expectedSetsForType){
      for (const expectedVariantNum of expectedSetsForType[expectedTypeId as SetTypes]){
        const doesExist = existingSets.some(set => set.set_type_id == expectedTypeId && set.set_type_variant_num == expectedVariantNum)
        if (!doesExist){
          missingSetRecords.push({set_type_id: +expectedTypeId, set_type_variant_num: +expectedVariantNum, created_by_uid: currentUid})
        }
      }
    }

    const setRecordsToCreate = missingSetRecords.map(r => {
      return {
        window_item_id: mwiId,
        created_by_uid: currentUid,
        set_type_id: r.set_type_id,
        set_type_variant_num: r.set_type_variant_num
      }
    });

    if (!setRecordsToCreate.length) return 0;
    await dbBulkInsert(this.app, 'marking_response_set', setRecordsToCreate);
    return setRecordsToCreate.length;
  }

  /** Clone relevant marking_response_set records from source item into new item*/
  async cloneSets(targetMwiId: number, sourceMwiId: number){
    // < 0.1 sec (background process)
    // Pull sets of the source item
    const setRecords = await dbRawReadReporting(this.app, {sourceMwiId}, `
    select 
    mrs.set_type_id
    , mrs.set_type_variant_num
    , mrs.created_by_uid
    , mrs.id cloned_from_set_id
    , mrs.is_revoked
    from marking_window_items_v2 mwi
    join marking_response_set mrs 
      on mrs.window_item_id = mwi.id
      and (mrs.is_revoked is null or mrs.is_revoked =0)
    where mwi.id = :sourceMwiId
    `)
    // Import as sets for the new item
    setRecords.forEach(record => {
      record.window_item_id = targetMwiId
    })

    // Insert to the db in chunks
    for(let setRecordsChunk of _.chunk(setRecords, 1000)){
      await dbBulkInsert(this.app, 'marking_response_set', setRecordsChunk);
    }

    return setRecords.length
  }

  /** Clone relevant marking_response_set_selections records from source item into new item*/
  async cloneSetSelections(targetMwiId: number, sourceMwiId: number){
    // < 0.1 sec (background process)
    // Pull sets selection records of the source item
    const setSelRecords = await dbRawReadReporting(this.app, {sourceMwiId, targetMwiId}, `
      select mrs_new.id set_id
        , mrsel_new.id selection_id
        , mrss.taqr_id
        , mrss.response_set_num
        , mrss.response_set_order
        , mrss.id cloned_from_id
      from marking_window_items_v2 mwi
      join marking_response_set_selections mrss  
        on mrss.window_item_id = mwi.id 
      join marking_response_set mrs_new 
        on mrs_new.cloned_from_set_id = mrss.set_id 
        and mrs_new.window_item_id = :targetMwiId
      join marking_response_selections mrsel_new 
        on mrsel_new.cloned_from_id = mrss.selection_id 
        and mrsel_new.window_item_id = :targetMwiId
      where mwi.id = :sourceMwiId
    `);

    // Import as set selections for the new item
    setSelRecords.forEach(record => {
      record.window_item_id = targetMwiId
    })

    // Insert to the db in chunks
    for(let setRecordsChunk of _.chunk(setSelRecords, 1000)){
      await dbBulkInsert(this.app, 'marking_response_set_selections', setRecordsChunk);
    }

    return setSelRecords.length;
  }


  /** Unlink scorer tasks for a given MWI */
  async unlinkScorerTasks(mwiId: number){
    // < 0.1 sec (background process)
    const tasksForMwi = await dbRawReadReporting(this.app, {mwiId}, `
      select id from marking_item_marker_tasks
      where marking_window_item_id = :mwiId;        
    `)
    const taskIds = tasksForMwi.map(t => t.id);
    for(let taskIdsChunk of _.chunk(taskIds, 500)){
      await dbRawWrite(this.app, [taskIdsChunk], `
      UPDATE marking_item_marker_tasks
      SET marking_window_item_id = -abs(marking_window_item_id)
      , uid = -abs(uid)
      where id in (:taskIdsChunk);
      `)
    }
    return taskIds.length;
  }

}
