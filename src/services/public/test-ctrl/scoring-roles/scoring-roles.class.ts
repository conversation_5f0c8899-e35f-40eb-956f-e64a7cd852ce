import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export enum MWRoleType {
  SCOR_LEAD = 'mrkg_ctrl',
  SCOR_CAS = 'mrkg_sensi',
  SCAN_REVW = 'scan_revw'
}

export class ScoringRoles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const {emails, marking_window_id} = params.query;
    // Client limits total string length to 500 so that's likely < 50 emails total
    // For 50 emails query takes < 7 secs
    const emailList = emails.split(",").map((email: string) => email.trim())
    const users = await dbRawRead(this.app, {emailList}, `
      select u.id uid,
            u.contact_email
      from users u
      where u.contact_email in (:emailList)
        and u.is_claimed = 1
    ;`);

    if (!users.length) {
      throw new Errors.NotFound('NO_USER_FOUND');
    }

    const targetRoleTypes = [MWRoleType.SCOR_LEAD, MWRoleType.SCOR_CAS, MWRoleType.SCAN_REVW]
    const uids = users.map(u => u.uid)

    // < 0.1 sec
    const userRoles = await dbRawRead(this.app, {uids, marking_window_id, targetRoleTypes}, `
      select ur.id ur_id
      , ur.role_type
      , ur.group_id
      , ur.uid
      from marking_windows mw
      join user_roles ur
        on mw.group_id = ur.group_id
        and ur.role_type in (:targetRoleTypes)
        and ur.is_revoked = 0
      where ur.uid in (:uids)
      and mw.id = :marking_window_id
    ;`)

    users.forEach(user => {
      user.userRoles = userRoles.filter(ur => ur.uid == user.uid)
    })

    return users;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        uid,
        role_type,
        group_id,
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      const roleCreateFields = {
        uid,
        role_type,
        group_id,
        created_by_uid: currentUID
      }
      return await this.app
        .service('db/write/user-roles')
        .create(roleCreateFields);  
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (id && params) {
      const currentUID = await currentUid(this.app, params);
      
      return await this.app
      .service('db/write/user-roles')
      .patch(id, {
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid: currentUID
      });

    }
    throw new Errors.BadRequest('MISSING_ROLE_ID');
  }
}
