import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application, ServiceTypes } from '../../../../declarations';
import { ITestWindow } from '../../../db/schemas/test_windows.schema';
import { Knex } from 'knex';
import { dbDateOffsetDays } from '../../../../util/db-dates';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { Errors } from '../../../../errors/general';
import { MPT_TEST_CTRL_GROUP_ID } from '../../../../constants/test-ctrl-constant';
//import { MPT_TEST_CTRL_GROUP_ID } from '../../test-admin/test-session-setup/test-windows/test-windows.class';

interface Data {}

interface ServiceOptions {}

interface ITestWindowSessionsCreated {
  test_window_id: number,
  num_sessions_created: number,
}

interface ITallyRecord {
  tally: number,
}

interface ISummary {
  test_windows: ITestWindowSummary[],
  accounts: {
    num_test_applicants: IAccountTally,
    num_session_bookings: IAccountTally,
    num_session_waitlisters: IAccountTally,
    num_accomm_pending: IAccountTally,
    num_institutions: IAccountTally,
    num_inst_mngrs: IAccountTally,
    num_accomm_coords: IAccountTally,
    num_invigs: IAccountTally,
    num_cert_bodys: IAccountTally,
    num_test_ctrls: IAccountTally,
  }
}
interface IAccountTally {
  r: number,
  l30: number,
  p?: number,
}
interface ITestWindowSummary {
  test_window_id: number,
  test_window_name: IInlineTranslation,
  test_window_date_start: string,
  test_window_date_end: string,
  num_sessions_created: number,
  num_sessions_administered: number,
  num_results_pending_release: number,
  is_allow_new_bookings: boolean,
}

interface IInlineTranslation {
  [index:string] : string,
}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {

    const MAX_WINDOWS_TO_LIST:number = 30;

    const testWindows:ITestWindowSummary[] = [];
    const testWindowRef:Map<number, ITestWindowSummary> = new Map();
    const tw_records = <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('is_archived', 0)
      .where('test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(MAX_WINDOWS_TO_LIST);
    tw_records.forEach(twr => {
      const test_window_id = <number> twr.id
      const testWindowSummary:ITestWindowSummary = {
        test_window_id,
        test_window_name: twr.title ? JSON.parse(twr.title) : null,
        test_window_date_start: <string> twr.date_start,
        test_window_date_end: <string> twr.date_end,
        is_allow_new_bookings: twr.is_allow_new_bookings ? true: false,
        num_sessions_created: 0,
        num_sessions_administered: 0,
        num_results_pending_release: 0,
      }
      testWindowRef.set(test_window_id, testWindowSummary);
      testWindows.push(testWindowSummary);
    })

    const tw_sessions_created = <ITestWindowSessionsCreated[]> await this.app
      .service('db/read/test-window-sessions-created')
      .db()
      .where('test_window_test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(MAX_WINDOWS_TO_LIST);
    tw_sessions_created.forEach(tw => {
      if (testWindowRef.has(tw.test_window_id)){
        const testWindowSummary = <ITestWindowSummary> testWindowRef.get(tw.test_window_id);
        testWindowSummary.num_sessions_created = tw.num_sessions_created;
      }
    })

    const getTally = async (query:Knex.QueryBuilder) : Promise<number> => {
      const tallyRecords = <ITallyRecord[]> await query.limit(1);
      if (tallyRecords && tallyRecords.length){
        return tallyRecords[0].tally;
      }
      return 0;
    }

    const getTableTally = async (query:Knex.QueryBuilder, isLast30:boolean=false) : Promise<IAccountTally> => {
      query = query.count('id as tally');
      const query_r = query.clone();
      const query_l30 = query.clone().where('created_on', '>', dbDateOffsetDays(this.app, -30));
      return {
        r: await getTally(query_r),
        l30: await getTally(query_l30),
      }
    }
    const getRoleTallySummary = async (groupType:DBD_U_GROUP_TYPES, role_type:DBD_U_ROLE_TYPES) : Promise<IAccountTally> => {
      return {
        r: await getRoleTally(groupType, role_type, true, false),
        l30: await getRoleTally(groupType, role_type, true, true),
        p: await getRoleTally(groupType, role_type, false, false),
      }
    }
    const getRoleTally = (groupType:DBD_U_GROUP_TYPES, role_type:DBD_U_ROLE_TYPES, isClaimed:boolean=true, isLast30:boolean=false) => {
      let query = this.app
        .service('db/read/user-role-active-w-group-type')
        .db()
        .where('group_type', groupType)
        .where('role_type', role_type)
        .where('is_revoked', 0)
        .where('is_claimed', isClaimed ? 1 : 0);
      if (isLast30){
        query = query.where('created_on', '>', dbDateOffsetDays(this.app, -30));
      }
      query = query.count('id as tally');
      return getTally( query );
    }

    const g_inst = DBD_U_GROUP_TYPES.mpt_institution;
    const g_sys = DBD_U_GROUP_TYPES.mpt_sys;
    const g_tc = DBD_U_GROUP_TYPES.mpt_test_controller;
    const g_ts = DBD_U_GROUP_TYPES.mpt_test_session;

    const num_institutions = getTableTally(
      this.app
        .service('db/read/institutions')
        .db()
        .where('is_active', 1)
        .where('is_shown', 1)
    );
    const num_inst_mngrs          = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_inst_mngr);
    const num_accomm_coords       = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_accomm_coord);
    const num_invigs              = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_test_admin_invig);
    const num_test_applicants     = getRoleTallySummary(g_sys, DBD_U_ROLE_TYPES.mpt_applicant);
    const num_session_bookings    = getRoleTallySummary(g_ts, DBD_U_ROLE_TYPES.mpt_booked_applicant);
    const num_session_waitlisters = getRoleTallySummary(g_ts, DBD_U_ROLE_TYPES.mpt_waiting_list_applicant);
    const num_accomm_applicants   = getRoleTallySummary(g_inst, DBD_U_ROLE_TYPES.mpt_accomm_applicant);
    const num_test_ctrls          = getRoleTallySummary(g_tc, DBD_U_ROLE_TYPES.test_ctrl_lias_test_admin);

    const res:ISummary = {
      test_windows: testWindows,
      accounts: {
        num_test_applicants: await num_test_applicants,
        num_session_bookings: await num_session_bookings,
        num_session_waitlisters: await num_session_waitlisters,
        num_accomm_pending:  await num_accomm_applicants,
        num_institutions: await num_institutions,
        num_inst_mngrs: await num_inst_mngrs,
        num_accomm_coords: await num_accomm_coords,
        num_invigs: await num_invigs,
        num_cert_bodys: { r: 0, l30: 0, p: 0, },
        num_test_ctrls: await num_test_ctrls,
      }
    }
    return res;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
