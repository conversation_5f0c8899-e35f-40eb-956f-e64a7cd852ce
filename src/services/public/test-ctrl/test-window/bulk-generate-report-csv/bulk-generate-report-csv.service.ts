// Initializes the `public/test-ctrl/test-window/bulk-generate-report-csv` service on path `/public/test-ctrl/test-window/bulk-generate-report-csv`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { BulkGenerateReportCsv } from './bulk-generate-report-csv.class';
import hooks from './bulk-generate-report-csv.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/bulk-generate-report-csv': BulkGenerateReportCsv & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/bulk-generate-report-csv', new BulkGenerateReportCsv(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/bulk-generate-report-csv');

  service.hooks(hooks);
}
