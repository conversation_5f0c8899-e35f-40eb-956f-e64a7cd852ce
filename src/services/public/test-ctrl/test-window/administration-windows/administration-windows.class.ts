import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class AdministrationWindows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {allowInactive} = params.query
      const records = await dbRawRead(this.app, [], `
        select tw.id
            , tw.test_ctrl_group_id 
            , tw.type_slug 
            , tw.title 
            , tw.is_active 
            , tw.is_qa 
            , tw.is_bg 
            , tw.date_start 
            , tw.date_end 
            , tw.created_on 
            , group_concat(mw.marking_window_id) marking_window_id
            , tw.academic_year
            , tw.isr_version
            , tw.auto_close_start_on
            , tw.auto_close_end_on
            , tw.auto_close_grace_period_m
            , tw.auto_close_default_extension_m
        from test_windows tw
        left join marking_window_test_window mw on mw.test_window_id = tw.id
        where 1=1
          ${allowInactive !== 'true' ? `and tw.is_active = 1` : ''}
          and tw.is_bg = 0 -- keeping this on for EQAO for now
        group by tw.id
        order by tw.id desc
      `);
      // for sake of test controller, treat bg as QA
      records.forEach(record => {
        // // not applicable for EQAO
        // if (record.is_bg == 1){
        //   record.is_qa = 1;
        // }
      })
      return records
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
