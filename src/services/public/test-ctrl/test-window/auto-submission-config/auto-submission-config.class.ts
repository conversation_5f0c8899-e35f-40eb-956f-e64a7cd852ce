import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { currentUid } from '../../../../../util/uid';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';

interface Data {
    [key: string]: any,
}

interface ServiceOptions {}

export class AutoSubmissionConfig implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (testWindowId: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params || !params.query || Object.keys(params.query).length === 0) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    if (params.query.auto_submission_ON === null || params.query.auto_submission_ON === undefined) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);
    const auto_submission_ON = JSON.parse(params.query.auto_submission_ON)

    if( auto_submission_ON === false ){
      await this.app
      .service('db/write/test-windows')
      .patch(testWindowId, {
        auto_close_start_on: null,
        auto_close_end_on: null,
        auto_close_grace_period_m: null,
        auto_close_default_extension_m: null,
        last_activated_on: dbDateNow(this.app),
        last_activated_by_uid: uid,
      });

      return { auto_submission_ON };
    }

    const {auto_close_start_on, auto_close_end_on, auto_close_grace_period_m, auto_close_default_extension_m} = data

    if(!auto_close_start_on.includes(':') || !auto_close_end_on.includes(':') || auto_close_grace_period_m === null || auto_close_default_extension_m === null){
      throw new Errors.BadRequest('MISSING_VALUE')
    }

    if(auto_close_grace_period_m < 0 || auto_close_default_extension_m < 0){
      throw new Errors.BadRequest('INVALID_VALUE')
    }

    if(auto_close_grace_period_m >=  auto_close_default_extension_m){
      throw new Errors.BadRequest('INVALID_GRACE_PERIOD_AND_EXTENSION_PERIOD')
    }

    await this.app
      .service('db/write/test-windows')
      .patch(testWindowId, {
        auto_close_start_on,
        auto_close_end_on,
        auto_close_grace_period_m,
        auto_close_default_extension_m,
        last_activated_on: dbDateNow(this.app),
        last_activated_by_uid: uid,
      });

    return { auto_submission_ON };
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
