import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { currentUid } from '../../../../../util/uid';
import { getPropVals } from '../../../../../util/param-sanitization';
import { ITestWindow } from '../../../../db/schemas/test_windows.schema';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import * as DBT from "../../../../../types/db-types";

interface Data {}

const ALLOWED_PROPS = [
  'title',
  'is_active',
  'date_start',
  'date_end',
  'test_design_id',
  'notes',
  'is_allow_new_ts',
  'is_allow_new_bookings',
  'is_allow_results_tt',
  'is_multi_attempt',
  'num_attempts',
  'attempts_intvl',
  'is_invig_unsubmit',
  'is_invig_taketest',
  'is_archived',
  'is_allow_test_centre',
  'is_allow_classroom',
  'is_allow_remote',
  'is_allow_mobile_tether',
  'is_allow_video_conference',
  'is_allow_appeals',
  'show_appeal_result',
  'test_centre_name_slug',
  'classroom_name_slug',
  'remote_name_slug',
  'type_slug',
  'window_code',
  'is_qa',
  'is_bg',
  'irt_ready',
  'is_closed',
  'reg_lock_on',
  'show_inactive_tw_report',
  'academic_year',
  'is_alt_file_access',
  'show_report_to_Board',
  'is_require_stu_validate',
  'max_stu_validate_attempts',
  'is_allow_session_schedule',
  'is_allow_multiple_module_check',
  'is_allow_check_ctrl_summaries',
  'show_isr_num_q',
  'unsubmit_request_opt2_link_to_acc',
  'is_allow_session_schedule',
  'isr_version',
  'is_end_window_isr'
]

interface ServiceOptions {}

export class Config implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (testWindowId: Id, data: Data, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params);
      const patchFields:Partial<ITestWindow> = getPropVals(data, ALLOWED_PROPS);
      this.associateFieldsUpdate(patchFields, uid)  //update assoicate field 

      // If unchecking "Alternative materials accessible", and it's a non-QA window, proceed to un-finalize alt materials for this assessment type
      if (patchFields['is_alt_file_access'] == 0) {
        const twRecord = await this.app.service('db/read/test-windows').get(testWindowId)
        if (twRecord && !twRecord.is_qa) {
          await this.app.service('public/test-ctrl/test-window/alt-material-access').patch(testWindowId, {}, params)
        }
      }

      await this.app
        .service('db/write/test-windows')
        .patch(testWindowId, {
          ... patchFields,
          last_activated_on: dbDateNow(this.app),
          last_activated_by_uid: uid,
        });

      return true;
    }
    throw new Errors.BadRequest();
  }

   /**",
   * add associate update field to patchFields when test window config changed
   * @param patchFields test window's update field
   * @param currentUid current test controller uid
   * @returns null
   */
  associateFieldsUpdate(patchFields:any, currentUid: number){
    const patchFieldsKeys = Object.keys(patchFields);
    for (const patchFieldsKey of patchFieldsKeys) {
      const value = patchFields[patchFieldsKey];
      switch(patchFieldsKey){
        case 'show_appeal_result':
          if(+value === 1){
            patchFields["show_appeal_result_on"] = dbDateNow(this.app)
            patchFields["show_appeal_result_by_uid"] = currentUid
          }
          if(+value === 0){
            patchFields["show_appeal_result_on"] = null
            patchFields["show_appeal_result_by_uid"] = null
          }
          break
        default:
          //do nothing
          break
      }
    }
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
