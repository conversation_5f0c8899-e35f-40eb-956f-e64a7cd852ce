const _ = require('lodash');
const moment = require('moment');
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
interface Data {
  id?:number,
  test_ctrl_group_id?: number,
  num_sessions_created?: number,
  num_sessions_administered?: number,
}


interface ITestWindow {
  id: number,
  title: string,
  is_active: number,
  date_start: string,
  date_end: string,
  test_design_id: number,
  notes: string,
  created_on: string,
  create_by_uid:number,
  last_activated_on:string,
  last_activated_by_uid: number,
  test_ctrl_group_id:number,
  reg_lock_on: string,
  show_appeal_result_by_uid:number
}

interface ServiceOptions {}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<any> {
    if (id){

      const testWindowRecord = <ITestWindow> await this.app
        .service('db/read/test-windows')
        .get(id);

      const assessmentPriceRecord = await dbRawRead(this.app, [id], `
        select price_per_student as ap_price_per_student
             , description as ap_description
             , created_on as ap_created_on
             , created_by_uid as ap_created_by_uid 
          from assessment_prices 
         where is_revoked = 0 
           and test_window_id = ?
      ;`)

      //fetch associate assessment component records 
      const assessmentComponentRecord = await this.getAssessmentComponentRecord(id)

      //fetch associate test window td component records 
      const testWindowTdComponentRecord = await this.getTestWindowTdComponentRecord(id)

      //fetch associate test window component reporting category records 
      const componentReportingCategoryRecord = await this.getTestWindowComponentReportingCategoryRecord(id)

      //fetch associate school semester records 
      const schoolSemesterRecord =await this.getSchoolSemesterRecord(id)

      //fetch basic info about scoring windows associated with this test window
      const markingWindowsRecord = await this.getMarkingWindowsForTestWindow(id)
      
      //fetch all pre- created test sessions
      const testWindowPreCreatedTestSessions = await this.app.service("public/test-ctrl/test-window/questionnaire-ts-config").getQestionnaireTsInfo(+id);

      //fetch all distinct auto_approved_date 
      const AutoApproveDates = await this.fetchAutoApproveDates(id);

      let testSessionStats;
      try {
        testSessionStats = await this.app
          .service('db/read/test-window-sessions-created')
          .get(id);
      }
      catch(e){
        testSessionStats = {
          num_sessions_created: 0,
          num_sessions_administered: 0,
        }
      }

      //get show_appeal_result_by's name if its not null
      const show_appeal_result_by_uid = testWindowRecord.show_appeal_result_by_uid
      const  show_appeal_result_by = await this.getShowPppealResultByName(show_appeal_result_by_uid)
    
      const returnValue = {
        ... testWindowRecord,
        ... assessmentPriceRecord[0], //should only have 1 assessment price only
        num_sessions_created: testSessionStats.num_sessions_created,
        num_sessions_administered: testSessionStats.num_sessions_administered,
        show_appeal_result_by,
        AutoApproveDates
      }

      return {returnValue, assessmentComponentRecord, testWindowTdComponentRecord, componentReportingCategoryRecord, schoolSemesterRecord, markingWindowsRecord, testWindowPreCreatedTestSessions} ;
    }
    return {};
  }

  async getShowPppealResultByName (show_appeal_result_by_uid:number) {
    let show_appeal_result_by;
    if(show_appeal_result_by_uid){
      const show_appeal_result_by_record = await dbRawRead(this.app, {show_appeal_result_by_uid}, `
        select concat(first_name, " ", last_name) as show_appeal_result_by from users where id = :show_appeal_result_by_uid
      ;`)
      show_appeal_result_by =  show_appeal_result_by_record[0].show_appeal_result_by
    }
    return show_appeal_result_by
  }  
  
  /**",
   * Fetch associate school semesters records for a test window
   * @param test_window_id  
   * @returns list of school semesters
   */
  async getSchoolSemesterRecord(test_window_id: any){
    const schoolSemesterRecord = <any>await dbRawRead(this.app, {test_window_id}, `
      select * from school_semesters where test_window_id = :test_window_id
    ;`)
    return schoolSemesterRecord
  }

  /**",
   * Fetch associate assessment component records for a test window
   * @param test_window_id  
   * @returns list of assessment components
   */
  async getAssessmentComponentRecord(test_window_id: any){
    const assessmentComponentRecord = <any>await dbRawRead(this.app, {test_window_id}, `
      select * from assessment_components where test_window_id = :test_window_id
    ;`)
    return assessmentComponentRecord
  }

   /**",
   * Fetch associate test window td component records for a test window
   * @param test_window_id  
   * @returns list of test window td components
   */
  async getTestWindowTdComponentRecord(test_window_id: any){
    const testWindowTdComponentRecord = <any>await dbRawRead(this.app, {test_window_id}, `
      select * from test_window_td_components where test_window_id = :test_window_id
    ;`)
    return testWindowTdComponentRecord
  }

  async getMarkingWindowsForTestWindow(test_window_id: any){
    // < 0.1 sec
    const markingWindowsRecord = await dbRawRead(this.app, {test_window_id}, `
      select 
      mw.id
      , mw.name
      , mw.start_on
      , mw.end_on
      , mw.lang
      from marking_windows mw
      join marking_window_test_window mwtw
        on mwtw.marking_window_id = mw.id
        and mwtw.test_window_id = :test_window_id
    where mwtw.is_material = 0 or mwtw.is_material is null;
    `)
    return markingWindowsRecord;
  }

  /**",
   * Fetch associate test window component Reporting Category records for a test window
   * @param test_window_id  
   * @returns list of test window component Reporting Category
   */
  async getTestWindowComponentReportingCategoryRecord(test_window_id: any){
    const componentReportingCategoryRecord = <any>await dbRawRead(this.app, {test_window_id}, `
    -- took 93ms to run on qc4
      select * from tw_component_reporting_categories where test_window_id = :test_window_id
    ;`)
    return componentReportingCategoryRecord
  }

  /**",
   * Fetch all distinct auto_approved_date 
   * @param data {test_window_id}
   * @returns AutoApproveDates list as string
   */
  async fetchAutoApproveDates(test_window_id:any){
    //fetch all distinct auto_approved_date 
    const AutoApproveDatesRecord = await dbRawRead(this.app, {test_window_id}, `
      select group_concat(distinct created_on) as AutoApproveDates from school_student_asmt_info_signoffs where is_revoked = 0 and is_auto_approve = 1 and test_window_id = :test_window_id
    ;`)
    const AutoApproveDates = AutoApproveDatesRecord[0]?.AutoApproveDates || null
    return AutoApproveDates
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }
    const create_by_uid = await currentUid(this.app, params);
    const test_ctrl_group_id = data.test_ctrl_group_id;
    const dateStart = moment().add(7, 'days');
    const dateEnd = moment(dateStart).add(7, 'days');
    const createFields = {
      test_ctrl_group_id: test_ctrl_group_id,
      is_qa: 1, // default to opening them in the background
      is_allow_new_ts: 0, // dont allow test sessions to be created until a test design is selected
      title: '{}',
      notes: '{}',
      date_start: dateStart.utc().format(),
      date_end: dateEnd.utc().format(),
      is_allow_appeals: 0,
      create_by_uid
    }
    const newRecord = await this.app
      .service('db/write/test-windows')
      .create(createFields);
    return {id: newRecord.id};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
