import { Id, NullableId, Pa<PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbEscapeNum, dbRawReadSingle } from '../../../../util/db-raw';
import { arrToMap } from '../../../../util/param-sanitization';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export const TWTDAR_PATCH_FIELDS = [
  {field:'type_slug', },
  {field:'slug', },
  {field:'order', },
  {field:'lang', },
  {field:'form_code', },
  {field:'test_design_id', },
  {field:'tqr_ovrd_td_id' },
  {field:'long_name', },
  {field:'generate_report', },
  {field:'is_scheduled', },
  {field:'is_secured', },
  {field:'is_alternative', },
  {field:'is_questionnaire', },
  {field:'is_sample', },
  {field:'is_classroom_common_form', },
  {field:'component_slug', },
  {field:'is_active', },
  {field:'num_sub_sessions', },
  {field:'can_credential', },
  {field:'is_pipeline_exclude', },
  {field:'req_sd_lang', },
  {field:'req_sd_lang_not', },
  {field:'subsession_meta', isBlankStringNull: true},
  {field:'user_metas_filter', },
  {field:'accomm_user_meta_constraint', },
  {field:'max_condition_on_option', },
]

export class TestDesigns implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      const records = await this.getTwtdarBaseRecords(test_window_id, tc_group_id);
      if (!records.length){
        return [];
      }
      await this.applyCachedTQRCountCheck(records);
      await this.applyLatestTdByQSCheck(records);
      return records;
    }
    throw new Errors.BadRequest();
  }

  async getTwtdarBaseRecords(test_window_id:number, tc_group_id:number) {
    return dbRawRead(this.app, [test_window_id, tc_group_id], `
      select twtar.id
          , twtar.type_slug 
          ${ 
            TWTDAR_PATCH_FIELDS
              .map(prop => `, twtar.${prop.field}`)
              .join('\n             ')
          }
          , concat(u_twtdar.first_name, ' ', u_twtdar.last_name) td_assigned_by
          , td.created_on td_created_on
          , td.created_by_uid td_created_by_uid
          , concat(u.first_name, ' ', u.last_name) td_created_by
          , count(distinct tf.id) num_forms
          , td.source_item_set_id
          , qs.slug qs_slug
          , qs.is_test_design qs_is_test_design
          , qs.name qs_name
          , qs.description qs_description
          , qs.languages qs_languages
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
      left join test_designs td
        on td.id = twtar.test_design_id
      left join test_forms tf
        on tf.test_design_id = td.id
        and tf.is_revoked = 0
      left join temp_question_set qs 
        on qs.id = td.source_item_set_id
      left join users u 
        on u.id = td.created_by_uid
      left join users u_twtdar
        on u_twtdar.id = twtar.td_assigned_by_uid
      where tw.id = ?
        and tw.test_ctrl_group_id = ?
      group by twtar.id
      order by twtar.slug, twtar.order
    `);
  }

  async applyCachedTQRCountCheck(records:any[]) {
    // identify test design ids and map them
    type TwtarSummaryRecord = any;
    const tdRecordRef:Map<number, TwtarSummaryRecord[]> = new Map();
    const test_design_ids:number[] = [];
    for (let r of records){
      const test_design_id = r.tqr_ovrd_td_id || r.test_design_id;
      // use an array to capture cases where multiple twtar are assigned the same test design id
      if (!tdRecordRef.has(test_design_id)){
        tdRecordRef.set(test_design_id, [])
        test_design_ids.push(test_design_id)
      }
      tdRecordRef.get(test_design_id)?.push(r);
    }
    // apply counts for all caching tables
    if (test_design_ids.length){
      const applyCounts = async (ids:number[], table:string, tableCode:string, is_revoked_incl:boolean, id_field_name = 'test_design_id') => {
        const countsById = await dbRawRead(this.app, {ids}, `
          select ${tableCode}.${id_field_name}, count(0) n
          from ${table} ${tableCode} 
          where ${tableCode}.${id_field_name} in (:ids)
          ${ is_revoked_incl ? 'and is_revoked = 0' : ''} 
          ${ table == 'test_question_register' ? 'and is_respondable = 1' : ''}
          group by ${tableCode}.${id_field_name}
        `)
        const countsRef = arrToMap(countsById, id_field_name, {reduceToProp: 'n'});
        for (let test_design_id of test_design_ids){
          const twtarRecords = tdRecordRef.get(test_design_id);
          if (twtarRecords){
            for (let record of twtarRecords){
              const tableCount = countsRef.get(test_design_id)
              record[tableCode+'_count'] = tableCount
            }
          }
        }
      }
      await applyCounts(test_design_ids, 'test_question_register',  'tqr' , false )
      await applyCounts(test_design_ids, 'test_form_module_items',  'tfmi', true ) // panel item mapping
      await applyCounts(test_design_ids, 'test_form_module_stages', 'tfms', true ) // panel stage mapping
    }
  }

  async applyLatestTdByQSCheck(records:any[]) {
    const qsIds = records.map(r => r.source_item_set_id);
    // get the latest published test design
    const latestTdByQS = await dbRawRead(this.app, [qsIds], `
      select t.source_item_set_id
          , t.lang
          , tf.test_design_id
      from (
        select td.source_item_set_id
            , tf.lang
            , max(tf.id) max_tf_id
        from test_designs td
        join test_forms tf
          on tf.test_design_id = td.id
          and tf.is_revoked = 0
        where td.source_item_set_id in (?)
        group by td.source_item_set_id, tf.lang
      )  t
      join test_forms tf
        on tf.id = t.max_tf_id
    ;`);
    const keyJoinChar = ';'
    const latestTdByQSRef = arrToMap(latestTdByQS, ['source_item_set_id', 'lang'], {reduceToProp: 'test_design_id', keyJoinChar});
    records.forEach(record => {
      const {source_item_set_id, lang, test_design_id} = record;
      const key = [source_item_set_id, lang].join(keyJoinChar)
      const latestTdId = latestTdByQSRef.get(key);
      if (+latestTdId !== +test_design_id){
        record.isNotLatestTd = 1
      }
    })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // load test designs (filter on local)
    const source_item_set_id = id;
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      const testDesigns = await dbRawRead(this.app, [], `
        select td.id
              , td.created_on
              , td.created_by_uid
              , td.name
              , td.is_error
              , count(tf.id) num_forms
              , concat(u.first_name, ' ', u.last_name) td_created_by
              , td.source_item_set_id
              , tf.lang
              , qs.slug qs_slug
              , qs.is_test_design qs_is_test_design
              , qs.name qs_name
              , qs.description qs_description
              , qs.languages qs_languages
        from test_designs td
        join temp_question_set qs 
          on qs.id = td.source_item_set_id
        join test_forms tf
          on tf.test_design_id = td.id
          and tf.is_revoked = 0
        join users u 
          on u.id = td.created_by_uid
        ${
          (source_item_set_id === -1) ? '' : `
            WHERE td.source_item_set_id = ${await dbEscapeNum(source_item_set_id)}
          `
        }
        group by td.id
        order by td.id desc
        limit 500
      `);
      return testDesigns;
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const {test_window_id, tc_group_id} = params.query;
      const payload:any = {};
      for (let prop of TWTDAR_PATCH_FIELDS){
        payload[prop.field] = (<any>data)[prop.field]
      }
      const td_assigned_by_uid = await currentUid(this.app, params);
      return <any>this.app
        .service('db/write/test-window-td-alloc-rules')
        .create({
          ... payload,
          td_assigned_by_uid,
          td_assigned_on: dbDateNow(this.app),
          test_window_id
        })
    }
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async validateWriteAccess(test_window_id:number, tc_group_id:number, twtar_id:NullableId) {
    const records = await dbRawRead(this.app, [test_window_id, tc_group_id, twtar_id], `
      select twtar.id
      from test_windows tw 
      join test_window_td_alloc_rules twtar 
        on twtar.test_window_id = tw.id 
      where tw.id = ?
        and tw.test_ctrl_group_id = ?
        and twtar.id = ?
    `);
    if (records.length === 0){
      throw new Errors.Forbidden('INVALID-test_ctrl_group_id')
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const twtar_id = id;
    if (params && params.query){
      const patched_by_uid = await currentUid(this.app, params);
      const {test_window_id, tc_group_id} = params.query;
      // note: the extra stuff here is for access control
      await this.validateWriteAccess(test_window_id, tc_group_id, twtar_id);
      const payload:any = {};
      for (let prop of TWTDAR_PATCH_FIELDS){
        let val = (<any>data)[prop.field];
        if(prop.isBlankStringNull){
          val = (val === '') ? null : val;
        } 
        if (val !== undefined){
          payload[prop.field] = val;
        }
      }
      const test_design_id = (<any>payload).test_design_id || null;
      await this.app
        .service('db/write/test-window-td-alloc-rules-log')
        .create({
          twtar_id,
          patched_by_uid,
          test_design_id,
          patches: JSON.stringify(payload, null, 2),
        })
      await this.app
        .service('db/write/test-window-td-alloc-rules')
        .patch(twtar_id, {
          ...payload,
          td_assigned_by_uid: patched_by_uid,
          td_assigned_on: dbDateNow(this.app),
        })
      return data;
    }

    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    const twtar_id = id;
    if (params && params.query){
      const patched_by_uid = await currentUid(this.app, params);
      const {test_window_id, tc_group_id} = params.query;
      // note: the extra stuff here is for access control
      await this.validateWriteAccess(test_window_id, tc_group_id, twtar_id);
      const payload:any = {
        is_active: 0
      };
      await this.app
        .service('db/write/test-window-td-alloc-rules-log')
        .create({
          twtar_id,
          patched_by_uid,
          patches: JSON.stringify(payload, null, 2),
        })
      await this.app
        .service('db/write/test-window-td-alloc-rules')
        .patch(twtar_id, {
          ... payload,
          td_assigned_by_uid: patched_by_uid,
          td_assigned_on: dbDateNow(this.app),
        })
      return {};
    }
    throw new Errors.BadRequest();
  }
}
