import { Id, NullableId, <PERSON>ginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class Roles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  roleTypes : string[];
  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.roleTypes = [
      DBD_U_ROLE_TYPES.translator,
      DBD_U_ROLE_TYPES.cron,
      DBD_U_ROLE_TYPES.debug,
      DBD_U_GROUP_TYPES.alt_version_req_ctrl,
      DBD_U_GROUP_TYPES.alt_version_req_shipping_vendor,
      DBD_U_ROLE_TYPES.payment_ctrl,
      DBD_U_ROLE_TYPES.test_ctrl_data_retr,
      DBD_U_ROLE_TYPES.test_ctrl_data_exporter,
      DBD_U_ROLE_TYPES.test_ctrl_issue_tracker,
      DBD_U_ROLE_TYPES.test_ctrl_lias_cert_body,
      DBD_U_ROLE_TYPES.test_ctrl_lias_internal,
      DBD_U_ROLE_TYPES.test_ctrl_lias_test_admin,
      DBD_U_ROLE_TYPES.test_ctrl_meta_reg,
      DBD_U_ROLE_TYPES.test_ctrl_reg_ctrl,
      DBD_U_ROLE_TYPES.test_ctrl_score_valid,
      DBD_U_ROLE_TYPES.test_ctrl_window_monitor,
      DBD_U_ROLE_TYPES.test_ctrl_issue_revw,
      DBD_U_ROLE_TYPES.test_ctrl_issue_revw_exempt_ovr,
      DBD_U_ROLE_TYPES.test_ctrl_ntf_ctrl,
      DBD_U_ROLE_TYPES.test_ctrl_mfa_ctrl,
      DBD_U_ROLE_TYPES.test_ctrl_auto_submit_ctrl,
      DBD_U_ROLE_TYPES.ctrl_system_monitor,
      DBD_U_ROLE_TYPES.mfa_exempt,
    ];
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid
    return <any> this.app.service('auth/user-role-actions').getSystemRoles(uid, DBD_U_GROUP_TYPES.mpt_test_controller);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    
    const records = await dbRawRead(this.app, {roleTypes: this.roleTypes}, `
    select u.id uid
      , u.first_name
      , u.last_name 
      , u.contact_email
      , ur.id ur_id
      , ur.role_type  
      , ur.created_on
      , ur.is_revoked
      , ur.revoked_on
    from user_roles ur 
    join users u
      on ur.uid = u.id
    where ur.role_type in (:roleTypes)
    order by uid
    ;`);
    return [{
      records
    }];
  }

  /**
   * Add user roles to email and uid(optional)
   * @param data {email, role_type, group_ids, target_uid}
   * @param params 
   */
  async create (data: Data, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = userInfo.uid;
    const {email, role_type, group_ids, target_uid} = <any> data;
    if(!created_by_uid || !email || !role_type || !group_ids){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if(!this.roleTypes.includes(role_type)){
      throw new Errors.BadRequest('INCORRECT_ROLE_TYPE');
    }
    const users = await dbRawReadReporting(this.app, {email, target_uid}, `
      -- 102ms
      select u.id uid 
        from users u
       where contact_email = :email
       ${target_uid?'and u.id = :target_uid':''}
    ;`);
    if(!users || users.length === 0){
      throw new Errors.BadRequest('USER_NOT_FOUND');
    }

    //if multiple uid found for an email set the first one as default uid
    let user = users[0]
    
    // when no target uid and there's multiple user with same  email, 
    // check auths table and use the uid in auth table 
    if(!target_uid && users.length > 1){
      const auths = await dbRawReadReporting(this.app, {email}, `
        -- 92ms
        select au.uid from auths au where au.email = :email
      ;`)
      if(auths && auths.length){
        user = auths[0]
      }
    }

    for (let group_id of group_ids){
      const roleData = {
        uid: user.uid,
        role_type,
        group_id,
        created_by_uid
      };
      
      await this.app.service('db/write/user-roles').create({
        ... roleData,
      });

    }
    
    return {
      success: true,
      message: "Role created successfully."
    }
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    await this.app.service('db/write/user-roles').patch(id, {
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
    })
    return {
      success: true,
      message: "Role removed successfully."
    }}
}
