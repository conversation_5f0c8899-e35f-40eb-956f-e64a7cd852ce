import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import { SINGLE_PJ_REPORT_FOLDER } from '../../../school-admin/pj-reports/pj-reports.class';
import { ISR_FILE_TYPES } from '../../../school-admin/osslt-pdf-reports/osslt-pdf-reports.class';

interface Data {}

interface ServiceOptions {}

export class StudentReports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Get the student ISR pdf
   */
  async find (params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const { attemptId, uid, schl_group_id } = params.query;

    const testWindowRecord = await dbRawRead(this.app, [uid, attemptId], `
      select tw.id, tw.type_slug
        from test_attempts ta 
        join test_sessions ts on ts.id = ta.test_session_id
        join test_windows tw on ts.test_window_id = tw.id
       where ta.uid = ?
         and ta.id = ?
         and ta.is_invalid = 0
      ;`)
      
    // G9
    if(testWindowRecord.length > 0 && testWindowRecord[0].type_slug === 'EQAO_G9M') {
      // need to get the class group id in order to call the getSingleReport function in educator/g9-repots.
      const studentClass = await dbRawRead(this.app, [attemptId], `
            -- 0.032s
            select ur.group_id
              from test_attempts ta
              join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
              join school_classes sc_wrote on sc_wrote.id = scts.school_class_id
         left join school_classes_guest scg on scg.invig_sc_group_id = sc_wrote.group_id
         left join school_classes sc_guest on sc_guest.group_id = scg.guest_sc_group_id
              join user_roles ur on ur.group_id in (sc_wrote.group_id, sc_guest.group_id)
               and ur.uid = ta.uid
             where ta.id = ? 
      ;`)
      params.query.schl_class_group_id  = studentClass[0].group_id
      return this.app.service('public/educator/g9-reports').getSingleReport(params)
    }

    // PJ
    if(testWindowRecord.length > 0 && (testWindowRecord[0].type_slug === 'EQAO_G3P' || testWindowRecord[0].type_slug === 'EQAO_G6J')) {
      const tw_id = testWindowRecord[0].id;
      const single_report_file_path = SINGLE_PJ_REPORT_FOLDER + 'tw_' + tw_id + '_uid_' + uid + '.pdf';
      return {message: 'REPORT_GENERATED', reportURL:generateS3DownloadUrl(single_report_file_path)}
    }

    //OSSLT
    if(testWindowRecord.length > 0 && testWindowRecord[0].type_slug === 'EQAO_G10L') {
      const tw_id = testWindowRecord[0].id;
      const singleStudentUid = uid
      const bulk_student_results_reports = await this.app.service('public/school-admin/osslt-pdf-reports').getBulkStudentResultsReportsGeneratingRecords(schl_group_id, tw_id)
      const timestamp = this.app.service('public/school-admin/osslt-pdf-reports').extraTimeStampFromLink(bulk_student_results_reports[0].s3_report_link);
      const single_report_file_path =  this.app.service('public/school-admin/osslt-pdf-reports').getISRFilePath(ISR_FILE_TYPES.SINGLE_ISR_PDF, tw_id, +timestamp, singleStudentUid)
      return {message: 'REPORT_GENERATED', reportURL:generateS3DownloadUrl(single_report_file_path)}
    }

    throw new Errors.BadRequest('NO_DATA_FOR_REPORT');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.BadRequest('ATTEMPT_ID_NO_INFO');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest('ATTEMPT_ID_NO_INFO');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest('ATTEMPT_ID_NO_INFO');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest('ATTEMPT_ID_NO_INFO');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.BadRequest('ATTEMPT_ID_NO_INFO');
  }
}
