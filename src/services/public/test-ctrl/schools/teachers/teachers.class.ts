import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { dbRawRead, dbRawReadCount, dbRawReadReporting } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { Application } from '../../../../../declarations';
import Axios from 'axios';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';

interface Data {}

interface ServiceOptions {}

export class Teachers implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    let records;
    if (params.query.bustCache) {
      records = await dbRawReadReporting(this.app, [params.query.test_window_id], `
      SELECT urt.uid, s.foreign_id as s_foreign_id, sd.foreign_id as sd_foreign_id, us.first_name, us.last_name, us.account_type,s.name as school_name, sc.name as class_name 
      FROM mpt_dev.schools s 
      JOIN mpt_dev.school_districts sd 
        on sd.group_id = s.schl_dist_group_id 
        and (sd.is_active = 1 and sd.is_sample = 0)
      JOIN mpt_dev.school_classes sc 
      ON  sc.schl_group_id = s.group_id
      JOIN mpt_dev.school_semesters ss
        on ss.id = sc.semester_id
      JOIN mpt_dev.user_roles urt 
        on urt.group_id = sc.group_id 
        and urt.role_type = 'schl_teacher'
        and urt.is_revoked != 1
      JOIN mpt_dev.users us on us.id = urt.uid
      WHERE ss.test_window_id = ?
      group by urt.uid
      ;`);
      } else {
      records = await this.readDataFromCache(params.query.test_window_id, params.query.order);
    }

    return records;
  }

  private async readDataFromCache(test_window_id: number, col: string) {
    const detailCache = await this.app
      .service('db/read/test-controller-dashboard-cache')
      .db()
      .where('test_window_id', test_window_id)
      .orderBy('timestamp', 'desc')
      .limit(1)

    const detailUrl = JSON.parse(detailCache[0].detail_cache)[col];

    const detailEntry:any = await this.getFileData(detailUrl);
    
    return detailEntry.teachers;
  }

  private async getFileData(url: string) {
    return await this.getS3FileData(url);
  }

  private async getS3FileData(filePath: string) {
    const url = generateS3DownloadUrl(filePath);
    const file:any = await Axios.get(url, {});
    return file.data;
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
