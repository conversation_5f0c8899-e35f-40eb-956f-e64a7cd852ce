import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class StudentReportGeneration implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const { attemptId, uid, schl_class_group_id, is_fp_only } = params.query;
    const created_by_uid = await currentUid(this.app, params);
    const fp_only = !is_fp_only || +is_fp_only === 1; // if is_fp_only is not provided, or is_fp_only is 1, then it is FP only

    if (fp_only) {
      const fullyParticipateAttemptNoASRG = await this.app.service('public/educator/g9-reports').getAttemptsReadyForReports(schl_class_group_id, attemptId)
      if (!fullyParticipateAttemptNoASRG.length) {
        throw new Errors.BadRequest('NO_ASRG_FOR_NFP')
      }
    }

    const asrg = await this.app
      .service('db/write/auto-student-report-generations')
      .create({
        student_uid: uid,
        test_attempt_id: attemptId,
        created_by_uid,
      })

    return asrg;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
