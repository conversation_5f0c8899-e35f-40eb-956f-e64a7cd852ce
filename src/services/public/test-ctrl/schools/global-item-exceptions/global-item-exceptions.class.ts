import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';
import { dbDateNow } from '../../../../../util/db-dates';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class GlobalItemExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;

      const records = await this.getTwExceptionsRecordsByTestWindow(test_window_id)
      return records;
    }
    throw new Errors.BadRequest('MISSING_PARAMS')
  }

  /**
   * Get tw exception items records by test window id
   * @param test_window_id 
   * @returns {any[]} tw exception items records
   */
  async getTwExceptionsRecordsByTestWindow(test_window_id:number): Promise<any[]>{
    return await dbRawWrite(this.app, [test_window_id], `
      SELECT
          tei.id
        , tei.test_window_id
        , tei.item_id
        , tei.item_label
        , tei.is_prorated
        , tei.is_score_override
        , tei.score_override
        , tei.match_response_value
        , tei.created_by_uid
        , tei.created_on
        , tei.is_revoked
        , tei.revoked_on
        , tei.revoked_by_uid
        , tei.notes
     FROM tw_exceptions_items tei
    WHERE tei.test_window_id = ?
 GROUP BY tei.id
    ;`)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (item_id: Id, params?: Params): Promise<Data> {
    if (!item_id || !params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const { lang } = params.query

    const tqerRecordByItemId = await dbRawRead(this.app, { item_id, lang }, `
    SELECT tq.question_label
         , tqer.formatted_response
         , tqer.lang
      FROM test_question_expected_responses tqer
      JOIN test_questions tq on tq.id = tqer.item_id
     WHERE tqer.item_id = :item_id 
       AND tqer.is_revoked = 0
      ${ lang ? "AND ( tqer.lang is null OR tqer.lang = :lang )" : "" }
  GROUP BY tqer.formatted_response
    `)

    return tqerRecordByItemId
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query || !data){
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const created_by_uid = await currentUid(this.app, params);
    const { item_id, notes, exceptionAction, match_response_value, new_score } = <any> data;
    const { test_window_id } = params.query

    if (!test_window_id || !item_id || !exceptionAction){
      throw new Errors.BadRequest('MISSING_PARAMS')
    }

    const test_question = await dbRawRead(this.app, { item_id }, `
      SELECT tq.question_label 
        FROM test_questions tq
       WHERE tq.id = :item_id
    `)

    if(!test_question.length){
      throw new Errors.BadRequest('NO_ITEM_FOUND_WITH_ID')
    }

    let addToDuplicatedItemQuery = ""
    if(exceptionAction === "is_prorated"){
      addToDuplicatedItemQuery = 'AND twei.is_prorated = 1'
    }
    if(exceptionAction === 'is_score_override'){
      if(Number.isNaN( parseInt(new_score) )){ // null return true, '0' return false
        throw new Errors.BadRequest('NO_NEW_SCORE_FOR_SCORE_OVERRIDE')
      }

      addToDuplicatedItemQuery = 'AND twei.match_response_value = :match_response_value AND twei.score_override = :new_score'
    }

    const duplicatedItem = await dbRawRead(this.app, { test_window_id, item_id, match_response_value, new_score }, `
      SELECT 
            twei.* 
       FROM tw_exceptions_items twei
      WHERE twei.test_window_id = :test_window_id
        AND twei.item_id = :item_id
        AND twei.is_revoked = 0
        ${addToDuplicatedItemQuery}
    `)

    if(duplicatedItem.length){
      throw new Errors.BadRequest('ITEM_ALREADY_EXIST')
    }

    await this.app.service('db/write/tw-exceptions-items').create({
      test_window_id: test_window_id, 
      item_id: item_id,
      item_label: test_question[0].item_label,
      is_prorated: exceptionAction === 'is_prorated' ? 1 : 0, 
      is_score_override: exceptionAction === 'is_score_override' ? 1 : 0, 
      score_override: exceptionAction === 'is_score_override' ? new_score : null,
      match_response_value: match_response_value,
      notes: notes,
      created_by_uid: created_by_uid,
      created_on: dbDateNow(this.app),
    })

    const fullTwExceptionRecordsByTW = await this.getTwExceptionsRecordsByTestWindow(test_window_id)

    return fullTwExceptionRecordsByTW
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (!id || !params){
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const revoked_by_uid = await currentUid(this.app, params);
    await this.app.service('db/write/tw-exceptions-items').patch(id, {
      is_revoked: 1,
      revoked_by_uid,
      revoked_on: dbDateNow(this.app),
    })
    return {id}
  }
}
