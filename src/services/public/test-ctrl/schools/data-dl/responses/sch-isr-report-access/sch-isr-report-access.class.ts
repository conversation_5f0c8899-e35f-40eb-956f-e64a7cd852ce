import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class SchIsrReportAccess implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    // Define the slug-to-UTARData mapping
    const slugToDataTargetMap: Record<string, string> = {
      'G9_OPERATIONAL': 'public/educator/g9-reports' ,
      'OSSLT_OPERATIONAL': 'public/school-admin/osslt-pdf-reports' ,
      'PRIMARY_OPERATIONAL': 'public/school-admin/pj-reports',
      'JUNIOR_OPERATIONAL': 'public/school-admin/pj-reports',
    };
    const slugNotInMaps = slugs.filter(slug => slugToDataTargetMap[slug])  //filter out slugs not in slugToDataTargetMap
    const slugToDataTarget = slugNotInMaps.map(slug => slugToDataTargetMap[slug]) // extract the corresponding values from the slugToDataTargetMap
    const data_targets = Array.from(new Set(slugToDataTarget)); // make data_targets a collection of unique values of slugToDataTarget

    const stream = await dbRawReadReportingStream(this.app, [slugs, data_targets], `
      
      select /*+ MAX_EXECUTION_TIME(1440000000)*/  
          sd.foreign_id as BrdMident
        , sd.name as BrdName
        , s.foreign_id as SchMident
        , s.name as SchName
        , s.lang as Lang
        , twtar.long_name as ComponentName
            , count(sr.id) num_reports_generated
            , DATE_FORMAT(min(sr.created_on), '%y-%m-%d') first_generated_on
            , case when (utar.id is not null) then DATE_FORMAT(min(utar.created_on),'%y-%m-%d')  else "" end first_accessed_on
            , DATE_FORMAT(max(sr.created_on), '%y-%m-%d') last_generated_on
            , case when (utar.id is not null) then DATE_FORMAT(max(utar.last_updated_on),'%y-%m-%d')  else "" end last_accessed_on
            , avg(sr.overall_raw_proportional) avg_overall_raw_score
            , min(sr.overall_raw_proportional) min_overall_raw_score
            , max(sr.overall_raw_proportional) max_overall_raw_score
          -- , sum(case when (sr.overall_raw_proportional<0.70) then 1 else 0 end)/count(sr.id) prop_below_70
            , sum(case when (sr.overall_raw_proportional>=0.70 and sr.overall_raw_proportional<=1.00) then 1 else 0 end)/count(sr.id) prop_70_plus
            -- , sum(case when (sr.overall_raw_proportional<0.60) then 1 else 0 end)/count(sr.id) prop_below_60
            , sum(case when (sr.overall_raw_proportional>=0.60 and sr.overall_raw_proportional<=1.00) then 1 else 0 end)/count(sr.id) prop_60_plus
            , sum(case when sr.overall_raw_proportional=0 then 1 else 0 end) b_e_0
            , sum(case when (sr.overall_raw_proportional>0 and sr.overall_raw_proportional<=0.1) then 1 else 0 end) b_10
            , sum(case when (sr.overall_raw_proportional>0.10 and sr.overall_raw_proportional<=0.2) then 1 else 0 end) b_20
            , sum(case when (sr.overall_raw_proportional>0.20 and sr.overall_raw_proportional<=0.30) then 1 else 0 end) b_30
            , sum(case when (sr.overall_raw_proportional>0.30 and sr.overall_raw_proportional<=0.40) then 1 else 0 end) b_40
            , sum(case when (sr.overall_raw_proportional>0.40 and sr.overall_raw_proportional<=0.50) then 1 else 0 end) b_50
            , sum(case when (sr.overall_raw_proportional>0.50 and sr.overall_raw_proportional<=0.60) then 1 else 0 end) b_20
            , sum(case when (sr.overall_raw_proportional>0.60 and sr.overall_raw_proportional<=0.70) then 1 else 0 end) b_30
            , sum(case when (sr.overall_raw_proportional>0.70 and sr.overall_raw_proportional<=0.80) then 1 else 0 end) b_40
            , sum(case when (sr.overall_raw_proportional>0.80 and sr.overall_raw_proportional<=0.90) then 1 else 0 end) b_90
            , sum(case when (sr.overall_raw_proportional>0.90 and sr.overall_raw_proportional<=1.00) then 1 else 0 end) b_100
      from test_attempts ta
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id and scts.slug IN (?)
      join test_sessions ts on ts.id = scts.test_session_id ${ testWindowId ? ` and ts.test_window_id = ${testWindowId}` : ''}
      join test_windows tw on tw.id = ts.test_window_id and tw.is_active=1
      join school_classes sc on sc.id = scts.school_class_id
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
      join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id and twtar.is_scheduled = 1
      join student_reports sr on sr.attempt_id = ta.id and sr.is_isr=1 and sr.is_revoked = 0 
 left join user_tw_access_records utar on utar.group_id = s.group_id and utar.data_target in (?) and utar.test_window_id = ts.test_window_id and utar.is_revoked = 0    
      group by s.id 
      order by s.lang, sd.name, s.name
      ;
    
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
