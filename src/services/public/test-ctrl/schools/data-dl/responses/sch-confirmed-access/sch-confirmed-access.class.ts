import { Id, NullableId, <PERSON><PERSON>ated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class SchConfirmedAccess implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {

    const stream = await dbRawReadReportingStream(this.app, [], `
    select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
        s.type_slug SchoolType
      , s.foreign_id SchoolMident
      , s.name SchoolName
      , case 
        when s.is_private = 1 then 'Yes'
        else 'No'
      end PrivateSchool
      , sd.foreign_id BoardMident
      , sd.name BoardName
      , a.email ConfirmedSchoolAdmin
    from schools s 
      join school_districts sd 
        on sd.group_id = s.schl_dist_group_id 
      left join user_roles ur 
        on s.group_id = ur.group_id 
        and ur.is_confirmed = 1
        and ur.is_conf_req = 1
        and ur.is_revoked  = 0
      left join auths a 
        on a.uid = ur.uid 
    order by sd.name, s.name 
    ;
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
