import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class Subm implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;
    //let ignoreTAStart: boolean = false;

    if (params && params.query) {
      const { ts_slugs, test_window_id, ignore_ta_start } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
      //if (ignore_ta_start) ignoreTAStart = ignore_ta_start;
    }

    const stream = await dbRawReadReportingStream(this.app, {slugs, testWindowId}, `
      -- took 254s on mirror for tw101
      select /*+ MAX_EXECUTION_TIME(1440000000)*/
          ta.id as test_attempt_id
        , ta.is_submitted
        -- , count(taqr.test_question_id) as num_items_responded
          , '' as num_items_responded
        , sd.foreign_id as BrdMident
        , sd.name as BrdName
        , s.foreign_id as SchMident
        , s.name as SchName
        , sc.name as GroupingCode
        , um2.value as Lang
        , twtar.slug as ComponentSlug
        , twtar.long_name as ComponentName
        , twtar.component_slug as \`Subject\`
        , tf.test_design_id
        , ts.date_time_start
        , ta.uid
        , ta.twtdar_id
        , ta.created_on
        , ta.started_on
        , ta.last_touch_on
        , ta.closed_on 
        -- ta.id, ts.date_time_start, ta.started_on
      from test_attempts ta
      join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and taqr.is_invalid = 0 
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id and scts.slug IN (:slugs)
      join test_sessions ts on ts.id = scts.test_session_id ${ testWindowId ? ` and ts.test_window_id = :testWindowId ` : ''}
      join test_windows tw on tw.id = ts.test_window_id and tw.is_active = 1
      join school_classes sc on sc.id = scts.school_class_id
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
      join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id and is_scheduled = 1
      join test_forms tf on tf.id = ta.test_form_id
      -- join test_question_register tqr on tqr.question_id = taqr.test_question_id and tqr.test_design_id = tf.test_design_id
      left join user_metas um on um.uid = ta.uid and um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN'
      left join user_metas um2 on um2.uid = ta.uid and um2.key_namespace = 'eqao_dyn' and um2.key = 'Lang'
      where ta.started_on is not null
        -- and ta.is_closed = 1
        -- and taqr.is_nr = 0
      group by ta.id
      order by ta.last_touch_on desc
      -- limit 1000
    
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
