import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class ByDate implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL', 'G9_SAMPLE'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const stream = await dbRawReadReportingStream(this.app, [slugs], `
      
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
        t.AssessmentType
      , t.SessionStart
      , t.SessionType
      , sum(t.NumStudents) as NumStudents
      , count(0) as NumSessions
      from (
      select sd.foreign_id as BrdMident, sd.name BrdName, scts.slug as AssessmentType, count(distinct ur.uid) as NumStudents, DATE_FORMAT(tsss.datetime_start, '%y-%m-%d') as SessionStart, tsss.caption as SessionType
      from test_windows tw
      join test_sessions ts  on tw.id = ts.test_window_id ${ testWindowId ? ` and ts.test_window_id = ${testWindowId}` : ''}
      join test_session_sub_sessions tsss on tsss.test_session_id = ts.id and tsss.caption IN ('A', 'B')
      join school_class_test_sessions scts on ts.id = scts.test_session_id 
      join school_classes sc on sc.id = scts.school_class_id
      join user_roles ur on ur.group_id = sc.group_id and ur.role_type like '%student%'
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
      left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.uid = ur.uid
      where scts.slug IN (?)
      and tw.is_active = 1
      and (ur.is_revoked = 0 or (ur.is_revoked = 1 and ta.started_on is not null))
      group by tsss.id
      ) t
      group by t.SessionStart, t.AssessmentType, t.SessionType
      order by t.AssessmentType, t.SessionStart, t.SessionType
      ;

    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
