import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class SchByDate implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL', 'G9_SAMPLE'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const stream = await dbRawReadReportingStream(this.app, [slugs], `
      
    select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
       t.AssessmentType
     , t.SessionAStart
     , t.BrdMident
     , t.BrdName
     , t.SchMident
     , t.SchName
     , t.Lang
     , sum(t.NumStudents) as NumStudents
     , count(0) as NumSessions
     ,  t.is_kiosk_approved
from (
    select sd.foreign_id as BrdMident, sd.name BrdName, s.foreign_id as SchMident, s.name SchName, s.lang as Lang, scts.slug as AssessmentType, count(distinct ur.uid) as NumStudents, DATE_FORMAT(ts.date_time_start, '%y-%m-%d') as SessionAStart
    , s.is_kiosk_approved
    from test_windows tw
    join test_sessions ts  on tw.id = ts.test_window_id ${ testWindowId ? ` and ts.test_window_id = ${testWindowId}` : ''}
    join school_class_test_sessions scts on ts.id = scts.test_session_id 
    join school_classes sc on sc.id = scts.school_class_id
    join user_roles ur on ur.group_id = sc.group_id and ur.role_type like '%student%'
    join schools s on s.group_id = sc.schl_group_id
    join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
    left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.uid = ur.uid
    where scts.slug IN (?)
      and tw.is_active = 1
      and (ur.is_revoked = 0 or (ur.is_revoked = 1 and ta.started_on is not null))
    group by scts.id
) t
group by t.SessionAStart, t.AssessmentType,  t.SchMident
order by t.AssessmentType, t.SessionAStart,  t.SchMident
;
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
