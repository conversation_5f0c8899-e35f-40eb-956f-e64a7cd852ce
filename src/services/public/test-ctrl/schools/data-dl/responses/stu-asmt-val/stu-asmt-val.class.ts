import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class StuAsmtVal implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { test_window_id } = params.query;
      if (test_window_id) testWindowId = test_window_id;
    }

    const stream = await dbRawReadReportingStream(this.app, {testWindowId}, `
      
      select /*+ MAX_EXECUTION_TIME(1440000000)*/ * from (
        select sd.foreign_id as BrdMident
          , sd.name as BrdName
          , s.foreign_id as SchMident
          , s.name as SchName
          , case when min(ssais.is_revoked) = 0 then 1 else 0 end as is_currently_validated
          , DATE_FORMAT(ssais.created_on, '%y-%m-%d') validated_on
          , min(ssais.is_revoked) is_revoked
        from schools s
        join school_districts sd on sd.group_id = s.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
        join school_classes sc on sc.schl_group_id = s.group_id
        join school_semesters ss on ss.id = sc.semester_id and ss.test_window_id = :testWindowId
        left join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = s.group_id and ssais.test_window_id = :testWindowId
        group by s.id
      ) t
      order by t.is_revoked, t.validated_on
      ;
    
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
