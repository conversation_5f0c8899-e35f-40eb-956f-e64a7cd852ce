import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class BySchool implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL', 'G9_SAMPLE'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const stream = await dbRawReadReportingStream(this.app, [], `
    
    select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
        t1.BrdMident
      , t1.BrdName
      , t1.SchMident
      , t1.SchName
      , t1.Lang
      , twtdar.type_slug as AssessmentType
      , t1.NumStudentsInitialLoad
      , t1.NumStudentsRegistered
      , count(distinct case when twtdar2.id is not null then ta.uid else null end) as NumStudentsAssignedSession -- not entirely accurate as it relies on the morning warm up
      , count(distinct case when twtdar2.id is not null and ta.started_on is not null then ta.uid else null end) as NumStudentsStarted
      , count(distinct case when (twtdar2.id is not null and ta.started_on is not null and ta.is_closed = 1) then ta.uid else null end) as NumStudentsSubmitted
      , count(distinct case when scts.slug = twtdar.type_slug then scts.id else null end) as NumSessions
      , t1.ConfirmedAccess
    from
    (
      select sd.foreign_id as BrdMident
        , sd.name BrdName
        , s.foreign_id as SchMident
        , s.group_id as schl_group_id
        , s.name SchName
        , s.lang as Lang
        , count(distinct case when um.key is not null then ta.uid else null end) as NumStudentsInitialLoad
        , count(distinct case when (ur.is_revoked = 0 or (ur.is_revoked = 1 and ta.started_on is not null)) then ur.uid else null end ) as NumStudentsRegistered
        , ifnull(max(ur_admin.is_confirmed), 0) as ConfirmedAccess
        , tw.id as test_window_id
      from test_windows tw
        join school_semesters ss on ss.test_window_id = tw.id 
        join school_classes sc on sc.semester_id = ss.id 
        join schools s on s.group_id = sc.schl_group_id
        join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 -- and sd.is_active = 1
        left join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student'
        left join school_class_test_sessions scts on sc.id = scts.school_class_id
        left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.uid = ur.uid -- and ur.is_revoked = 0
        left join user_metas um on ta.uid = um.uid and \`key\` = 'ProjectId' and ur.is_revoked = 0
        left join user_roles ur_admin 
          on s.group_id = ur_admin.group_id 
          and ur_admin.role_type = 'schl_admin'
          and ur_admin.is_conf_req = 1
          and ur_admin.is_revoked  = 0
      where tw.is_active = 1
        ${ testWindowId ? ` and tw.id = ${testWindowId}` : ''}
      group by SchMident
    ) t1
    join school_classes sc on sc.schl_group_id = t1.schl_group_id
    join school_semesters ss on ss.test_window_id = t1.test_window_id and ss.id = sc.semester_id
    left join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student'
    left join school_class_test_sessions scts on sc.id = scts.school_class_id 
    left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.uid = ur.uid -- and ur.is_revoked = 0
    left join test_window_td_alloc_rules twtdar on twtdar.test_window_id = t1.test_window_id and twtdar.lang = t1.Lang and twtdar.is_questionnaire = 0
    left join test_window_td_alloc_rules twtdar2 on twtdar2.id = ta.twtdar_id and twtdar2.id = twtdar.id
    group by SchMident, AssessmentType
    ;
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
