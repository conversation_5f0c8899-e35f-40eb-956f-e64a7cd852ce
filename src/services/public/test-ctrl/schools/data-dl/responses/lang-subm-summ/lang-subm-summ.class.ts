import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class LangSubmSumm implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;
    //let ignoreTAStart: boolean = false;

    if (params && params.query) {
      const { ts_slugs, test_window_id, ignore_ta_start } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
      //if (ignore_ta_start) ignoreTAStart = ignore_ta_start;
    }

    const stream = await dbRawReadReportingStream(this.app, [slugs], `
      
    select /*+ MAX_EXECUTION_TIME(1440000000)*/ 
       s.lang Lang
     , count(distinct sd.foreign_id) NumofBoards
     , count(distinct s.foreign_id) NumofSchools
     , count(distinct sc.id) NumofClasses
     , count(distinct case when um.key is not null then ta.uid else null end) as NumStudentsInitialLoad
     , count(distinct case when (ur.is_revoked = 0 or (ur.is_revoked = 1 and ta.started_on is not null)) then ur.uid else null end ) as NumStudentsRegistered
     , count(distinct ta.uid) as NumStudentsAssignedSession -- not entirely accurate as it relies on the morning warm up
     , count(distinct case when ta.started_on is not null then ta.uid else null end) as NumStudentsStarted
     , count(distinct case when (ta.started_on is not null and ta.is_closed = 1) then ta.uid else null end) as NumStudentsSubmitted
     , count(distinct scts.id) as NumOperationalSessions
    from test_windows tw
      join school_semesters ss on ss.test_window_id = tw.id 
      join school_classes sc on sc.semester_id = ss.id 
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
      join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student'
      left join school_class_test_sessions scts on sc.id = scts.school_class_id 
      left join test_attempts ta on ta.test_session_id = scts.test_session_id and ta.uid = ur.uid -- and ur.is_revoked = 0
      left join user_metas um on ta.uid = um.uid and \`key\` = 'ProjectId' and ur.is_revoked = 0
    where tw.is_active = 1
      and scts.slug IN (?)
      ${ testWindowId ? ` and tw.id = ${testWindowId}` : ''}
    group by Lang
    ;
    
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
