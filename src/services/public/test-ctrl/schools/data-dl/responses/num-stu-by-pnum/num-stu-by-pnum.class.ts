import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawReadReportingStream } from '../../../../../../../util/db-raw';
import { Readable } from 'stream';

interface Data {}

interface ServiceOptions {}

export class NumStuByPnum implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Readable> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const stream = await dbRawReadReportingStream(this.app, [testWindowId], `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/
      slug, panel_number, count(0) NumAttempts, sum(is_closed) NumClosed, sum(is_submitted) isSubmitted
      from ( 
        select tf.source_tf_id panel_number, twtar.slug, ta.id, ta.is_closed, ta.is_submitted
        from test_window_td_alloc_rules twtar
        join test_attempts ta
            on ta.twtdar_id = twtar.id
            and twtar.is_secured = 1
            and twtar.is_questionnaire  = 0
        join test_forms tf 
            on tf.id = ta.test_form_id 
        join school_class_test_sessions scts
            on scts.test_session_id = ta.test_session_id
        join school_classes sc
            on sc.id = scts.school_class_id
        join school_districts sd
            on sd.group_id = sc.schl_dist_group_id
            and sd.is_sample = 0
            and sd.is_active = 1
        where twtar.test_window_id = ?
        group by ta.id
      ) t 
      group by slug, panel_number
      ;
    `)
    return stream;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
