import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class StudentItemExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;
      const records = await db<PERSON>awRead(this.app, {test_window_id}, `
        select 
            tesi.id
          , tesi.test_window_id
          , tesi.uid
          -- , student_foreign_id
          -- , assessment_type
          , sd.foreign_id BoardMident
          , s.foreign_id SchoolMident -- tes.school_group_id
          , um.value StudentOEN
          , tesi.item_id
          , tq.question_label item_label
          -- , item_label
          , tesi.created_by_uid
          , tesi.created_on
          , tesi.is_revoked
          , tesi.revoked_on
          , tesi.revoked_by_uid
          , tesi.notes
          , tesi.revoke_notes
          , tesi.ric_id
          , tesi.is_prorated
          , tesi.is_score_override
          , tesi.new_score
          -- , tesi.lang
        from tw_exceptions_student_items tesi
        join test_window_td_alloc_rules twtar 
          on twtar.test_window_id = tesi.test_window_id
          and twtar.is_secured = 1
        join test_attempts ta 
          on ta.twtdar_id = twtar.id 
          and ta.uid = tesi.uid 
          and ta.is_invalid = 0
        join school_class_test_sessions scts
          on scts.test_session_id = ta.test_session_id 
        join school_classes sc 
          on sc.id = scts.school_class_id 
        join schools s 
          on s.group_id = sc.schl_group_id 
        join school_districts sd 
          on sd.group_id = s.schl_dist_group_id
        join user_metas um 
          on um.uid = tesi.uid 
          and um.key_namespace = 'eqao_sdc'
          and um.key = 'StudentOEN'
        join test_questions tq 
          on tq.id = tesi.item_id
        where tesi.test_window_id = :test_window_id
        group by tesi.id

      `)
      return records;
    }
    throw new Errors.BadRequest('MISSING_PARAMS')
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const uid = id;
    if (params && params.query){
      const {test_window_id} = params.query;
      const records = await dbRawRead(this.app, {uid, test_window_id}, `
        select 
          id
          , test_window_id
          , uid
          , student_foreign_id
          , assessment_type
          , item_id
          , item_label
          , created_by_uid
          , created_on
          , is_revoked
          , revoked_on
          , revoked_by_uid
          , revoke_notes
          , notes
          , lang
        from tw_exceptions_student_items tesi
        where tesi.test_window_id = :test_window_id
          and uid = :uid
          -- and is_revoked = 0 -- we deliberately include all revoked exceptions nad leave the filtering to the front end
      `)
      return {records};
    }
    throw new Errors.BadRequest('MISSING_PARAMS')
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> { //
    if (params){
      const {ta_id, test_window_id, uids, uid, item_id, ric_id, notes, is_prorated, is_score_override, new_score, is_invalidated} = <any> data;
      const created_by_uid = await currentUid(this.app, params);
      const isFromExceptionsTab = JSON.parse(params.query?.isFromExceptionsTab)

      if(isFromExceptionsTab){
        if(!ta_id || !test_window_id || !uid || !item_id){
          throw new Errors.BadRequest('MISSING_PARAMS')
        }

        const taqrRecord = await dbRawRead(this.app, { ta_id, uid, test_window_id, item_id }, `
          SELECT 
              taqr.test_question_id AS item_id
            , ts.test_window_id AS test_window_id
            , ric.id AS ric_id 
         FROM test_attempt_question_responses taqr
         JOIN test_attempts ta 
           ON ta.id = taqr.test_attempt_id
          AND ta.id = :ta_id
          AND ta.uid = :uid
         JOIN test_sessions ts 
           ON ts.id = ta.test_session_id 
          AND ts.test_window_id = :test_window_id
    LEFT JOIN reported_issues_common ric 
           ON ric.test_session_id = ts.id
        WHERE taqr.test_question_id = :item_id
        `)
  
        if(!taqrRecord.length){
          throw new Errors.BadRequest('NO_ITEM_FOUND_WITH_ID')
        }

        if(ric_id){
          if(!taqrRecord.some(record => record.ric_id === +ric_id)){
            throw new Errors.BadRequest('RIC_ID_MISMATCH')
          }
        }

        if(is_score_override && Number.isNaN( parseInt(new_score) )){ // null return true, '0' return false
          throw new Errors.BadRequest('NO_NEW_SCORE_FOR_SCORE_OVERRIDE')
        }
      }

      const studentUids = uids || [uid];
      const createdRecords:any[] = [];
      const retrievedRecords:any[] = [];
      for (let studentUid of studentUids){

        // check for existing records and revoke 
        const revoked_by_uid = created_by_uid;
        await this.removeExistingRecords(item_id, test_window_id, studentUid, revoked_by_uid);
        
        const createdRecord = await this.app.service('db/write/tw-exceptions-student-items').create({
          test_window_id, 
          uid: studentUid, 
          ric_id, 
          item_id,
          is_prorated, 
          is_score_override, 
          new_score,
          is_invalidated,
          notes,
          created_by_uid,
        })
        createdRecords.push(createdRecord);

        if(is_invalidated) {
          let tw_esi_id;
          if(createdRecord) {
            tw_esi_id = createdRecord.id
          }
          await this.invalidateTaqr(ta_id, item_id, tw_esi_id);
        }
      }
      return {createdRecords, retrievedRecords}
    }
    throw new Errors.BadRequest('MISSING_PARAMS')
  }

  async removeExistingRecords(item_id:number, test_window_id: number, studentUid:number, revoked_by_uid:number){
    const retrievedRecords = [];
    const existingRecords = <any[]> await this.app.service('db/write/tw-exceptions-student-items').find({ paginate: false, query: {
      test_window_id, 
      item_id,
      uid: studentUid, 
      is_revoked: 0,
    }});
    for (let record of existingRecords){
      retrievedRecords.push(record); // we might want to know about any unintentional overrides
      await this.app.service('db/write/tw-exceptions-student-items').patch(record.id, {
        is_revoked: 1,
        revoked_by_uid,
        revoked_on: dbDateNow(this.app),
      })
    }
    return retrievedRecords;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (params && params.query){
      const revoked_by_uid = await currentUid(this.app, params);
      if (id == '0'){
        const {item_id, test_window_id, uid} = params.query;
        const res = await this.removeExistingRecords(item_id, test_window_id, uid, revoked_by_uid);
        return res
      }
      else if (id){
        return this.app.service('db/write/tw-exceptions-student-items').patch(id, {
          is_revoked: 1,
          revoked_by_uid,
          revoked_on: dbDateNow(this.app),
        })
        return { id };
      }
    }
    throw new Errors.BadRequest('MISSING_PARAMS')
  }

  async invalidateTaqr(ta_id:number, test_question_id:number, tw_esi_id:number) {
    if (!ta_id || !test_question_id){
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const taqrRecords = await dbRawRead(this.app, [ta_id, test_question_id], `
        select * 
          from test_attempt_question_responses taqr 
         where taqr.test_attempt_id = ? 
           and taqr.test_question_id = ?
           and taqr.is_invalid = 0
    ;`);
    const taqrRecord = taqrRecords[0];
    const invalidationNote = {
      tw_esi_id
    }
    if(taqrRecord) {
      await this.app
            .service('db/write/test-attempt-question-responses')
            .patch(taqrRecord.id, { 
              is_invalid: 1, 
              invalidation_note: JSON.stringify(invalidationNote), 
              invalidated_on: dbDateNow(this.app), 
            });
    }
  }
}
