import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { AND_MATCH_ANY, dbEscapeString, dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../../util/db-raw';
import { arrToMap } from '../../../../../util/param-sanitization';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import { EXEMPTION_TYPES, maxReportGenerateMinutes } from '../../../school-admin/pj-reports/pj-reports.class'
interface Data {}

interface ServiceOptions {}

export class Student implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {
        excludeNotStartedAttempt, // required
        test_window_id, // required
        studentIdentifier, // optional
        studentFirstName, // optional
        studentLastName, // optional
        schoolForeignId, // optional
        schoolName, // optional
        attemptId, // optional
        uid, // optional
        ts_id, // optional
      } = params.query;
      const allowedParams:{prop:string, isNum?:boolean, isExact?:boolean, val:string|number }[] = [
        {prop: 'u.id', isNum:true, isExact:true, val:uid },
        {prop: 'ta.id', isNum:true, isExact:true, val:attemptId},
        {prop: 'scts.test_session_id', isNum:true, isExact:true, val:ts_id},
        {prop: 'um_oen.value', isExact:true, val:studentIdentifier},
        {prop: 'u.first_name', val:studentFirstName},
        {prop: 'u.last_name', val:studentLastName},
        {prop: 's.foreign_id', isExact:true, val:schoolForeignId},
        {prop: 's.name', val:schoolName},
      ]
      const AND_PARAM_CLAUSES:string[] = [];
      const NON_EMPTY_PARAM_CLAUSES:object[] = [];
      for (let i=0; i<allowedParams.length; i++){
        const param = allowedParams[i];
        if(param.val) {
          NON_EMPTY_PARAM_CLAUSES.push({prop: param.prop, val: param.val})
        }
        AND_PARAM_CLAUSES.push(
          await AND_MATCH_ANY({}, param.prop, !param.isNum, param.val, param.isExact)
        )
      }   

      let isGuestStudent = false;
      let studentRecords = await this.getStudentRecord(excludeNotStartedAttempt, test_window_id, AND_PARAM_CLAUSES, isGuestStudent);
      isGuestStudent = true;
      const guestStudentRecords = await this.getStudentRecord(excludeNotStartedAttempt, test_window_id, AND_PARAM_CLAUSES, isGuestStudent);
      guestStudentRecords.forEach( (guestStudentRecord:any) =>{
        // if guestStudentRecord can be found in studentRecords use guestStudentRecord instead 
        const existingStudentRecordIndex = studentRecords.findIndex((studentRecord: any) => studentRecord.ta_id === guestStudentRecord.ta_id);
        if (existingStudentRecordIndex !== -1) {
          studentRecords.splice(existingStudentRecordIndex, 1);
        }      
        studentRecords.push(guestStudentRecord)
      })

      if(studentRecords.length === 0) {
        throw new Errors.BadRequest('ERR_NO_STUDENT_RECORD_FOUND', NON_EMPTY_PARAM_CLAUSES)
      }

      const ta_ids = studentRecords.map(s => s.ta_id);
      studentRecords.forEach( s => { 
        s.responses = [];
      })
      // load responses
      const includeResponseCount = false;
      if (includeResponseCount){
        if (ta_ids.length){
          const taqrRecords = await dbRawRead(this.app, [ta_ids], `
            select 
                taqr.test_attempt_id ta_id
              , count(taqr.id) n
            from test_attempt_question_responses taqr 
            join test_questions tq 
              on tq.id = taqr.test_question_id 
              and taqr.is_invalid  = 0
            where taqr.test_attempt_id in (?)
          `)
          taqrRecords.forEach(taqrRecord => { 
            const {ta_id, n} = taqrRecord;
            studentRecords.forEach(s => {
              if (s.ta_id === ta_id && s.responses){
                s.numResponses = n
              }
            })
          })
        }
        studentRecords.forEach( s => s.numResponses = s.responses?.length )
      }
      return studentRecords;
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const taqr_id = id;
    if (taqr_id == -1){
      const {itemId} = <any>params?.query
      return dbRawReadSingle(this.app, [itemId], `
        SELECT tq.config AS config, tqs.style_profile AS style_profile
        FROM test_questions tq
        LEFT JOIN temp_question_set tqs
        ON tqs.id = tq.question_set_id
        WHERE tq.id = ?;

      `)
    }
    const taqr = await dbRawReadSingle(this.app, [taqr_id ], `
      select taqr.response_raw response_raw
           , tq.config config
           ,  tq.question_set_id
      from test_attempt_question_responses taqr
      join test_questions tq 
        on taqr.test_question_id = tq.id
      where taqr.id = ?
    `)
    const style_profile = await dbRawReadSingle(this.app, [taqr?.question_set_id], 
      `
      select style_profile
      from temp_question_set
      where id = ?
      `)
    const scanInfo = await this.getTaqrScanInfo(taqr_id)
    return {
      ...taqr, 
      scan_url: scanInfo?.scan_url,
      full_scan: scanInfo?.full_scan,
      is_scan_validated: scanInfo?.is_scan_validated,
      ...style_profile
    }
  }

  async getStudentRecord(excludeNotStartedAttempt:any, test_window_id:any, AND_PARAM_CLAUSES:any, isGuestStudent = false) {
    const exemptionKeys = [ EXEMPTION_TYPES.ExemptionRead,EXEMPTION_TYPES.ExemptionWrite,EXEMPTION_TYPES.ExemptionMath]
    const studentRecords = await dbRawReadReporting(this.app, {exemptionKeys, test_window_id , maxReportGenerateMinutes}, `
      -- Benchmark: 0.312 sec on mirror db
      select 
            ur.uid
          , um_oen.value StudentOEN
          , u.first_name
          , u.last_name
          , sd.foreign_id sd_code
          , sd.name sd_name
          , s.id as schl_id
          , s.foreign_id s_code
          , s.name s_name
          , s.group_id as schl_group_id
          , ur.created_on
          , sc.id sc_id
          , sc.group_id as sc_group_id
          , sc.name sc_name
          , sc.access_code
          , ur.is_revoked
          , ur.revoked_on
          , ss.test_window_id
          , ta.id ta_id
          , ta.test_form_id
          , sc.id sc_id
          , sc.name sc_name
          , tf.lang
          , ta.test_session_id ts_id
          , ta.is_closed 
          , ts.test_window_id ts_test_window_id
          , twtar.type_slug 
          , twtar.slug
          , twtar.is_sample
          , twtar.long_name assessment_name
          , case when (asrg.id is not null and asrg.student_pdf_generated_on is not null) then asrg.id
                 else null 
			      end as asrg_id
          , case when (ansrg.id is not null and ansrg.student_pdf_generated_on is not null) then ansrg.id
                 else null 
			      end as ansrg_id
          , bsrr.id as bsrr_id  -- bsrr.id  generated report id
          , bsrr2.id as bsrr2_id  -- bsrr2.id generating report id
          , sr.id as sr_id
          , case when (asrg.id is null and twtar.generate_report = 1) then 1
                 else 0
            end as report_generation_available
          , case when (asrg.id is not null and asrg.student_pdf_generated_on is null and twtar.generate_report = 1) then 1
                 else 0
            end as report_generating
          , ts.is_closed as ts_is_closed
      from user_metas um_oen 
      join user_roles ur 
        on ur.uid = um_oen.uid
        and um_oen.key = 'StudentOEN'
        and um_oen.key_namespace = 'eqao_sdc'
      join users u
        on u.id = ur.uid
      join school_classes sc 
        on sc.group_id = ur.group_id
      join school_semesters ss
        on ss.id = sc.semester_id
        and ss.test_window_id = :test_window_id
      join schools s 
        on s.group_id = sc.schl_group_id 
      join school_districts sd 
        on sd.group_id = s.schl_dist_group_id 
        /* and sd.is_sample = 0 */ -- exclude samples here
      ${isGuestStudent ? 
        `join school_classes_guest scg on scg.guest_sc_group_id = sc.group_id and scg.is_revoked = 0
         join school_classes sc_invi on sc_invi.group_id = scg.invig_sc_group_id` : ``}
      left join school_class_test_sessions scts 
        on ${isGuestStudent ? `scts.school_class_id = sc_invi.id` : `scts.school_class_id = sc.id`}
      left join test_attempts ta 
        on ta.uid = u.id
        and ta.test_session_id = scts.test_session_id
        ${excludeNotStartedAttempt === 'true' ? `and ta.started_on is not null` : ``}
      left join school_classes sc_2 
        on sc_2.id = scts.school_class_id
      left join test_sessions ts 
        on ts.id = ta.test_session_id 
      left join test_forms tf 
        on tf.id = ta.test_form_id
      left join test_window_td_alloc_rules twtar 
        on twtar.id = ta.twtdar_id
      left join auto_student_report_generations asrg 
        on asrg.test_attempt_id = ta.id 
      	and asrg.student_uid = ur.uid
        and asrg.is_revoked = 0
      left join auto_nfp_student_report_generations ansrg 
        ON ansrg.student_uid = ur.uid 
        AND ansrg.test_window_id = (:test_window_id) 
        AND ansrg.is_revoked != 1
      left join bulk_student_results_reports bsrr on bsrr.school_groud_id = s.group_id and bsrr.test_window_id = :test_window_id and bsrr.s3_report_link is not null and bsrr.is_revoked = 0 
      left join bulk_student_results_reports bsrr2 on bsrr2.school_groud_id = s.group_id and bsrr2.test_window_id = :test_window_id and bsrr2.s3_report_link is null and bsrr2.is_revoked = 0 and (bsrr2.created_on >= NOW() - INTERVAL (:maxReportGenerateMinutes) MINUTE)
      left join student_reports sr 
        on sr.attempt_id = ta.id 
        and sr.uid = ur.uid
        and sr.is_revoked = 0
      where twtar.test_window_id = :test_window_id
        ${AND_PARAM_CLAUSES.join('\n')}
      group by ur.uid, ta.id
      order by u.last_name
            , u.first_name
            , ta.id
            , twtar.order
      limit 3000
    `);
    return studentRecords;
  }

  async getTaqrScanInfo(taqr_id:Id){
    const scanRecord = await dbRawReadSingle(this.app, [taqr_id], `
      select tasr.id
           , tasr.scan
           , tasr.full_scan
           , tasr.is_allow_review_bypass
           , srtp.id srtp_id
           , srtp.is_locked
      from test_attempt_scan_responses tasr 
      left join scan_review_taqr_pool srtp
        on tasr.taqr_id = srtp.taqr_id
        and tasr.id = srtp.cached_tasr_id
      where tasr.taqr_id = ?
        and tasr.is_discarded = 0
      order by srtp.id desc -- to make sure that the one that is linked is picked first
    `)
    if (!scanRecord) return null

    const processURLs = [scanRecord.scan, scanRecord.full_scan]
                        .map(scanRecordUrl => {
                          const PREFIX = 'http'
                          return scanRecordUrl.startsWith(PREFIX) ? scanRecordUrl : generateS3DownloadUrl(scanRecordUrl)
                        })
    const [scan_url, full_scan] = processURLs
    //In issue reviewer, still display invalid scans (not placeholder image), but return validated status
    let is_scan_validated = (!scanRecord.srtp_id || +scanRecord.is_locked !== 1) ? false : true
    if (scanRecord.is_allow_review_bypass == 1) {
      is_scan_validated = true;
    }
    return {is_scan_validated, scan_url, full_scan}   
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
