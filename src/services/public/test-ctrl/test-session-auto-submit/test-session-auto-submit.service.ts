// Initializes the `public/test-ctrl/test-session-auto-submit` service on path `/public/test-ctrl/test-session-auto-submit`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TestSessionAutoSubmit } from './test-session-auto-submit.class';
import hooks from './test-session-auto-submit.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-session-auto-submit': TestSessionAutoSubmit & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-session-auto-submit', new TestSessionAutoSubmit(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-session-auto-submit');

  service.hooks(hooks);
}
