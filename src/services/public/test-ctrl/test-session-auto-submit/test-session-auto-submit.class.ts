import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import moment from 'moment';

interface Data {}

interface ServiceOptions {}

export class TestSessionAutoSubmit implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params || !params.query){
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    
    const {test_window_id, simulate_run_time, isDryRun, IsSentPopup } = data

    if (!test_window_id) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const appDateNow = moment().utc()
    const jobRunTime = simulate_run_time ? moment.tz(simulate_run_time, 'America/Toronto').utc() : appDateNow

    // Fetch the actual test window from database
    const testWindowQuery = await this.app.get('knexClientRead')
      .select('*')
      .from('test_windows')
      .where('id', test_window_id)
      .first();

    if (!testWindowQuery) {
      throw new Errors.BadRequest('TEST_WINDOW_NOT_FOUND');
    }

    const testWindow = {
      id: testWindowQuery.id,
      auto_close_start_on: testWindowQuery.auto_close_start_on,
      auto_close_end_on: testWindowQuery.auto_close_end_on,
      auto_close_default_extension_m: testWindowQuery.auto_close_default_extension_m || 0,
      auto_close_grace_period_m: testWindowQuery.auto_close_grace_period_m || 0
    };

    const autoCloseData = await this.app.service('public/cron/test-session-auto-submit').runAutoSubmitJobByTestWindow(testWindow, jobRunTime, IsSentPopup, isDryRun)

    if (!autoCloseData) {
      throw new Errors.BadRequest('NO_SESSION_TO_AUTO_CLOSED');
    }

    return autoCloseData;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
