
import * as DBT from "../../../types/db-types";
import { AccountType } from "../../../types/account-types";

export interface IUserRole {
    id ?: DBT.ID,
    role_type : DBT.VARCHAR,
    uid : DBT.ID,
    group_id : DBT.ID,
    created_on ?: DBT.DATETIME,
    created_by_uid ?: DBT.ID,
    expires_on ?: DBT.DATETIME,
    is_revoked ?: DBT.ID,
    revoked_on ?: DBT.DATETIME,
    confirmed_on ?: DBT.DATETIME,
    is_confirmed ?: DBT.BOOL_INT,
    is_conf_req ?: DBT.BOOL_INT
}

