export enum EncryptKeyOption {
  Key1Key2 = 'Key1Key2',
  Key2Key1 = 'Key2Key1',
}
  
/**
 * Enum representing the various security levels available in the LockDown Browser
 * The LockDown Browser allows different security levels to be set using CGI variables.
 */
export enum LDBSecurityLevel {
  /**
   * Low security: Blocks access to other applications but allows navigation to course tools like chat and search boxes.
   */
  rldbsl = 'rldbsl',

  /**
   * Medium security: Blocks access to other applications and restricts navigation to course tools like chat and search boxes.
   */
  rldbsm = 'rldbsm',

  /**
   * Medium high security: Navigation is blocked, refresh is disabled, but allows students to close the browser with a confirmation prompt. Recommended for exams.
   */
  rldbsp = 'rldbsp',

  /**
   * High security: "Quiz mode," students are not allowed to close the browser or exit the page until the quiz is completed. Refresh is allowed.
   */
  rldbsh = 'rldbsh',

  /**
   * Very high security: Similar to high security but blocks the refresh button.
   */
  rldbsv = 'rldbsv',

  /**
   * Exit browser: Closes the browser automatically.
   */
  rldbxb = 'rldbxb',
}

export enum LDBCommand {
  BLOCKLIST = 'rldbbl',
  WHITELIST = 'rldbwl',
  DOMAIN_WHITELIST = 'rldbacd'
}

export interface ILDBLinkData {
  clientDomain: string,
  lang: string,
  cbLDBex: number,
  LDBLoginConfiguration: ILDBLoginConfiguration
}

export interface ILDBLoginConfiguration {
  strategy: LDBLoginStrategy,
  authToken?: string,
  passwordSeed?: number,
  loginInfo?: {
    accessCode: string,
    studentNumber: string,
    isSasnLogin: string,
    dob: string
  }
}

export enum LDBLoginStrategy {
  AUTH_TOKEN = 'AUTH_TOKEN',
  ACCESS_CODE = 'ACCESS_CODE'
}

export enum LDBConfigLevel {
  SYSTEM = 'SYSTEM',
  BOARD = 'BOARD',
  SCHOOL = 'SCHOOL',
  CLASS = 'CLASS'
}