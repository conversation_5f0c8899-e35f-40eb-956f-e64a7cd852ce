// Initializes the `auth/lock-down-browser` service on path `/auth/lock-down-browser`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { LockDownBrowser } from './lock-down-browser.class';
import hooks from './lock-down-browser.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'auth/lock-down-browser': LockDownBrowser & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/auth/lock-down-browser', new LockDownBrowser(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('auth/lock-down-browser');

  service.hooks(hooks);
}