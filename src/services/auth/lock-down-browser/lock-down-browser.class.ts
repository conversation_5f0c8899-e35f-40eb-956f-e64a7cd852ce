import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';
import crypto from 'crypto';
import logger from '../../../logger';
import { IRespondusConfig } from '../../../types/app-types';
import { getSysConstString } from '../../../util/sys-const-string';
import { EncryptKeyOption, ILDBLinkData, ILDBLoginConfiguration, LDBCommand, LDBLoginStrategy, LDBSecurityLevel } from './data/types';
import { Knex } from 'knex';
import { dbRawRead } from '../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class LockDownBrowser implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  respondusConfig: IRespondusConfig;

  started_page = 'ldb-redirect'
  validateString = 'just some random stuff'  // Challange Phrase, can be different for every student but need to stored in redis

  applicationBlockList = [  //Blocked Applications
    //'BlockBrowsers',  // close Chrome, Edge, Safari, Brave, etc.
    //'chrome.exe', // Chrome browser
    'mspaint.exe', // Windows Paint
  ]

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    
    this.respondusConfig = app.get('respondusBrowser') as IRespondusConfig;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Validate the challenge response from lock down browser
   * @param params 
   */
  async get (id: Id, params?: Params): Promise<Data> {

    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    return this.validateChallenge(params)
  }

  /**
   * Get LDB(Lock Down Browser) Encrpted Launch Link
   * @returns string Encrpted Launch Link
   */
  async create (data: ILDBLinkData, params?: Params): Promise<Data> {
    if (!data || !params || !data?.LDBLoginConfiguration?.strategy) {
      throw new Errors.BadRequest();
    }
    const { clientDomain, lang, LDBLoginConfiguration, cbLDBex } = data;

    return this.getLDBEncrptedLaunchLink(clientDomain, lang, cbLDBex, LDBLoginConfiguration)
  }

  /**
   * Used to check if student should use LDB or not
   * @param params 
   * @returns bool:  challenge response result
   */
  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }

    // const { accessCode } = data;
    // const classesGroupType = await dbRawReadReporting(this.app, { accessCode }, `
    //   select sc.group_type from mpt_dev.school_classes sc where sc.access_code = :accessCode
    // ;`);
    // const useLDB = classesGroupType[0]?.group_type !== 'EQAO_G10' //Force to use LDB except OSSLT
    const useLDB = true //Force to use LDB

    return { useLDB }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Get LDB Encrypted Launch Link
   */
  async getLDBEncrptedLaunchLink(
    clientDomain: string, 
    lang: string, 
    cbLDBex: number, 
    LDBLoginConfiguration: ILDBLoginConfiguration
  ) {

    let restart_page: string;
    if(LDBLoginConfiguration.strategy === LDBLoginStrategy.ACCESS_CODE && LDBLoginConfiguration.loginInfo) {
      const {accessCode, studentNumber, isSasnLogin, dob} = LDBLoginConfiguration.loginInfo
      restart_page = `${clientDomain}#/${lang}/${this.started_page}/${accessCode}/${studentNumber}/${isSasnLogin}/${dob}`;
    } else if (LDBLoginConfiguration.strategy === LDBLoginStrategy.AUTH_TOKEN && LDBLoginConfiguration.authToken) {
      const authToken = LDBLoginConfiguration.authToken;
      restart_page = `${clientDomain}#/${lang}/${this.started_page}/${authToken}`;
    } else {
      throw new Errors.BadRequest('ERR_LDB_INVALID_STRATEGY')
    }

    const ldbLaunchConfig = await this.getLdbLaunchConfiguration(<number>LDBLoginConfiguration.passwordSeed);
    
    // set up chanllenge to check if client is using LDB, 
    // the client need to sent back challenge response to API to validate (Create function)
    const challenge = this.getChallenge()
    if(challenge){
      restart_page += `?rldbsi=1&rldbacv=${challenge}`;
    }
    
    // set up security level
    const securityLevelURLParams = this.getSecurityLevelURLParams(ldbLaunchConfig.security_level)
    if(securityLevelURLParams){
      restart_page += `&${securityLevelURLParams}`;
    }

    if(LDBLoginConfiguration.strategy = LDBLoginStrategy.AUTH_TOKEN) {
      restart_page += `&authtoken=1`
    }

    // Set up encrypted exist browser pwd
    const exitBrowserPWDURLParams = this.getExitBrowserPWDURLParams(LDBLoginConfiguration.passwordSeed || 123456);
    if(exitBrowserPWDURLParams){
      restart_page += `&${exitBrowserPWDURLParams}`;
    }
    
    // set up block/close application while LDB start
    const applicationBlockListURLParams = await this.getLDBCommand(LDBCommand.BLOCKLIST, ldbLaunchConfig.blacklist);
    if(applicationBlockListURLParams){
      restart_page += applicationBlockListURLParams;
    }

    const ldbAllowlist = await this.getLDBCommand(LDBCommand.WHITELIST, ldbLaunchConfig.whitelist);
    if(ldbAllowlist){
      restart_page += ldbAllowlist;
    }

    const domainList = await this.app.service('public/anon/ldb-config').getLdbDomainlist(ldbLaunchConfig.domain_whitelist);
    const ldbDomainAllowlist = await this.getLDBCommand(LDBCommand.DOMAIN_WHITELIST, domainList);
    if(ldbDomainAllowlist){
      restart_page += ldbDomainAllowlist;
    }

	  let plaintext = `<z><u>${restart_page}</u></z>`;

	  // launch link
    const base64 = this.lockDownBrowserPlainTextEncrypt(plaintext, EncryptKeyOption.Key1Key2)
	  let link = `ldb1:${this.respondusConfig.secret_index}:${this.respondusConfig.secret_version}:%5B${base64}%5D`;

    // Check if its using chrome extension, launch link is different in that case
    const isChromebook = cbLDBex == 1
    if(isChromebook){
      link = `data://text/plain,${link}`
    }

    logger.info('LDB_STARTLINK', { link, plaintext, clientDomain, lang, LDBLoginConfiguration })

    return link  
  }

  /**
   * Gets relevant launch LDB configuration based on the class_id
   * - fetches relevant school and board
   * - fetches system default configuration
   */
  async getLdbLaunchConfiguration(class_id: number) {
    const details = await dbRawRead(this.app, { class_id }, `
      SELECT
        sd.id as school_board_id,
        sc.id as school_class_id,
        s.id  as school_id
      FROM school_classes sc
      JOIN schools s ON s.group_id = sc.schl_group_id
      JOIN school_districts sd ON sd.group_id = s.schl_dist_group_id
      WHERE sc.id = :class_id
      `);

    const ldbConfigs = await this.app.service('public/anon/ldb-config').getLdbConfig(details[0]);
    const compiledConfig = await this.app.service('public/anon/ldb-config').compileLdbConfig(ldbConfigs);

    return compiledConfig;
  }

  /**
   * Get the challenge string
   *  - The challenge is a string that the client must encrypt and send back to the server to validate that the client is using the LockDown Browser
   *  - The challenge should be unique for each student and should be stored in a session or database
   *  - The challenge should be tied to the current browser session to prevent replay attacks
   *  - The challenge can be time-based but should not expire for several hours, because some exams can take 2+ hours
   */
  private getChallenge(){
    const raw_plaintext = this.validateString

    // Recommended: you can technically use any plaintext up to 128 characters long, but the MD5 hash sets the length to exactly 32 characters and removes any sensitive info
    let plaintext = raw_plaintext;

    // (A MD5 hash is already padded to a multiple of 16 since it is 32 hex characters.  This can be skipped if you use MD5 hash)
    // space-pad plaintext to a length that is a multiple of 16, ensures encryption library will not do its own padding. DO NOT use zero padding or other padding
    // unlike with the launch link, PKCS#7 is also valid.  The resposne will use ALL of the plaintext INCLUDING this padding
    // plaintext = this.padPlainText(plaintext)	
    
    // This should be stored linked to the session so that it can be retrieved from the server to computer the expected response
    const base64 = this.lockDownBrowserPlainTextEncrypt(plaintext, EncryptKeyOption.Key1Key2)

    // as a CGI variable the base64 will need to be URL-encoded by the caller
    const challenge_string = `${this.respondusConfig.secret_index}:${this.respondusConfig.secret_version}:${base64}`
    const returnString = encodeURIComponent(challenge_string)
    
    logger.info('LDB_CHALLENGE', { plaintext, challenge_string, returnString })

    return returnString
  }

  /**
   * Validate the LDB challenge response
   * @param params 
   * @returns bool:  challenge response result
   */
  public validateChallenge(params?:Params){
    // get validate value
    const responseCookieValueURLDecoded = decodeURIComponent(params?.query?.rldbarv)

    // in production plaintext should be fetched from a database or session storage because it must be identical to the plaintext used for the challenge
    const raw_plaintext = this.validateString // maybe need to change this later
    let plaintext = raw_plaintext;

    // encrypt to binary using AES256, 32-byte key, CBC mode
    const expected_response = this.lockDownBrowserPlainTextEncrypt(plaintext, EncryptKeyOption.Key2Key1)

    // Validate
    const responses = {
      responseCookieValueURLDecoded,
      plaintext,
      expected_response,
      result: false
    }

    if(responseCookieValueURLDecoded === expected_response){
      responses.result = true
    }

    logger.info('LDB_VAILDATION_RESPONSES', { responses })

    return responses
  }

  /**
   * Encrypt Plain Text for Lock Down Browser
   *  - Encrypts plaintext using AES256, 32-byte key, CBC mode
    * - Pads plaintext to a multiple of 16 bytes
    * - Returns base64-encoded ciphertext
   * @param plaintext 
   * @returns encrypted value
   */
  private lockDownBrowserPlainTextEncrypt(plaintext:string, keyOption:EncryptKeyOption) {
    //const key = `${this.secret_1}${this.secret_2}`;
    const key = this.getEncryptedKey(keyOption)
    const keyBuffer = Buffer.from(key);  // Convert key to Buffer
    const ivBuffer = Buffer.from(this.respondusConfig.secret_iv);  // Convert IV to Buffer

    // Create cipher object
    const cipher = crypto.createCipheriv(this.respondusConfig.cipher, keyBuffer, ivBuffer);
    
    // Disable padding (equivalent to OPENSSL_ZERO_PADDING in PHP)
    cipher.setAutoPadding(false);

    // Manually pad plaintext to match block size if necessary
    const paddedPlaintext = this.padPlaintext(plaintext);

    // Encrypt the plaintext
    let encrypted = cipher.update(paddedPlaintext, 'utf-8', 'binary');
    encrypted += cipher.final('binary'); // Finalize encryption

    // Convert binary string to Buffer, than to base 64
    const string = Buffer.from(encrypted, 'binary');
    const base64 = this.toBase64(string); 

    return base64;
  }

  /**
   * Get the key for encrption
   * @param keyOption 
   * @returns key
   */
  private getEncryptedKey(keyOption:EncryptKeyOption){
    switch(keyOption){
      case EncryptKeyOption.Key2Key1:
        return `${this.respondusConfig.secret_2}${this.respondusConfig.secret_1}`
      case EncryptKeyOption.Key1Key2:
      default: //set Key1Key2 as default EncryptedKey
         return `${this.respondusConfig.secret_1}${this.respondusConfig.secret_2}`
    }
  }

  /**
   * Pad plaintext to match block size
   * @param blockSize default `16`
   */
  private padPlaintext(plaintext: string, blockSize: number = 16): string {
    const paddingLength = blockSize - (plaintext.length % blockSize);
    return plaintext + ' '.repeat(paddingLength);
  }

  /** Convert to Base64 */ 
  private toBase64(buffer:any) {
    return buffer.toString('base64');
  }
  
  /**
   * Get the security url params
   */
  private getSecurityLevelURLParams(securityLevel = LDBSecurityLevel.rldbsp){    
    return `${securityLevel}=1`
  }

  /**
   * Sets proctor (exit) password for the LDB
   */
  private getExitBrowserPWDURLParams(passwordSeed: number) {
    const proctorPassword = this.generateAccessCode(passwordSeed);

    const encryptedExitBrowserPwd = this.lockDownBrowserPlainTextEncrypt(proctorPassword, EncryptKeyOption.Key1Key2)
    const exitBrowserPwdURLParams = `rldbapw=${encodeURIComponent(encryptedExitBrowserPwd)}`
    return exitBrowserPwdURLParams
  }

  /** Add CGI command for allowlist for LDB */
  private async getLDBCommand(command: LDBCommand, list: string[]) {
    if(!list || !list.length) {
      return '';
    }

    return '&' + command + '=' + encodeURIComponent(`(${list.join(',')})`);
  }

  /**
   * Generates a fixed-length access code based on the provided input number.
   * - The access code is deterministic for a given input, meaning the same input number will always produce the same code.
   * - It consists of uppercase letters and numbers, excluding look-alike characters such as 'I', 'O', '1', and '0' to avoid confusion.
   * @param inputNumber - The number used as a seed to generate the access code.
   * @returns A string representing the generated access code.
   * 
   */
  public generateAccessCode(inputNumber: number): string {
    // Linear Congruential Generator for pseudo-random number generation
    const m = 4294967296; // 2^32
    const a = 1664525;
    const c = 1013904223;
    let state = inputNumber % m;

    const nextFloat = (): number => {
        state = (a * state + c) % m;
        return state / m;
    };

    // Define character set excluding look-alike characters
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excludes I, O, 0, 1

    // Set code length and hyphen position
    const codeLength = 7;
    const hyphenPosition = 3;

    let code = '';

    for (let i = 0; i < codeLength; i++) {
        if (i === hyphenPosition) {
            code += '-';
        } else {
            const randomIndex = Math.floor(nextFloat() * charset.length);
            code += charset.charAt(randomIndex);
        }
    }

    logger.info('LDB_PROCTOR_PASSWORD', {sch_class_id: inputNumber, code})

    return code;
  }
}