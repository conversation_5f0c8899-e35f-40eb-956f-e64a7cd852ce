import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { IURoleActions } from '../../db/schemas/u-role-actions.schema';
import { IUserRole } from '../../db/schemas/user-roles.schema';
import { DB_MAX_ROLE_ACTIONS_DEF, DB_MAX_GROUP_ROLES_PER_USER, DB_MAX_SINGULAR_GROUPS } from '../../../constants/db-limits';
import { IUGroupSingular } from '../../db/schemas/u_groups_singular.schema';
import { dbDateNow, dbDateOffsetDays } from '../../../util/db-dates';
import { Knex } from 'knex';
import { Errors } from '../../../errors/general';
import { DBD_U_ROLE_TYPES } from '../../../constants/db-extracts';
import logger from '../../../logger';
import { Redis } from 'ioredis';

interface Data {}

interface ServiceOptions {}

interface IRoleCreation {
  uid : number,
  role_type : string,
  group_id : number,
  created_by_uid ?: number,
  expiryDaysOffset ?: number,
  isCheckForExisting ?: boolean,
  isConfirmReq ?:boolean
}

type DataTarget = string;
type DataMethod = string;
type RoleType = string;

export class UserRoleActions implements ServiceMethods<Data> {

  app: Application;
  options: ServiceOptions;
  private roleActionMap:Map<DataTarget, Map<DataMethod, RoleType[]>> = new Map();
  private singularGroups:Map<string, number> = new Map();
  private initState:{isRoleActionMapInitComplete: boolean, isSingularGroupsInitComplete:boolean};

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.initRoleActionMap();
    this.initSingularGroups();
    this.initState = {
      isRoleActionMapInitComplete: false,
      isSingularGroupsInitComplete: false
    };
  }

  public isInitComplete () {
    return this.initState.isRoleActionMapInitComplete && this.initState.isSingularGroupsInitComplete;
  }

  public isInitSuccessful () {
    return this.isInitComplete() && this.singularGroups.size !== 0 && this.roleActionMap.size !== 0;
  }

  private async asyncErrorThrow(error:Error, errorThrower:(error:Error) => any){
    setTimeout(() => errorThrower.call(this, error), 1); // to avoid breaking the initilization process unecessarily
  }

  private async initRoleActionMap(){
    let initRoleActionMapError:null|Error = null, roleActionRecords:Paginated<IURoleActions>;
    try {
      roleActionRecords = <Paginated<IURoleActions>> await this.app
        .service('db/read/u-role-actions')
        .find({query:{$limit: DB_MAX_ROLE_ACTIONS_DEF}})

      logger.info('Loaded roleActionRecords: %d of %d', roleActionRecords.total, roleActionRecords.limit);
      roleActionRecords.data.forEach(record => {
        const roleTypes = this.tunnelMapToRoleTypes(record.data_target, record.data_method, true);
        roleTypes.push(record.role_type)
      })
    } catch (e) {
      initRoleActionMapError = e as Error;
      initRoleActionMapError.message = 'ROLE_ACTIONS_INIT_FAILURE\n' + initRoleActionMapError.message
      delete (initRoleActionMapError as any).hook;
    } finally {
      this.asyncErrorThrow(initRoleActionMapError || new Errors.GeneralError('ROLE_ACTIONS_INIT_FAILURE'), (error:Error) => {
        this.initState.isRoleActionMapInitComplete = true;
        if (!roleActionRecords?.data?.length) {
          throw error;
        }
      })
    }
  }

  private async initSingularGroups(){
    let initSingularGroupsError:null|Error = null, groupsSingularRecords:Paginated<IUGroupSingular>;
    try {
      // pre-pull all of the groups that are designed to be the only one of their kind in the system
      groupsSingularRecords = <Paginated<IUGroupSingular>> await this.app
      .service('db/read/u-groups-singular')
      .find({query:{$limit: DB_MAX_SINGULAR_GROUPS}});

      logger.info('Loaded groupsSingularRecord: %d of %d', groupsSingularRecords.total, groupsSingularRecords.limit);
      groupsSingularRecords.data.forEach(record => {
      this.singularGroups.set(record.group_type, <number>record.id);
      })
    } catch (e) {
      initSingularGroupsError = e as Error;
      initSingularGroupsError.message = 'SINGULAR_GROUPS_INIT_FAILURE\n' + initSingularGroupsError.message
      delete (initSingularGroupsError as any).hook;
    } finally {
      this.asyncErrorThrow(initSingularGroupsError || new Errors.GeneralError('SINGULAR_GROUPS_INIT_FAILURE'), (error:Error) => {
        this.initState.isSingularGroupsInitComplete = true;
        if (!groupsSingularRecords?.data?.length) {
          throw error;
        }
      })
    }
  }

  async getSingularGroupId(singularGroupType:string){
    if (this.singularGroups.size === 0) {
      throw new Errors.GeneralError('NO_SINGULAR_GROUPS_FOUND');
    }
    return this.singularGroups.get(singularGroupType);
  }

  private tunnelMapToRoleTypes(dataTarget:string, dataMethod:string, isContructing:boolean=false) {
    const targetMap = this.roleActionMap;
    if (targetMap.size === 0 && !isContructing) {
      throw new Errors.GeneralError('NO_ROLE_ACTIONS_FOUND');
    }
    if (!targetMap.has(dataTarget)){
      if (!isContructing){ return []; }
      targetMap.set(dataTarget, new Map());
    }
    const methodMap = <Map<DataMethod, RoleType[]>> targetMap.get(dataTarget);
    if (!methodMap.has(dataMethod)){
      if (!isContructing){ return []; }
      methodMap.set(dataMethod, []);
    }
    return <string[]> methodMap.get(dataMethod);
  }

  private async isRoleForTargetMethod(dataTarget:string, dataMethod:string, roleTypesFromUserGroup:string[]){
    const roleTypesRef = this.tunnelMapToRoleTypes(dataTarget, dataMethod);
    logger.silly('isRoleForTargetMethod', { roleTypesRef, dataTarget, dataMethod, roleTypesFromUserGroup });
    for (let i=0; i<roleTypesFromUserGroup.length; i++){
      const roleType = roleTypesFromUserGroup[i];
      if (roleTypesRef.indexOf(roleType) !== -1){
        return true;
      }
    }

    /// temp
    // throw new Errors.GeneralError(JSON.stringify({
    //   roleTypesRef,
    //   dataTarget,
    //   dataMethod,
    //   roleTypesFromUserGroup
    // }))
    return false;
  }

  async downloadDataFilePath(id: number, singularGroupType:string){
    const groupId = await this.app
      .service('auth/user-role-actions')
      .getSingularGroupId(singularGroupType);
    const record = await this.app.service('db/read/downloadable-data-files').get(id);
    if (record){
      if (record.group_id === groupId){
        return record;
      }
      else{
        throw new Errors.NotFound('GROUP_ROLE_REQ_FOR_FILE');
      }
    }
    else{
      throw new Errors.NotFound('NO_RECORDS');
    }
  }

  async getSystemRoles(uid:number, singularGroupType:string){
    const groupId = await this.app
      .service('auth/user-role-actions')
      .getSingularGroupId(singularGroupType);

    if (groupId !== undefined){
      return this.getUserRoles(groupId, uid);
    }
    return [];
  }

  public async getUserRolesByGroupType(uid: number, groupType:string){
    const db:Knex = this.app.get('knexClientRead');
    const res = await db.raw(`
      select ur.group_id, ur.role_type
      from user_roles ur
      join u_groups g
        on g.id = ur.group_id
        and g.group_type = ?
      where ur.uid = ?
        and ur.is_revoked = 0
        AND ((ur.expires_on IS NULL) OR (ur.expires_on > NOW()))
    `, [groupType, uid])
    const records = res[0];
    return records;
  }


  private async getUserRolesMultGroups(groupIds: number[], uid?: number) {
    const userRoleQueryFields = {
      uid,
      group_id: {$in: groupIds},
      is_revoked: <any>{ $ne: 1 },
      $or: [
        {expires_on: null},
        {expires_on: {
            $gt: dbDateNow(this.app)
          }
        }
      ]
    }
    const userRoleRecords = await this.app
      .service('db/read/user-roles')
      .find({query:{
        $limit: DB_MAX_GROUP_ROLES_PER_USER,
        ... userRoleQueryFields
      }, paginate: false}) as any[];

    return userRoleRecords.map(userRole => userRole.role_type);
  }

  private async getUserRoles(groupId:number, uid: number){
    return this.getUserRolesMultGroups([groupId], uid);
  }



  public async revokeRoleFromGroup(group_id:number, role_type:string, revoked_by_uid: number|null = null){
    // revoke single role which user has from the group
    return this.applyRevokeToQuery(
      revoked_by_uid,
      this.startUserRoleWrite()
        .where('role_type', role_type)
        .where('group_id', group_id)
    )
  }

  public async revokeUserFromGroup(uid: number, group_id:number, revoked_by_uid :number|null= null){
    return this.revokeUserFromGroups(uid, [group_id], revoked_by_uid);
  }

  public async revokeUserFromGroups(uid: number, group_ids: number[], revoked_by_uid :number|null= null) {
        // revoke all roles which the user has on the group
        return this.applyRevokeToQuery(
          revoked_by_uid,
          this.startUserRoleWrite()
            .where('uid', uid)
            .whereIn('group_id', group_ids)
        )
  }

  public async revokeUserRoleById(id: number, revoked_by_uid:number|null = null){
    // revoke all roles which the user has on the group
    return this.applyRevokeToQuery(
      revoked_by_uid,
      this.startUserRoleWrite()
        .where('id', id)
        .limit(1)
    )
  }

  public async revokeUserRoleByIds(ids: number[], revoked_by_uid?:number){
    return this.applyRevokeToQuery(revoked_by_uid,
      this.startUserRoleWrite()
        .whereIn('id', ids)
    )
  }

  public async revokeUser(uid: number, revoked_by_uid:number|null = null){
    // revoke all roles which the user has on the group
    return this.applyRevokeToQuery(
      revoked_by_uid,
      this.startUserRoleWrite()
        .where('uid', uid)
    )
  }

  public async revokeUserRolesFromGroupNotInRoleTypes(uid: number, group_id: number, role_types: string[]) {
    return this.applyRevokeToQuery(null,
      this.startUserRoleWrite()
      .whereNotIn('role_type', role_types)
      .where('uid', uid)
      .where('group_id', group_id)
    )
  }

  public async revokeUserRolesFromGroup(uid: number, group_id: number, role_types: string[], revoked_by_uid: number | null = null) {
    return this.applyRevokeToQuery(revoked_by_uid,
      this.startUserRoleWrite()
      .whereIn('role_type', role_types)
      .where('uid', uid)
      .where('group_id', group_id)
    )
  }

  public async revokeUserRoleFromGroup(uid: number, group_id:number, role_type:string, revoked_by_uid: number | null = null){
    return this.revokeUserRolesFromGroup(uid, group_id, [role_type], revoked_by_uid);
  }

  private startUserRoleWrite() : Knex{
    return this.app
      .service('db/write/user-roles')
      .db()
  }
  private applyRevokeToQuery(revoked_by_uid: number | null = null, query:Knex.QueryBuilder){
    return query
      .whereNot('is_revoked', 1)
      .update({
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid,
      })
  }

  public async assignUserRoleToGroup(config:IRoleCreation) : Promise<IUserRole> {
    const {
      uid,
      group_id,
      role_type,
      created_by_uid,
      expiryDaysOffset, // option
      isCheckForExisting, // option
      isConfirmReq // option
    } = config;

    if (config.isCheckForExisting){
      const existingRecords = <Paginated<any>> await this.app
        .service('db/write/user-roles')
        .find({
          query: {
            uid,
            group_id,
            role_type,
            is_revoked: 0,
          }
        });
      if (existingRecords.total > 0){
        return existingRecords.data[0];
      }
    }
    const userRoleCreateField: Partial<IUserRole> = {
      uid,
      group_id,
      role_type,
      created_by_uid,
    }
    if ((config.expiryDaysOffset !== undefined) && (config.expiryDaysOffset !== null)){
      userRoleCreateField.expires_on = dbDateOffsetDays(this.app, config.expiryDaysOffset);
    }
    if(isConfirmReq) {
      userRoleCreateField.is_conf_req = 1
    }

    // create new role
    return this.app
      .service('db/write/user-roles')
      .create({
        ... userRoleCreateField
      })
  }

  async updateInsertUserMetaRecord(payload:{uid:number, key_namespace:string, key:string, value:string}, isSkipCheck:boolean = false, isPreventOverwrite = false){
    if (!isSkipCheck){
      const existingRecords = <any[]> await this.app
        .service('db/write/user-metas')
        .find({
          query: {
            uid: payload.uid,
            key_namespace: payload.key_namespace,
            key: payload.key,
          },
          paginate: false
        })
      if (existingRecords.length > 0){
        const record = existingRecords[0];
        if (!isPreventOverwrite){
          await this.app.service('db/write/user-metas').patch(record.id, {
            value: payload.value
          });
        }
        return
      }
    }
    await this.app.service('db/write/user-metas').create(payload);
  }

  async checkUserRole(dataTarget:string, dataMethod:string, groupIds:(number | string)[], uid?: number){
    const groupIdsNumeric = groupIds.filter(id => id).map(groupId => parseInt(''+groupId)) ;

    const roleTypes:string[] = await this.getUserRolesMultGroups(groupIdsNumeric, uid);

    return this.isRoleForTargetMethod(dataTarget, dataMethod, roleTypes);
  }

  /**
   * Check whether we allow caching the role hook results for this specific dataTarget
   */
  async isDataTargetCacheable(dataTarget: string): Promise<boolean> {
    const cacheableTargets = ['public/student/session-question', 'public/educator/session']

    return cacheableTargets.includes(dataTarget);

    // const redis: Redis = this.app.get('redis');
    // return !!(await redis.sismember('allowedTargets', dataTarget));
  }

  /**
   * Cache the access result for the user based on the dataTarget and dataMethod
   */
  async cacheAccess(dataTarget: string, dataMethod: string, group_ids: number[], uid: number): Promise<void> {
    const redis: Redis = this.app.get('redis');
    await redis.hset(`access:${uid}`, `${dataTarget}:${dataMethod}`, group_ids.join(','));
  }

  /**
   * Check whether we have a cached access result for the user based on the dataTarget and dataMethod
   */
  async hasCachedAccess(dataTarget: string, dataMethod: string, user_group_ids: number[], uid: number): Promise<boolean> {
    const redis: Redis = this.app.get('redis');
    const group_ids = await redis.hget(`access:${uid}`, `${dataTarget}:${dataMethod}`);

    if (group_ids) {
      const group_ids_array = group_ids.split(',');
      return user_group_ids.some(group_id => group_ids_array.includes(group_id.toString()));
    }

    return false
  }

  async checkUserGroupRoles(groupIds:(number | string)[], uid: number, targetRoles:DBD_U_ROLE_TYPES[]){
    const groupIdsNumeric = groupIds.filter(id => id).map(groupId => parseInt(''+groupId)) ;
    const userGroupRoleTypes:string[] = await this.getUserRolesMultGroups(groupIdsNumeric, uid);
    let isMatch = false;
    targetRoles.forEach(targetRole => {
      if (userGroupRoleTypes.indexOf(targetRole) !== -1){
        isMatch = true;
      }
    })
    return isMatch;
  }


  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }
    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

}
