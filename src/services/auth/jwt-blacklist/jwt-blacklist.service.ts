// Initializes the `auth/jwt-blacklist` service on path `/auth/jwt-blacklist`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { JwtBlacklist } from './jwt-blacklist.class';
import hooks from './jwt-blacklist.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'auth/jwt-blacklist': JwtBlacklist & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/auth/jwt-blacklist', new JwtBlacklist(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('auth/jwt-blacklist');

  service.hooks(hooks);
}
