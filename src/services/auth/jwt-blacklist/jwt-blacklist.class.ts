import { Id, NullableId, Paginated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';
import { Redis } from 'ioredis';
import { KInvalidatedJWT } from '../../../redis/redis';
import crypto from 'crypto';
const decode = require('jwt-decode');
import logger from '../../../logger';

interface Data {
  isBlacklisted?: boolean;
  success?: boolean;
  message?: string;
  removed?: number;
  [key: string]: any;
}

interface ServiceOptions {}

interface JWTPayload {
  exp?: number;
  iat?: number;
  uid?: number;
  [key: string]: any;
}

interface BlacklistTokenData {
  token: string;
}

export class JwtBlacklist implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  private redis: Redis;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.redis = app.get('redis');
  }

  /**
   * Generate SHA-256 hash of JWT token for Redis key
   */
  private generateTokenHash(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Extract expiration time from JWT token with fallback
   */
  private getTokenExpiration(token: string): number {
    try {
      const decoded = decode(token);
      if (decoded.exp) {
        return decoded.exp;
      }
    } catch (error) {
      logger.warn('Failed to decode JWT token, using default expiration', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Fallback to default JWT expiration (24 hours)
    const defaultExpirationHours = this.app.get('authentication')?.jwtOptions?.expiresIn || '24h';
    const hours = typeof defaultExpirationHours === 'string' ?
      parseInt(defaultExpirationHours.replace('h', '')) : 24;
    return Math.floor(Date.now() / 1000) + (hours * 60 * 60);
  }

  /**
   * Calculate TTL for Redis key based on JWT expiration
   */
  private calculateTTL(tokenExp: number): number {
    const now = Math.floor(Date.now() / 1000);
    const bufferMinutes = 10; // 10 minute buffer
    const ttl = (tokenExp - now) + (bufferMinutes * 60);

    // Ensure TTL is positive, minimum 1 minute
    return Math.max(ttl, 60);
  }

  /**
   * Add JWT token to blacklist
   */
  async create(data: BlacklistTokenData | BlacklistTokenData[], params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    const { token } = data;

    if (!token) {
      throw new Errors.BadRequest('Token is required');
    }

    if (!this.redis) {
      return { success: false, message: 'Redis not available' };
    }

    try {
      const tokenHash = this.generateTokenHash(token);
      const tokenExp = this.getTokenExpiration(token);
      const ttl = this.calculateTTL(tokenExp);
      const redisKey = KInvalidatedJWT(tokenHash);

      // Store simple boolean value with TTL
      await this.redis.pipeline()
        .set(redisKey, '1')
        .expire(redisKey, ttl)
        .exec();

      return { success: true };

    } catch (error) {
      logger.error('Failed to blacklist JWT token', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { success: false, message: 'Failed to blacklist token' };
    }
  }

  /**
   * Check if JWT token is blacklisted
   */
  async find(params?: Params): Promise<Data> {
    const { token } = params?.query || {};

    if (!token) {
      throw new Errors.BadRequest('Token is required for blacklist check');
    }

    if (!this.redis) {
      // If Redis is down, allow the request to proceed (graceful degradation)
      return { isBlacklisted: false };
    }

    try {
      const tokenHash = this.generateTokenHash(token);
      const redisKey = KInvalidatedJWT(tokenHash);
      const exists = await this.redis.exists(redisKey);

      return { isBlacklisted: !!exists };

    } catch (error) {
      logger.error('Failed to check token blacklist', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // If there's an error checking the blacklist, allow the request to proceed
      return { isBlacklisted: false };
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('GET method not supported');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('UPDATE method not supported');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed('PATCH method not supported');
  }

  /**
   * Remove token from blacklist
   */
  async remove(id: NullableId, params?: Params): Promise<Data> {
    if (!id) {
      throw new Errors.BadRequest('Token hash is required');
    }

    if (!this.redis) {
      throw new Errors.GeneralError('Redis not available');
    }

    try {
      const redisKey = KInvalidatedJWT(id.toString());
      const result = await this.redis.del(redisKey);
      return { success: result > 0, removed: result };
    } catch (error) {
      logger.error('Failed to remove token from blacklist', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Errors.GeneralError('Failed to remove token from blacklist');
    }
  }
}
