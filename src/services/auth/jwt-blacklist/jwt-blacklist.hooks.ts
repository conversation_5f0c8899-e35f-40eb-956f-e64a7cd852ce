import * as authentication from '@feathersjs/authentication';
import { HooksObject } from '@feathersjs/feathers';

const { authenticate } = authentication.hooks;

export default {
  before: {
    all: [],
    find: [], // No auth required for checking blacklist (used internally)
    get: [],
    create: [], // No auth required for blacklisting (used during logout)
    update: [],
    patch: [],
    remove: [ authenticate('jwt') ] // Require auth for manual token removal
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
