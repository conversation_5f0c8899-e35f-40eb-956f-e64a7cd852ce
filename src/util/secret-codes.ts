import crypto from 'crypto';

export const generateSecretCode = (len:number=8) => {
    return crypto.randomBytes(len).toString('hex');
}

export const renderInvitationCode = (invitationId:number, secretKey:string) => {
    return [invitationId, 'X', secretKey].join('')
}

export const hashValues = (strs:(string|number)[]) => {
    return hashGivenString(strs.join(','));
}
export const hashGivenString = (str:string) => {
    const hash = crypto.createHash('sha256');
    return hash.update(str).digest('hex');
}

export const generateAccessCode = (len:number) => {
    let result       = '';
    const characters = 'BCDFGHJKLMNPQRSTVWXZ';
    const numbers    = '23456789';
    const pick = (str:string) => str.charAt(Math.floor(Math.random() * str.length));
    const charactersLength = characters.length;
    for ( var i = 0; i < len; i++ ) {
        if ((i % 2) === 0){
            result += pick(characters)
        }
        else{
            result += pick(numbers)
        }
    }
    return result;
}
