import { Params } from '@feathersjs/feathers';
import { Errors } from '../errors/general';

export const ALWAYS_ALLOWED_DOMAINS:string[] = [
    'http://localhost:4200/',
    'http://localhost:4401/',
    'undefined/',
]

export const ensureLockedDomain = (params: Params, lockedDomain?:string) => {
  if (lockedDomain){
    let currentDomain = '';
    if (params && params.headers){
      currentDomain = `${params.headers.origin}/`;
    }
    if (!ALWAYS_ALLOWED_DOMAINS.includes(currentDomain)) {
      if (lockedDomain !== currentDomain) {
        throw new Errors.Forbidden('DOMAIN_LOCKED', {lockedDomain});
      }
    }
  }
}