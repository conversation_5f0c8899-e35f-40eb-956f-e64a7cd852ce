
/**
 * Generate uppercase prefix from a camelCase, snake_case and kebab-case string, 
 * e.g "component_slugs" or "COMPONENT_SLUGS" -> "CS"
 * @param {string} nameStr camelCase, snake_case and kebab-case string
 * @returns {string} Generated prefix
 */
export const generatePrefixFromString = (nameStr:string) : string => {
    const joiningChar = /[_-]/;
    let firstLetters = [];
  
    if (joiningChar.test(nameStr)) {
      // Handle snake_case or kebab-case
      firstLetters = nameStr.split(joiningChar).map(word => word[0]);
    } else {
      // Handle camelCase
      firstLetters = nameStr.match(/[A-Z]?[a-z]+/g)?.map(word => word[0]) || [];
    }
  
    return firstLetters.join('').toUpperCase();
  };