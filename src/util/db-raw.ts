import { Application } from '@feathersjs/feathers';
import { Knex } from 'knex';
var SqlString = require('sqlstring');


export const dbRawRead = async (app:Application, props:any[] | {} , query:string) => {
  const db:Knex = app.get('knexClientRead');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}
export const dbRawReadReporting = async (app:Application, props:any[] | {}, query:string) => {
  const db:Knex = app.get('knexClientReadReporting');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}
export const dbRawReadReportingStream = async (app:Application, props:any[] | {}, query:string, streamOptions?: {}) => {
  const db:Knex = app.get('knexClientReadReporting');
  const stream = await db.raw(query, props).stream(streamOptions);
  return stream;
}
export const dbRawReadSingle = async (app:Application, props:any[] | {}, query:string) => {
  const records = await dbRawRead(app, props, query);
  return records[0];
}

export const dbRawReadSingleReporting = async (app:Application, props:any[] | {}, query:string) => {
  const records = await dbRawReadReporting(app, props, query);
  return records[0];
}

export const dbEscapeString = async (str:string, isWildcardWrapper:boolean = false, isQuoteless:boolean = false) => {
  let wrappedString = SqlString.escape(str);
  if (isWildcardWrapper){
    wrappedString = `'%${wrappedString.substr(1, wrappedString.length-2)}%'`
  }
  else if (isQuoteless){
    return wrappedString.substr(1, wrappedString.length-2);
  }
  return wrappedString
}

export const dbEscapeNum = async (str:string | number) => {
  return SqlString.escape(str);
}



export const dbRawReadCount = async (app:Application, query:string, props:any[] = []) => {
  const records = await dbRawRead(app, props, `
    select count(0) as count
    from (
      ${query}
    ) t_count
  ;`)
  return records[0].count;
}

export const dbRawReadCountReporting = async (app:Application, query:string, props:any[] = []) => {
  const records = await dbRawReadReporting(app, props, `
    select count(0) as count
    from (
      ${query}
    ) t_count
  ;`)
  return records[0].count;
}

export const dbRawWrite = async (app:Application, props:any[], query:string) => {
  const db:Knex = app.get('knexClientWrite');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}

export const dbRawWriteMulti = async <T>(app:Application, query:string, source:T[], propExtractor: (entry:T) => any[]) => {
  if(!source || source.length === 0) {
    return;
  }
  const wQueries:string[] = [];
  let wProps:any[] = [];
  source.forEach(entry => {
    wQueries.push(query);
    wProps.splice(wProps.length, 0, ...propExtractor(entry))
  })
  return dbRawWrite(app, wProps, wQueries.join(';') + ";")
}

export const AND_MATCH_ANY = async (query:any, prop:string, isString=true, val?:number|string, isExact?:boolean) => {
  let filterValue = val || query[prop];
  if (!filterValue){ return ''; }
  filterValue = (''+filterValue).trim();
  if (!filterValue){ return ''; }
  const filterOptions = filterValue.split('\n')
  const clauseElements:string[] = [];
  for (let i=0; i<filterOptions.length; i++){
    let filterOption:string = filterOptions[i];
    if (filterOption && filterOption.trim()){
      if (isString){
        if (isExact){
          clauseElements.push(`(${prop} = ${await dbEscapeString(filterOption)})`)
        }
        else {
          clauseElements.push(`(${prop} LIKE ${await dbEscapeString(filterOption, true)})`)
        }
      }
      else{
        clauseElements.push(`(${prop} = ${await dbEscapeNum(filterOption)})`)
      }
    }
  }
  if (filterOptions.length === 0){
    return '';
  }
  return `AND (${ clauseElements.join(' OR ') }) `
}

/**
 *
 * @param app
 * @param table the name of the table to insert into
 * @param rows
 * @param idCol
 * @returns
 */
export async function dbBulkInsert (app:Application, table:string, rows:any[], idCol?:string) {
  const db:Knex = app.get('knexClientWrite');
  const firstInsertId:number = await db(table).insert(rows);
  if (idCol) {
    let currentId = firstInsertId;
    rows.forEach(row => row[idCol] = currentId++)
  }
  return firstInsertId;
}
