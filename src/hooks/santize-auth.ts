// Use this hook to manipulate incoming or outgoing data.
// For more information on hooks see: http://docs.feathersjs.com/api/hooks.html
import { Hook, HookContext, Paginated } from '@feathersjs/feathers';
import { IUTestAdmin } from '../services/db/schemas/u_test_admins.schema';
import { IUser } from '../services/db/schemas/users.schema';
import { AccountType } from '../types/account-types';
import { IAuth } from '../services/db/schemas/auths.schema';
import { IUserInfoCore } from '../services/public/auth/user-info-core/user-info-core.class';
import * as Errors from '@feathersjs/errors';
import { AUTH_ERROR } from '../errors/auth';
import logger from '../logger';
import { dbDateNow } from '../util/db-dates';
type IBefore = IAuth
export default (options = {}): Hook => {
  return async (context: HookContext) => {
    const result = context.dispatch || context.result;
    
    const skipSanitization = false         // If any of the following criteria are met: 
      || (!result)                        //   1. There is no result object (this would be unusual, but it saves us an error down the line by checking for this first).
      || (!context.params.provider)       //   2. limit the sanitization for the external providers (otherwise this sequence will be called 3 times as much).
      || (context.params.isRecordLookup)  //   3. this is a custom flag which is explicitly used to access records from the `auths` table, so no morphing is needed/desired in this case.
    if (skipSanitization){ return context; }

    // asynchronous sanitization function (does not just trim, but adds in important users data too)
    const sanitize = async (data:IBefore) : Promise<IBefore | IUserInfoCore> =>{
      if (!data.id){ return data; }
      const { email, id, uid, } = data;
      const app = context.app;

      //logger it
      logger.info(
        'LocalStrategy', {
        uid,
        method: context.method,
        originalUrl: context.params.originalUrl,
        ip: context.params.ip,
      });

      context.app.service('db/write/auths').patch(id, {
        last_login_date: dbDateNow(app),
      })


      ///commonet test
      
      const userInfoService = app.service('public/auth/user-info-core');
      const { accountType, firstName, lastName, isTotpUser } = await userInfoService.retrieveUserInfo(uid)
      return {
        email, id, uid,
        accountType, firstName, lastName, isTotpUser,
      }
    }

    const resetAttempts = async (email:string, id:number) : Promise<IBefore | IUserInfoCore> =>{
      const dbAuthTable = context.app.service('db/write/auths');   
      const authFindFieldQuery:Partial<IAuth> = { 
        email: email,
        id: id
      } 
      const matchingAuthRecord = <any> await dbAuthTable.find({
        query: { 
          ... authFindFieldQuery
        } 
      });
      if (matchingAuthRecord.total === 0){
        throw new Errors.GeneralError(AUTH_ERROR.NOT_FOUND)
      }

      const retrieveAuthRecord = matchingAuthRecord.data[0];   

      if(retrieveAuthRecord.is_pass_reset_req == 1){
        throw new Errors.Forbidden(AUTH_ERROR.PSW_RESET_REQ);
      }
      
      if(retrieveAuthRecord.failed_login_attempts === 0){
        return {}
      }
      if(retrieveAuthRecord.failed_login_attempts > 4){
        throw new Errors.Forbidden(AUTH_ERROR.MAX_LOGIN_ATTEMPT);
      }


      const authPatchFieldQuery:Partial<IAuth> = { 
        failed_login_attempts: 0
      }
      await dbAuthTable.patch(retrieveAuthRecord.id, {
        ... authPatchFieldQuery
      })
      return retrieveAuthRecord;
    }

    //if account is not enabled, reply with verification step
    if(result.enabled === 0){
      throw new Errors.Forbidden(AUTH_ERROR.NOT_VERIFIED)
    }

    // sanitize results
    if (Array.isArray(result)) {
      context.dispatch = await result.map(sanitize);
    }
    else if (result.data && context.method === 'find') {
      context.dispatch = Object.assign({}, result, {
          data: result.data.map(sanitize)
      });
    }
    else{
      context.dispatch = await sanitize(result);
    }
    context.result = context.dispatch;
    await resetAttempts(context.dispatch.email, context.dispatch.id)
    return context;

  };
}
