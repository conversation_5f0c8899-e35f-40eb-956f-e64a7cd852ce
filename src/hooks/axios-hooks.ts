import axios from 'axios';
import { HookContext } from '@feathersjs/feathers';

const axiosErrorHandler = async (context: HookContext): Promise<HookContext> => {
  // Check if there is an error and if it's an Axios error
  if (context.error && axios.isAxiosError(context.error)) {
    
    context.app?.service('public/log').create(
        {
            slug:"AXIOS_ERROR",
            data:{
                endpoint_params: context?.params,
                axios_error: {
                    stack: context.error.stack,
                    message: context.error.message,
                    requested_url: context?.error.config?.url || context.error.config?.baseURL,
                    method: context.error.config?.method,
                    headers: context.error.config?.headers,
                    data: context.error.config?.data,
                    axios_req_params: context.error.config?.params,
                    config: context.error.config // This will have the repeated elements from above but I think the above items need to be highlighted
                }
            }
        },
        context.params
    )
  }
  return context;
};

export default axiosErrorHandler;
