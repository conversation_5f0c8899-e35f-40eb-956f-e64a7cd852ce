{"name": "mtic3", "version": "1.0.0", "engines": {"node": "^18.18.2", "npm": ">=8.19.3"}, "scripts": {"ng": "ng", "start": "node --no-experimental-fetch --openssl-legacy-provider --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng serve", "start-alt": "ng serve --port 4401", "build": "ng build --prod ", "build-opt": "node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng build --prod --optimization && /bin/bash package-check.sh", "build-opt-with-src-maps": "NODE_OPTS='--max_old_space_size=14000 --no-experimental-fetch' ng build --prod --optimization --sourceMap=true && /bin/bash package-check.sh", "test": "ng test --watch false", "lint": "ng lint", "e2e": "cypress open", "build:all": "ng build --prod && ng run mtic3:server && npm run webpack:prerender && npm run prerender", "serve:fast": "node --no-experimental-fetch --max_old_space_size=14000 --openssl-legacy-provider ./node_modules/@angular/cli/bin/ng serve --source-map=false", "serve:prerender": "http-server dist/mtic3 -c-1", "webpack:prerender": "webpack --config webpack.prerender.config.js", "prerender": "node dist/mtic3/prerender.js", "cypress": "concurrently \"ng serve\" \"cypress open\"", "build:stats": "ng build --stats-json", "analyze-deprecated": "webpack-bundle-analyzer dist/mtic3/stats-es2015.json", "analyze": "source-map-explorer dist/mtic3/*.js"}, "private": true, "dependencies": {"@angular/animations": "^10.2.5", "@angular/cdk": "^10.2.7", "@angular/common": "^10.2.5", "@angular/compiler": "^10.2.5", "@angular/core": "^10.2.5", "@angular/forms": "^10.2.5", "@angular/material": "^10.2.7", "@angular/material-moment-adapter": "10.2.7", "@angular/platform-browser": "^10.2.5", "@angular/platform-browser-dynamic": "^10.2.5", "@angular/platform-server": "^10.2.5", "@angular/router": "^10.2.5", "@angular/service-worker": "^10.2.5", "@ckeditor/ckeditor5-angular": "^1.2.3", "@ckeditor/ckeditor5-basic-styles": "^25.0.0", "@ckeditor/ckeditor5-build-classic": "^25.0.0", "@ctrl/ngx-codemirror": "4.1.1", "@ctrl/ngx-emoji-mart": "^4.0.2", "@feathersjs/authentication-client": "^4.3.10", "@feathersjs/feathers": "^4.3.10", "@feathersjs/rest-client": "^4.3.10", "@fortawesome/angular-fontawesome": "^0.7.0", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fullcalendar/angular": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@swimlane/ngx-charts": "^16.0.0", "@tweenjs/tween.js": "^17.4.0", "@types/diff": "^5.0.0", "@types/jquery": "^3.5.1", "accounting-js": "1.1.1", "ag-grid-angular": "^25.3.0", "ag-grid-community": "^25.3.0", "amazon-chime-sdk-js": "^2.1.0", "ang-jsoneditor": "^1.10.5", "angular-split": "^3.0.3", "async-mutex": "^0.3.2", "ckeditor5-build-classic-alignment-highlight": "^12.1.1", "ckeditor5-build-classic-all-plugin": "^16.0.5", "ckeditor5-build-classic-balloon": "^1.0.0", "core-js": "^3.8.3", "deep-diff": "^1.0.2", "diagramatics": "1.1.3", "diff": "^5.0.0", "file-saver": "^2.0.5", "fuse": "^0.4.0", "fuse.js": "^3.4.0", "ip-cidr": "^2.1.4", "jquery": "^3.5.1", "jsoneditor": "^9.5.1", "jszip": "^3.6.0", "jwt-decode": "^3.1.2", "katex": "^0.16.8", "lodash": "^4.17.21", "mathlive": "0.68.0", "moment": "^2.29.4", "moment-timezone": "^0.5.27", "ng": "0.0.0", "ng-jcrop": "^2.2.1", "ng2-file-upload": "^1.4.0", "ng2-tooltip-directive": "^2.9.22", "ngx-autosize-input": "11.0.0", "ngx-clipboard": "^12.3.0", "ngx-json-viewer": "2.4.0", "ngx-markdown": "10.1.1", "ngx-mask": "^11.1.5", "ngx-plyr": "3.0.1", "normalize-diacritics": "^4.0.3", "npm": "^6.14.15", "papaparse": "^5.3.0", "peerjs": "^1.2.0", "pixi.js": "^5.3.9", "plyr": "^3.5.10", "postcss-loader": "3.0.0", "qrcode": "^1.5.0", "recorder-js": "^1.0.7", "rxjs": "~6.5.5", "socket.io-client": "^2.3.0", "sockette": "^2.0.6", "speech-rule-engine": "^5.0.0-alpha.8", "start": "^5.1.0", "tslib": "^2.0.0", "upgrade": "^1.1.0", "vidar": "^0.8.1", "words-to-numbers": "^1.5.1", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1002.4", "@angular/cli": "^10.2.4", "@angular/compiler-cli": "^10.2.5", "@angular/language-service": "^10.2.5", "@cypress/webpack-preprocessor": "^2.0.1", "@types/angular": "^1.7.0", "@types/chance": "^1.0.1", "@types/jasmine": "~2.8.6", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@types/tween.js": "^17.2.0", "@types/webspeechapi": "^0.0.29", "bulma": "^0.8.1", "chance": "^1.0.16", "codelyzer": "^6.0.2", "concurrently": "^5.2.0", "cypress": "^3.4.0", "fs-extra": "^6.0.1", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "1.5.4", "protractor": "~7.0.0", "sass": "^1.54.0", "source-map-explorer": "^2.5.2", "ts-loader": "^4.3.0", "ts-node": "~5.0.1", "tslint": "~6.1.0", "typescript": "4.0.8", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^2.1.3", "ws": "^5.1.1", "xmlhttprequest": "^1.8.0"}}