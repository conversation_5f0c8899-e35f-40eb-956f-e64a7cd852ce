{"name": "proc-cert-api", "description": "Back-end to support light-weight scheduling and test administration tool that connects Applicants, Test Administrators, Test Controllers, and the Certification Body.", "version": "0.0.0", "homepage": "", "main": "src", "keywords": ["feathers"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "engines": {"node": "^18.18.2", "npm": ">=8.19.3"}, "scripts": {"test": "npm run compile && npm run mocha", "dev": "ts-node-dev --no-notify src/", "start": "node lib/", "serve": "npm run compile && node lib/ --trace-warnings", "serve:watch": "nodemon --watch src --ext \"ts,js,json\" --exec \"npm run serve:fast\"", "serve:fast": "npm run compile:shc && node lib/ --trace-warnings", "production-serve": "isProduction=true npm run serve", "mocha": "ts-mocha \"test/**/*.ts\" --recursive --exit", "compile": "shx rm -rf lib/ && tsc --extendedDiagnostics", "compile:shc": "shx rm -rf lib/ && npx swc ./src -d lib/"}, "types": "lib/", "dependencies": {"@aws-sdk/client-cloudfront": "^3.451.0", "@aws-sdk/cloudfront-signer": "^3.451.0", "@feathersjs/authentication": "^4.5.18", "@feathersjs/authentication-local": "^4.5.18", "@feathersjs/authentication-oauth": "^4.5.18", "@feathersjs/cli": "^4.8.0", "@feathersjs/configuration": "^4.5.17", "@feathersjs/errors": "^4.5.17", "@feathersjs/express": "^4.5.18", "@feathersjs/feathers": "^4.5.17", "@feathersjs/socketio": "^4.5.18", "@types/lodash": "^4.14.201", "@types/seedrandom": "^3.0.8", "async": "^3.2.5", "async-es": "^2.6.4", "aws-sdk": "^2.1496.0", "axios": "^0.21.1", "bcrypt": "^5.1.1", "bluebird": "^3.7.2", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^1.8.2", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "exceljs": "^4.4.0", "express-fileupload": "^1.4.2", "feathers-hooks-common": "^4.20.7", "feathers-knex": "^8.0.1", "feathers-swagger": "^1.2.2", "helmet": "^3.23.3", "ioredis": "^5.3.2", "ip-cidr": "^2.1.5", "jwt-decode": "^2.2.0", "knex": "^2.5.1", "lodash": "^4.17.21", "lz-string": "^1.5.0", "markdown": "^0.5.0", "marked": "^0.8.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql2": "^1.7.0", "node-object-hash": "^2.3.10", "pdf-lib": "^1.17.1", "pdf-to-base64": "^1.0.3", "qs": "^6.11.2", "random-name": "^0.1.2", "redlock": "^5.0.0-beta2", "replace-special-characters": "^1.2.7", "safe-json-stringify": "^1.2.0", "seedrandom": "^3.0.5", "serve-favicon": "^2.5.0", "short-uuid": "^4.2.2", "source-map-support": "^0.5.21", "speakeasy": "^2.0.0", "sqlstring": "^2.3.3", "stripe": "^8.222.0", "turndown": "^7.1.2", "ua-parser-js": "^0.8.1", "uuid": "^8.3.2", "winston": "^3.11.0", "xlsx": "^0.18.5", "xml-js": "^1.6.11"}, "devDependencies": {"@swc/cli": "^0.1.62", "@swc/core": "^1.3.96", "@types/bluebird": "^3.5.42", "@types/compression": "0.0.36", "@types/cors": "^2.8.16", "@types/helmet": "0.0.48", "@types/jsonwebtoken": "^8.5.9", "@types/jwt-decode": "^3.1.0", "@types/mocha": "^5.2.7", "@types/qs": "^6.9.10", "@types/random-name": "^0.1.2", "@types/serve-favicon": "^2.5.7", "@types/uuid": "^8.3.4", "axios": "^0.27.2", "fs-extra": "^9.1.0", "mocha": "^6.2.3", "mustache": "^3.2.1", "nodemon": "^1.19.4", "shx": "^0.3.4", "ts-mocha": "^6.0.0", "ts-node-dev": "^1.1.8", "tslint": "^5.20.1", "typescript": "^4.9.5"}}